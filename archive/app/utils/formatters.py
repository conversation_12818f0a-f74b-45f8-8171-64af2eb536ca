# -*- coding: utf-8 -*-
"""
Formatting Utilities

This module contains utility functions for formatting numbers, percentages,
and other data for display purposes.
"""

import logging

logger = logging.getLogger(__name__)


def safe_float(value, default=0.0):
    """Safely convert a value to float, returning a default if conversion fails."""
    if value is None:
        return default
    try:
        if isinstance(value, str):
            # Remove currency symbols, commas, percentage signs before conversion
            value = value.replace(',', '').replace('$', '').replace('%', '').strip()
            if value == '' or value.lower() in ['na', 'n/a', 'none', '-']:
                return default
        return float(value)
    except (ValueError, TypeError):
        return default


def handle_round(value, digits=2):
    """Handles rounding for template display, returning '—' if value is None or non-numeric."""
    num_value = safe_float(value, None)  # Use None as default to distinguish failure
    if num_value is None:
        return "—"  # Use consistent dash instead of N/A
    try:
        # Avoid rounding 'inf' or '-inf'
        if abs(num_value) == float('inf'):
            return "—"  # Return dash for infinity values too
        return round(num_value, digits)
    except (TypeError, ValueError):
        logger.warning(f"Could not round value '{value}' in handle_round.")
        return "—"  # Use consistent dash instead of N/A


def format_large_number(value, decimals=2, is_monetary=False):
    """Formats large numbers into K/M/B/T for display, with proper handling of edge cases."""
    num = safe_float(value, None)
    if num is None:
        return "—"  # Use consistent dash instead of N/A

    # Handle zero
    if num == 0:
        return "$0" if is_monetary else "0"

    # Handle very large numbers that might cause display issues
    if abs(num) >= 1_000_000_000_000:  # Trillions
        formatted = f"{num / 1_000_000_000_000:.{decimals}f}T"
    elif abs(num) >= 1_000_000_000:  # Billions
        formatted = f"{num / 1_000_000_000:.{decimals}f}B"
    elif abs(num) >= 1_000_000:  # Millions
        formatted = f"{num / 1_000_000:.{decimals}f}M"
    elif abs(num) >= 1_000:  # Thousands
        formatted = f"{num / 1_000:.{decimals}f}K"
    else:
        formatted = f"{num:.{decimals}f}"

    # Add currency prefix if monetary
    if is_monetary and not formatted.startswith('-'):
        formatted = f"${formatted}"
    elif is_monetary and formatted.startswith('-'):
        formatted = f"-${formatted[1:]}"

    return formatted


def format_percentage(value, decimals=2):
    """Format a value as a percentage with proper handling of edge cases."""
    num = safe_float(value, None)
    if num is None:
        return "—"

    # Convert to percentage if the value is in decimal form (0.15 -> 15%)
    if abs(num) <= 1:
        percentage = num * 100
    else:
        percentage = num

    return f"{percentage:.{decimals}f}%"


def format_metric_with_peer_context(value, benchmark_value, metric_name, is_higher_better=True):
    """
    Format a metric value with peer comparison context.

    Args:
        value (float): Company's metric value
        benchmark_value (float): Industry benchmark value
        metric_name (str): Name of the metric
        is_higher_better (bool): Whether higher values are better

    Returns:
        str: Formatted string with peer context
    """
    if value is None:
        return "N/A"

    if benchmark_value is None:
        return f"{value:.2%}" if metric_name in ['roic', 'return_on_equity', 'operating_margin'] else f"{value:.2f}"

    # Format the value
    if metric_name in ['roic', 'return_on_equity', 'return_on_assets', 'operating_margin', 'gross_margin', 'net_margin', 'fcf_yield']:
        value_str = f"{value:.2%}"
        benchmark_str = f"{benchmark_value:.2%}"
    else:
        value_str = f"{value:.2f}"
        benchmark_str = f"{benchmark_value:.2f}"

    # Calculate performance vs benchmark
    ratio = value / benchmark_value

    if is_higher_better:
        if ratio >= 1.2:
            performance = "significantly above"
        elif ratio >= 1.05:
            performance = "above"
        elif ratio >= 0.95:
            performance = "in line with"
        elif ratio >= 0.8:
            performance = "below"
        else:
            performance = "significantly below"
    else:
        if ratio <= 0.8:
            performance = "significantly better than"
        elif ratio <= 0.95:
            performance = "better than"
        elif ratio <= 1.05:
            performance = "in line with"
        elif ratio <= 1.2:
            performance = "worse than"
        else:
            performance = "significantly worse than"

    return f"{value_str} ({performance} industry avg of {benchmark_str})"
