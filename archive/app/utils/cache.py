# -*- coding: utf-8 -*-
"""
Cache Utilities

This module contains caching utilities and decorators.
"""

from datetime import datetime, timedelta, timezone
from functools import lru_cache, wraps
import logging

logger = logging.getLogger(__name__)


def timed_lru_cache(seconds: int, maxsize: int = 128):
    """
    Simple timed LRU cache decorator.
    
    Args:
        seconds (int): Cache expiration time in seconds
        maxsize (int): Maximum cache size
    
    Returns:
        Decorator function
    """
    def wrapper_cache(func):
        _func = lru_cache(maxsize=maxsize)(func)  # Basic LRU
        _func.delta = timedelta(seconds=seconds)
        _func.expiration = datetime.now(timezone.utc) + _func.delta

        @wraps(func)
        def wrapped_func(*args, **kwargs):
            now = datetime.now(timezone.utc)
            if now >= _func.expiration:
                _func.cache_clear()
                _func.expiration = now + _func.delta
                logger.info(f"Cache expired and cleared for {func.__name__}")
            return _func(*args, **kwargs)
        
        # Add a way to manually clear this specific cache if needed
        wrapped_func.cache_clear = _func.cache_clear
        wrapped_func.cache_info = _func.cache_info
        return wrapped_func
    return wrapper_cache
