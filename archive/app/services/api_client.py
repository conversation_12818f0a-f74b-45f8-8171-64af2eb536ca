# -*- coding: utf-8 -*-
"""
API Client Service

This module handles all external API integrations including EODHD, Google Search,
and LLM APIs.
"""

import requests
import json
from datetime import date, timedelta
from flask import current_app

from ..utils.cache import timed_lru_cache
from ..utils.formatters import safe_float


def fetch_data(url, use_eodhd=False, is_json=True, params=None, timeout=10):
    """
    Generic data fetching function with error handling.
    
    Args:
        url (str): API endpoint URL
        use_eodhd (bool): Whether to use EODHD API key
        is_json (bool): Whether to parse response as JSON
        params (dict): Additional URL parameters
        timeout (int): Request timeout in seconds
        
    Returns:
        dict/str/None: API response data or None on error
    """
    try:
        # Add API key for EODHD requests
        if use_eodhd:
            if not current_app.config.get('EODHD_API_KEY'):
                current_app.logger.error("EODHD API key not configured")
                return None
            
            if params is None:
                params = {}
            params['api_token'] = current_app.config['EODHD_API_KEY']
        
        response = requests.get(url, params=params, timeout=timeout)
        response.raise_for_status()
        
        if is_json:
            data = response.json()
            
            # Handle EODHD specific error responses
            if isinstance(data, dict) and data.get('error'):
                if 'not found' in data['error'].lower():
                    return "Ticker Not Found"
                current_app.logger.error(f"API error response: {data['error']}")
                return None
            
            return data
        else:
            return response.text
            
    except requests.exceptions.Timeout:
        current_app.logger.error(f"Request timeout for URL: {url}")
        return None
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Request error for URL {url}: {e}")
        return None
    except json.JSONDecodeError as e:
        current_app.logger.error(f"JSON decode error for URL {url}: {e}")
        return None
    except Exception as e:
        current_app.logger.error(f"Unexpected error fetching data from {url}: {e}")
        return None


@timed_lru_cache(seconds=3600 * 1, maxsize=256)
def fetch_eodhd_fundamentals(ticker_with_exchange):
    """Fetches fundamental data from EODHD. Expects SYMBOL.EXCHANGE format."""
    ticker_eod = ticker_with_exchange.upper().strip()
    if '.' not in ticker_eod:
        current_app.logger.error(f"❌ Ticker '{ticker_eod}' missing exchange code. Cannot fetch fundamentals.")
        return None

    url = f"https://eodhd.com/api/fundamentals/{ticker_eod}"
    current_app.logger.info(f"🔍 Fetching EODHD fundamentals for {ticker_eod}")
    data = fetch_data(url, use_eodhd=True, is_json=True)

    # Handle specific "Ticker not found." response from fetch_data
    if isinstance(data, str) and data == "Ticker Not Found":
        current_app.logger.warning(f"⚠️  EODHD fundamentals: Ticker not found for {ticker_eod}")
        return None

    if isinstance(data, dict) and 'General' in data and 'Name' in data['General']:
        current_app.logger.info(f"✅ Successfully fetched EODHD fundamentals for {ticker_eod} - Company: {data['General']['Name']}")

        # Log available data sections for debugging
        sections = list(data.keys())
        current_app.logger.debug(f"📊 Available data sections for {ticker_eod}: {sections}")

        # Check for critical financial data
        if 'Financials' in data:
            current_app.logger.debug(f"💰 Financial data available for {ticker_eod}")
        else:
            current_app.logger.warning(f"⚠️  No financial data found for {ticker_eod}")

        if 'Highlights' in data:
            current_app.logger.debug(f"📈 Highlights data available for {ticker_eod}")
        else:
            current_app.logger.warning(f"⚠️  No highlights data found for {ticker_eod}")

        return data
    elif isinstance(data, dict) and not data:
        current_app.logger.warning(f"❌ Received empty dictionary for EODHD fundamentals for {ticker_eod}.")
        return None
    elif data is not None:
        current_app.logger.warning(f"❌ Unexpected EODHD fundamentals format for {ticker_eod}: Type {type(data)}, Data: {str(data)[:200]}...")
        return None
    
    current_app.logger.error(f"❌ Failed to fetch fundamentals for {ticker_eod} - data is None")
    return None


@timed_lru_cache(seconds=60 * 2, maxsize=512)
def fetch_eodhd_realtime_price(ticker_with_exchange):
    """Fetches real-time price data from EODHD. Expects SYMBOL.EXCHANGE format."""
    ticker_eod = ticker_with_exchange.upper().strip()
    if '.' not in ticker_eod:
        current_app.logger.error(f"Ticker '{ticker_eod}' missing exchange code. Cannot fetch real-time price.")
        return None

    url = f"https://eodhd.com/api/real-time/{ticker_eod}"
    data = fetch_data(url, use_eodhd=True, is_json=True, timeout=5)

    # Handle specific "Ticker not found." response
    if isinstance(data, str) and data == "Ticker Not Found":
        current_app.logger.warning(f"EODHD real-time: Ticker not found for {ticker_eod}")
        return None

    price_data = None
    if isinstance(data, list) and data:
        price_data = data[0]
    elif isinstance(data, dict):
        price_data = data

    # Check for a valid 'close' price
    if isinstance(price_data, dict) and safe_float(price_data.get('close'), None) is not None:
        return price_data
    else:
        if price_data is not None:
            current_app.logger.warning(f"Could not find valid 'close' price in EODHD real-time data for {ticker_eod}: {price_data}")
        return None


@timed_lru_cache(seconds=3600 * 4, maxsize=1024)
def fetch_eodhd_historical_ohlc(ticker_with_exchange, start_date, end_date):
    """Fetch Daily OHLC data from EODHD for the given period. Expects SYMBOL.EXCHANGE format."""
    ticker_eod = ticker_with_exchange.upper().strip()
    if '.' not in ticker_eod:
        current_app.logger.error(f"Ticker '{ticker_eod}' missing exchange code. Cannot fetch historical data.")
        return {}

    url = f"https://eodhd.com/api/eod/{ticker_eod}"
    params = {
        'from': start_date,
        'to': end_date,
        'period': 'd',
    }
    current_app.logger.info(f"Fetching EODHD Historical OHLC for {ticker_eod} from {start_date} to {end_date}")
    data = fetch_data(url, use_eodhd=True, is_json=True, params=params)

    # Handle specific "Ticker not found." response
    if isinstance(data, str) and data == "Ticker Not Found":
        current_app.logger.warning(f"EODHD historical: Ticker not found for {ticker_eod}")
        return {}

    if data and isinstance(data, list):
        ohlc_dict = {}
        valid_points = 0
        for day_data in data:
            close_price = day_data.get('adjusted_close', day_data.get('close'))
            if ('date' in day_data and close_price is not None
                and safe_float(close_price, None) is not None):
                ohlc_dict[day_data['date']] = {
                    'open': safe_float(day_data.get('open')),
                    'high': safe_float(day_data.get('high')),
                    'low': safe_float(day_data.get('low')),
                    'close': safe_float(close_price),
                    'volume': safe_float(day_data.get('volume'))
                }
                valid_points += 1
        current_app.logger.info(f"Successfully processed {valid_points} EODHD OHLC data points for {ticker_eod}.")
        return ohlc_dict
    elif data is None:
        current_app.logger.error(f"Fetch returned None for EODHD Historical OHLC for {ticker_eod}.")
    else:
        current_app.logger.warning(f"No historical OHLC data returned or unexpected format for {ticker_eod}: Type {type(data)}, Data: {str(data)[:200]}...")
    return {}


@timed_lru_cache(seconds=60 * 15, maxsize=256)
def fetch_eodhd_news(ticker_with_exchange=None, tag=None, limit=5):
    """Fetches latest news from EODHD by ticker or tag."""
    url = "https://eodhd.com/api/news"
    params = {'limit': limit}
    
    if ticker_with_exchange:
        params['s'] = ticker_with_exchange.upper().strip()
    elif tag:
        params['t'] = tag
    else:
        current_app.logger.warning("No ticker or tag provided for news fetch")
        return None
    
    current_app.logger.info(f"Fetching EODHD news for {ticker_with_exchange or tag}")
    data = fetch_data(url, use_eodhd=True, is_json=True, params=params)
    
    if isinstance(data, list):
        current_app.logger.info(f"Successfully fetched {len(data)} news items")
        return data
    else:
        current_app.logger.warning(f"Unexpected news data format: {type(data)}")
        return None


@timed_lru_cache(seconds=3600 * 1, maxsize=512)
def fetch_eodhd_sentiments(ticker_with_exchange):
    """Fetches latest sentiment data for a ticker from EODHD."""
    ticker_eod = ticker_with_exchange.upper().strip()
    url = f"https://eodhd.com/api/sentiments/{ticker_eod}"
    
    current_app.logger.info(f"Fetching EODHD sentiments for {ticker_eod}")
    data = fetch_data(url, use_eodhd=True, is_json=True)
    
    if isinstance(data, dict) and 'sentiment' in data:
        current_app.logger.info(f"Successfully fetched sentiment data for {ticker_eod}")
        return data
    else:
        current_app.logger.warning(f"No sentiment data available for {ticker_eod}")
        return None


@timed_lru_cache(seconds=3600 * 2, maxsize=128)
def fetch_fmp_dcf(ticker):
    """Fetches DCF value from Financial Modeling Prep."""
    fmp_key = current_app.config.get('FMP_API_KEY')
    if not fmp_key:
        return None
    
    url = f"https://financialmodelingprep.com/api/v3/discounted-cash-flow/{ticker}"
    params = {'apikey': fmp_key}
    
    current_app.logger.info(f"Fetching FMP DCF for {ticker}")
    data = fetch_data(url, is_json=True, params=params)
    
    if isinstance(data, list) and data:
        dcf_data = data[0]
        if 'dcf' in dcf_data:
            current_app.logger.info(f"Successfully fetched FMP DCF for {ticker}")
            return dcf_data['dcf']
    
    current_app.logger.warning(f"No DCF data available from FMP for {ticker}")
    return None


@timed_lru_cache(seconds=3600, maxsize=50)
def perform_google_search(query, num_results=5):
    """Performs Google Custom Search and returns specified number of results."""
    google_key = current_app.config.get('GOOGLE_API_KEY')
    google_cse = current_app.config.get('GOOGLE_CSE_ID')
    
    if not google_key or not google_cse:
        current_app.logger.warning("Google API Key or CSE ID not configured for search.")
        return None

    service_url = 'https://www.googleapis.com/customsearch/v1'
    params = {'key': google_key, 'cx': google_cse, 'q': query, 'num': num_results}
    current_app.logger.info(f"Performing Google Search for query: '{query}', num_results: {num_results}")

    try:
        response = requests.get(service_url, params=params, timeout=7)
        response.raise_for_status()
        results = response.json()
        items = results.get('items', [])
        if items:
            current_app.logger.info(f"Google Search returned {len(items)} items.")
            return items
        else:
            current_app.logger.info(f"Google Search returned no items for query: '{query}'")
            return []
    except requests.exceptions.RequestException as e:
        status_code = e.response.status_code if e.response is not None else 'N/A'
        current_app.logger.error(f"Google Search API request error: Status {status_code}, Error: {e}")
        
        if e.response is not None:
            try:
                error_detail = e.response.json()
                current_app.logger.error(f"Google Search API Error Detail: {error_detail}")
                if 'error' in error_detail and 'message' in error_detail['error']:
                    if 'API key not valid' in error_detail['error']['message']:
                        return "API_KEY_INVALID"
                    elif 'Quota exceeded' in error_detail['error']['message']:
                        return "QUOTA_EXCEEDED"
            except json.JSONDecodeError:
                current_app.logger.error(f"Could not decode JSON error response from Google Search API.")
        return "SEARCH_API_ERROR"
    except Exception as e:
        current_app.logger.error(f"Google Search processing error: {e}", exc_info=True)
        return "SEARCH_PROCESSING_ERROR"


# LLM API integration functions
def call_openai_api(prompt, model="gpt-3.5-turbo", max_tokens=1000):
    """Call OpenAI API with the given prompt."""
    api_key = current_app.config.get('OPENAI_API_KEY')
    if not api_key:
        return {"error": "OpenAI API key not configured"}
    
    try:
        import openai
        openai.api_key = api_key
        
        response = openai.ChatCompletion.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=max_tokens,
            temperature=0.7
        )
        
        return {
            "response": response.choices[0].message.content,
            "usage": response.usage
        }
    except Exception as e:
        current_app.logger.error(f"OpenAI API error: {e}")
        return {"error": str(e)}


def call_anthropic_api(prompt, model="claude-3-sonnet-20240229", max_tokens=1000):
    """Call Anthropic API with the given prompt."""
    api_key = current_app.config.get('ANTHROPIC_API_KEY')
    if not api_key:
        return {"error": "Anthropic API key not configured"}
    
    try:
        import anthropic
        client = anthropic.Anthropic(api_key=api_key)
        
        response = client.messages.create(
            model=model,
            max_tokens=max_tokens,
            messages=[{"role": "user", "content": prompt}]
        )
        
        return {
            "response": response.content[0].text,
            "usage": response.usage
        }
    except Exception as e:
        current_app.logger.error(f"Anthropic API error: {e}")
        return {"error": str(e)}


def call_google_gemini_api(prompt, model="gemini-pro", max_tokens=1000):
    """Call Google Gemini API with the given prompt."""
    api_key = current_app.config.get('GEMINI_API_KEY')
    if not api_key:
        return {"error": "Gemini API key not configured"}
    
    try:
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        
        model_instance = genai.GenerativeModel(model)
        response = model_instance.generate_content(prompt)
        
        return {
            "response": response.text,
            "usage": {"prompt_tokens": 0, "completion_tokens": 0}  # Gemini doesn't provide detailed usage
        }
    except Exception as e:
        current_app.logger.error(f"Gemini API error: {e}")
        return {"error": str(e)}