# -*- coding: utf-8 -*-
"""
Chatbot Service

This module contains chatbot business logic including message processing,
LLM integration, and response generation.
"""

import re
import random
import markdown
from flask import current_app, session

from .api_client import call_openai_api, call_anthropic_api, call_google_gemini_api, perform_google_search


# LLM Dispatch Dictionary
LLM_DISPATCH = {
    'openai': call_openai_api,
    'anthropic': call_anthropic_api,
    'gemini': call_google_gemini_api,
}


def process_message(message, context=None):
    """
    Process user message and generate appropriate response.
    
    Args:
        message (str): User message
        context (dict): Optional context information
        
    Returns:
        dict: Response with message, type, and optional metadata
    """
    try:
        current_app.logger.info(f"Chatbot received message: '{message}'")
        normalized_message = message.lower().strip()
        
        # Check for pending analysis state
        pending_analysis = session.get('pending_analysis', None)
        years_match = re.search(r'(\d{1,2})', message.strip())
        
        if pending_analysis and years_match:
            return handle_pending_analysis(message, years_match, pending_analysis)
        elif pending_analysis:
            # Clear stale pending state
            session.pop('pending_analysis', None)
            session.modified = True
        
        # Handle different message types
        response = handle_greetings(normalized_message)
        if response:
            return response
            
        response = handle_navigation(normalized_message)
        if response:
            return response
            
        response = handle_financial_commands(message)
        if response:
            return response
            
        # Default to general LLM response or search
        return handle_general_query(message)
        
    except Exception as e:
        current_app.logger.error(f"Error processing chatbot message: {e}")
        return {
            'message': "Sorry, I encountered an issue processing your request.",
            'type': 'error'
        }


def handle_greetings(normalized_message):
    """Handle greeting messages."""
    greetings = ["hi", "hello", "hey", "greetings", "good morning", "good afternoon", "good evening"]
    
    if normalized_message in greetings:
        current_app.logger.info("Chatbot detected greeting.")
        return {
            'message': "Hello! How can I help you with stock information or site navigation today? 😊",
            'type': 'greeting'
        }
    
    if "how are you" in normalized_message:
        current_app.logger.info("Chatbot detected 'how are you'.")
        return {
            'message': "I'm just a bot, but I'm running smoothly and ready to search for financial data!",
            'type': 'status'
        }
    
    if normalized_message in ["thank you", "thanks", "thx"]:
        current_app.logger.info("Chatbot detected thanks.")
        return {
            'message': "You're welcome! Let me know if you need anything else.",
            'type': 'acknowledgement'
        }
    
    return None


def handle_navigation(normalized_message):
    """Handle navigation requests."""
    nav_portfolio = ["go to portfolio", "show portfolio", "open portfolio", "view portfolio", 
                    "where is the portfolio", "portfolio page", "portfolio dashboard"]
    nav_dcf = ["go to dcf", "show dcf", "open dcf", "dcf calculator", "discounted cash flow"]
    nav_home = ["go home", "home page", "main page"]
    
    if any(phrase in normalized_message for phrase in nav_portfolio):
        current_app.logger.info("Chatbot detected navigation intent: Portfolio")
        return {
            'message': "Okay, navigating you to the [Portfolio Dashboard](/portfolio).",
            'type': 'navigation_link'
        }
    elif any(phrase in normalized_message for phrase in nav_dcf):
        current_app.logger.info("Chatbot detected navigation intent: DCF")
        return {
            'message': "Okay, navigating you to the [DCF Calculator](/dcf).",
            'type': 'navigation_link'
        }
    elif any(phrase in normalized_message for phrase in nav_home):
        current_app.logger.info("Chatbot detected navigation intent: Home")
        return {
            'message': "Okay, navigating you to the [Home Page](/).",
            'type': 'navigation_link'
        }
    
    return None


def handle_financial_commands(message):
    """Handle financial analysis commands."""
    # Extract ticker from message
    ticker = extract_ticker_context(message)
    
    if ticker:
        # Check for analysis request
        analysis_keywords = ["analyze", "analysis", "evaluate", "review", "assess"]
        if any(keyword in message.lower() for keyword in analysis_keywords):
            current_app.logger.info(f"Detected analysis request for ticker: {ticker}")
            
            # Set pending analysis state
            session['pending_analysis'] = {'ticker': ticker}
            session.modified = True
            
            return {
                'message': f"I'll analyze {ticker} for you. How many years would you like me to use for the DCF analysis? (Please specify between 1-30 years)",
                'type': 'analysis_prompt',
                'ticker': ticker
            }
    
    return None


def handle_pending_analysis(message, years_match, pending_analysis):
    """Handle continuation of pending analysis."""
    years = int(years_match.group(1))
    if 1 <= years <= 30:
        ticker_to_analyze = pending_analysis['ticker']
        current_app.logger.info(f"Continuing analysis for {ticker_to_analyze} with {years} years.")
        
        # Clear the pending state
        session.pop('pending_analysis', None)
        session.modified = True
        
        # Generate analysis (this would call the actual analysis function)
        analysis_result = generate_stock_analysis(ticker_to_analyze, years)
        
        return {
            'message': analysis_result['html'],
            'type': 'analysis_response',
            'analysis': {
                'score': analysis_result.get('score', 75),
                'ticker': ticker_to_analyze
            }
        }
    else:
        # Invalid number of years
        session.pop('pending_analysis', None)
        session.modified = True
        current_app.logger.warning(f"Invalid number of years ({years}) provided for pending analysis.")
        return {
            'message': "Please provide a number of years between 1 and 30.",
            'type': 'error'
        }


def handle_general_query(message):
    """Handle general queries using LLM or search."""
    # Try to use available LLM
    llm_response = try_llm_response(message)
    if llm_response:
        return llm_response
    
    # Fallback to search
    search_response = try_search_response(message)
    if search_response:
        return search_response
    
    # Final fallback
    return {
        'message': "I'm sorry, I couldn't find relevant information for your query. Please try rephrasing or ask about specific stocks.",
        'type': 'fallback'
    }


def try_llm_response(message):
    """Try to generate response using available LLM."""
    try:
        # Check for available API keys
        selected_llm_key = None
        llm_api_key = None
        
        # Check session for stored API keys
        for model_key in LLM_DISPATCH.keys():
            api_key_session_name = f'api_key_{model_key}'
            if api_key_session_name in session and session[api_key_session_name]:
                selected_llm_key = model_key
                llm_api_key = session[api_key_session_name]
                break
        
        if selected_llm_key and llm_api_key and selected_llm_key in LLM_DISPATCH:
            current_app.logger.info(f"Routing general query to LLM: {selected_llm_key}")
            llm_function = LLM_DISPATCH[selected_llm_key]
            
            general_prompt = (
                f"You are H Trader Assistant, a helpful AI integrated into a finance website. "
                f"Answer the following user query concisely and helpfully. If it relates to finance, "
                f"use your general knowledge. If it asks about the website, explain its features "
                f"(portfolio, DCF, stock details, AI chat). Avoid giving financial advice.\n\n"
                f"User Query: {message}"
            )
            
            response = llm_function(general_prompt)
            if response and 'response' in response and not response.get('error'):
                formatted_response = markdown.markdown(response['response'])
                return {
                    'message': formatted_response,
                    'type': 'llm_response'
                }
        
        return None
        
    except Exception as e:
        current_app.logger.error(f"Error in LLM response: {e}")
        return None


def try_search_response(message):
    """Try to generate response using search."""
    try:
        search_results = perform_google_search(message, num_results=3)
        
        if search_results and isinstance(search_results, list) and len(search_results) > 0:
            response_html = "<p>Here's what I found:</p><ul>"
            for item in search_results[:3]:
                title = item.get('title', 'No Title')
                link = item.get('link', '#')
                snippet = item.get('snippet', 'No description available.')
                response_html += f'<li><a href="{link}" target="_blank">{title}</a><br><small>{snippet}</small></li>'
            response_html += "</ul>"
            
            return {
                'message': response_html,
                'type': 'search_results'
            }
        
        return None
        
    except Exception as e:
        current_app.logger.error(f"Error in search response: {e}")
        return None


def extract_ticker_context(message):
    """
    Extract ticker symbol from message.
    
    Args:
        message (str): User message
        
    Returns:
        str or None: Extracted ticker symbol
    """
    try:
        # Look for common ticker patterns
        ticker_patterns = [
            r'\b([A-Z]{1,5})\b',  # 1-5 uppercase letters
            r'\$([A-Z]{1,5})\b',  # Dollar sign followed by ticker
            r'\b([A-Z]{1,5}\.[A-Z]{1,3})\b'  # Ticker with exchange (e.g., AAPL.US)
        ]
        
        for pattern in ticker_patterns:
            matches = re.findall(pattern, message.upper())
            if matches:
                # Return first match, could be enhanced with validation
                ticker = matches[0]
                # Basic validation - exclude common words
                excluded_words = ['THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'HAD', 'BUT', 'WHAT', 'SO', 'UP', 'OUT', 'IF', 'ABOUT', 'WHO', 'GET', 'WHICH', 'GO', 'ME']
                if ticker not in excluded_words and len(ticker) >= 1:
                    return ticker
        
        return None
        
    except Exception as e:
        current_app.logger.error(f"Error extracting ticker: {e}")
        return None


def generate_stock_analysis(ticker, years):
    """
    Generate stock analysis (placeholder - would integrate with actual analysis functions).
    
    Args:
        ticker (str): Stock ticker
        years (int): Number of years for DCF
        
    Returns:
        dict: Analysis results
    """
    try:
        # This would integrate with the actual analysis generation functions
        # For now, return a placeholder
        
        analysis_html = f"""
        <div class="analysis-result">
            <h3>Analysis for {ticker}</h3>
            <p>DCF Analysis using {years} years projection:</p>
            <ul>
                <li>Fair Value: $XX.XX</li>
                <li>Current Price: $XX.XX</li>
                <li>Recommendation: Hold</li>
            </ul>
            <p><em>This is a placeholder analysis. Full integration with analysis functions pending.</em></p>
        </div>
        """
        
        return {
            'html': analysis_html,
            'score': random.randint(60, 95)
        }
        
    except Exception as e:
        current_app.logger.error(f"Error generating stock analysis: {e}")
        return {
            'html': f"<p>Error generating analysis for {ticker}: {str(e)}</p>",
            'score': 50
        }


def get_chatbot_settings():
    """Get current chatbot settings."""
    try:
        settings = {
            'available_models': list(LLM_DISPATCH.keys()),
            'api_key_status': {},
            'default_model': session.get('default_llm_model', 'openai')
        }
        
        # Check API key status
        for model_key in LLM_DISPATCH.keys():
            api_key_session_name = f'api_key_{model_key}'
            settings['api_key_status'][model_key] = bool(session.get(api_key_session_name))
        
        return settings
        
    except Exception as e:
        current_app.logger.error(f"Error getting chatbot settings: {e}")
        return {'error': str(e)}


def update_chatbot_settings(settings_data):
    """Update chatbot settings."""
    try:
        # Update default model
        if 'default_model' in settings_data:
            session['default_llm_model'] = settings_data['default_model']
        
        # Update API keys
        if 'api_keys' in settings_data:
            for model_key, api_key in settings_data['api_keys'].items():
                if model_key in LLM_DISPATCH:
                    api_key_session_name = f'api_key_{model_key}'
                    if api_key:
                        session[api_key_session_name] = api_key
                    else:
                        session.pop(api_key_session_name, None)
        
        session.modified = True
        return {'success': True, 'message': 'Settings updated successfully'}
        
    except Exception as e:
        current_app.logger.error(f"Error updating chatbot settings: {e}")
        return {'success': False, 'error': str(e)}


def clear_api_keys():
    """Clear all stored API keys."""
    try:
        keys_cleared = []
        for model_key in LLM_DISPATCH.keys():
            api_key_session_name = f'api_key_{model_key}'
            if api_key_session_name in session:
                session.pop(api_key_session_name)
                keys_cleared.append(model_key)
        
        session.modified = True
        return {
            'success': True,
            'message': f'Cleared API keys for: {", ".join(keys_cleared)}' if keys_cleared else 'No API keys to clear'
        }
        
    except Exception as e:
        current_app.logger.error(f"Error clearing API keys: {e}")
        return {'success': False, 'error': str(e)}