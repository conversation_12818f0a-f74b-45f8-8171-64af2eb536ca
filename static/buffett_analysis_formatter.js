/* static/buffett_analysis_formatter.js */
/* Comprehensive Buffett-Munger Style Analysis Text Formatter */

/**
 * Advanced Buffett-Munger Analysis Formatter
 * Fixes structural, analytical, and presentation issues in stock analysis
 */
class BuffettAnalysisFormatter {
    constructor() {
        this.placeholderPatterns = [
            // HTML/JavaScript artifacts
            /\+\s*generate\w+HTML\(\)\s*\+/gi,
            /\+\s*generate\w+ChartHTML\([^)]*\)\s*\+/gi,
            /\+\s*\w+Chart\w*\([^)]*\)\s*\+/gi,
            /\{\{\s*\w+\s*\}\}/g,
            /\$\{\s*\w+\s*\}/g,
            /<script[^>]*>[\s\S]*?<\/script>/gi,
            /function\s+\w+\([^)]*\)\s*\{[^}]*\}/gi,

            // Template artifacts
            /\'\s*\+\s*\'/g,
            /\"\s*\+\s*\"/g,
            /\s*\+\s*\'/g,
            /\'\s*\+\s*/g,
            /\s*\+\s*\"/g,
            /\"\s*\+\s*/g,

            // Visual Rating Component artifacts
            /\'\s*\+\s*Visual Rating Component:/gi,
            /Visual Rating Component\s*\+/gi,
            /\+\s*Visual Rating Component/gi,
        ];

        this.ratingPatterns = {
            overall: /Overall Rating:?\s*(\d+(?:\.\d+)?)\s*(?:\/\d+)?\s*\((\d+(?:\.\d+)?)%?\)/gi,
            business: /Business Quality(?:\/Moat)?:?\s*(\d+(?:\.\d+)?)\s*(?:\/\d+)?\s*\((\d+(?:\.\d+)?)%?\)/gi,
            financial: /Financial (?:Strength|Health):?\s*(\d+(?:\.\d+)?)\s*(?:\/\d+)?\s*\((\d+(?:\.\d+)?)%?\)/gi,
            valuation: /Valuation:?\s*(\d+(?:\.\d+)?)\s*(?:\/\d+)?\s*\((\d+(?:\.\d+)?)%?\)/gi,
            management: /Management:?\s*(\d+(?:\.\d+)?)\s*(?:\/\d+)?\s*\((\d+(?:\.\d+)?)%?\)/gi,
            moat: /(?:Economic\s+)?Moat:?\s*(\d+(?:\.\d+)?)\s*(?:\/\d+)?\s*\((\d+(?:\.\d+)?)%?\)/gi
        };

        this.buffettPrinciples = {
            circleOfCompetence: [
                'understanding the business model',
                'predictable earnings',
                'simple business operations',
                'consistent profitability'
            ],
            economicMoat: [
                'brand strength',
                'pricing power',
                'switching costs',
                'network effects',
                'cost advantages',
                'regulatory protection'
            ],
            managementQuality: [
                'capital allocation skills',
                'shareholder orientation',
                'transparent communication',
                'long-term thinking'
            ],
            marginOfSafety: [
                'conservative valuation',
                'discount to intrinsic value',
                'downside protection',
                'stress-tested assumptions'
            ]
        };

        this.investmentGrades = {
            90: { grade: 'A+', description: 'Exceptional - Warren\'s dream investment' },
            85: { grade: 'A', description: 'Outstanding - Wonderful business at fair price' },
            80: { grade: 'A-', description: 'Excellent - Strong competitive advantages' },
            75: { grade: 'B+', description: 'Good - Solid long-term investment' },
            70: { grade: 'B', description: 'Above Average - Reasonable investment' },
            65: { grade: 'B-', description: 'Average - Fair value with some concerns' },
            60: { grade: 'C+', description: 'Below Average - Limited upside' },
            55: { grade: 'C', description: 'Mediocre - Significant concerns' },
            50: { grade: 'C-', description: 'Poor - Avoid unless deeply discounted' },
            0: { grade: 'F', description: 'Unacceptable - Does not meet Buffett criteria' }
        };
    }

    /**
     * Main formatting function
     */
    formatBuffettAnalysis(rawText, metadata = {}) {
        console.log('[BuffettFormatter] Processing analysis text...');

        let formatted = rawText;

        // Step 1: Remove all artifacts and placeholders
        formatted = this.removeAllArtifacts(formatted);

        // Step 2: Extract and validate ratings
        const ratings = this.extractAndValidateRatings(formatted);

        // Step 3: Fix structural issues
        formatted = this.fixStructuralProblems(formatted);

        // Step 4: Ensure analytical consistency
        formatted = this.ensureAnalyticalConsistency(formatted, ratings, metadata);

        // Step 5: Apply Buffett-Munger framework
        formatted = this.applyBuffettFramework(formatted, ratings, metadata);

        // Step 6: Enhance presentation and add visual elements
        formatted = this.enhancePresentation(formatted, ratings, metadata);

        // Step 7: Final quality check and cleanup
        formatted = this.finalQualityCheck(formatted);

        console.log('[BuffettFormatter] Analysis formatting complete');
        return {
            formattedText: formatted,
            extractedRatings: ratings,
            qualityScore: this.calculateQualityScore(formatted, ratings)
        };
    }

    /**
     * Remove all placeholder artifacts and broken code
     */
    removeAllArtifacts(text) {
        let cleaned = text;

        // Remove all placeholder patterns
        this.placeholderPatterns.forEach(pattern => {
            cleaned = cleaned.replace(pattern, '');
        });

        // Clean up concatenation artifacts more thoroughly
        cleaned = cleaned.replace(/\s*\+\s*'/g, ' ');
        cleaned = cleaned.replace(/'\s*\+\s*/g, ' ');
        cleaned = cleaned.replace(/\s*\+\s*"/g, ' ');
        cleaned = cleaned.replace(/"\s*\+\s*/g, ' ');
        cleaned = cleaned.replace(/\s*\+\s*/g, ' ');

        // Remove broken HTML/template tags
        cleaned = cleaned.replace(/<[^>]*>/g, ' ');
        cleaned = cleaned.replace(/&[a-zA-Z0-9#]+;/g, ' ');

        // Clean up excessive whitespace
        cleaned = cleaned.replace(/\s{3,}/g, '  ');
        cleaned = cleaned.replace(/\n{4,}/g, '\n\n\n');
        cleaned = cleaned.replace(/\.{3,}/g, '...');

        return cleaned.trim();
    }

    /**
     * Extract and validate all ratings for consistency
     */
    extractAndValidateRatings(text) {
        const ratings = {};
        const foundRatings = [];

        // Extract all rating instances
        Object.entries(this.ratingPatterns).forEach(([key, pattern]) => {
            const matches = [...text.matchAll(pattern)];
            if (matches.length > 0) {
                // Get percentage values (second capture group)
                const percentageValues = matches.map(m => parseFloat(m[2])).filter(v => !isNaN(v));
                if (percentageValues.length > 0) {
                    // Use the most frequent value or first if all unique
                    ratings[key] = this.getMostFrequentValue(percentageValues);
                    foundRatings.push({
                        category: key,
                        values: percentageValues,
                        finalValue: ratings[key]
                    });
                }
            }
        });

        // Validate rating consistency (all should be 0-100)
        Object.keys(ratings).forEach(key => {
            if (ratings[key] < 0 || ratings[key] > 100) {
                console.warn(`[BuffettFormatter] Invalid rating for ${key}: ${ratings[key]}`);
                ratings[key] = Math.max(0, Math.min(100, ratings[key]));
            }
        });

        return ratings;
    }

    /**
     * Fix structural and formatting problems
     */
    fixStructuralProblems(text) {
        let fixed = text;

        // Remove redundant headings
        fixed = this.consolidateRedundantHeadings(fixed);

        // Fix punctuation and spacing
        fixed = fixed.replace(/\s+([,.!?])/g, '$1');
        fixed = fixed.replace(/([,.!?])\s*([,.!?])/g, '$1');
        fixed = fixed.replace(/\.\s+([a-z])/g, '. $1');
        fixed = fixed.replace(/([.!?])\s*([A-Z])/g, '$1 $2');

        // Fix broken sentences and flow
        fixed = fixed.replace(/([a-z])\s+([A-Z][a-z])/g, '$1. $2');

        // Remove incomplete sentences that end with artifacts
        fixed = fixed.replace(/[^.!?]*\+[^.!?]*$/gm, '');

        // Clean up bullet points
        fixed = fixed.replace(/^\s*[-•*]\s*/gm, '• ');

        // Fix paragraph structure
        fixed = fixed.replace(/\n\s*\n\s*\n/g, '\n\n');

        return fixed;
    }

    /**
     * Consolidate redundant or repeated headings
     */
    consolidateRedundantHeadings(text) {
        const seenHeadings = new Set();
        const lines = text.split('\n');
        const cleanLines = [];

        for (let line of lines) {
            const trimmed = line.trim();

            // Check if it's a heading
            if (this.isHeading(trimmed)) {
                const normalized = this.normalizeHeading(trimmed);

                if (!seenHeadings.has(normalized)) {
                    seenHeadings.add(normalized);
                    cleanLines.push(line);
                }
                // Skip duplicate headings
            } else {
                cleanLines.push(line);
            }
        }

        return cleanLines.join('\n');
    }

    /**
     * Ensure analytical consistency and fix contradictions
     */
    ensureAnalyticalConsistency(text, ratings, metadata) {
        let consistent = text;

        // Fix rating inconsistencies
        consistent = this.fixRatingInconsistencies(consistent, ratings);

        // Reconcile contradictory statements
        consistent = this.reconcileContradictions(consistent, ratings);

        // Add missing context for metrics
        consistent = this.addMetricContext(consistent);

        // Fix DCF and valuation logic
        consistent = this.fixValuationLogic(consistent, metadata);

        return consistent;
    }

    /**
     * Apply Buffett-Munger investment framework
     */
    applyBuffettFramework(text, ratings, metadata) {
        let enhanced = text;

        // Add circle of competence assessment
        enhanced = this.addCircleOfCompetenceAnalysis(enhanced, ratings);

        // Enhance economic moat discussion
        enhanced = this.enhanceEconomicMoatAnalysis(enhanced, ratings);

        // Improve management quality assessment
        enhanced = this.enhanceManagementAnalysis(enhanced, ratings);

        // Add proper margin of safety discussion
        enhanced = this.addMarginOfSafetyAnalysis(enhanced, ratings, metadata);

        // Apply Buffett's decisiveness to recommendations
        enhanced = this.applyBuffettDecisiveness(enhanced, ratings);

        return enhanced;
    }

    /**
     * Enhance presentation with visual elements and structure
     */
    enhancePresentation(text, ratings, metadata) {
        let enhanced = text;

        // Add executive summary
        enhanced = this.addExecutiveSummary(enhanced, ratings);

        // Create ratings summary table
        enhanced = this.addRatingsSummaryTable(enhanced, ratings);

        // Add DCF sensitivity analysis
        enhanced = this.addDCFSensitivityTable(enhanced, metadata);

        // Add risk matrix
        enhanced = this.addRiskMatrix(enhanced, ratings);

        // Enhance language and remove passive voice
        enhanced = this.enhanceLanguageStyle(enhanced);

        // Add proper section structure
        enhanced = this.addProperSectionStructure(enhanced);

        return enhanced;
    }

    /**
     * Add executive summary based on Buffett principles
     */
    addExecutiveSummary(text, ratings) {
        if (!ratings.overall) return text;

        const grade = this.getInvestmentGrade(ratings.overall);
        const recommendation = this.getBuffettRecommendation(ratings);

        const summary = `
## Executive Summary - Buffett-Munger Analysis

**Investment Grade:** ${grade.grade} (${ratings.overall}%)
**Assessment:** ${grade.description}
**Recommendation:** ${recommendation.action}

**Key Verdict:** ${recommendation.rationale}

**Quick Check Against Buffett's Criteria:**
- Circle of Competence: ${this.assessCircleOfCompetence(ratings)}
- Economic Moat: ${this.assessEconomicMoat(ratings)}
- Management Quality: ${this.assessManagement(ratings)}
- Margin of Safety: ${this.assessMarginOfSafety(ratings)}

---

`;
        return summary + text;
    }

    /**
     * Add comprehensive ratings summary table
     */
    addRatingsSummaryTable(text, ratings) {
        if (Object.keys(ratings).length === 0) return text;

        let table = `
## Buffett-Munger Rating Summary

| Investment Criteria | Score | Grade | Buffett's Perspective |
|-------------------|-------|-------|---------------------|
`;

        const criteriaMap = {
            overall: 'Overall Investment Merit',
            business: 'Business Quality & Moat',
            financial: 'Financial Strength',
            valuation: 'Valuation & Margin of Safety',
            management: 'Management Quality'
        };

        Object.entries(ratings).forEach(([key, score]) => {
            const grade = this.getInvestmentGrade(score);
            const perspective = this.getBuffettPerspective(key, score);
            const criteriaName = criteriaMap[key] || key;

            table += `| ${criteriaName} | ${score}% | ${grade.grade} | ${perspective} |\n`;
        });

        table += `\n**Scoring Guide:** A+ (90-100%) = Exceptional, A (80-89%) = Outstanding, B+ (70-79%) = Good, B (60-69%) = Average, C+ (50-59%) = Below Average, C- (40-49%) = Poor, F (0-39%) = Unacceptable\n\n`;

        return this.insertAfterSection(text, 'Executive Summary', table);
    }

    /**
     * Add DCF sensitivity analysis table
     */
    addDCFSensitivityTable(text, metadata) {
        if (!metadata.dcfValue && !metadata.currentPrice) return text;

        const baseGrowth = metadata.terminalGrowth || 3.0;
        const baseDiscount = metadata.discountRate || 10.0;

        let table = `
## DCF Sensitivity Analysis

**Base Case Assumptions:**
- Terminal Growth Rate: ${baseGrowth}%
- Discount Rate: ${baseDiscount}%
- Current Price: $${metadata.currentPrice || 'N/A'}

| Terminal Growth → | ${(baseGrowth - 1).toFixed(1)}% | ${baseGrowth.toFixed(1)}% | ${(baseGrowth + 1).toFixed(1)}% |
|------------------|----------|----------|----------|
| **Discount Rate ↓** | **Intrinsic Value** | **Intrinsic Value** | **Intrinsic Value** |
| ${(baseDiscount - 1).toFixed(1)}% | $${this.calculateDCFValue(metadata, baseDiscount - 1, baseGrowth - 1)} | $${this.calculateDCFValue(metadata, baseDiscount - 1, baseGrowth)} | $${this.calculateDCFValue(metadata, baseDiscount - 1, baseGrowth + 1)} |
| ${baseDiscount.toFixed(1)}% | $${this.calculateDCFValue(metadata, baseDiscount, baseGrowth - 1)} | $${this.calculateDCFValue(metadata, baseDiscount, baseGrowth)} | $${this.calculateDCFValue(metadata, baseDiscount, baseGrowth + 1)} |
| ${(baseDiscount + 1).toFixed(1)}% | $${this.calculateDCFValue(metadata, baseDiscount + 1, baseGrowth - 1)} | $${this.calculateDCFValue(metadata, baseDiscount + 1, baseGrowth)} | $${this.calculateDCFValue(metadata, baseDiscount + 1, baseGrowth + 1)} |

**Buffett's Take:** ${this.getBuffettDCFPerspective(metadata)}

`;
        return this.insertAfterSection(text, 'Valuation', table);
    }

    /**
     * Add risk assessment matrix
     */
    addRiskMatrix(text, ratings) {
        const risks = this.identifyKeyRisks(text, ratings);

        let matrix = `
## Risk Assessment Matrix

| Risk Factor | Probability | Impact | Buffett's Mitigation Strategy |
|-------------|-------------|--------|------------------------------|
`;

        risks.forEach(risk => {
            matrix += `| ${risk.factor} | ${risk.probability} | ${risk.impact} | ${risk.mitigation} |\n`;
        });

        matrix += `\n**Risk Scoring:** High/Medium/Low probability and impact. Buffett focuses on risks that can permanently impair capital.\n\n`;

        return this.insertAfterSection(text, 'Investment Risks', matrix);
    }

    /**
     * Apply Buffett's decisiveness to recommendations
     */
    applyBuffettDecisiveness(text, ratings) {
        let decisive = text;

        // Replace wishy-washy language with Buffett-style decisiveness
        const decisiveReplacements = [
            {
                vague: /may be worth considering/gi,
                decisive: 'merits serious consideration for value investors'
            },
            {
                vague: /could be interesting/gi,
                decisive: 'presents a compelling investment opportunity'
            },
            {
                vague: /might be a good investment/gi,
                decisive: 'meets our investment criteria'
            },
            {
                vague: /appears to be fairly valued/gi,
                decisive: 'trades near intrinsic value with limited margin of safety'
            },
            {
                vague: /the valuation may be fair but not compelling/gi,
                decisive: 'current valuation offers insufficient margin of safety for conservative investors'
            }
        ];

        decisiveReplacements.forEach(replacement => {
            decisive = decisive.replace(replacement.vague, replacement.decisive);
        });

        return decisive;
    }

    /**
     * Final quality check and cleanup
     */
    finalQualityCheck(text) {
        let final = text;

        // Remove any remaining artifacts
        final = final.replace(/\+[^.!?]*\+/g, '');
        final = final.replace(/\s+\+\s*/g, ' ');

        // Fix any remaining formatting issues
        final = final.replace(/\s{3,}/g, '  ');
        final = final.replace(/\n{4,}/g, '\n\n\n');

        // Ensure proper capitalization after periods
        final = final.replace(/\.\s+([a-z])/g, (match, letter) => '. ' + letter.toUpperCase());

        // Fix common grammatical issues
        final = final.replace(/\bi\.e\.\s/g, 'i.e., ');
        final = final.replace(/\be\.g\.\s/g, 'e.g., ');

        return final.trim();
    }

    /**
     * Helper methods
     */
    getMostFrequentValue(values) {
        const frequency = {};
        values.forEach(val => {
            frequency[val] = (frequency[val] || 0) + 1;
        });
        return parseFloat(Object.keys(frequency).reduce((a, b) =>
            frequency[a] > frequency[b] ? a : b
        ));
    }

    isHeading(line) {
        return /^#{1,6}\s+/.test(line) ||
               /^[A-Z][^.!?]*:?\s*$/.test(line) ||
               /^(Overall|Valuation|Financial|Business|Management|Risk|Recommendation)/i.test(line);
    }

    normalizeHeading(heading) {
        return heading.toLowerCase()
                     .replace(/^#{1,6}\s+/, '')
                     .replace(/[^a-z0-9\s]/g, '')
                     .replace(/\s+/g, ' ')
                     .trim();
    }

    getInvestmentGrade(score) {
        const thresholds = Object.keys(this.investmentGrades).map(Number).sort((a, b) => b - a);
        for (let threshold of thresholds) {
            if (score >= threshold) {
                return this.investmentGrades[threshold];
            }
        }
        return this.investmentGrades[0];
    }

    getBuffettRecommendation(ratings) {
        const overall = ratings.overall || 0;
        const business = ratings.business || 0;
        const valuation = ratings.valuation || 0;

        if (overall >= 85 && business >= 80 && valuation >= 70) {
            return {
                action: 'Strong Buy',
                rationale: 'Exceptional business with adequate margin of safety - a "wonderful company at a fair price"'
            };
        } else if (overall >= 75 && business >= 70 && valuation >= 60) {
            return {
                action: 'Buy',
                rationale: 'Quality business with reasonable valuation meets our investment criteria'
            };
        } else if (overall >= 65) {
            return {
                action: 'Hold/Watch',
                rationale: 'Decent business but wait for better entry point or improved fundamentals'
            };
        } else if (overall >= 50) {
            return {
                action: 'Pass',
                rationale: 'Does not meet minimum quality thresholds for long-term investment'
            };
        } else {
            return {
                action: 'Avoid',
                rationale: 'Significant concerns across multiple criteria - outside circle of competence'
            };
        }
    }

    calculateQualityScore(text, ratings) {
        let score = 0;

        // Check for required sections (20 points)
        const requiredSections = ['business', 'financial', 'valuation', 'management', 'risks'];
        requiredSections.forEach(section => {
            if (new RegExp(section, 'i').test(text)) score += 4;
        });

        // Check for Buffett principles (30 points)
        if (text.includes('economic moat') || text.includes('competitive advantage')) score += 10;
        if (text.includes('margin of safety') || text.includes('intrinsic value')) score += 10;
        if (text.includes('circle of competence')) score += 10;

        // Check for quantitative analysis (20 points)
        if (text.includes('DCF') || text.includes('discounted cash flow')) score += 10;
        if (/\$[\d,]+/.test(text)) score += 10; // Has specific dollar amounts

        // Check for rating consistency (20 points)
        if (Object.keys(ratings).length >= 4) score += 10;
        if (this.ratingsAreConsistent(ratings)) score += 10;

        // Check for clear recommendation (10 points)
        if (/\b(buy|sell|hold|avoid)\b/i.test(text)) score += 10;

        return Math.min(100, score);
    }

    ratingsAreConsistent(ratings) {
        const values = Object.values(ratings);
        if (values.length < 2) return false;

        const avg = values.reduce((a, b) => a + b, 0) / values.length;
        return values.every(val => Math.abs(val - avg) <= 20); // Within 20 points of average
    }

    // Additional helper methods for specific analyses...
    assessCircleOfCompetence(ratings) {
        const business = ratings.business || 0;
        if (business >= 80) return '✅ Well understood business model';
        if (business >= 60) return '⚠️ Moderately complex business';
        return '❌ Outside circle of competence';
    }

    assessEconomicMoat(ratings) {
        const business = ratings.business || 0;
        if (business >= 85) return '🏰 Wide moat with durable advantages';
        if (business >= 70) return '🛡️ Narrow moat with some protection';
        return '🌊 No meaningful economic moat';
    }

    assessManagement(ratings) {
        const mgmt = ratings.management || 0;
        if (mgmt >= 80) return '👑 Exceptional capital allocators';
        if (mgmt >= 60) return '👥 Competent management team';
        return '⚠️ Management concerns identified';
    }

    assessMarginOfSafety(ratings) {
        const valuation = ratings.valuation || 0;
        if (valuation >= 80) return '🛡️ Substantial discount to intrinsic value';
        if (valuation >= 60) return '📊 Moderate margin of safety';
        return '⚠️ Limited or no margin of safety';
    }

    insertAfterSection(text, sectionName, content) {
        const sectionRegex = new RegExp(`(#{1,3}\\s*${sectionName}[^\\n]*\\n[\\s\\S]*?)(?=\\n#{1,3}\\s|$)`, 'i');
        const match = text.match(sectionRegex);

        if (match) {
            return text.replace(sectionRegex, match[1] + '\n' + content);
        }

        return text + '\n\n' + content;
    }

    // Additional methods would be implemented here for:
    // - fixRatingInconsistencies()
    // - reconcileContradictions()
    // - addMetricContext()
    // - fixValuationLogic()
    // - addCircleOfCompetenceAnalysis()
    // - enhanceEconomicMoatAnalysis()
    // - enhanceManagementAnalysis()
    // - addMarginOfSafetyAnalysis()
    // - enhanceLanguageStyle()
    // - addProperSectionStructure()
    // - identifyKeyRisks()
    // - getBuffettPerspective()
    // - getBuffettDCFPerspective()
    // - calculateDCFValue()
}

/**
 * Utility function to format analysis text using Buffett principles
 */
function formatBuffettAnalysis(rawText, metadata = {}) {
    const formatter = new BuffettAnalysisFormatter();
    return formatter.formatBuffettAnalysis(rawText, metadata);
}

/**
 * Quick cleanup function for removing obvious artifacts
 */
function quickCleanAnalysis(text) {
    const formatter = new BuffettAnalysisFormatter();
    let cleaned = formatter.removeAllArtifacts(text);
    cleaned = formatter.fixStructuralProblems(cleaned);
    return cleaned;
}

/**
 * Extract ratings for chart integration
 */
function extractBuffettRatings(text) {
    const formatter = new BuffettAnalysisFormatter();
    return formatter.extractAndValidateRatings(text);
}

// Export for global use
window.BuffettAnalysisFormatter = BuffettAnalysisFormatter;
window.formatBuffettAnalysis = formatBuffettAnalysis;
window.quickCleanAnalysis = quickCleanAnalysis;
window.extractBuffettRatings = extractBuffettRatings;

console.log('[BuffettFormatter] Buffett-Munger Analysis Formatter loaded');
