/* static/chatbot_rating_integration.js */
/* Integration layer for Enhanced Rating Charts with Chatbot System */

// --- Integration State Management ---
let integrationState = {
  initialized: false,
  activeCharts: new Map(),
  messageObservers: new Map(),
  enhancedMode: true,
};

// --- Configuration ---
const INTEGRATION_CONFIG = {
  chatbot: {
    messagesContainerId: "chatbot-messages",
    ratingChartClass: "rating-charts",
    analysisMessageType: "analysis_response",
  },
  charts: {
    containerClass: "enhanced-rating-container",
    minWidth: 350,
    defaultHeight: 300,
    animationDelay: 500,
  },
  responsive: {
    mobile: 768,
    tablet: 1024,
  },
};

/**
 * Initialize the integration system
 */
function initializeChatbotRatingIntegration() {
  if (integrationState.initialized) return;

  console.log("[Integration] Initializing enhanced rating charts integration");

  // Check for required dependencies
  if (typeof EnhancedRatingChart === "undefined" || typeof d3 === "undefined") {
    console.warn(
      "[Integration] Required dependencies not loaded yet, retrying...",
    );
    setTimeout(initializeChatbotRatingIntegration, 1000);
    return;
  }

  // Verify chatbot container exists
  const chatbotContainer = document.getElementById(
    INTEGRATION_CONFIG.chatbot.messagesContainerId,
  );
  if (!chatbotContainer) {
    console.warn("[Integration] Chatbot container not found, retrying...");
    setTimeout(initializeChatbotRatingIntegration, 1000);
    return;
  }

  integrationState.enhancedMode = true;

  // Override existing rating chart functions
  overrideRatingChartFunctions();

  // Setup message observer for new messages
  setupMessageObserver();

  // Enhance existing charts in the chatbot
  enhanceExistingCharts();

  integrationState.initialized = true;
  console.log(
    "[Integration] Enhanced rating charts integration initialized successfully",
  );
}

/**
 * Override existing rating chart functions with enhanced versions
 */
function overrideRatingChartFunctions() {
  // Store original functions
  const originalFunctions = {
    updateGlobalRatingChart: window.updateGlobalRatingChart,
    updateMoatRatingChart: window.updateMoatRatingChart,
    updateFinancialRatingChart: window.updateFinancialRatingChart,
    updateValuationRatingChart: window.updateValuationRatingChart,
    updateManagementRatingChart: window.updateManagementRatingChart,
    addMessage: window.addMessage,
  };

  // Enhanced Global Rating Chart Update
  window.updateGlobalRatingChart = function (rating) {
    console.log(
      "[Integration] Enhanced updateGlobalRatingChart called with:",
      rating,
    );

    if (!integrationState.enhancedMode) {
      return originalFunctions.updateGlobalRatingChart?.call(this, rating);
    }

    const chartData = [
      {
        label: "Overall Rating",
        value: rating,
        maxValue: 100,
      },
    ];

    createEnhancedChatbotChart("global-rating", chartData, {
      title: "Overall Stock Rating",
      type: "single",
    });
  };

  // Enhanced Multiple Rating Charts Update
  window.updateAllRatingCharts = function (ratings) {
    console.log(
      "[Integration] Enhanced updateAllRatingCharts called with:",
      ratings,
    );

    if (!integrationState.enhancedMode) {
      // Fallback to individual updates
      if (ratings.overall)
        originalFunctions.updateGlobalRatingChart?.call(this, ratings.overall);
      if (ratings.moat)
        originalFunctions.updateMoatRatingChart?.call(this, ratings.moat);
      if (ratings.financial)
        originalFunctions.updateFinancialRatingChart?.call(
          this,
          ratings.financial,
        );
      if (ratings.valuation)
        originalFunctions.updateValuationRatingChart?.call(
          this,
          ratings.valuation,
        );
      if (ratings.management)
        originalFunctions.updateManagementRatingChart?.call(
          this,
          ratings.management,
        );
      return;
    }

    const chartData = [];

    if (ratings.overall > 0)
      chartData.push({ label: "Overall Rating", value: ratings.overall });
    if (ratings.moat > 0)
      chartData.push({ label: "Business Moat", value: ratings.moat });
    if (ratings.financial > 0)
      chartData.push({ label: "Financial Health", value: ratings.financial });
    if (ratings.valuation > 0)
      chartData.push({ label: "Valuation", value: ratings.valuation });
    if (ratings.management > 0)
      chartData.push({
        label: "Management Quality",
        value: ratings.management,
      });

    if (chartData.length > 0) {
      createEnhancedChatbotChart("comprehensive-rating", chartData, {
        title: "Stock Analysis Summary",
        type: "comprehensive",
      });
    }
  };

  // Override addMessage to intercept rating chart creation
  if (originalFunctions.addMessage) {
    window.addMessage = function (sender, messageData) {
      const result = originalFunctions.addMessage.call(
        this,
        sender,
        messageData,
      );

      // If this is a bot message with analysis data, enhance it
      if (sender === "bot" && messageData && typeof messageData === "object") {
        setTimeout(() => {
          enhanceLatestMessage();
        }, 100);
      }

      return result;
    };
  }

  // Individual rating chart updates (enhanced)
  const ratingTypes = ["moat", "financial", "valuation", "management"];
  const ratingLabels = {
    moat: "Business Moat Quality",
    financial: "Financial Strength",
    valuation: "Valuation Score",
    management: "Management Quality",
  };

  ratingTypes.forEach((type) => {
    const functionName = `update${type.charAt(0).toUpperCase() + type.slice(1)}RatingChart`;
    window[functionName] = function (rating) {
      console.log(
        `[Integration] Enhanced ${functionName} called with:`,
        rating,
      );

      if (!integrationState.enhancedMode) {
        return originalFunctions[functionName]?.call(this, rating);
      }

      const chartData = [
        {
          label: ratingLabels[type],
          value: rating,
          maxValue: 100,
        },
      ];

      createEnhancedChatbotChart(`${type}-rating`, chartData, {
        title: ratingLabels[type],
        type: "single",
      });
    };
  });
}

/**
 * Create enhanced chatbot chart
 */
function createEnhancedChatbotChart(chartId, data, options = {}) {
  // Find the most recent message container or create one
  const messagesContainer = document.getElementById(
    INTEGRATION_CONFIG.chatbot.messagesContainerId,
  );
  if (!messagesContainer) {
    console.error("[Integration] Chatbot messages container not found");
    return;
  }

  // Look for existing chart container in the latest bot message
  let chartContainer = findLatestChartContainer();

  if (!chartContainer) {
    // Create new message with chart container
    chartContainer = createNewChartMessage();
  }

  // Clear existing content in the chart container
  chartContainer.innerHTML = "";

  // Remove existing chart if updating
  const existingChart = integrationState.activeCharts.get(chartId);
  if (existingChart && typeof existingChart.destroy === "function") {
    existingChart.destroy();
    integrationState.activeCharts.delete(chartId);
  }

  // Add commentary if available
  const commentary = generateChartCommentary(data, options);

  const visualizationData = {
    title: options.title || "Stock Rating Analysis",
    ratings: data,
    commentary: commentary,
  };

  // Render the enhanced visualization with scroll animation disabled initially
  const result = createEnhancedRatingVisualization(
    chartContainer,
    visualizationData,
    {
      chartOptions: {
        animateOnScroll: false, // We'll trigger this manually
        responsive: true,
        width: getResponsiveWidth(),
        height: calculateChartHeight(data.length),
      },
      textOptions: {
        animateOnScroll: false, // We'll trigger this manually
        staggerDelay: 150,
      },
    },
  );

  // Store the chart reference
  integrationState.activeCharts.set(chartId, result.ratingChart);

  // Trigger animation after a short delay
  setTimeout(() => {
    if (
      result.ratingChart &&
      typeof result.ratingChart.animateChart === "function"
    ) {
      result.ratingChart.animateChart(result.ratingChart.elements || []);
    }
    if (
      result.textCommentary &&
      typeof result.textCommentary.animateText === "function"
    ) {
      result.textCommentary.animateText(result.textCommentary.elements || []);
    }
  }, 200);

  // Scroll to show the new chart
  setTimeout(() => {
    scrollToLatestChart(chartContainer);
  }, 300);

  console.log(`[Integration] Enhanced chart ${chartId} created successfully`);
}

/**
 * Find the latest chart container in chatbot messages
 */
function findLatestChartContainer() {
  const messagesContainer = document.getElementById(
    INTEGRATION_CONFIG.chatbot.messagesContainerId,
  );
  const botMessages = messagesContainer.querySelectorAll(".message.bot");

  // Look in reverse order for the most recent bot message
  for (let i = botMessages.length - 1; i >= 0; i--) {
    const message = botMessages[i];
    let chartContainer = message.querySelector(
      `.${INTEGRATION_CONFIG.charts.containerClass}`,
    );

    if (!chartContainer) {
      // Check for existing rating-charts container to replace
      const oldContainer = message.querySelector(".rating-charts");
      if (oldContainer) {
        chartContainer = document.createElement("div");
        chartContainer.className = INTEGRATION_CONFIG.charts.containerClass;
        oldContainer.parentNode.replaceChild(chartContainer, oldContainer);
      }
    }

    if (chartContainer) {
      return chartContainer;
    }
  }

  return null;
}

/**
 * Create new message with chart container
 */
function createNewChartMessage() {
  const messagesContainer = document.getElementById(
    INTEGRATION_CONFIG.chatbot.messagesContainerId,
  );

  // Create new bot message
  const li = document.createElement("li");
  li.classList.add("message", "bot");

  // Add bot avatar
  const avatar = document.createElement("span");
  avatar.classList.add("bot-avatar");
  avatar.innerHTML = '<i class="fas fa-robot chatbot-icon-small"></i>';
  li.appendChild(avatar);

  // Create message content container
  const messageDiv = document.createElement("div");
  messageDiv.classList.add("message-content");

  // Create chart container
  const chartContainer = document.createElement("div");
  chartContainer.className = INTEGRATION_CONFIG.charts.containerClass;
  chartContainer.style.width = "100%";
  chartContainer.style.minHeight = "200px";

  messageDiv.appendChild(chartContainer);
  li.appendChild(messageDiv);
  messagesContainer.appendChild(li);

  return chartContainer;
}

/**
 * Generate commentary for chart data
 */
function generateChartCommentary(data, options) {
  if (options.type === "comprehensive") {
    return generateComprehensiveCommentary(data);
  } else {
    return generateSingleRatingCommentary(data[0]);
  }
}

/**
 * Generate comprehensive commentary for multiple ratings
 */
function generateComprehensiveCommentary(data) {
  const commentary = [];
  const overallScore =
    data.find((item) => item.label.toLowerCase().includes("overall"))?.value ||
    0;

  // Overall assessment
  if (overallScore >= 80) {
    commentary.push(
      `<strong>Strong Investment Candidate:</strong> With an overall rating of ${overallScore}%, this stock demonstrates excellent fundamentals across multiple criteria.`,
    );
  } else if (overallScore >= 60) {
    commentary.push(
      `<strong>Solid Investment Option:</strong> The ${overallScore}% overall rating indicates good investment potential with manageable risks.`,
    );
  } else if (overallScore >= 40) {
    commentary.push(
      `<strong>Mixed Signals:</strong> The ${overallScore}% rating suggests average performance with both strengths and weaknesses to consider.`,
    );
  } else {
    commentary.push(
      `<strong>Proceed with Caution:</strong> The ${overallScore}% overall rating indicates significant concerns that warrant careful analysis.`,
    );
  }

  // Individual metric insights
  const highPerformers = data.filter((item) => item.value >= 75);
  const lowPerformers = data.filter((item) => item.value < 50);

  if (highPerformers.length > 0) {
    const strongAreas = highPerformers
      .map((item) => item.label.toLowerCase())
      .join(", ");
    commentary.push(
      `<strong>Key Strengths:</strong> The company shows particular strength in ${strongAreas}, which are positive indicators for long-term performance.`,
    );
  }

  if (lowPerformers.length > 0) {
    const weakAreas = lowPerformers
      .map((item) => item.label.toLowerCase())
      .join(", ");
    commentary.push(
      `<strong>Areas of Concern:</strong> Lower scores in ${weakAreas} suggest these areas may need closer examination before making investment decisions.`,
    );
  }

  return commentary;
}

/**
 * Generate commentary for single rating
 */
function generateSingleRatingCommentary(data) {
  const { label, value } = data;
  const commentary = [];

  if (value >= 80) {
    commentary.push(
      `<strong>Excellent ${label}:</strong> The ${value}% score indicates exceptional performance in this area, representing a significant competitive advantage.`,
    );
  } else if (value >= 60) {
    commentary.push(
      `<strong>Good ${label}:</strong> With a score of ${value}%, this metric shows solid performance that supports the investment thesis.`,
    );
  } else if (value >= 40) {
    commentary.push(
      `<strong>Average ${label}:</strong> The ${value}% score suggests moderate performance that neither strongly supports nor detracts from the investment case.`,
    );
  } else {
    commentary.push(
      `<strong>Below Average ${label}:</strong> The ${value}% score indicates potential concerns that should be carefully evaluated in the context of the overall investment decision.`,
    );
  }

  return commentary;
}

/**
 * Get responsive width based on screen size
 */
function getResponsiveWidth() {
  const screenWidth = window.innerWidth;

  if (screenWidth < INTEGRATION_CONFIG.responsive.mobile) {
    return Math.min(screenWidth - 40, 350);
  } else if (screenWidth < INTEGRATION_CONFIG.responsive.tablet) {
    return 400;
  } else {
    return 500;
  }
}

/**
 * Calculate chart height based on number of items
 */
function calculateChartHeight(itemCount) {
  const baseHeight = 100;
  const itemHeight = 60;
  return baseHeight + itemCount * itemHeight;
}

/**
 * Setup message observer to detect new messages
 */
function setupMessageObserver() {
  const messagesContainer = document.getElementById(
    INTEGRATION_CONFIG.chatbot.messagesContainerId,
  );
  if (!messagesContainer) return;

  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === "childList") {
        mutation.addedNodes.forEach((node) => {
          if (
            node.nodeType === Node.ELEMENT_NODE &&
            node.classList.contains("message")
          ) {
            // Delay to allow message content to be fully rendered
            setTimeout(() => {
              enhanceMessageCharts(node);
            }, 100);
          }
        });
      }
    });
  });

  observer.observe(messagesContainer, {
    childList: true,
    subtree: true,
  });

  integrationState.messageObservers.set("main", observer);
}

/**
 * Enhance charts in a specific message
 */
function enhanceMessageCharts(messageElement) {
  if (!integrationState.enhancedMode) return;

  // Look for old rating charts to replace
  const oldCharts = messageElement.querySelectorAll(
    ".rating-charts, .rating-chart-container, .component-ratings-grid, .overall-rating-section",
  );
  oldCharts.forEach((oldChart) => {
    // Skip if already enhanced
    if (oldChart.classList.contains("enhanced-rating-container")) return;

    // Try to extract rating data from the old chart
    const ratingData = extractRatingDataFromElement(oldChart);

    if (ratingData && ratingData.length > 0) {
      // Create enhanced container
      const enhancedContainer = document.createElement("div");
      enhancedContainer.className = INTEGRATION_CONFIG.charts.containerClass;

      // Replace old chart
      oldChart.parentNode.replaceChild(enhancedContainer, oldChart);

      // Create enhanced visualization
      const visualizationData = {
        title: "Stock Analysis Ratings",
        ratings: ratingData,
        commentary: generateChartCommentary(ratingData, {
          type: "comprehensive",
        }),
      };

      createEnhancedRatingVisualization(enhancedContainer, visualizationData, {
        chartOptions: { animateOnScroll: true },
        textOptions: { animateOnScroll: true },
      });

      console.log(
        "[Integration] Replaced old chart with enhanced visualization",
      );
    }
  });
}

/**
 * Extract rating data from existing chart elements
 */
function extractRatingDataFromElement(element) {
  const ratings = [];

  // Try to extract from various chart formats
  const chartElements = element.querySelectorAll(
    ".component-rating-item, .category-rating, .rating-item",
  );

  chartElements.forEach((item) => {
    const labelElement = item.querySelector(
      ".component-name, .category-label, .rating-label",
    );
    const valueElement = item.querySelector(
      ".score-text, .rating-value, .percent-value",
    );

    if (labelElement && valueElement) {
      const label = labelElement.textContent.trim();
      const valueText = valueElement.textContent.replace(/[%\s]/g, "");
      const value = parseFloat(valueText);

      if (!isNaN(value) && value >= 0 && value <= 100) {
        ratings.push({ label, value });
      }
    }
  });

  // If no ratings found, try to extract from text content
  if (ratings.length === 0) {
    const textContent = element.textContent;
    const ratingPatterns = [
      /Overall Rating[:\s]*(\d+)%?/i,
      /Business Quality[:\s]*(\d+)%?/i,
      /Financial Strength[:\s]*(\d+)%?/i,
      /Valuation[:\s]*(\d+)%?/i,
      /Management[:\s]*(\d+)%?/i,
    ];

    const labels = [
      "Overall Rating",
      "Business Quality",
      "Financial Strength",
      "Valuation",
      "Management",
    ];

    ratingPatterns.forEach((pattern, index) => {
      const match = textContent.match(pattern);
      if (match) {
        const value = parseFloat(match[1]);
        if (!isNaN(value)) {
          ratings.push({ label: labels[index], value });
        }
      }
    });
  }

  return ratings;
}

/**
 * Enhance the latest message that was just added
 */
function enhanceLatestMessage() {
  const messagesContainer = document.getElementById(
    INTEGRATION_CONFIG.chatbot.messagesContainerId,
  );
  if (!messagesContainer) return;

  const messages = messagesContainer.querySelectorAll(".message.bot");
  if (messages.length > 0) {
    const latestMessage = messages[messages.length - 1];
    enhanceMessageCharts(latestMessage);
  }
}

/**
 * Enhance existing charts in the chatbot
 */
function enhanceExistingCharts() {
  const messagesContainer = document.getElementById(
    INTEGRATION_CONFIG.chatbot.messagesContainerId,
  );
  if (!messagesContainer) return;

  const existingMessages = messagesContainer.querySelectorAll(".message.bot");
  existingMessages.forEach((message) => {
    enhanceMessageCharts(message);
  });
}

/**
 * Scroll to show the latest chart
 */
function scrollToLatestChart(chartContainer) {
  chartContainer.scrollIntoView({
    behavior: "smooth",
    block: "nearest",
    inline: "nearest",
  });
}

/**
 * Handle window resize for responsive charts
 */
function handleResize() {
  // Update active charts with new dimensions
  integrationState.activeCharts.forEach((chart, chartId) => {
    if (chart && typeof chart.resize === "function") {
      chart.resize({
        width: getResponsiveWidth(),
      });
    }
  });
}

/**
 * Cleanup function
 */
function cleanupIntegration() {
  // Cleanup active charts
  integrationState.activeCharts.forEach((chart) => {
    if (chart && typeof chart.destroy === "function") {
      chart.destroy();
    }
  });
  integrationState.activeCharts.clear();

  // Cleanup observers
  integrationState.messageObservers.forEach((observer) => {
    observer.disconnect();
  });
  integrationState.messageObservers.clear();

  // Cleanup enhanced charts
  if (typeof cleanupEnhancedCharts === "function") {
    cleanupEnhancedCharts();
  }

  integrationState.initialized = false;
}

/**
 * Public API for external integration
 */
window.ChatbotRatingIntegration = {
  init: initializeChatbotRatingIntegration,
  createChart: createEnhancedChatbotChart,
  cleanup: cleanupIntegration,
  isEnhanced: () => integrationState.enhancedMode,
  getActiveCharts: () => integrationState.activeCharts,
};

// Auto-initialize when DOM is ready
document.addEventListener("DOMContentLoaded", () => {
  // Wait for other scripts to load, especially the main script.js
  setTimeout(() => {
    console.log("[Integration] Starting delayed initialization");
    initializeChatbotRatingIntegration();
  }, 2000);
});

// Also try to initialize when the chatbot is ready
if (typeof window.initializeChatbot === "function") {
  const originalInitChatbot = window.initializeChatbot;
  window.initializeChatbot = function () {
    const result = originalInitChatbot.apply(this, arguments);
    setTimeout(initializeChatbotRatingIntegration, 500);
    return result;
  };
}

// Handle window resize
window.addEventListener("resize", debounce(handleResize, 250));

// Cleanup on page unload
window.addEventListener("beforeunload", cleanupIntegration);

// Utility function for debouncing
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

console.log("[Integration] Chatbot Rating Integration script loaded");
