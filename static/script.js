/* static/script.js */
/* Combined JavaScript for H Trader Dashboard */

// --- Global Variables & Constants ---
let currentChartRange = "1y"; // Default performance chart range
let isBotTyping = false; // Chatbot state
let currentBotAction = null; // Track required bot action (e.g., 'choose_model', 'provide_key', 'awaiting_query', null)
let autoRefreshIntervalId = null; // Portfolio refresh interval ID
const REFRESH_INTERVAL_MS = 60 * 1000; // Refresh portfolio prices every 60 seconds

// Element ID Constants (Update if your HTML IDs are different)
const TICKER_CONTAINER_ID = "stockTickerContainer";
const TICKER_MOVE_ID = "stockTickerMove";
const TICKER_PLACEHOLDER_ID = "ticker-placeholder";
const PAGE_HEADER_ID = "pageHeader";
const PORTFOLIO_TABLE_BODY_SELECTOR = ".portfolio-table tbody";
const CHART_LOADER_ID = "chart-loader"; // Generic ID for performance chart loader
const ASSET_CHART_ID = "asset-allocation-chart";
const ASSET_LOADER_ID = "asset-loader";
const INDUSTRY_CHART_ID = "industry-allocation-chart";
const INDUSTRY_LOADER_ID = "industry-loader";
const SECTOR_CHART_ID = "sector-allocation-chart"; // Keep if using Plotly/D3 for sector
const SECTOR_LOADER_ID = "sector-loader";
const D3_PERF_CONTAINER_ID = "portfolio-chart-container";
const D3_PERF_SVG_ID = "d3-portfolio-chart";
const D3_PERF_TOOLTIP_ID = "d3-performance-tooltip";
const D3_PERF_VALUE_SPAN_ID = "chart-performance-value";
const CHATBOT_CONTAINER_ID = "chatbot-container";
const CHATBOT_TOGGLE_ID = "chatbot-toggle";
const CHATBOT_WINDOW_ID = "chatbot-window";
const CHATBOT_CLOSE_ID = "chatbot-close";
const CHATBOT_MESSAGES_ID = "chatbot-messages";
const CHATBOT_INPUT_ID = "chatbot-input";
const CHATBOT_SEND_ID = "chatbot-send";
const CHATBOT_TYPING_ID = "typing-indicator";
const CHATBOT_SETTINGS_ID = "chatbot-settings"; // New settings button ID
const EDIT_MODAL_ID = "editStockModal"; // Example modal ID
const EDIT_FORM_ID = "editStockForm"; // Example form ID
const EDIT_TICKER_DISPLAY_ID = "editTickerDisplay";
const EDIT_AMOUNT_INPUT_ID = "editAmountInvested";
const EDIT_TICKER_HIDDEN_ID = "editTickerHidden";

// --- Utility Functions ---
function safeJsFloat(value, defaultVal = null) {
  if (value === null || value === undefined || value === "") return defaultVal;
  // Enhanced replacement to handle currency symbols, commas, percentages, spaces etc.
  if (typeof value === "string") {
    // Remove common currency symbols, thousands separators, trailing % etc.
    value = value
      .replace(/[$,£€%]/g, "")
      .replace(/,/g, "")
      .trim();
    // Handle scientific notation (e.g., 1.23e+5) - pattern should allow 'e' or 'E'
    // Pattern: optional minus, digits, optional decimal point with digits, optional e/E with optional sign and digits.
    if (!/^-?\d*(\.\d+)?([eE][-+]?\d+)?$/.test(value)) {
      // If it doesn't look like a number after cleaning, return default
      console.warn(
        `safeJsFloat: Value "${value}" doesn't look like a valid number after cleaning.`,
      );
      return defaultVal;
    }
  }
  const num = Number(value);
  // Keep isNaN and isFinite check
  return isNaN(num) || !isFinite(num) ? defaultVal : num;
}

function getCssVariable(varName, element = document.documentElement) {
  if (!element || typeof getComputedStyle === "undefined") return null;
  return getComputedStyle(element).getPropertyValue(varName).trim() || null;
}

function showLoader(loaderId) {
  const loader = document.getElementById(loaderId);
  if (loader) {
    loader.style.display = "flex"; // Or 'block' depending on your loader style
  } else {
    console.warn(`Loader element #${loaderId} not found.`);
  }
}

function hideLoader(loaderId) {
  const loader = document.getElementById(loaderId);
  if (loader) {
    loader.style.display = "none";
  } else {
    // Don't warn every time if it just doesn't exist on a page
    // console.warn(`Loader element #${loaderId} not found for hiding.`);
  }
}

function formatCurrency(value, currency = "USD", digits = 2) {
  const num = safeJsFloat(value, null);
  if (num === null) return "N/A";
  // Basic formatting, consider Intl.NumberFormat for more complex needs
  return `${currency === "USD" ? "$" : ""}${num.toLocaleString(undefined, { minimumFractionDigits: digits, maximumFractionDigits: digits })}`;
}

// --- Theme Handling ---
function getParticleColors(theme) {
  const style = getComputedStyle(document.body);
  if (theme === "dark") {
    return {
      color: style.getPropertyValue("--highlight-color").trim() || "#8c4fff",
      linkColor:
        style.getPropertyValue("--highlight-color").trim() || "#8c4fff",
      bgColor: style.getPropertyValue("--bg-color").trim() || "#12121f",
    };
  } else {
    return {
      color: style.getPropertyValue("--highlight-color").trim() || "#007bff",
      linkColor:
        style.getPropertyValue("--highlight-color").trim() || "#007bff",
      bgColor: style.getPropertyValue("--bg-color").trim() || "#f8f9fc",
    };
  }
}

function updateParticlesTheme(theme) {
  if (typeof particlesJS === "undefined") {
    // Guard against particlesJS library not loaded
    console.warn("Particles.js library not loaded, cannot update theme.");
    return;
  }

  if (window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS) {
    // Check pJS exists
    const colors = getParticleColors(theme);
    const pJS = window.pJSDom[0].pJS;
    if (pJS.particles && pJS.particles.color && pJS.particles.line_linked) {
      // Deeper check
      pJS.particles.color.value = colors.color;
      pJS.particles.line_linked.color = colors.linkColor;
      const particlesElement = document.getElementById("particles-js");
      if (particlesElement) {
        particlesElement.style.backgroundColor = colors.bgColor;
      }
      if (typeof pJS.fn.particlesRefresh === "function") {
        pJS.fn.particlesRefresh();
      } else {
        console.warn("pJS.fn.particlesRefresh is not a function.");
      }
    } else {
      console.warn(
        "Particles.js pJS object is not as expected, cannot update theme values.",
      );
    }
  } else {
    console.warn(
      "Particles.js not fully initialized (pJSDom missing/invalid), cannot update theme. Attempting re-init if possible.",
    );
    // Optionally, try to re-initialize particles here if it makes sense in your flow,
    // but this might be too aggressive if initParticles has its own logic.
    // The base.html change is generally safer.
    // if (document.getElementById('particles-js') && typeof initParticles === 'function') {
    //    console.log("Attempting to re-initialize particles for theme update.");
    //    initParticles(); // This might cause its own issues if not careful
    // }
  }
}

function setTheme(themeName) {
  document.body.setAttribute("data-theme", themeName);
  const themeToggleSidebar = document.getElementById(
    "themeToggleCheckboxSidebar",
  );
  if (themeToggleSidebar) {
    themeToggleSidebar.checked = themeName === "dark";
  }
  localStorage.setItem("theme", themeName);

  // Update components sensitive to theme changes
  updateParticlesTheme(themeName);
  // Update Plotly charts theme if function exists and charts are present
  if (typeof updatePlotlyChartTheme === "function") {
    updatePlotlyChartTheme(themeName);
  }
  // Trigger D3 chart updates (important for colors/gradients)
  if (typeof d3 !== "undefined") {
    // Only redraw if the charts actually exist on the page
    if (
      document.getElementById(D3_PERF_SVG_ID) &&
      typeof fetchChartData === "function"
    ) {
      console.log("Theme change: Redrawing D3 Performance Chart");
      fetchChartData(currentChartRange, false); // Redraw without entry animation
    }
    if (
      document.getElementById(ASSET_CHART_ID) &&
      typeof renderD3AssetDonut === "function" &&
      cachedAssetChartData
    ) {
      console.log("Theme change: Redrawing D3 Asset Donut Chart");
      renderD3AssetDonut(cachedAssetChartData);
    }
    if (
      document.getElementById(INDUSTRY_CHART_ID) &&
      typeof renderD3IndustryDonut === "function" &&
      cachedIndustryChartData
    ) {
      console.log("Theme change: Redrawing D3 Industry Donut Chart");
      renderD3IndustryDonut(cachedIndustryChartData);
    }
  }
}

function toggleTheme() {
  const currentTheme = document.body.getAttribute("data-theme") || "light";
  const newTheme = currentTheme === "dark" ? "light" : "dark";
  setTheme(newTheme);
}

// --- Active Sidebar Link ---
function setActiveSidebarLink() {
  const currentPath = window.location.pathname;
  const sidebarLinks = document.querySelectorAll(".sidebar a");
  sidebarLinks.forEach((link) => {
    link.classList.remove("active");
    const linkHref = link.getAttribute("href");
    // Handle exact match or root path special case
    if (linkHref === currentPath || (currentPath === "/" && linkHref === "/")) {
      link.classList.add("active");
    }
    // Special case for portfolio page if URL has parameters or trailing slash
    if (linkHref === "/portfolio" && currentPath.startsWith("/portfolio")) {
      link.classList.add("active");
    }
    // Special case for dcf page
    if (linkHref === "/dcf" && currentPath.startsWith("/dcf")) {
      link.classList.add("active");
    }
  });
}

// --- Ticker Banner Handling ---
function createTickerHTML(data) {
  const validData = data.filter(
    (item) =>
      item && item.symbol && item.price !== null && item.price !== "N/A",
  );
  if (validData.length === 0) {
    return `<span id="${TICKER_PLACEHOLDER_ID}">Ticker data unavailable.</span>`;
  }
  const itemsHTML = validData
    .map((item) => {
      const priceNum = safeJsFloat(item.price, null);
      const changePercent = safeJsFloat(item.changesPercentage, 0);
      const changeClass =
        changePercent >= 0 ? "ticker-positive" : "ticker-negative";
      const sign = changePercent >= 0 ? "+" : "";
      const formattedChange = `(${sign}${changePercent.toFixed(2)}%)`;
      const trendIcon =
        changePercent > 0
          ? '<i class="fas fa-arrow-up"></i>'
          : changePercent < 0
            ? '<i class="fas fa-arrow-down"></i>'
            : '<i class="fas fa-minus"></i>';
      // Use more decimals for certain types if needed
      const priceDecimals =
        item.symbol === "Bitcoin" ||
        item.symbol === "EUR/USD" ||
        item.symbol === "US 10Y"
          ? 4
          : 2;
      const formattedPrice =
        priceNum !== null
          ? priceNum.toLocaleString(undefined, {
              minimumFractionDigits: priceDecimals,
              maximumFractionDigits: priceDecimals,
            })
          : "N/A";
      const pricePrefix =
        item.symbol === "Bitcoin" || item.symbol === "Gold (GLD)" ? "$" : ""; // Add $ for specific symbols

      return `<span class="ticker-item">
                    <span class="symbol">${item.symbol}</span>
                    <span class="price">${pricePrefix}${formattedPrice}</span>
                    <span class="change ${changeClass}">
                        ${trendIcon} ${formattedChange}
                    </span>
                </span>`;
    })
    .join("");
  // Duplicate content for seamless scroll effect
  return itemsHTML + itemsHTML;
}

async function fetchTickerData() {
  const tickerContainer = document.getElementById(TICKER_CONTAINER_ID);
  const tickerMove = document.getElementById(TICKER_MOVE_ID);
  if (!tickerContainer || !tickerMove) return;

  try {
    const response = await fetch("/api/ticker-data"); // Use correct endpoint
    if (!response.ok) {
      throw new Error(`HTTP ${response.status} ${response.statusText}`);
    }
    const data = await response.json();

    if (Array.isArray(data) && data.length > 0) {
      const tickerContent = createTickerHTML(data);
      if (tickerMove.innerHTML !== tickerContent) {
        // Avoid unnecessary updates
        tickerMove.innerHTML = tickerContent;
      }
    } else {
      throw new Error("No valid ticker data received");
    }
  } catch (error) {
    // Inside catch block of fetchTickerData
    console.error("Error fetching ticker data:", error.message || error); // Log detailed error
    if (tickerMove) {
      // Ensure tickerMove exists
      const errorMessage = `Market data unavailable. Error: ${error.message || "Fetch failed"}`;
      // Add specific error class for styling
      tickerMove.innerHTML = `<span id="ticker-error" class="ticker-error-message" title="${errorMessage}">⚠️ Market Data Error</span>`;
    }
  }
}

function showTicker() {
  const tickerContainer = document.getElementById(TICKER_CONTAINER_ID);
  const pageHeader = document.getElementById(PAGE_HEADER_ID);
  if (tickerContainer && pageHeader) {
    tickerContainer.style.display = "block";
    pageHeader.style.top = `var(--ticker-height)`;
    document.body.style.paddingTop = `calc(var(--header-height) + var(--ticker-height))`;
    fetchTickerData(); // Initial fetch
    // Clear any existing interval before starting a new one
    if (window.tickerIntervalId) clearInterval(window.tickerIntervalId);
    window.tickerIntervalId = setInterval(fetchTickerData, 90 * 1000); // Refresh every 90 seconds
  }
}
f
function hideTicker() {
  const tickerContainer = document.getElementById(TICKER_CONTAINER_ID);
  const pageHeader = document.getElementById(PAGE_HEADER_ID);
  if (tickerContainer && pageHeader) {
    tickerContainer.style.display = "none";
    pageHeader.style.top = `0px`;
    document.body.style.paddingTop = `var(--header-height)`;
    if (window.tickerIntervalId) clearInterval(window.tickerIntervalId);
    window.tickerIntervalId = null;
  }
}

function checkAndSetTickerVisibility() {
  // Show ticker only on the home page
  const isHomePage =
    window.location.pathname === "/" ||
    window.location.pathname === "/index.html" ||
    window.location.pathname.endsWith("/home"); // Adjust if needed
  if (isHomePage) {
    showTicker();
  } else {
    hideTicker();
  }
}

// --- Particles Background ---
function initParticles() {
  const theme = document.body.getAttribute("data-theme") || "light";
  const colors = getParticleColors(theme);
  const particlesElement = document.getElementById("particles-js");
  if (!particlesElement || typeof particlesJS === "undefined") {
    console.warn("Particles container or library not found.");
    return;
  }
  particlesElement.style.backgroundColor = colors.bgColor;

  particlesJS("particles-js", {
    particles: {
      number: { value: 50, density: { enable: true, value_area: 800 } },
      color: { value: colors.color },
      shape: { type: "circle" },
      opacity: {
        value: 0.4,
        random: true,
        anim: { enable: true, speed: 0.5, opacity_min: 0.1, sync: false },
      },
      size: { value: 3, random: true, anim: { enable: false } },
      line_linked: {
        enable: true,
        distance: 150,
        color: colors.linkColor,
        opacity: 0.2,
        width: 1,
      },
      move: {
        enable: true,
        speed: 1,
        direction: "none",
        random: true,
        straight: false,
        out_mode: "out",
        bounce: false,
      },
    },
    interactivity: {
      detect_on: "canvas",
      events: {
        onhover: { enable: true, mode: "grab" },
        onclick: { enable: false },
      },
      modes: { grab: { distance: 140, line_linked: { opacity: 0.5 } } },
    },
    retina_detect: true,
  });
}

// --- D3 Chart Rendering Functions ---
// --- Portfolio Performance Chart (D3) ---
let cachedPerformanceData = null; // Cache data for potential redraws
let chartHasBeenAnimated = false; // Prevent re-animating on redraw

function renderD3PortfolioChart(chartData, animateEntry = false) {
  const svgId = D3_PERF_SVG_ID;
  const tooltipId = D3_PERF_TOOLTIP_ID;
  const containerId = D3_PERF_CONTAINER_ID;
  const perfValueSpanId = D3_PERF_VALUE_SPAN_ID;

  console.log(`[D3 Perf Chart] Rendering. Animate: ${animateEntry}`);
  if (typeof d3 === "undefined") {
    console.error("[D3 Perf Chart] D3 library is not loaded.");
    hideLoader(CHART_LOADER_ID);
    return;
  }

  const svgContainer = d3.select(`#${svgId}`);
  const tooltip = d3.select(`#${tooltipId}`);
  const chartContainerDiv = document.getElementById(containerId);
  const pValueSpan = document.getElementById(perfValueSpanId);

  // Essential element checks
  if (svgContainer.empty() || !chartContainerDiv) {
    console.error("[D3 Perf Chart] SVG container or parent DIV not found.");
    hideLoader(CHART_LOADER_ID);
    return;
  }
  if (tooltip.empty())
    console.warn(
      `[D3 Perf Chart] Tooltip element #${tooltipId} not found. Hover effects disabled.`,
    );
  if (!pValueSpan)
    console.warn(
      `[D3 Perf Chart] Performance SPAN #${perfValueSpanId} not found.`,
    );

  // --- Data Validation & Parsing ---
  const parseTime = d3.timeParse("%Y-%m-%d");
  const dataValid =
    chartData &&
    Array.isArray(chartData.dates) &&
    Array.isArray(chartData.values) &&
    chartData.dates.length === chartData.values.length;
  let parsedData = [];
  if (dataValid && chartData.dates.length > 0 && parseTime) {
    parsedData = chartData.dates
      .map((d, i) => ({
        date: parseTime(d),
        value: safeJsFloat(chartData.values[i], null),
      }))
      .filter((d) => d.date !== null && d.value !== null);
    if (parsedData.length === 0) dataValid = false;
  } else {
    dataValid = false;
    if (!parseTime) console.error("[D3 Perf Chart] d3.timeParse failed!");
  }
  const purchaseEvents =
    chartData && Array.isArray(chartData.purchase_events)
      ? chartData.purchase_events
      : [];
  cachedPerformanceData = chartData; // Cache raw data

  // --- Clear Previous Chart/Messages --- //
  svgContainer.selectAll("*").remove();
  if (!tooltip.empty())
    tooltip
      .style("opacity", 0)
      .style("visibility", "hidden")
      .html("")
      .classed("visible", false);
  chartContainerDiv = document.getElementById(containerId); // Ensure we have the container div (Removed const)
  const existingMsg = chartContainerDiv
    ? chartContainerDiv.querySelector(".chart-error, .chart-info")
    : null;
  if (existingMsg) existingMsg.remove(); // Clear previous info/error messages

  // --- Handle No Data or Parse Failure --- //
  dataValid =
    chartData &&
    chartData.dates &&
    chartData.values &&
    chartData.dates.length === chartData.values.length; // (Removed const)
  pValueSpan = document.getElementById(D3_PERF_VALUE_SPAN_ID); // Get span for value update (Removed const)

  if (!dataValid || parsedData.length === 0) {
    // Check both initial validation and post-parsing length
    console.warn(
      "[D3 Perf Chart] No valid data points to render after parsing.",
    );
    if (chartContainerDiv) {
      // Only add message if container exists
      const infoP = document.createElement("p");
      infoP.className = "chart-info"; // Use info class
      infoP.textContent =
        chartData?.warning ||
        "No performance data available for the selected range."; // Use backend warning if available
      chartContainerDiv.insertBefore(infoP, chartContainerDiv.firstChild);
    }
    // Clear performance value as well
    if (pValueSpan) {
      pValueSpan.textContent = "N/A";
      pValueSpan.className = "";
    }
    hideLoader(CHART_LOADER_ID);
    return; // Stop rendering
  }
  // If data is valid, proceed with const data = parsedData;
  const data = parsedData;

  // --- Dimensions & Setup ---
  const margin = { top: 25, right: 40, bottom: 50, left: 75 };
  const parentWidth = chartContainerDiv.clientWidth;
  const width =
    parentWidth > 0 ? parentWidth - margin.left - margin.right : 300;
  const chartHeight = Math.max(320, Math.min(parentWidth * 0.65, 450)); // Responsive height
  const height = chartHeight - margin.top - margin.bottom;
  if (width <= 0 || height <= 0) {
    console.error("[D3 Perf Chart] Calculated dimensions invalid.");
    hideLoader(CHART_LOADER_ID);
    return;
  }
  svgContainer
    .attr(
      "viewBox",
      `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`,
    )
    .attr("preserveAspectRatio", "xMidYMid meet");
  const svg = svgContainer
    .append("g")
    .attr("transform", `translate(${margin.left},${margin.top})`);

  // --- Scaling ---
  const x = d3.scaleTime().range([0, width]);
  const y = d3.scaleLinear().range([height, 0]);
  x.domain(d3.extent(data, (d) => d.date));
  const dataExtent = d3.extent(data, (d) => d.value);
  const yMinVal = dataExtent[0] ?? 0;
  const yMaxVal = dataExtent[1] ?? 0;
  const rangeVal = yMaxVal - yMinVal;
  const padding =
    rangeVal === 0 ? Math.max(Math.abs(yMinVal * 0.15), 1) : rangeVal * 0.2; // Ensure padding > 0
  y.domain([yMinVal - padding, yMaxVal + padding]).nice(5);

  // --- Axes ---
  const xTickCount = Math.max(Math.floor(width / 90), 2);
  const xAxis = d3.axisBottom(x).ticks(xTickCount).tickSizeOuter(0);
  const yTickCount = Math.max(Math.floor(height / 55), 4);
  const yAxis = d3
    .axisLeft(y)
    .ticks(yTickCount)
    .tickFormat((d) => `$${d3.format(",.0f")(d)}`)
    .tickSize(-width);
  svg
    .append("g")
    .attr("class", "d3-axis x-axis")
    .attr("transform", `translate(0,${height})`)
    .call(xAxis);
  svg.append("g").attr("class", "d3-axis y-axis").call(yAxis);

  // --- Gradient ---
  const defs = svg.append("defs");
  const gradientId = `d3-area-gradient-${svgId.replace(/[^a-zA-Z0-9]/g, "")}`;
  const gradient = defs
    .append("linearGradient")
    .attr("id", gradientId)
    .attr("gradientUnits", "userSpaceOnUse")
    .attr("x1", 0)
    .attr("y1", y(y.domain()[1]))
    .attr("x2", 0)
    .attr("y2", y(y.domain()[0]));
  const highlightColor = getCssVariable("--highlight-color") || "#007bff";
  gradient
    .append("stop")
    .attr("offset", "0%")
    .attr("stop-color", highlightColor)
    .attr("stop-opacity", 0.45);
  gradient
    .append("stop")
    .attr("offset", "100%")
    .attr("stop-color", highlightColor)
    .attr("stop-opacity", 0.05);

  // --- Line & Area ---
  if (data.length >= 2) {
    const area = d3
      .area()
      .x((d) => x(d.date))
      .y0(height)
      .y1((d) => y(d.value))
      .curve(d3.curveMonotoneX);
    const line = d3
      .line()
      .x((d) => x(d.date))
      .y((d) => y(d.value))
      .curve(d3.curveMonotoneX);
    const areaPath = svg
      .append("path")
      .datum(data)
      .attr("class", "d3-performance-area")
      .attr("fill", `url(#${gradientId})`)
      .attr("d", area);
    const linePath = svg
      .append("path")
      .datum(data)
      .attr("class", "d3-performance-line")
      .attr("d", line);
    if (animateEntry && !chartHasBeenAnimated) {
      const totalLength = linePath.node().getTotalLength();
      linePath
        .attr("stroke-dasharray", totalLength + " " + totalLength)
        .attr("stroke-dashoffset", totalLength)
        .transition()
        .duration(1600)
        .ease(d3.easeCubicOut)
        .attr("stroke-dashoffset", 0);
      areaPath
        .style("opacity", 0)
        .transition()
        .delay(400)
        .duration(1200)
        .ease(d3.easeCubicOut)
        .style("opacity", 1);
      chartHasBeenAnimated = true;
    } else {
      areaPath.style("opacity", 1);
    }
  } else if (data.length === 1) {
    svg
      .append("circle")
      .attr("cx", x(data[0].date))
      .attr("cy", y(data[0].value))
      .attr("r", 5.5)
      .attr("class", "d3-performance-line")
      .style("fill", highlightColor);
  }

  // --- Purchase Events ---
  const parseEventDate = d3.timeParse("%Y-%m-%d");
  const annotationGroup = svg.append("g").attr("class", "purchase-annotations");
  purchaseEvents.forEach((event) => {
    const eventDate = parseEventDate(event.date);
    if (eventDate && eventDate >= x.domain()[0] && eventDate <= x.domain()[1]) {
      const eventX = x(eventDate);
      if (isNaN(eventX)) return;
      const eventGroup = annotationGroup
        .append("g")
        .attr("class", "purchase-event-group")
        .attr("transform", `translate(${eventX}, 0)`);
      eventGroup
        .append("line")
        .attr("class", "purchase-event-line")
        .attr("x1", 0)
        .attr("y1", height)
        .attr("x2", 0)
        .attr("y2", height - 10);
      eventGroup
        .append("circle")
        .attr("class", "purchase-event-circle")
        .attr("cy", height);
      const investedFormatted = event.invested
        ? `$${event.invested.toLocaleString(undefined, { maximumFractionDigits: 0 })}`
        : "";
      const tooltipText = `${event.ticker.split(".")[0]} Added (${event.date})${investedFormatted ? " - " + investedFormatted + " Invested" : ""}`;
      eventGroup.append("title").text(tooltipText); // Simple native tooltip
      if (event.logoUrl && event.logoUrl !== "null" && event.logoUrl !== "") {
        eventGroup
          .append("image")
          .attr("class", "purchase-event-logo")
          .attr("xlink:href", event.logoUrl)
          .attr("x", -9)
          .attr("y", height - 30)
          .attr("width", 18)
          .attr("height", 18)
          .style("pointer-events", "none")
          .append("title")
          .text(tooltipText);
      }
    }
  });

  // --- Enhanced Tooltip Interaction Logic ---
  if (data.length >= 1 && !tooltip.empty()) {
    const bisectDate = d3.bisector((d) => d.date).left;
    const timeFormat = d3.timeFormat("%b %d, %Y");
    const valueFormat = d3.format(",.0f");

    // Create enhanced focus elements
    const focusGroup = svg
      .append("g")
      .attr("class", "focus-elements")
      .style("opacity", 0)
      .style("pointer-events", "none");

    // Vertical crosshair line
    const focusLine = focusGroup
      .append("line")
      .attr("class", "focus-line")
      .attr("y1", 0)
      .attr("y2", height)
      .style("stroke", "var(--highlight-color, #007bff)")
      .style("stroke-width", "2px")
      .style("stroke-dasharray", "4,4")
      .style("opacity", 0.8);

    // Focus circle with glow effect
    const focusCircle = focusGroup
      .append("circle")
      .attr("class", "focus-circle")
      .attr("r", 6)
      .style("fill", "var(--highlight-color, #007bff)")
      .style("stroke", "white")
      .style("stroke-width", "3px")
      .style("filter", "drop-shadow(0 0 8px rgba(0, 123, 255, 0.6))");

    // Invisible interaction area for smooth tracking - slightly expanded for reliable detection
    const interactionRect = svg
      .append("rect")
      .attr("class", "interaction-rect")
      .attr("x", -10)
      .attr("y", -10)
      .attr("width", width + 20)
      .attr("height", height + 20)
      .style("fill", "none")
      .style("pointer-events", "all")
      .style("cursor", "crosshair");

    // Ultra-stable tooltip state management
    let isTooltipVisible = false;
    let currentDataPoint = null;
    let isMouseOverChart = false;
    let tooltipUpdateTimer = null;

    // Ultra-smooth tooltip positioning with no glitches
    let lastTooltipX = null;
    let lastTooltipY = null;

    const updateTooltipPosition = (event) => {
      if (!isTooltipVisible) return;

      // Clear any pending updates to prevent rapid firing
      if (tooltipUpdateTimer) {
        clearTimeout(tooltipUpdateTimer);
      }

      tooltipUpdateTimer = setTimeout(() => {
        const mouseX = event.clientX;
        const mouseY = event.clientY;

        // Smooth movement threshold to prevent micro-jitters
        if (lastTooltipX !== null && lastTooltipY !== null) {
          const deltaX = Math.abs(mouseX - lastTooltipX);
          const deltaY = Math.abs(mouseY - lastTooltipY);
          if (deltaX < 5 && deltaY < 5) {
            return; // Don't update for tiny movements
          }
        }

        lastTooltipX = mouseX;
        lastTooltipY = mouseY;

        const tooltipNode = tooltip.node();
        if (!tooltipNode) return;

        // Get tooltip dimensions
        const tooltipRect = tooltipNode.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Determine optimal positioning based on available space
        const spaceRight = viewportWidth - mouseX;
        const spaceLeft = mouseX;
        const spaceTop = mouseY;
        const spaceBottom = viewportHeight - mouseY;

        let left, top;

        // Horizontal positioning - prefer right side, but ensure visibility
        if (spaceRight >= tooltipRect.width + 30) {
          // Enough space on the right
          left = mouseX + 15;
        } else if (spaceLeft >= tooltipRect.width + 30) {
          // Not enough space on right, use left
          left = mouseX - tooltipRect.width - 15;
        } else {
          // Very tight space, center on screen with offset
          left = Math.max(10, Math.min(viewportWidth - tooltipRect.width - 10, mouseX - tooltipRect.width / 2));
        }

        // Vertical positioning - prefer above cursor
        if (spaceTop >= tooltipRect.height + 30) {
          // Enough space above
          top = mouseY - tooltipRect.height - 15;
        } else if (spaceBottom >= tooltipRect.height + 30) {
          // Not enough space above, use below
          top = mouseY + 15;
        } else {
          // Very tight space, position optimally
          top = Math.max(10, Math.min(viewportHeight - tooltipRect.height - 10, mouseY - tooltipRect.height / 2));
        }

        // Apply position smoothly with enhanced visibility
        tooltip
          .style("left", `${left}px`)
          .style("top", `${top}px`)
          .style("z-index", "10001") // Ensure it's above everything
          .classed("tooltip-right", left < mouseX) // Add class when tooltip is on the left of cursor
          .classed("tooltip-left", left >= mouseX); // Add class when tooltip is on the right of cursor
      }, 4); // Minimal delay for smooth but responsive movement
    };

    const handleMouseMove = (event) => {
      try {
        const pointerCoords = d3.pointer(event, svg.node());
        const pointerX = pointerCoords[0];
        const pointerY = pointerCoords[1];

        // Expanded chart area bounds to prevent tooltip disappearing at edges
        const chartBounds = {
          left: -50,
          right: width + 50,
          top: -30,
          bottom: height + 30
        };

        // Mark that mouse is over chart area (with very generous bounds)
        isMouseOverChart = (
          pointerX >= chartBounds.left &&
          pointerX <= chartBounds.right &&
          pointerY >= chartBounds.top &&
          pointerY <= chartBounds.bottom
        );

        // Show tooltip immediately when entering chart area and keep it visible
        if (isMouseOverChart) {
          if (!isTooltipVisible) {
            isTooltipVisible = true;
            tooltip
              .classed("visible", true)
              .style("opacity", 1)
              .style("visibility", "visible");
            focusGroup
              .style("opacity", 1)
              .style("visibility", "visible");
          }
        }

        // Process data even slightly outside chart bounds to prevent jumping
        const dataProcessingBounds = {
          left: -20,
          right: width + 20
        };

        if (pointerX < dataProcessingBounds.left || pointerX > dataProcessingBounds.right) {
          // Still update position even outside bounds
          updateTooltipPosition(event);
          return;
        }

        // Find closest data point with clamping to prevent edge issues
        const clampedX = Math.max(0, Math.min(width, pointerX));
        const x0 = x.invert(clampedX);
        const i = bisectDate(data, x0, 1);
        const d0 = data[i - 1];
        const d1 = data[i];
        const d = !d0 ? d1 : !d1 ? d0 : x0 - d0.date > d1.date - x0 ? d1 : d0;

        if (!d || !d.date || d.value === null) {
          updateTooltipPosition(event);
          return;
        }

        // Always update focus elements smoothly
        const focusXPos = x(d.date);
        const focusYPos = y(d.value);

        if (isNaN(focusXPos) || isNaN(focusYPos)) {
          updateTooltipPosition(event);
          return;
        }

        // Smooth focus updates - no transitions
        focusLine.attr("x1", focusXPos).attr("x2", focusXPos);
        focusCircle.attr("cx", focusXPos).attr("cy", focusYPos);

        // Update tooltip content when data point changes (more responsive)
        const dataPointKey = `${d.date.getTime()}-${d.value.toFixed(2)}`;
        const currentKey = currentDataPoint ? `${currentDataPoint.date.getTime()}-${currentDataPoint.value.toFixed(2)}` : null;

        // Very responsive updates - update on any meaningful change
        const shouldUpdateContent = !currentDataPoint ||
          Math.abs(d.date.getTime() - currentDataPoint.date.getTime()) > 3600000 || // Different hour (more responsive)
          Math.abs(d.value - currentDataPoint.value) > (d.value * 0.0005); // 0.05% difference (more sensitive)

        if (dataPointKey !== currentKey && shouldUpdateContent) {
          currentDataPoint = d;

          // Calculate performance metrics
          const firstValue = data[0].value;
          const change = d.value - firstValue;
          const percentChange = firstValue !== 0 ? (change / firstValue) * 100 : 0;
          const changeClass = percentChange >= 0 ? "positive" : "negative";
          const sign = percentChange >= 0 ? "+" : "";

          // Update tooltip content smoothly - ensure it stays visible
          requestAnimationFrame(() => {
            // Always update if tooltip should be visible and we're in chart area
            if (isTooltipVisible && isMouseOverChart) {
              tooltip.html(`
                <div class="tooltip-header">${timeFormat(d.date)}</div>
                <div class="tooltip-value">$${valueFormat(d.value)}</div>
                <div class="tooltip-portfolio-info">
                  <span class="${changeClass}">${sign}${percentChange.toFixed(2)}%</span> from start
                </div>
              `);

              // Ensure tooltip remains visible
              tooltip
                .classed("visible", true)
                .style("opacity", 1)
                .style("visibility", "visible");
            }
          });
        }

        // Always update position smoothly
        updateTooltipPosition(event);

      } catch (error) {
        console.error("[D3 Perf Chart] Error in mousemove handler:", error);
      }
    };

    const handleMouseLeave = () => {
      // Immediately mark as outside chart area
      isMouseOverChart = false;

      // Add a longer delay to prevent premature hiding when moving between chart elements
      setTimeout(() => {
        // Double-check if mouse is really outside the chart area
        if (!isMouseOverChart) {
          isTooltipVisible = false;
          currentDataPoint = null;
          lastTooltipX = null;
          lastTooltipY = null;

          // Clear any pending tooltip updates
          if (tooltipUpdateTimer) {
            clearTimeout(tooltipUpdateTimer);
            tooltipUpdateTimer = null;
          }

          tooltip
            .classed("visible", false)
            .style("opacity", 0)
            .style("visibility", "hidden");
          focusGroup
            .style("opacity", 0)
            .style("visibility", "hidden");
        }
      }, 150); // Longer delay to ensure tooltip doesn't flicker when moving within chart
    };

    // Handle mouse enter to ensure tooltip shows reliably
    const handleMouseEnter = (event) => {
      isMouseOverChart = true;

      // Immediately show tooltip when entering chart frame
      if (!isTooltipVisible) {
        isTooltipVisible = true;
        tooltip
          .classed("visible", true)
          .style("opacity", 1)
          .style("visibility", "visible");
        focusGroup
          .style("opacity", 1)
          .style("visibility", "visible");
      }

      // Trigger initial mouse move to position tooltip
      handleMouseMove(event);
    };

    // Attach event listeners for stable hover behavior
    interactionRect
      .on("mouseenter", handleMouseEnter)
      .on("mousemove", handleMouseMove)
      .on("mouseleave", handleMouseLeave);
  }

  // --- Update Performance Value ---
  if (pValueSpan && data.length > 0) {
    const latestValue = data[data.length - 1].value;
    const firstValue = data[0].value;
    const change = latestValue - firstValue;
    const percentChange = firstValue !== 0 ? (change / firstValue) * 100 : 0;
    const changeClass = percentChange >= 0 ? "positive" : "negative";
    const sign = percentChange >= 0 ? "+" : "";

    pValueSpan.textContent = `${formatCurrency(latestValue)} (${sign}${percentChange.toFixed(2)}%)`;
    pValueSpan.className = `performance-value ${changeClass}`; // Reset classes and add new one
  } else if (pValueSpan) {
    pValueSpan.textContent = "N/A";
    pValueSpan.className = "performance-value";
  }

  hideLoader(CHART_LOADER_ID);
  console.log("[D3 Perf Chart] Rendering complete.");
}

// --- Asset Allocation Chart (D3 Donut) ---
let cachedAssetChartData = null; // Cache data for redraws

function renderD3AssetDonut(chartData) {
  const svgId = ASSET_CHART_ID;
  const loaderId = ASSET_LOADER_ID;
  const containerId = "asset-allocation-container"; // Assuming a container div exists

  console.log("[D3 Asset Donut] Rendering.");
  if (typeof d3 === "undefined") {
    console.error("[D3 Asset Donut] D3 library not loaded.");
    hideLoader(loaderId);
    return;
  }

  const svgContainer = d3.select(`#${svgId}`);
  const chartContainerDiv = document.getElementById(containerId);

  if (svgContainer.empty() || !chartContainerDiv) {
    console.error(
      `[D3 Asset Donut] SVG container #${svgId} or parent #${containerId} not found.`,
    );
    hideLoader(loaderId);
    return;
  }

  // --- Data Validation ---
  const dataValid =
    chartData &&
    Array.isArray(chartData.labels) &&
    Array.isArray(chartData.values) &&
    chartData.labels.length === chartData.values.length &&
    chartData.labels.length > 0;
  if (!dataValid) {
    console.warn("[D3 Asset Donut] Invalid or empty data provided:", chartData);
    svgContainer.selectAll("*").remove(); // Clear previous chart
    const existingMsg = chartContainerDiv.querySelector(".chart-info");
    if (existingMsg) existingMsg.remove();
    const infoP = document.createElement("p");
    infoP.className = "chart-info";
    infoP.textContent = "No asset allocation data available.";
    chartContainerDiv.insertBefore(infoP, chartContainerDiv.firstChild);
    hideLoader(loaderId);
    return;
  }
  cachedAssetChartData = chartData; // Cache valid data

  // --- Clear Previous Chart/Messages ---
  svgContainer.selectAll("*").remove();
  const existingMsg = chartContainerDiv.querySelector(".chart-info");
  if (existingMsg) existingMsg.remove();

  // --- Dimensions & Setup ---
  const parentWidth = chartContainerDiv.clientWidth;
  const width = parentWidth > 0 ? parentWidth : 300;
  const height = Math.max(280, Math.min(width * 0.8, 350)); // Responsive height
  const radius = (Math.min(width, height) / 2) * 0.85; // Adjust radius multiplier as needed
  const arcThickness = radius * 0.3; // Thickness relative to radius
  const innerRadius = radius - arcThickness;

  svgContainer
    .attr("viewBox", `0 0 ${width} ${height}`)
    .attr("preserveAspectRatio", "xMidYMid meet");
  const svg = svgContainer
    .append("g")
    .attr("transform", `translate(${width / 2},${height / 2})`);

  // --- Color Scale ---
  // Use a predefined D3 scheme or create a custom one based on theme
  const theme = document.body.getAttribute("data-theme") || "light";
  const colorScale = d3.scaleOrdinal(
    theme === "dark"
      ? d3.schemeSpectral[chartData.labels.length]
      : d3.schemeTableau10,
  ); // Example: different schemes for themes

  // --- Data Preparation ---
  const dataset = chartData.labels
    .map((label, i) => ({
      label: label,
      value: safeJsFloat(chartData.values[i], 0),
      dollarValue: safeJsFloat(
        chartData.dollar_values ? chartData.dollar_values[i] : 0,
        0,
      ), // Add dollar value
    }))
    .filter((d) => d.value > 0); // Filter out zero values if needed

  // --- Pie & Arc Generators ---
  const pie = d3
    .pie()
    .value((d) => d.value)
    .sort(null); // Disable sorting by value
  const arcGen = d3.arc().innerRadius(innerRadius).outerRadius(radius);
  const arcHover = d3
    .arc()
    .innerRadius(innerRadius - 4)
    .outerRadius(radius + 4); // Slightly larger arc on hover

  // --- Draw Arcs ---
  const arcs = svg
    .selectAll(".arc")
    .data(pie(dataset))
    .enter()
    .append("g")
    .attr("class", "arc");

  arcs
    .append("path")
    .attr("d", arcGen)
    .attr("fill", (d) => colorScale(d.data.label))
    .attr("stroke", getCssVariable("--bg-color") || "#ffffff") // Use background color for stroke
    .style("stroke-width", "1.5px")
    .style("opacity", 0.85)
    .transition()
    .duration(800)
    .attrTween("d", function (d) {
      const i = d3.interpolate({ startAngle: 0, endAngle: 0 }, d);
      return function (t) {
        return arcGen(i(t));
      };
    });

  // --- Tooltip ---
  const tooltip = d3
    .select("body")
    .append("div")
    .attr("class", "d3-chart-tooltip") // Use a generic tooltip class
    .style("opacity", 0)
    .style("position", "absolute")
    .style("pointer-events", "none"); // Prevent tooltip from blocking mouse events

  // --- Interaction ---
  arcs
    .on("mouseover", function (event, d) {
      d3.select(this)
        .select("path")
        .transition()
        .duration(150)
        .attr("d", arcHover)
        .style("opacity", 1);

      tooltip.transition().duration(150).style("opacity", 0.95);
      const percentage = (
        ((d.endAngle - d.startAngle) / (2 * Math.PI)) *
        100
      ).toFixed(1);
      const dollarAmount = formatCurrency(d.data.dollarValue); // Format the dollar value
      tooltip
        .html(
          `<strong>${d.data.label}</strong><br>${percentage}%<br>${dollarAmount}`,
        )
        .style("left", event.pageX + 10 + "px")
        .style("top", event.pageY - 28 + "px");
    })
    .on("mouseout", function (event, d) {
      d3.select(this)
        .select("path")
        .transition()
        .duration(150)
        .attr("d", arcGen)
        .style("opacity", 0.85);
      tooltip.transition().duration(200).style("opacity", 0);
    });

  // --- Center Text (Total Value & Daily Change) ---
  // This will be updated by calculateAndDisplayPortfolioDailyChange via updateAssetChartCenterText
  svg
    .append("text")
    .attr("id", "asset-chart-center-value")
    .attr("text-anchor", "middle")
    .attr("dy", "-0.2em") // Position value slightly above center
    .attr("class", "d3-center-text-value")
    .text("Loading..."); // Initial text

  svg
    .append("text")
    .attr("id", "asset-chart-center-change")
    .attr("text-anchor", "middle")
    .attr("dy", "1.1em") // Position change below value
    .attr("class", "d3-center-text-change")
    .text(""); // Initial text

  hideLoader(loaderId);
  console.log("[D3 Asset Donut] Rendering complete.");
}

// --- Industry Allocation Chart (D3 Donut) ---
let cachedIndustryChartData = null;

function renderD3IndustryDonut(chartData) {
  const svgId = INDUSTRY_CHART_ID;
  const loaderId = INDUSTRY_LOADER_ID;
  const containerId = "industry-allocation-container";

  console.log("[D3 Industry Donut] Rendering.");
  if (typeof d3 === "undefined") {
    console.error("[D3 Industry Donut] D3 library not loaded.");
    hideLoader(loaderId);
    return;
  }

  const svgContainer = d3.select(`#${svgId}`);
  const chartContainerDiv = document.getElementById(containerId);

  if (svgContainer.empty() || !chartContainerDiv) {
    console.error(
      `[D3 Industry Donut] SVG container #${svgId} or parent #${containerId} not found.`,
    );
    hideLoader(loaderId);
    return;
  }

  // --- Data Validation ---
  const dataValid =
    chartData &&
    Array.isArray(chartData.labels) &&
    Array.isArray(chartData.values) &&
    chartData.labels.length === chartData.values.length &&
    chartData.labels.length > 0;
  if (!dataValid) {
    console.warn(
      "[D3 Industry Donut] Invalid or empty data provided:",
      chartData,
    );
    svgContainer.selectAll("*").remove();
    const existingMsg = chartContainerDiv.querySelector(".chart-info");
    if (existingMsg) existingMsg.remove();
    const infoP = document.createElement("p");
    infoP.className = "chart-info";
    infoP.textContent = "No industry allocation data available.";
    chartContainerDiv.insertBefore(infoP, chartContainerDiv.firstChild);
    hideLoader(loaderId);
    return;
  }
  cachedIndustryChartData = chartData;

  // --- Clear Previous Chart/Messages ---
  svgContainer.selectAll("*").remove();
  const existingMsg = chartContainerDiv.querySelector(".chart-info");
  if (existingMsg) existingMsg.remove();

  // --- Dimensions & Setup ---
  const parentWidth = chartContainerDiv.clientWidth;
  const width = parentWidth > 0 ? parentWidth : 300;
  const height = Math.max(280, Math.min(width * 0.8, 350));
  const radius = (Math.min(width, height) / 2) * 0.85;
  const arcThickness = radius * 0.3;
  const innerRadius = radius - arcThickness;

  svgContainer
    .attr("viewBox", `0 0 ${width} ${height}`)
    .attr("preserveAspectRatio", "xMidYMid meet");
  const svg = svgContainer
    .append("g")
    .attr("transform", `translate(${width / 2},${height / 2})`);

  // --- Color Scale ---
  const theme = document.body.getAttribute("data-theme") || "light";
  const colorScale = d3.scaleOrdinal(
    theme === "dark" ? d3.schemeSet3 : d3.schemeCategory10,
  ); // Different scheme

  // --- Data Preparation ---
  const dataset = chartData.labels
    .map((label, i) => ({
      label: label,
      value: safeJsFloat(chartData.values[i], 0),
      dollarValue: safeJsFloat(
        chartData.dollar_values ? chartData.dollar_values[i] : 0,
        0,
      ),
    }))
    .filter((d) => d.value > 0);

  // --- Pie & Arc Generators ---
  const pie = d3
    .pie()
    .value((d) => d.value)
    .sort(null);
  const arcGen = d3.arc().innerRadius(innerRadius).outerRadius(radius);
  const arcHover = d3
    .arc()
    .innerRadius(innerRadius - 4)
    .outerRadius(radius + 4);

  // --- Draw Arcs ---
  const arcs = svg
    .selectAll(".arc")
    .data(pie(dataset))
    .enter()
    .append("g")
    .attr("class", "arc");

  arcs
    .append("path")
    .attr("d", arcGen)
    .attr("fill", (d) => colorScale(d.data.label))
    .attr("stroke", getCssVariable("--bg-color") || "#ffffff")
    .style("stroke-width", "1.5px")
    .style("opacity", 0.85)
    .transition()
    .duration(800)
    .attrTween("d", function (d) {
      const i = d3.interpolate({ startAngle: 0, endAngle: 0 }, d);
      return function (t) {
        return arcGen(i(t));
      };
    });

  // --- Tooltip (Re-use or create new) ---
  let tooltip = d3.select(".d3-chart-tooltip"); // Try to re-use existing tooltip
  if (tooltip.empty()) {
    // If not found, create it
    tooltip = d3
      .select("body")
      .append("div")
      .attr("class", "d3-chart-tooltip")
      .style("opacity", 0)
      .style("position", "absolute")
      .style("pointer-events", "none");
  }

  // --- Interaction ---
  arcs
    .on("mouseover", function (event, d) {
      d3.select(this)
        .select("path")
        .transition()
        .duration(150)
        .attr("d", arcHover)
        .style("opacity", 1);

      tooltip.transition().duration(150).style("opacity", 0.95);
      const percentage = (
        ((d.endAngle - d.startAngle) / (2 * Math.PI)) *
        100
      ).toFixed(1);
      const dollarAmount = formatCurrency(d.data.dollarValue);
      tooltip
        .html(
          `<strong>${d.data.label}</strong><br>${percentage}%<br>${dollarAmount}`,
        )
        .style("left", event.pageX + 10 + "px")
        .style("top", event.pageY - 28 + "px");
    })
    .on("mouseout", function (event, d) {
      d3.select(this)
        .select("path")
        .transition()
        .duration(150)
        .attr("d", arcGen)
        .style("opacity", 0.85);
      tooltip.transition().duration(200).style("opacity", 0);
    });

  // --- Center Text (Total Value - Static for this chart) ---
  svg
    .append("text")
    .attr("id", "industry-chart-center-value")
    .attr("text-anchor", "middle")
    .attr("dy", "0.35em") // Vertically center
    .attr("class", "d3-center-text-value") // Use same class as asset chart for consistency
    .text("Loading..."); // Initial text

  svg
    .append("text")
    .attr("id", "industry-chart-center-change")
    .attr("text-anchor", "middle")
    .attr("dy", "1.1em") // Position change below value
    .attr("class", "d3-center-text-change")
    .text(""); // Initial text

  hideLoader(loaderId);
  console.log("[D3 Industry Donut] Rendering complete.");
}

// --- Donut Chart Rendering for Chatbot ---
function renderDonutChart(ratingValue, chartElement) {
  if (!chartElement) {
    console.warn(`Donut chart element not provided.`);
    return;
  }
  
  // Make sure we have a valid rating value
  if (ratingValue === undefined || ratingValue === null || isNaN(ratingValue) || ratingValue <= 0) {
    console.warn(`[renderDonutChart] Invalid rating value: ${ratingValue}, using default of 80%`);
    ratingValue = 80;
  }
  
  // Log the rating for debugging
  console.log(`[renderDonutChart] Rendering chart with rating: ${ratingValue}%`);

  // Ensure ratingValue is within 0-100
  const clampedRating = Math.max(0, Math.min(100, ratingValue));

  // Get CSS variables for colors from the chartElement itself or root
  const activeColor =
    getCssVariable("--highlight-color", chartElement) ||
    getCssVariable("--highlight-color") ||
    "#007bff";
  const trackColor =
    getCssVariable("--border-color-light", chartElement) ||
    getCssVariable("--border-color-light") ||
    "#e9ecef";

  chartElement.style.setProperty(
    "background",
    `conic-gradient(
        ${activeColor} 0% ${clampedRating}%,
        ${trackColor} ${clampedRating}% 100%
    )`,
  );

  // Add/update the rating text inside the donut
  let textSpan = chartElement.querySelector(".donut-chart-text");
  if (!textSpan) {
    textSpan = document.createElement("span");
    textSpan.className = "donut-chart-text"; // Ensure this class is styled
    chartElement.appendChild(textSpan);
  }
  textSpan.textContent = clampedRating + "%"; // Or however you want to format it
}

// --- [Removed] renderD3DonutChart function ---
// --- [Removed] renderD3DonutChart function ---
// --- [Removed] renderD3DonutChart function ---

// Inside static/script.js

// --- Undervaluation Chart (D3 - Example, might be replaced or removed) ---
// Cache for potential redraws or updates
let undervaluationChartInstance = null;

/**
 * Renders or updates a D3-based gauge chart for undervaluation score.
 * @param {number} score - The undervaluation score (0-100).
 * @param {string} targetElementId - The ID of the div where the chart should be rendered.
 */
function renderUndervaluationChart(score, targetElementId) {
  console.log(
    `[D3 Undervaluation] Rendering/Updating chart in #${targetElementId} with score: ${score}`,
  );
  if (typeof d3 === "undefined") {
    console.error("[D3 Undervaluation] D3 library not loaded.");
    const container = document.getElementById(targetElementId);
    if (container) container.textContent = "Chart Error (D3 missing)";
    return;
  }

  const container = d3.select(`#${targetElementId}`);
  if (container.empty()) {
    console.error(
      `[D3 Undervaluation] Target element #${targetElementId} not found.`,
    );
    return;
  }

  // --- Configuration ---
  const width = 120; // Slightly larger size
  const height = 100; // Adjusted height for semi-circle
  const thickness = 12;
  const radius = Math.min(width / 2, height) - thickness / 2; // Radius based on height for semi-circle
  const innerRadius = radius - thickness;
  const startAngle = -Math.PI / 2; // -90 degrees (top)
  const endAngle = Math.PI / 2; // +90 degrees (bottom)
  const fullAngleRange = endAngle - startAngle; // Total angle span (PI radians or 180 degrees)

  // --- Data Validation ---
  const validScore = Math.max(0, Math.min(100, safeJsFloat(score, 0))); // Clamp score 0-100
  const scoreAngle = startAngle + (validScore / 100) * fullAngleRange;

  // --- Color based on score ---
  function getRatingColor(score) {
    // Handle N/A case explicitly
    if (score === "N/A") return "#6c757d"; // Gray for N/A

    // Convert score to number if it's a string
    if (typeof score === "string") {
      const parsedScore = parseFloat(score);
      if (isNaN(parsedScore)) return "#6c757d"; // Gray for invalid values
      score = parsedScore;
    }

    // Map score (0-100) to color
    if (score >= 85) return "#198754"; // Success green
    if (score >= 70) return "#28a745"; // Regular green
    if (score >= 60) return "#ffc107"; // Warning yellow
    if (score >= 50) return "#fd7e14"; // Orange
    return "#dc3545"; // Danger red
  }
  const scoreColor = getRatingColor(score);
  const neutralColor = getCssVariable("--chatbot-input-bg") || "#e9ecef";

  // --- Arc Generators ---
  const arc = d3
    .arc()
    .innerRadius(innerRadius)
    .outerRadius(radius)
    .startAngle(startAngle)
    .cornerRadius(5); // Add rounded corners

  const backgroundArc = d3
    .arc()
    .innerRadius(innerRadius)
    .outerRadius(radius)
    .startAngle(startAngle)
    .endAngle(endAngle)
    .cornerRadius(5);

  // --- SVG Setup (or selection if updating) ---
  let svg = container.select("svg");
  let g; // Group element

  if (svg.empty()) {
    // --- Create SVG ---
    svg = container
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", `0 0 ${width} ${height}`) // Adjust viewBox
      .attr("preserveAspectRatio", "xMidYMid meet");

    g = svg.append("g").attr("transform", `translate(${width / 2},${height})`); // Translate to bottom-center

    // --- Background Arc (Neutral Color) ---
    g.append("path")
      .attr("class", "gauge-background-arc")
      .attr("d", backgroundArc)
      .style("fill", neutralColor)
      .style("opacity", 0.7);

    // --- Foreground Arc (Score Color) ---
    g.append("path")
      .attr("class", "gauge-foreground-arc")
      .datum({ endAngle: startAngle }) // Start at the beginning
      .style("fill", scoreColor)
      .attr("d", arc); // Apply arc generator

    // --- Center Text ---
    g.append("text")
      .attr("class", "gauge-center-text")
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "ideographic") // Baseline at the bottom of text
      .attr("dy", "-5px") // Adjust position slightly above the bottom center
      .style("font-size", "20px")
      .style("font-weight", "bold")
      .style("fill", "var(--chatbot-text-color)") // Use CSS variable
      .text("0%"); // Initial text

    undervaluationChartInstance = g; // Store the group element
    console.log("[D3 Undervaluation] Chart created.");
  } else {
    // --- Update Existing Chart ---
    g = undervaluationChartInstance; // Retrieve stored group
    console.log("[D3 Undervaluation] Updating existing chart.");
  }

  // --- Animation ---
  const foregroundPath = g.select(".gauge-foreground-arc");
  const centerText = g.select(".gauge-center-text");

  // Define the tween function for the arc animation
  const arcTween = (newAngle) => (d) => {
    const interpolate = d3.interpolate(d.endAngle, newAngle);
    return function (t) {
      return arc(interpolate(t));
    };
  };

  // Animate the foreground arc
  foregroundPath
    .transition()
    .duration(1000)
    .ease(d3.easeCubicOut)
    .style("fill", scoreColor) // Update color immediately or transition it
    .attrTween("d", arcTween(scoreAngle));

  // Animate the center text (optional: number counting effect)
  centerText
    .transition()
    .duration(1000)
    .ease(d3.easeCubicOut)
    .style("fill", scoreColor) // Match text color to arc color
    .tween("text", function () {
      // Use safeJsFloat on the current text content, stripping '%'
      const currentText = d3.select(this).text();
      const currentValue = safeJsFloat(currentText.replace("%", ""), 0);
      const interpolate = d3.interpolate(currentValue, validScore);
      return function (t) {
        d3.select(this).text(Math.round(interpolate(t)) + "%");
      };
    });

  console.log(`[D3 Undervaluation] Animation started for score ${validScore}`);
}

// --- Plotly Chart Theme Update ---
function updatePlotlyChartTheme(theme) {
  const plotlyLayoutUpdate = {
    paper_bgcolor:
      theme === "dark" ? "rgba(18, 18, 31, 0.8)" : "rgba(248, 249, 252, 0.8)", // Match body bg
    plot_bgcolor:
      theme === "dark" ? "rgba(30, 30, 45, 0.8)" : "rgba(233, 236, 239, 0.8)", // Slightly different plot bg
    "font.color": theme === "dark" ? "#EAEAEA" : "#333333",
    "xaxis.gridcolor":
      theme === "dark" ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)",
    "yaxis.gridcolor":
      theme === "dark" ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)",
    "xaxis.linecolor":
      theme === "dark" ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.2)",
    "yaxis.linecolor":
      theme === "dark" ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.2)",
    "xaxis.zerolinecolor":
      theme === "dark" ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.3)",
    "yaxis.zerolinecolor":
      theme === "dark" ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.3)",
    "title.font.color": theme === "dark" ? "#FFFFFF" : "#111111", // Brighter title color in dark mode
    "legend.bgcolor":
      theme === "dark" ? "rgba(40, 40, 60, 0.7)" : "rgba(255, 255, 255, 0.7)",
    "legend.bordercolor":
      theme === "dark" ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.2)",
  };

  // Update existing Plotly charts
  const charts = document.querySelectorAll(".plotly-chart-container > div"); // Adjust selector if needed
  charts.forEach((chartDiv) => {
    if (chartDiv.id && typeof Plotly !== "undefined") {
      Plotly.relayout(chartDiv.id, plotlyLayoutUpdate).catch((err) =>
        console.warn(`Plotly relayout failed for ${chartDiv.id}:`, err),
      );
    }
  });
}

// --- Portfolio Page Specific Functions ---

// --- Fetch Performance Chart Data ---
function fetchChartData(range, animate = false) {
  const chartContainer = document.getElementById(D3_PERF_CONTAINER_ID);
  if (!chartContainer) return; // Don't fetch if container isn't on the page

  console.log(`[D3 Perf Chart] Fetching data for range: ${range}`);
  showLoader(CHART_LOADER_ID); // Show loader before fetch

  fetch(`/api/portfolio-history?range=${range}`)
    .then((response) => {
      if (!response.ok) {
        return response.json().then((err) => {
          throw new Error(err.error || `HTTP error ${response.status}`);
        });
      }
      return response.json();
    })
    .then((data) => {
      console.log("[D3 Perf Chart] Data received:", data);
      if (typeof renderD3PortfolioChart === "function") {
        renderD3PortfolioChart(data, animate); // Pass animate flag
      } else {
        console.error("renderD3PortfolioChart function not found!");
        hideLoader(CHART_LOADER_ID);
      }
    })
    .catch((error) => {
      console.error("Error fetching portfolio performance:", error);
      hideLoader(CHART_LOADER_ID);
      const chartContainerDiv = document.getElementById(D3_PERF_CONTAINER_ID);
      if (chartContainerDiv) {
        const existingMsg = chartContainerDiv.querySelector(
          ".chart-error, .chart-info",
        );
        if (existingMsg) existingMsg.remove();
        const errorP = document.createElement("p");
        errorP.className = "chart-error"; // Use error class
        errorP.textContent = `Error loading chart: ${error.message}. Please try again later.`;
        chartContainerDiv.insertBefore(errorP, chartContainerDiv.firstChild); // Add error message inside container
      }
      // Clear performance value on error
      const pValueSpan = document.getElementById(D3_PERF_VALUE_SPAN_ID);
      if (pValueSpan) {
        pValueSpan.textContent = "Error";
        pValueSpan.className = "performance-value error";
      }
    });
}

// --- Fetch Diversification Chart Data ---
async function fetchAndRenderDiversificationCharts() {
  const assetContainer = document.getElementById("asset-allocation-container");
  const industryContainer = document.getElementById(
    "industry-allocation-container",
  );
  // const sectorContainer = document.getElementById('sector-allocation-container'); // If using sector chart

  if (!assetContainer && !industryContainer) return; // Don't fetch if no containers exist

  console.log("[Diversification] Fetching data...");
  if (assetContainer) showLoader(ASSET_LOADER_ID);
  if (industryContainer) showLoader(INDUSTRY_LOADER_ID);
  // if (sectorContainer) showLoader(SECTOR_LOADER_ID);

  try {
    const response = await fetch("/api/portfolio-diversification");
    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: `HTTP error ${response.status}` }));
      throw new Error(errorData.error || `HTTP error ${response.status}`);
    }
    const data = await response.json();
    console.log("[Diversification] Data received:", data);

    // Render Asset Allocation (D3)
    if (
      assetContainer &&
      data.asset_allocation &&
      typeof renderD3AssetDonut === "function"
    ) {
      renderD3AssetDonut(data.asset_allocation);
    } else if (assetContainer) {
      console.warn(
        "Asset allocation data missing or render function not found.",
      );
      hideLoader(ASSET_LOADER_ID);
      assetContainer.innerHTML =
        '<p class="chart-info">Asset allocation data unavailable.</p>';
    }

    // Render Industry Allocation (D3)
    if (
      industryContainer &&
      data.industry_allocation &&
      typeof renderD3IndustryDonut === "function"
    ) {
      renderD3IndustryDonut(data.industry_allocation);
    } else if (industryContainer) {
      console.warn(
        "Industry allocation data missing or render function not found.",
      );
      hideLoader(INDUSTRY_LOADER_ID);
      industryContainer.innerHTML =
        '<p class="chart-info">Industry allocation data unavailable.</p>';
    }

    // Render Sector Allocation (Plotly - Example)
    // if (sectorContainer && data.sector_allocation && typeof renderSimplePlotlyPie === 'function') {
    //     renderSimplePlotlyPie(SECTOR_CHART_ID, data.sector_allocation, 'Sector Allocation');
    //     hideLoader(SECTOR_LOADER_ID);
    // } else if (sectorContainer) {
    //     console.warn("Sector allocation data missing or render function not found.");
    //     hideLoader(SECTOR_LOADER_ID);
    //     sectorContainer.innerHTML = '<p class="chart-info">Sector allocation data unavailable.</p>';
    // }
  } catch (error) {
    console.error("Error fetching diversification data:", error);
    if (assetContainer) {
      hideLoader(ASSET_LOADER_ID);
      assetContainer.innerHTML = `<p class="chart-error">Error loading asset chart: ${error.message}</p>`;
    }
    if (industryContainer) {
      hideLoader(INDUSTRY_LOADER_ID);
      industryContainer.innerHTML = `<p class="chart-error">Error loading industry chart: ${error.message}</p>`;
    }
    // if (sectorContainer) { hideLoader(SECTOR_LOADER_ID); sectorContainer.innerHTML = `<p class="chart-error">Error loading sector chart: ${error.message}</p>`; }
  }
}

// --- Update Performance Chart Range ---
function updateChartRange(buttonElement) {
  const newRange = buttonElement.dataset.range;
  if (newRange === currentChartRange) return; // Do nothing if range hasn't changed

  currentChartRange = newRange;
  console.log(`[Chart Range] Set to: ${currentChartRange}`);

  // Update button active states
  document.querySelectorAll(".chart-range-btn").forEach((btn) => {
    btn.classList.remove("active");
  });
  buttonElement.classList.add("active");

  // Fetch new data for the performance chart (with animation only on initial load potentially)
  fetchChartData(currentChartRange, !chartHasBeenAnimated); // Animate only if not already animated
}

// --- Fetch Live Prices for Portfolio Table ---
async function fetchAndUpdateLivePrices() {
  const tableBody = document.querySelector(PORTFOLIO_TABLE_BODY_SELECTOR);
  if (!tableBody) return; // Exit if table body not found

  const tickers = Array.from(tableBody.querySelectorAll("tr[data-ticker]"))
    .map((row) => row.dataset.ticker)
    .filter((ticker) => ticker); // Get unique tickers from rows

  if (tickers.length === 0) {
    // console.log("No tickers found in portfolio table for price update.");
    return; // No tickers to update
  }

  // console.log("Fetching live prices for:", tickers);

  try {
    const response = await fetch("/api/portfolio-summary", {
      method: "GET",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ tickers: tickers }),
    });

    if (!response.ok) {
      // Try to get error message from response body
      let errorMsg = `HTTP error ${response.status}`;
      try {
        const errorData = await response.json();
        errorMsg = errorData.error || errorMsg;
      } catch (parseError) {
        // Ignore if response body is not JSON
      }
      throw new Error(errorMsg);
    }

    const priceData = await response.json();
    // console.log("Live prices received:", priceData);

    // Update table rows
    tickers.forEach((ticker) => {
      const data = priceData[ticker];
      if (data) {
        const priceStr =
          data.price !== null ? formatCurrency(data.price) : "N/A";
        const changeStr =
          data.changePercent !== null
            ? `${data.changePercent >= 0 ? "+" : ""}${data.changePercent.toFixed(2)}%`
            : "N/A";
        const changeClass =
          data.changePercent !== null
            ? data.changePercent >= 0
              ? "positive"
              : "negative"
            : "";
        updatePriceDisplay(ticker, priceStr, changeStr, changeClass);
      } else {
        // Handle cases where price data for a specific ticker wasn't returned
        updatePriceDisplay(ticker, "N/A", "N/A", "");
        console.warn(`No live price data returned for ${ticker}`);
      }
    });

    // After updating individual prices, recalculate and display the total daily change
    await calculateAndDisplayPortfolioDailyChange();
  } catch (error) {
    console.error("Error fetching live prices:", error);
    // Optionally display an error message to the user (e.g., in a status bar)
    // Example: updateStatusBar("Error updating prices: " + error.message);
    // For now, just log it and potentially stop refresh if errors persist
    stopAutoRefresh(); // Stop refreshing on error to avoid spamming logs/server
    console.warn("Auto-refresh stopped due to price fetch error.");
    // Indicate error state in the UI if possible (e.g., disable refresh button, show error icon)
    // Indicate error in the daily change display
    updateAssetChartCenterText(null, null, null, true); // Pass error flag
  }
}

// --- Update Price Display in Table Row ---
function updatePriceDisplay(ticker, priceStr, changeStr, changeClass) {
  const row = document.querySelector(
    `${PORTFOLIO_TABLE_BODY_SELECTOR} tr[data-ticker="${ticker}"]`,
  );
  if (!row) return;

  const priceCell = row.querySelector(".price-cell");
  const changeCell = row.querySelector(".change-cell");

  if (priceCell) {
    priceCell.textContent = priceStr;
    // Optional: Add subtle animation on update
    priceCell.classList.add("price-updated");
    setTimeout(() => priceCell.classList.remove("price-updated"), 500);
  }
  if (changeCell) {
    changeCell.textContent = changeStr;
    changeCell.className = `change-cell ${changeClass}`; // Reset and apply class
    // Optional: Add subtle animation on update
    changeCell.classList.add("price-updated");
    setTimeout(() => changeCell.classList.remove("price-updated"), 500);
  }
}

// --- Auto Refresh Control ---
function startAutoRefresh() {
  if (autoRefreshIntervalId) {
    // console.log("Auto-refresh already running.");
    return; // Already running
  }
  console.log("Starting auto-refresh for portfolio prices...");
  fetchAndUpdateLivePrices(); // Initial fetch immediately
  autoRefreshIntervalId = setInterval(
    fetchAndUpdateLivePrices,
    REFRESH_INTERVAL_MS,
  );
}

function stopAutoRefresh() {
  if (autoRefreshIntervalId) {
    clearInterval(autoRefreshIntervalId);
    autoRefreshIntervalId = null;
    console.log("Auto-refresh stopped.");
  }
}

// --- REVISED addMessage function - More aggressive iframe visibility ---
function addMessage(sender, messageData) {
  const messagesContainer = document.getElementById("chatbot-messages");
  if (!messagesContainer) {
    console.error("[addMessage] Chatbot messages container not found.");
    return;
  }

  const li = document.createElement("li");
  li.classList.add("message", sender);

  const avatar = document.createElement("span");
  avatar.classList.add(sender === "user" ? "user-avatar" : "bot-avatar");
  avatar.innerHTML = `<i class="fas ${sender === "user" ? "fa-user-circle" : "fa-robot"} chatbot-icon-small"></i>`;
  li.appendChild(avatar);

  const messageBubbleP = document.createElement("p");

  let textForDisplay = "";
  let isHTMLContent = false;
  let analysisPayloadForChart = null;
  let riskTextToDisplay = null;
  let iframeSrc = null;
  let messageTypeForClass = "general";
  let tickerForDataset = null;

  if (typeof messageData === "object" && messageData !== null) {
    textForDisplay = messageData.message || "";
    // Ensure isHTMLContent is handled correctly if message itself is HTML
    isHTMLContent =
      messageData.messageType === "analysis_response" ||
      (messageData.is_html !== undefined ? messageData.is_html : false);
    messageTypeForClass = messageData.messageType || "general";
    tickerForDataset = messageData.ticker || null;

    console.log(
      "[addMessage] Received messageData:",
      JSON.stringify(messageData).substring(0, 500) + "...",
    );

    // This is the crucial part for the chart
    if (
      messageData.messageType === "analysis_response" &&
      messageData.analysis &&
      typeof messageData.analysis === "object"
    ) {
      analysisPayloadForChart = messageData.analysis; // <= Assign here
      riskTextToDisplay = analysisPayloadForChart.risk_text; // For text below chart

      console.log(
        "[addMessage] analysis_response detected. Payload:",
        analysisPayloadForChart,
      );

      // More flexible handling of score and color for the chart
      // Check if the analysis object has a score (either number or 'N/A')
      if (analysisPayloadForChart.hasOwnProperty("score")) {
        let score = analysisPayloadForChart.score;
        let color;

        // Check if color is provided, otherwise generate based on score
        if (
          analysisPayloadForChart.hasOwnProperty("color") &&
          typeof analysisPayloadForChart.color === "string"
        ) {
          color = analysisPayloadForChart.color;
        } else {
          // Generate color based on score if it's a number
          if (typeof score === "number") {
            color = getRatingColor(score);
          } else if (score === "N/A") {
            color = "#6c757d"; // Gray for N/A
          } else {
            color = "#6c757d"; // Default gray
          }
        }

        // Encode values for URL
        const encodedColor = encodeURIComponent(color);
        iframeSrc = `/chatbot_chart_display?score=${score}&color=${encodedColor}`;
        console.log(
          `[addMessage] iframeSrc successfully created: ${iframeSrc}`,
        );
      } else {
        console.warn(
          "[addMessage] Score missing in analysis data. Chart iframe will not be rendered.",
          analysisPayloadForChart,
        );
        iframeSrc = null; // Explicitly nullify if data is bad
      }
    } else if (messageData.messageType === "analysis_response") {
      console.warn(
        "[addMessage] 'analysis_response' type, but 'analysis' object is missing or invalid.",
      );
    }
  } else if (typeof messageData === "string") {
    textForDisplay = messageData;
    isHTMLContent = false;
  } else {
    console.warn(
      "[addMessage] Received unexpected messageData format:",
      messageData,
    );
    textForDisplay = "[Error: Invalid message format]";
    isHTMLContent = false;
  }

  // ... (set classes on 'li' element) ...

  // Append text content to bubble (if any)
  if (textForDisplay) {
    const textDiv = document.createElement("div");
    textDiv.classList.add("message-text-content");
    if (isHTMLContent) {
      const tempDiv = document.createElement("div"); // Sanitize HTML
      tempDiv.innerHTML = textForDisplay;
      tempDiv.querySelectorAll("script").forEach((script) => script.remove()); // Basic sanitization
      tempDiv.innerHTML = tempDiv.innerHTML;
    } else {
      textDiv.textContent = textForDisplay;
    }
    messageBubbleP.appendChild(textDiv);
  }

  // If iframeSrc is prepared, create and append the iframe
  if (iframeSrc) {
    console.log(
      "[addMessage] Attempting to append iframe with src:",
      iframeSrc,
    );
    const iframeContainer = document.createElement("div");
    iframeContainer.classList.add("score-gauge-chart-container"); // Use your existing CSS class
    // Add aggressive debug styles to the container FOR NOW if needed, but CSS should handle it.
    // iframeContainer.style.border = "2px dashed blue"; // Temporary for visibility

    const iframe = document.createElement("iframe");
    iframe.src = iframeSrc;
    iframe.setAttribute("title", "Analysis Score Chart");
    iframe.setAttribute("scrolling", "no"); // Important for small iframes
    iframe.setAttribute("frameborder", "0"); // Remove border
    // Style iframe for fixed size. Your CSS already has aggressive styles for this.
    // iframe.style.width = "80px"; // Match iframe body/target div
    // iframe.style.height = "80px";
    // iframe.style.border = "1px solid red"; // Temporary for visibility
    // iframe.style.overflow = "hidden"; // Hide scrollbars

    iframeContainer.appendChild(iframe);
    messageBubbleP.appendChild(iframeContainer); // Append iframe container to the message bubble
    console.log(
      `[addMessage] SUCCESS: Appended iframe chart container to message bubble.`,
    );
  } else if (messageData && messageData.messageType === "analysis_response") {
    // Log if it was an analysis response but iframeSrc didn't get set (e.g., missing score/color)
    console.warn(
      "[addMessage] iframeSrc was null for analysis_response. Chart not added. Analysis Payload:",
      analysisPayloadForChart,
    );
  }

  // Append risk text (if any)
  if (riskTextToDisplay) {
    const riskTextDiv = document.createElement("div");
    riskTextDiv.classList.add("score-gauge-risk-text"); // Use your existing CSS class
    riskTextDiv.textContent = riskTextToDisplay;
    if (analysisPayloadForChart && analysisPayloadForChart.color) {
      riskTextDiv.style.color = analysisPayloadForChart.color; // Color the risk text
    }
    messageBubbleP.appendChild(riskTextDiv);
  }

  li.appendChild(messageBubbleP);
  messagesContainer.appendChild(li);

  li.style.opacity = 0;
  li.style.transform = "translateY(10px)";
  requestAnimationFrame(() => {
    setTimeout(() => {
      li.style.opacity = 1;
      li.style.transform = "translateY(0)";
    }, 10);
  });

  scrollToBottom();
}

// --- Update Industry Chart Center Text ---
function updateIndustryChartCenterText(totalValueDisplayStr) {
  const valueTextElement = document.getElementById(
    "industry-chart-center-value",
  );
  if (valueTextElement) {
    valueTextElement.textContent = totalValueDisplayStr || "N/A";
  }
}

// --- Portfolio Edit Modal & Form Handling ---
function setupModal() {
  const modalElement = document.getElementById(EDIT_MODAL_ID);
  if (!modalElement) return; // Modal doesn't exist on this page

  // Get Bootstrap Modal instance if available
  const modalInstance =
    bootstrap.Modal.getInstance(modalElement) ||
    new bootstrap.Modal(modalElement);

  // Add event listeners to buttons that should open the modal
  document.querySelectorAll(".edit-stock-btn").forEach((button) => {
    button.addEventListener("click", (event) => {
      const ticker = event.target.closest("tr").dataset.ticker;
      const currentAmount = event.target.closest("tr").dataset.amountInvested; // Get from data attribute
      if (ticker) {
        populateEditModal(ticker, currentAmount);
        modalInstance.show();
      } else {
        console.error("Could not find ticker for editing.");
      }
    });
  });

  // Setup form submission handler
  setupEditFormSubmission();
}

function populateEditModal(ticker, currentAmount) {
  const tickerDisplay = document.getElementById(EDIT_TICKER_DISPLAY_ID);
  const amountInput = document.getElementById(EDIT_AMOUNT_INPUT_ID);
  const tickerHiddenInput = document.getElementById(EDIT_TICKER_HIDDEN_ID);

  if (tickerDisplay) tickerDisplay.textContent = ticker;
  if (amountInput) amountInput.value = safeJsFloat(currentAmount, ""); // Use safeJsFloat and default to empty string
  if (tickerHiddenInput) tickerHiddenInput.value = ticker;
}

function setupEditFormSubmission() {
  const form = document.getElementById(EDIT_FORM_ID);
  if (!form) return;

  form.addEventListener("submit", async (event) => {
    event.preventDefault(); // Prevent default form submission

    const ticker = document.getElementById(EDIT_TICKER_HIDDEN_ID)?.value;
    const newAmount = document.getElementById(EDIT_AMOUNT_INPUT_ID)?.value;
    const modalElement = document.getElementById(EDIT_MODAL_ID);
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    const feedbackElement = form.querySelector(".form-feedback"); // Get feedback element

    if (!ticker || newAmount === undefined || newAmount === null) {
      console.error("Missing ticker or amount for update.");
      if (feedbackElement) {
        feedbackElement.textContent = "Error: Missing data.";
        feedbackElement.style.display = "block";
        feedbackElement.style.color = "red";
      }
      return;
    }

    const updateUrl = `/api/portfolio/update/${ticker}`;

    try {
      const response = await fetch(updateUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          // Add CSRF token header if needed (using Flask-WTF example)
          // 'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        },
        body: JSON.stringify({
          amount_invested: safeJsFloat(newAmount, 0), // Send cleaned number
        }),
      });

      const result = await response.json();

      if (response.ok) {
        console.log("Update successful:", result);
        if (modalInstance) modalInstance.hide(); // Hide modal on success
        // Optionally: Show success message briefly
        // updateStatusBar("Update successful!", "success");

        // Refresh the page or update the table row dynamically
        window.location.reload(); // Simple refresh for now
      } else {
        console.error("Update failed:", result);
        if (feedbackElement) {
          feedbackElement.textContent = `Error: ${result.error || "Unknown error"}`;
          feedbackElement.style.display = "block";
          feedbackElement.style.color = "red";
        }
      }
    } catch (error) {
      console.error("Error submitting update form:", error);
      if (feedbackElement) {
        feedbackElement.textContent = `Network Error: ${error.message}`;
        feedbackElement.style.display = "block";
        feedbackElement.style.color = "red";
      }
    }
  });
}

// --- Chatbot Specific Functions ---

// Global references to chatbot elements (declared here for broader scope)
let chatbotContainer,
  chatbotToggle,
  chatbotWindow,
  chatbotClose,
  chatbotMessages,
  chatbotInput,
  chatbotSend,
  typingIndicator,
  chatbotSettingsBtn,
  settingsModal,
  settingsForm,
  apiKeyInputContainer,
  modelSelect,
  clearKeysBtn;

// --- Initialize Chatbot ---

/**
 * Renders an SVG score gauge/progress circle.
 * @param {string} targetElementId - The ID of the container div to render the SVG into.
 * @param {object} analysisData - Object containing { score: number, color: string }.
 */
function renderScoreGaugeSVG(targetElementId, analysisScore) {
  // Handle both object and direct score inputs
  let analysisData = analysisScore;
  if (typeof analysisScore === 'number') {
    analysisData = { score: analysisScore };
  } else if (!analysisData || typeof analysisData !== 'object') {
    console.warn(`[SVG Gauge] Invalid data format for #${targetElementId}, using default`);
    analysisData = { score: 80 }; // Default to 80% if data is invalid
  }
  
  console.log(`[SVG Gauge] Rendering for #${targetElementId}`, analysisData);
  const container = document.getElementById(targetElementId);
  if (!container) {
    console.error(`[SVG Gauge] Target element #${targetElementId} not found.`);
    return;
  }

  // --- Validate Data ---
  // Make sure we have a valid score - default to 80 if invalid
  const rawScore = typeof analysisData === 'object' ? analysisData.score : analysisData;
  const score = rawScore && !isNaN(rawScore) && rawScore > 0 ?
    Math.max(0, Math.min(100, safeJsFloat(rawScore))) : 
    80; // Default to 80% if invalid
    
  console.log(`[SVG Gauge] Using score: ${score}% for #${targetElementId}`);
  
  // Determine color based on score
  let scoreColor;
  if (analysisData.color && CSS.supports("color", analysisData.color)) {
    scoreColor = analysisData.color;
  } else {
    // Use a color scale based on the score
    if (score >= 80) scoreColor = "#28a745"; // Success green
    else if (score >= 60) scoreColor = "#ffc107"; // Warning yellow
    else if (score >= 40) scoreColor = "#fd7e14"; // Orange
    else scoreColor = "#dc3545"; // Danger red
  }
  
  const neutralColor = getCssVariable("--chatbot-input-bg") || "#e9ecef"; // Use theme variable

  // --- Configuration ---
  const size = 80; // Fixed size for the gauge
  const strokeWidth = 8; // Thickness of the gauge ring
  const radius = size / 2 - strokeWidth / 2; // Radius of the circle's centerline
  const circumference = 2 * Math.PI * radius;
  const offset = circumference * (1 - score / 100); // Calculate stroke offset

  const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  svg.setAttribute("width", size);
  svg.setAttribute("height", size);
  svg.setAttribute("viewBox", `0 0 ${size} ${size}`);
  svg.classList.add("score-gauge-svg"); // Add class for potential styling

  // --- Background Circle (Track) ---
  const backgroundCircle = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "circle",
  );
  backgroundCircle.setAttribute("cx", size / 2);
  backgroundCircle.setAttribute("cy", size / 2);
  backgroundCircle.setAttribute("r", radius);
  backgroundCircle.setAttribute("fill", "none");
  backgroundCircle.setAttribute("stroke", neutralColor);
  backgroundCircle.setAttribute("stroke-width", strokeWidth);
  backgroundCircle.classList.add("gauge-background");
  svg.appendChild(backgroundCircle);

  // --- Foreground Circle (Progress) ---
  const foregroundCircle = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "circle",
  );
  foregroundCircle.setAttribute("cx", size / 2);
  foregroundCircle.setAttribute("cy", size / 2);
  foregroundCircle.setAttribute("r", radius);
  foregroundCircle.setAttribute("fill", "none");
  foregroundCircle.setAttribute("stroke", scoreColor); // Use the score color
  foregroundCircle.setAttribute("stroke-width", strokeWidth);
  foregroundCircle.setAttribute("stroke-dasharray", circumference);
  foregroundCircle.setAttribute("stroke-dashoffset", circumference); // Start fully hidden
  foregroundCircle.setAttribute("stroke-linecap", "round"); // Rounded ends
  foregroundCircle.setAttribute("transform", `rotate(-90)`);
  foregroundCircle.classList.add("gauge-foreground");
  svg.appendChild(foregroundCircle);

  // --- Center Text (Score Percentage) ---
  const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
  text.setAttribute("x", "50%");
  text.setAttribute("y", "50%");
  text.setAttribute("text-anchor", "middle");
  text.setAttribute("dominant-baseline", "middle");
  text.classList.add("gauge-center-text");

  const scoreTspan = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "tspan",
  );
  scoreTspan.classList.add("gauge-score");
  scoreTspan.textContent = Math.round(score); // Display rounded score
  text.appendChild(scoreTspan);

  const percentTspan = document.createElementNS(
    "http://www.w3.org/2000/svg",
    "tspan",
  );
  percentTspan.classList.add("gauge-percent-sign");
  percentTspan.textContent = "%";
  percentTspan.setAttribute("dy", "-0.1em"); // Slightly raise the % sign
  text.appendChild(percentTspan);

  svg.appendChild(text);

  // --- Append SVG to Container ---
  container.appendChild(svg);

  // --- Animate Foreground Circle ---
  // Use requestAnimationFrame to ensure the element is in the DOM before animating
  requestAnimationFrame(() => {
    setTimeout(() => {
      // Small delay helps ensure rendering before transition starts
      foregroundCircle.style.transition = "stroke-dashoffset 0.8s ease-out";
      foregroundCircle.setAttribute("stroke-dashoffset", offset);
    }, 50);
  });

  console.log(
    `[SVG Gauge] Rendered successfully for #${targetElementId}. Offset: ${offset}`,
  );
}

// --- End renderScoreGaugeSVG ---

// Function to extract ratings from text
function extractRatingsFromText(text) {
  console.log(
    "[extractRatingsFromText] Processing text:",
    text.substring(0, 200) + "...",
  );

  const ratings = {
    overall: 0,
    business: 0, // renamed from moat to align with new prompt format
    financial: 0,
    valuation: 0,
    management: 0,
  };

  // Look for patterns like "Business Quality/Moat: 6/10 (60%)" or "Overall Rating: 13/30 (43.3%)"
  
  // For overall rating - EXPANDED PATTERN MATCHING
  const overallMatches = [
    // Look for patterns like "Overall Rating: 77.5%" or "Overall Investment Rating: 77.5%"
    text.match(/Overall (?:Investment )?Rating:?\s*(\d+(\.\d+)?)%/i),
    // Pattern like "Overall Rating: 13/30 (43.3%)"
    text.match(/Overall (?:Investment )?Rating:?\s*(\d+(\.\d+)?)\/(\d+)\s*\((\d+(\.\d+)?)%\)/i),
    // Pattern like "overallRating: 77.5" or "overallRating: 77.5%"
    text.match(/overallRating:?\s*(\d+(\.\d+)?)%?/i),
    // Pattern like "Overall: 77.5%" 
    text.match(/Overall:?\s*(\d+(\.\d+)?)%/i),
    // Direct JSON format like "overallRating": 77.5
    text.match(/"overallRating":\s*(\d+(\.\d+)?)/i),
    // Direct number after Overall Investment Rating header
    text.match(/Overall Investment Rating[:\s]*(\d+(\.\d+)?)%?/i),
    // Look for standalone patterns like "80%" right after Overall Investment Rating on a new line
    text.match(/Overall (?:Investment )?Rating[^\n]*\n\s*(\d+(\.\d+)?)%/i),
    // Look for Overall Rating: followed by a number on a new line
    text.match(/Overall (?:Investment )?Rating[^\n]*\n\s*(\d+(\.\d+)?)\s*(?:\n|$)/i),
    // Pattern for "Overall Rating: XX%" at the end of analysis
    text.match(/Overall Rating:[\s\n]*(\d+(?:\.\d+)?)%/i),
    // Pattern for plain "80%" after "Overall Rating:" line
    text.match(/Overall Rating:[\s\n]*(\d+(?:\.\d+)?)/i),
    // Fallback - find any section starting with "Overall Rating" followed eventually by a percentage anywhere
    (function() {
      const overallSection = text.match(/Overall (?:Investment )?Rating[\s\S]*?(\d+(?:\.\d+)?)%/i);
      if (overallSection) return overallSection;
      return null;
    })(),
    // Look for standalone number after "Overall Rating:" header
    text.match(/Overall Rating:\s*\n*\s*(\d+(?:\.\d+)?)/i),
    // Last resort - look for any pattern at the end that has "Overall" and a number
    (function() {
      // Look at the last few paragraphs
      const lastPart = text.split("\n").slice(-20).join("\n");
      const overallMatch = lastPart.match(/Overall[^0-9]*(\d+)%?/i);
      if (overallMatch) return overallMatch;
      return null;
    })()
  ];
  
  // Find the first match that worked
  let overallMatch = null;
  let matchIndex = -1;
  
  for (let i = 0; i < overallMatches.length; i++) {
    const match = overallMatches[i];
    if (match) {
      overallMatch = match;
      matchIndex = i;
      console.log(`[extractRatingsFromText] Match found for overall rating at pattern index ${i}:`, match[0]);
      break;
    }
  }
  
  if (!overallMatch) {
    console.warn("[extractRatingsFromText] No match found for overall rating. Checking the last part of the text...");
    // Last resort attempt - search in the last part of the text for something that looks like a rating
    const lastPart = text.split("\n").slice(-30).join("\n");
    console.log("Last part of text:", lastPart);
    
    // Try a very simple pattern that might catch most cases
    const simpleMatch = lastPart.match(/Overall[^:]*:[^\d]*(\d+)/i);
    if (simpleMatch) {
      console.log("[extractRatingsFromText] Found overall rating with last resort simple pattern:", simpleMatch[1]);
      overallMatch = simpleMatch;
    } else {
      // Try patterns specifically for the format in the user's example
      const patternVariants = [
        // Pattern for "Overall Rating: XX%" at the very end
        /Overall Rating:[\s\n]*(\d+)%?$/i,
        // Pattern for section ending with "Overall Rating: XX%"
        /Overall Rating:[\s\n]*(\d+)%?[\s\n]*\(/i,
        // Pattern for "Overall Rating:" followed by a line containing just a number
        /Overall Rating:[\s\n]*?^(\d+)$/mi,
            // Pattern matching specifically the format in the example where 80% is on a line after "Overall Rating:"
    /Overall Rating:[\s\S]*?[\n\r]\s*(\d+)\s*%/i,
    // Direct pattern for the exact format in the user's example
    /Overall Rating:\s*\n*\s*(\d+)%?\s*$/i,
    // Most general fallback for anything mentioning "Overall" and a number near the end of the text
    /Overall.*(\d+)%?\s*$/i
      ];
      
      for (const pattern of patternVariants) {
        const match = text.match(pattern);
        if (match) {
          console.log("[extractRatingsFromText] Found overall rating with specific pattern:", match[1]);
          overallMatch = match;
          break;
        }
      }
    }
  }
  
  if (overallMatch) {
    // If we have a format with numerator/denominator (X/Y (Z%))
    if (overallMatch[3] && overallMatch[4]) {
      ratings.overall = parseFloat(overallMatch[4]); // Use the percentage directly
    } else {
      // Otherwise, just use the first captured number
      ratings.overall = parseFloat(overallMatch[1]);
    }
    console.log(
      "[extractRatingsFromText] Found overall rating:",
      ratings.overall,
    );
  }

  // For business quality/moat
  const businessMatches = [
    // Look for patterns like "Business Quality: 70%" or "Business: 70%"
    text.match(/Business (?:Quality)?:?\s*(\d+(\.\d+)?)%/i),
    // Pattern like "Business Quality: 7/10 (70%)"
    text.match(/Business (?:Quality)?(?:\/Moat)?:?\s*(\d+(\.\d+)?)\/(\d+)\s*\((\d+(\.\d+)?)%\)/i),
    // Pattern like "Business Quality/Moat: 7/10 (70%)"
    text.match(/Business (?:Quality\/Moat)?:?\s*(\d+(\.\d+)?)\/(\d+)\s*\((\d+(\.\d+)?)%\)/i),
    // Direct number after Business Quality header
    text.match(/Business Quality[:\s]*(\d+(\.\d+)?)%?/i),
    // Look for business quality followed by a number on a new line
    text.match(/Business Quality[^\n]*\n\s*(\d+(\.\d+)?)\s*(?:\n|$)/i),
    // Look for standalone patterns like "90%" right after Business Quality on a new line
    text.match(/Business Quality[^\n]*\n\s*(\d+(\.\d+)?)%/i),
    // Standalone business quality section with number
    (function() {
      const section = text.match(/🏰\s*\n*\s*Business Quality\s*\n*\s*(\d+)(?:\.\d+)?(?:\s*%)?/i);
      if (section) return section;
      return null;
    })()
  ];
  
  let businessMatch = null;
  for (const match of businessMatches) {
    if (match) {
      businessMatch = match;
      break;
    }
  }
  
  if (businessMatch) {
    // If we have a format with percentage in parentheses
    if (businessMatch[4]) {
      ratings.business = parseFloat(businessMatch[4]); // Use the percentage directly  
    } else {
      // Otherwise, just use the first captured number
      ratings.business = parseFloat(businessMatch[1]);
    }
    console.log("[extractRatingsFromText] Found business rating:", ratings.business);
  }

  // For financial strength
  const financialMatches = [
    // Look for patterns like "Financial Strength: 85%" or "Financial: 85%"
    text.match(/Financial (?:Strength)?:?\s*(\d+(\.\d+)?)%/i), 
    // Pattern like "Financial Strength: 8.5/10 (85%)"
    text.match(/Financial (?:Strength)?:?\s*(\d+(\.\d+)?)\/(\d+)\s*\((\d+(\.\d+)?)%\)/i),
    // Direct number after Financial Strength header
    text.match(/Financial Strength[:\s]*(\d+(\.\d+)?)%?/i),
    // Look for financial strength followed by a number on a new line
    text.match(/Financial Strength[^\n]*\n\s*(\d+(\.\d+)?)\s*(?:\n|$)/i),
    // Look for standalone patterns like "90%" right after Financial Strength on a new line
    text.match(/Financial Strength[^\n]*\n\s*(\d+(\.\d+)?)%/i),
    // Standalone financial strength section with number
    (function() {
      const section = text.match(/💪\s*\n*\s*Financial Strength\s*\n*\s*(\d+)(?:\.\d+)?(?:\s*%)?/i);
      if (section) return section;
      return null;
    })()
  ];
  
  let financialMatch = null;
  for (const match of financialMatches) {
    if (match) {
      financialMatch = match;
      break;
    }
  }
  
  if (financialMatch) {
    // If we have a format with percentage in parentheses
    if (financialMatch[4]) {
      ratings.financial = parseFloat(financialMatch[4]); // Use the percentage directly
    } else {
      // Otherwise, just use the first captured number
      ratings.financial = parseFloat(financialMatch[1]);
    }
    console.log(
      "[extractRatingsFromText] Found financial rating:",
      ratings.financial,
    );
  }

  // For valuation
  const valuationMatches = [
    // Look for patterns like "Valuation: 75%" 
    text.match(/Valuation:?\s*(\d+(\.\d+)?)%/i),
    // Pattern like "Valuation: 7.5/10 (75%)"
    text.match(/Valuation:?\s*(\d+(\.\d+)?)\/(\d+)\s*\((\d+(\.\d+)?)%\)/i),
    // Direct number after Valuation header
    text.match(/Valuation[:\s]*(\d+(\.\d+)?)%?/i),
    // Look for valuation followed by a number on a new line
    text.match(/Valuation[^\n]*\n\s*(\d+(\.\d+)?)\s*(?:\n|$)/i),
    // Look for standalone patterns like "70%" right after Valuation on a new line
    text.match(/Valuation[^\n]*\n\s*(\d+(\.\d+)?)%/i),
    // Standalone valuation section with number
    (function() {
      const section = text.match(/💰\s*\n*\s*Valuation\s*\n*\s*(\d+)(?:\.\d+)?(?:\s*%)?/i);
      if (section) return section;
      return null;
    })()
  ];
  
  let valuationMatch = null;
  for (const match of valuationMatches) {
    if (match) {
      valuationMatch = match;
      break;
    }
  }
  
  if (valuationMatch) {
    // If we have a format with percentage in parentheses
    if (valuationMatch[4]) {
      ratings.valuation = parseFloat(valuationMatch[4]); // Use the percentage directly
    } else {
      // Otherwise, just use the first captured number
      ratings.valuation = parseFloat(valuationMatch[1]);
    }
    console.log(
      "[extractRatingsFromText] Found valuation rating:",
      ratings.valuation,
    );
  }

  // For management
  const managementMatches = [
    // Look for patterns like "Management: 65%" or "Management & Capital Allocation: 65%"
    text.match(/Management(?: & Capital Allocation)?:?\s*(\d+(\.\d+)?)%/i),
    // Pattern like "Management: 6.5/10 (65%)"
    text.match(/Management(?: & Capital Allocation)?:?\s*(\d+(\.\d+)?)\/(\d+)\s*\((\d+(\.\d+)?)%\)/i),
    // Special case for N/A
    text.match(/Management(?: & Capital Allocation)?:?\s*N\/A\s*\((\d+)%\)/i),
    // Direct number after Management header
    text.match(/Management[:\s]*(\d+(\.\d+)?)%?/i),
    // Look for management followed by a number on a new line
    text.match(/Management(?: & Capital Allocation)?[^\n]*\n\s*(\d+(\.\d+)?)\s*(?:\n|$)/i),
    // Look for standalone patterns like "70%" right after Management on a new line
    text.match(/Management(?: & Capital Allocation)?[^\n]*\n\s*(\d+(\.\d+)?)%/i),
    // Standalone management section with number
    (function() {
      const section = text.match(/👥\s*\n*\s*Management(?:\s*&\s*Capital\s*Allocation)?\s*\n*\s*(\d+)(?:\.\d+)?(?:\s*%)?/i);
      if (section) return section;
      return null;
    })()
  ];
  
  let managementMatch = null;
  for (const match of managementMatches) {
    if (match) {
      managementMatch = match;
      break;
    }
  }
  
  if (managementMatch) {
    // Handle N/A case
    if (managementMatch[0] && managementMatch[0].includes("N/A")) {
      // Extract the percentage value in the N/A case
      const percentage = parseFloat(managementMatch[1]);
      ratings.management = percentage;
    } 
    // If we have a format with percentage in parentheses
    else if (managementMatch[4]) {
      ratings.management = parseFloat(managementMatch[4]); // Use the percentage directly
    } else {
      // Otherwise, just use the first captured number
      ratings.management = parseFloat(managementMatch[1]);
    }
    console.log(
      "[extractRatingsFromText] Found management rating:",
      ratings.management,
    );
  }

  // ALWAYS calculate the overall rating from components, even if we found one directly in the text
  // This ensures consistency between component ratings and overall rating
  if ((ratings.business || ratings.financial || ratings.valuation || ratings.management)) {
    console.log("[extractRatingsFromText] Calculating overall rating from components");
    
    // Define weights for each component
    const weights = {
      business: 0.35,   // Business quality is critical
      financial: 0.25,  // Financial strength is important
      valuation: 0.25,  // Valuation is important
      management: 0.15  // Management contributes less to the overall score
    };
    
    // Count how many valid ratings we have
    const validRatings = Object.keys(ratings)
      .filter(key => key !== 'overall' && ratings[key] > 0)
      .length;
    
    if (validRatings > 0) {
      // Calculate weighted sum of available ratings
      let weightedSum = 0;
      let weightSum = 0;
      
      for (const [component, weight] of Object.entries(weights)) {
        if (ratings[component] > 0) {
          weightedSum += ratings[component] * weight;
          weightSum += weight;
        }
      }
      
      // Calculate weighted average - ALWAYS override the overall rating
      if (weightSum > 0) {
        const calculatedOverall = Math.round(weightedSum / weightSum);
        console.log(`[extractRatingsFromText] Original overall: ${ratings.overall}%, Calculated overall: ${calculatedOverall}%`);
        
        // Always use the calculated overall
        ratings.overall = calculatedOverall;
      }
    }
    
    // Last resort failsafe: if we have the component ratings but somehow overall is still 0,
    // just average the component ratings
    if (!ratings.overall && validRatings > 0) {
      let sum = 0;
      let count = 0;
      
      if (ratings.business > 0) { sum += ratings.business; count++; }
      if (ratings.financial > 0) { sum += ratings.financial; count++; }
      if (ratings.valuation > 0) { sum += ratings.valuation; count++; }
      if (ratings.management > 0) { sum += ratings.management; count++; }
      
      if (count > 0) {
        ratings.overall = Math.round(sum / count);
        console.log(`[extractRatingsFromText] Failsafe overall rating: ${ratings.overall}%`);
      }
    }
  }
  
  // Final failsafe: if we have no valid ratings at all, see if we can find an overall rating
  // from a specific section format at the end of the text
  if (!ratings.overall) {
    console.warn("[extractRatingsFromText] No overall rating found, searching for final section format");
    
    // Try to find an overall rating at the end of the text in a section like "Overall Rating: 80%"
    const finalSections = text.split(/\n\s*\n/).slice(-5); // Get the last few sections
    
    for (const section of finalSections) {
      if (section.includes("Overall Rating")) {
        const match = section.match(/Overall Rating:?\s*(\d+)%?/i);
        if (match && match[1]) {
          ratings.overall = parseInt(match[1], 10);
          console.log(`[extractRatingsFromText] Found overall rating in final section: ${ratings.overall}%`);
          break;
        }
      }
    }
    
    // If still no overall rating but we have the example format with "80%" at the end
    if (!ratings.overall) {
      // This targets the exact format from the user's example
      const lines = text.split('\n');
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes("Overall Rating:")) {
          // Check the next few lines for a standalone number
          for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
            const match = lines[j].trim().match(/^(\d+)%?$/);
            if (match) {
              ratings.overall = parseInt(match[1], 10);
              console.log(`[extractRatingsFromText] Found overall rating in standalone format: ${ratings.overall}%`);
              break;
            }
          }
          break;
        }
      }
    }
  }
  
  // Final hardcoded fallback - set a minimum rating based on the component ratings
  if (!ratings.overall && (ratings.business || ratings.financial || ratings.valuation || ratings.management)) {
    ratings.overall = 80; // Use a reasonable default based on the example
    console.warn(`[extractRatingsFromText] Using fallback overall rating: ${ratings.overall}%`);
  }
  
  // Return the extracted ratings
  return ratings;
}

// Updated addMessage to handle both string messages and structured analysis objects
// --- REVISED addMessage Function (Relevant Part) ---
// --- REVISED addMessage Function (Focus on analysis_response handling) ---
// In static/script.js

// Ensure safeJsFloat and getCssVariable are defined globally or within this scope
// (They are already defined in your provided script.js)

// --- REFINED addMessage Function for Correct Chart Placement ---
// --- REFINED addMessage Function for Correct Chart Placement and Structure ---
// --- REFINED addMessage Function for Iframe Chart Placement ---
// --- REVISED addMessage function (replaces the old one) ---
function addMessage(
  sender,
  messageData,
  isHTML = false,
  messageType = "general",
  ticker = null,
) {
  try {
    const messagesContainer = document.getElementById("chatbot-messages");
    if (!messagesContainer) {
      console.error("[addMessage] Chatbot messages container not found.");
      return;
    }

    // Ensure backwards compatibility with string messages
    let textForDisplay = "";
    let isHTMLContent = isHTML;
    let messageTypeForClass = messageType;
    let tickerForDataset = ticker;
    let ratings = null;

    // Handle object-style messages
    if (typeof messageData === "object" && messageData !== null) {
      textForDisplay = messageData.text || messageData.message || "";
      isHTMLContent =
        messageData.is_html !== undefined ? messageData.is_html : isHTML;
      messageTypeForClass =
        messageData.messageType || messageData.type || messageType;
      tickerForDataset = messageData.ticker || ticker;
    } else if (typeof messageData === "string") {
      textForDisplay = messageData;
    } else {
      console.warn("[addMessage] Unexpected messageData format:", messageData);
      textForDisplay = "Error displaying message";
    }

    console.log("[addMessage] Processing message:", {
      sender,
      textLength: textForDisplay.length,
      isHTML: isHTMLContent,
      type: messageTypeForClass,
      ticker: tickerForDataset,
    });

    const li = document.createElement("li");
    li.classList.add("message", sender);
    li.style.opacity = 0;
    li.style.transform = "translateY(10px)";

    const avatar = document.createElement("span");
    avatar.classList.add(sender === "user" ? "user-avatar" : "bot-avatar");
    avatar.innerHTML = `<i class="fas ${sender === "user" ? "fa-user-circle" : "fa-robot"} chatbot-icon-small"></i>`;
    li.appendChild(avatar);

    const messageBubbleP = document.createElement("p");

    // Extract ratings if this is an analysis response
    if (
      (messageTypeForClass === "analysis_response" ||
        (sender === "bot" &&
          typeof textForDisplay === "string" &&
          (textForDisplay.includes("Business Quality/Moat") ||
            textForDisplay.includes("Overall Rating") ||
            textForDisplay.includes("Business Quality:") ||
            textForDisplay.includes("Financial Strength:")))) &&
      typeof textForDisplay === "string"
    ) {
      try {
        ratings = extractRatingsFromText(textForDisplay);
        console.log("[addMessage] Extracted ratings:", ratings);
        
        // Create a chart visualization element for embedding in the message
        if (ratings && Object.values(ratings).some(val => val > 0)) {
          // Create a visualization container for the message 
          const messageChartVisualization = document.createElement("div"); 
          messageChartVisualization.className = "message-rating-charts"; 
          
          // Add this to the message
          messageBubbleP.appendChild(messageChartVisualization);
          
          // Make sure we have an overall rating
        if (!ratings.overall && (ratings.business || ratings.financial || ratings.valuation || ratings.management)) {
          // Calculate weighted average for the overall rating
          const weights = { business: 0.35, financial: 0.25, valuation: 0.25, management: 0.15 };
          let weightedSum = 0;
          let weightSum = 0;
          
          for (const [component, weight] of Object.entries(weights)) {
            if (ratings[component] > 0) {
              weightedSum += ratings[component] * weight;
              weightSum += weight;
            }
          }
          
          if (weightSum > 0) {
            ratings.overall = Math.round(weightedSum / weightSum);
          } else {
            // If we still don't have weights, use 80 as default
            ratings.overall = 80;
          }
          
          console.log("[addMessage] Forced overall rating calculation:", ratings.overall);
        }
        
        // Create the component charts
        const components = [
            { name: "Overall Investment Rating", value: ratings.overall || 80, icon: "⭐" }, // Always provide a value
            { name: "Business Quality", value: ratings.business || 0, icon: "🏰" },
            { name: "Financial Strength", value: ratings.financial || 0, icon: "💪" },
            { name: "Valuation", value: ratings.valuation || 0, icon: "💰" },
            { name: "Management", value: ratings.management || 0, icon: "👥" }
          ]; 
          
          // Filter out zero values
          const validComponents = components.filter(comp => comp.value > 0);
          
          // Create a grid layout for the charts
          if (validComponents.length > 0) {
            const chartGrid = document.createElement("div");
            chartGrid.className = "rating-chart-grid";
            chartGrid.style.display = "grid";
            chartGrid.style.gridTemplateColumns = "repeat(auto-fit, minmax(150px, 1fr))";
            chartGrid.style.gap = "15px";
            chartGrid.style.marginTop = "10px";
            chartGrid.style.marginBottom = "15px";
            
            validComponents.forEach(comp => {
              const chartContainer = document.createElement("div");
              chartContainer.className = "rating-chart-container-message";
              
              const chartLabel = document.createElement("div");
              chartLabel.className = "chart-label";
              chartLabel.innerHTML = `${comp.icon} ${comp.name}`;
              
              const chartElement = document.createElement("div");
              chartElement.className = "component-chart";
              
              const scoreDisplay = document.createElement("div");
              scoreDisplay.className = "score-display";
              scoreDisplay.innerHTML = `<strong>${Math.round(comp.value)}%</strong>`;
              
              chartContainer.append(chartLabel, chartElement, scoreDisplay);
              chartGrid.appendChild(chartContainer);
              
              // Render the donut chart
              renderDonutChart(comp.value, chartElement);
            });
            
            messageChartVisualization.appendChild(chartGrid);
          }
        }
        
      } catch (ratingError) {
        console.warn("[addMessage] Error extracting ratings:", ratingError);
      }

      // Update the global rating charts with these ratings
      if (ratings.overall > 0) {
        // Make sure the overall rating matches the one shown at the top
        if (typeof updateGlobalRatingChart === "function") {
          updateGlobalRatingChart(ratings.overall);
          console.log("[addMessage] Updating global rating chart with overall rating:", ratings.overall);
        }
      }
      if (ratings.business > 0) {
        if (typeof updateMoatRatingChart === "function") {
          updateMoatRatingChart(ratings.business);
          console.log("[addMessage] Updating moat rating chart with business rating:", ratings.business);
        }
      }
      if (ratings.financial > 0) {
        if (typeof updateFinancialRatingChart === "function") {
          updateFinancialRatingChart(ratings.financial);
          console.log("[addMessage] Updating financial rating chart with financial rating:", ratings.financial);
        }
      }
      if (ratings.valuation > 0) {
        if (typeof updateValuationRatingChart === "function") {
          updateValuationRatingChart(ratings.valuation);
          console.log("[addMessage] Updating valuation rating chart with valuation rating:", ratings.valuation);
        }
      }
      if (ratings.management > 0) {
        if (typeof updateManagementRatingChart === "function") {
          updateManagementRatingChart(ratings.management);
        }
      }

      // Make charts container visible
      const chartsContainer = document.querySelector(".charts-container");
      if (chartsContainer) {
        chartsContainer.style.display = "flex";
      }

      // ✨ CREATE PREMIUM ENHANCED RATING VISUALIZATION ✨
      if (ratings && Object.values(ratings).some(val => val > 0)) {
        try {
          // Create the premium enhanced container
          let enhancedRatingContainer = document.querySelector(".enhanced-rating-charts-container");
          if (!enhancedRatingContainer) {
            enhancedRatingContainer = document.createElement("div");
            enhancedRatingContainer.className = "enhanced-rating-charts-container";
            if (chartsContainer) chartsContainer.appendChild(enhancedRatingContainer);
          }

          // Clear existing content
          enhancedRatingContainer.innerHTML = '';

          // Add content wrapper for layering effects
          const contentWrapper = document.createElement("div");
          contentWrapper.className = "content-wrapper";

          // Add shimmer overlay for premium effect
          const shimmerOverlay = document.createElement("div");
          shimmerOverlay.className = "shimmer-overlay";

          // Calculate overall rating with enhanced logic
          let overallRating = ratings.overall;

          if (!overallRating && (ratings.business || ratings.financial || ratings.valuation || ratings.management)) {
            const weights = { business: 0.35, financial: 0.25, valuation: 0.25, management: 0.15 };
            let weightedSum = 0;
            let weightSum = 0;

            for (const [component, weight] of Object.entries(weights)) {
              if (ratings[component] > 0) {
                weightedSum += ratings[component] * weight;
                weightSum += weight;
              }
            }

            if (weightSum > 0) {
              overallRating = Math.round(weightedSum / weightSum);
            } else {
              // Simple average fallback
              let sum = 0, count = 0;
              ['business', 'financial', 'valuation', 'management'].forEach(key => {
                if (ratings[key] > 0) { sum += ratings[key]; count++; }
              });
              overallRating = count > 0 ? Math.round(sum / count) : 80;
            }
          }
          overallRating = overallRating || 80;

          // Ensure we have a valid rating
          if (overallRating === 0 || isNaN(overallRating)) {
            overallRating = 75; // Default to a reasonable rating
          }

          // 🌟 CREATE SPECTACULAR OVERALL INVESTMENT RATING SECTION 🌟
          const overallSection = document.createElement("div");
          overallSection.className = "overall-rating-section";

          // Add exceptional rating class for high scores
          if (overallRating >= 85) {
            overallSection.classList.add("exceptional-rating");
          }

          // Create the spectacular overall chart wrapper
          const overallChartWrapper = document.createElement("div");
          overallChartWrapper.className = "overall-chart-wrapper";
          overallChartWrapper.id = "overall-rating-chart-container";

          // Add the chart wrapper to the section first
          overallSection.appendChild(overallChartWrapper);

          // Create the spectacular overall investment rating chart
          console.log(`[SpectacularChart] Creating overall investment rating chart with rating: ${overallRating}%`);
          setTimeout(() => {
            createSpectacularOverallChart("overall-rating-chart-container", overallRating);
          }, 100);

          // Create premium rating details
          const overallDetails = document.createElement("div");
          overallDetails.className = "overall-rating-details";

          const categoryLabel = document.createElement("div");
          categoryLabel.className = "rating-category";
          categoryLabel.textContent = "Overall Investment Rating";

          const scoreText = document.createElement("div");
          scoreText.className = "rating-score-text";
          scoreText.innerHTML = `<span>${Math.round(overallRating)}</span><span class="percent-sign">%</span>`;

          const ratingDesc = document.createElement("div");
          ratingDesc.className = "rating-description";
          ratingDesc.textContent = overallRating >= 85 ? "🌟 Exceptional Investment" :
                                 overallRating >= 75 ? "⭐ Strong Buy Opportunity" :
                                 overallRating >= 65 ? "📈 Good Investment" :
                                 overallRating >= 50 ? "⚖️ Hold Position" : "⚠️ High Risk Investment";

          // Add metric tiles for interactive details
          const metricTiles = document.createElement("div");
          metricTiles.className = "metric-tiles-container";

          const metrics = [
            { label: "Score", value: `${overallRating}%`, color: "#00d4ff" },
            { label: "Grade", value: overallRating >= 85 ? "A+" : overallRating >= 75 ? "A" : overallRating >= 65 ? "B+" : overallRating >= 50 ? "B" : "C", color: "#ff6b35" },
            { label: "Risk", value: overallRating >= 75 ? "Low" : overallRating >= 50 ? "Med" : "High", color: "#00ff88" }
          ];

          metrics.forEach(metric => {
            const tile = document.createElement("div");
            tile.className = "metric-tile";
            tile.innerHTML = `
              <div class="metric-tile-label">${metric.label}</div>
              <div class="metric-tile-value" style="color: ${metric.color}">${metric.value}</div>
            `;
            metricTiles.appendChild(tile);
          });

          overallDetails.append(categoryLabel, scoreText, ratingDesc, metricTiles);
          overallSection.appendChild(overallDetails);

          // 💎 CREATE PREMIUM COMPONENT RATING GRID 💎
          const componentsGrid = document.createElement("div");
          componentsGrid.className = "component-ratings-grid";

          const components = [
            { name: "Business Quality", value: ratings.business, icon: "🏰", color: "#00d4ff" },
            { name: "Financial Strength", value: ratings.financial, icon: "💪", color: "#ff6b35" },
            { name: "Valuation", value: ratings.valuation, icon: "💰", color: "#00ff88" },
            { name: "Management", value: ratings.management, icon: "👥", color: "#7928ca" }
          ];

          components.forEach((comp, index) => {
            if (comp.value > 0) {
              const componentItem = document.createElement("div");
              componentItem.className = "component-rating-item";
              componentItem.style.setProperty('--comp-color', comp.color);
              componentItem.style.setProperty('--comp-color-transparent', comp.color + '40');

              // Add staggered animation delay
              componentItem.style.animationDelay = `${index * 0.2}s`;

              const header = document.createElement("div");
              header.className = "component-header";
              header.innerHTML = `
                <div style="font-size: 1.5rem; margin-bottom: 5px;">${comp.icon}</div>
                <div class="component-name">${comp.name}</div>
              `;

              const chartWrapper = document.createElement("div");
              chartWrapper.className = "component-chart-wrapper";

              // Create premium donut chart
              renderPremiumDonutChart(comp.value, chartWrapper, comp.color);

              const scoreDisplay = document.createElement("div");
              scoreDisplay.className = "component-score-display";
              scoreDisplay.innerHTML = `<span>${Math.round(comp.value)}</span><span class="percent-sign">%</span>`;

              // Add hover overlay for premium effect
              const hoverOverlay = document.createElement("div");
              hoverOverlay.className = "hover-overlay";

              componentItem.append(header, chartWrapper, scoreDisplay, hoverOverlay);
              componentsGrid.appendChild(componentItem);
            }
          });

          // Assemble the premium container
          contentWrapper.append(overallSection, componentsGrid);
          enhancedRatingContainer.append(shimmerOverlay, contentWrapper);

          // Trigger premium animations
          setTimeout(() => {
            enhancedRatingContainer.classList.add("visible");

            // Animate individual elements
            const categoryLabel = overallDetails.querySelector(".rating-category");
            const scoreText = overallDetails.querySelector(".rating-score-text");
            const ratingDesc = overallDetails.querySelector(".rating-description");

            setTimeout(() => categoryLabel.style.opacity = "1", 300);
            setTimeout(() => scoreText.style.opacity = "1", 600);
            setTimeout(() => ratingDesc.style.opacity = "1", 900);

            // Animate component cards
            const componentItems = componentsGrid.querySelectorAll(".component-rating-item");
            componentItems.forEach((item, index) => {
              setTimeout(() => {
                item.style.opacity = "1";
                item.style.transform = "scale(1) translateY(0) rotateX(0deg)";
                item.classList.add("visible");
              }, 1200 + (index * 200));
            });
          }, 100);

        } catch (e) {
          console.error("Error creating premium rating visualization:", e);
        }
      }
    }

    if (messageTypeForClass)
      li.classList.add(`message-type-${messageTypeForClass}`);
    if (tickerForDataset) li.dataset.ticker = tickerForDataset;

    // Create a ratings container for this message if ratings exist
    if (
      ratings &&
      (ratings.overall ||
        ratings.moat ||
        ratings.financial ||
        ratings.valuation ||
        ratings.management)
    ) {
      const ratingContainer = document.createElement("div");
      ratingContainer.classList.add("message-rating-container");
      ratingContainer.style.marginBottom = "10px";
      ratingContainer.style.display = "flex";
      ratingContainer.style.flexWrap = "wrap";
      ratingContainer.style.gap = "10px";
      ratingContainer.style.justifyContent = "space-between";

      // Create a div for text display of ratings
      const ratingTextDiv = document.createElement("div");
      ratingTextDiv.classList.add("rating-text");
      ratingTextDiv.style.flex = "1";
      ratingTextDiv.style.minWidth = "180px";

      let ratingHTML =
        '<div class="rating-header"><strong>Analysis Ratings</strong></div>';

      // Format display for the different types of ratings (including N/A)
      if (ratings.overall) {
        const overallText =
          ratings.overall === "N/A"
            ? "N/A"
            : `${parseFloat(ratings.overall).toFixed(1)}/10 (${Math.round(parseFloat(ratings.overall) * 10)}%)`;
        ratingHTML += `<div><strong>Overall Rating:</strong> ${overallText}</div>`;
      }
      if (ratings.moat) {
        const moatText =
          ratings.moat === "N/A"
            ? "N/A"
            : `${parseFloat(ratings.moat).toFixed(1)}/10 (${Math.round(parseFloat(ratings.moat) * 10)}%)`;
        ratingHTML += `<div><strong>Business Quality/Moat:</strong> ${moatText}</div>`;
      }
      if (ratings.financial) {
        const financialText =
          ratings.financial === "N/A"
            ? "N/A"
            : `${parseFloat(ratings.financial).toFixed(1)}/10 (${Math.round(parseFloat(ratings.financial) * 10)}%)`;
        ratingHTML += `<div><strong>Financial Strength:</strong> ${financialText}</div>`;
      }
      if (ratings.valuation) {
        const valuationText =
          ratings.valuation === "N/A"
            ? "N/A"
            : `${parseFloat(ratings.valuation).toFixed(1)}/10 (${Math.round(parseFloat(ratings.valuation) * 10)}%)`;
        ratingHTML += `<div><strong>Valuation:</strong> ${valuationText}</div>`;
      }
      if (ratings.management) {
        const managementText =
          ratings.management === "N/A"
            ? "N/A"
            : `${parseFloat(ratings.management).toFixed(1)}/10 (${Math.round(parseFloat(ratings.management) * 10)}%)`;
        ratingHTML += `<div><strong>Management:</strong> ${managementText}</div>`;
      }

      ratingTextDiv.innerHTML = ratingHTML;
      ratingContainer.appendChild(ratingTextDiv);

      // Add chart container div
      const chartContainer = document.createElement("div");
      chartContainer.classList.add("rating-charts");
      chartContainer.style.flex = "1";
      chartContainer.style.display = "flex";
      chartContainer.style.flexWrap = "wrap";
      chartContainer.style.gap = "5px";
      chartContainer.style.justifyContent = "center";
      chartContainer.style.alignItems = "center";
      ratingContainer.appendChild(chartContainer);

      // Create direct DOM chart elements for each rating (no iframe)
      function createChartIframe(ratingValue, ratingName) {
        console.log(
          `[createChart] Creating chart for ${ratingName} with value:`,
          ratingValue,
        );
        // Skip if rating is 0 or not present
        if (!ratingValue) {
          console.log(
            `[createChart] Skipping chart for ${ratingName} - no value provided`,
          );
          return null;
        }

        // Create container with label
        const chartItemContainer = document.createElement("div");
        chartItemContainer.classList.add("chart-item");
        chartItemContainer.style.textAlign = "center";
        chartItemContainer.style.margin = "5px";
        chartItemContainer.style.width = "100px";

        // Add label above chart
        const label = document.createElement("div");
        label.classList.add("chart-label");
        label.textContent = ratingName;
        label.style.fontSize = "10px";
        label.style.marginBottom = "3px";
        label.style.fontWeight = "bold";
        chartItemContainer.appendChild(label);

        // Calculate score percentage for the chart
        let score, color;
        if (ratingValue === "N/A") {
          score = "N/A";
          color = "#6c757d"; // Gray for N/A
        } else {
          // Use percentage value directly (0-100)
          score = parseFloat(ratingValue);
          // Ensure it's a valid number and round it
          score = !isNaN(score) ? Math.round(score) : 0;
          color = getRatingColor(score); // Get appropriate color based on score
        }

        console.log(
          `[createChart] ${ratingName} - Calculated score:`,
          score,
          "color:",
          color,
        );

        // Simple colored box approach instead of circular charts
        const scoreBox = document.createElement("div");
        scoreBox.style.width = "80px";
        scoreBox.style.height = "50px";
        scoreBox.style.margin = "0 auto";
        scoreBox.style.display = "flex";
        scoreBox.style.alignItems = "center";
        scoreBox.style.justifyContent = "center";
        scoreBox.style.fontWeight = "bold";
        scoreBox.style.fontSize = "18px";
        scoreBox.style.color = "white";
        scoreBox.style.backgroundColor = color;
        scoreBox.style.borderRadius = "6px";
        scoreBox.style.boxShadow = "0 2px 5px rgba(0,0,0,0.2)";

        if (score === "N/A") {
          scoreBox.textContent = "N/A";
        } else {
          scoreBox.textContent = score + "%";
        }

        chartItemContainer.appendChild(scoreBox);

        return chartItemContainer;
      }

      // Add each chart to the container
      if (ratings.overall) {
        const overallChart = createChartIframe(
          ratings.overall,
          "Overall Rating",
        );
        if (overallChart) chartContainer.appendChild(overallChart);
      }

      if (ratings.moat) {
        const moatChart = createChartIframe(
          ratings.moat,
          "Business Quality/Moat",
        );
        if (moatChart) chartContainer.appendChild(moatChart);
      }

      if (ratings.financial) {
        const financialChart = createChartIframe(
          ratings.financial,
          "Financial Strength",
        );
        if (financialChart) chartContainer.appendChild(financialChart);
      }

      if (ratings.valuation) {
        const valuationChart = createChartIframe(
          ratings.valuation,
          "Valuation",
        );
        if (valuationChart) chartContainer.appendChild(valuationChart);
      }

      if (ratings.management) {
        const managementChart = createChartIframe(
          ratings.management,
          "Management",
        );
        if (managementChart) chartContainer.appendChild(managementChart);
      }

      // Append the entire rating container
      messageBubbleP.appendChild(ratingContainer);
    }

    // Append the text content
    if (textForDisplay) {
      const textDiv = document.createElement("div");
      textDiv.classList.add("message-text-content");
      if (isHTMLContent) {
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = textForDisplay;
        tempDiv.querySelectorAll("script").forEach((script) => script.remove());
        textDiv.innerHTML = tempDiv.innerHTML;
      } else {
        textDiv.textContent = textForDisplay;
      }
      messageBubbleP.appendChild(textDiv);
    }

    if (messageTypeForClass)
      li.classList.add(`message-type-${messageTypeForClass}`);
    if (tickerForDataset) li.dataset.ticker = tickerForDataset;

    li.appendChild(messageBubbleP);
    messagesContainer.appendChild(li);

    // Scroll to the bottom after adding the message
    scrollToBottom();

    // Animate message appearance
    requestAnimationFrame(() => {
      setTimeout(() => {
        li.style.opacity = 1;
        li.style.transform = "translateY(0)";
      }, 10);
    });
  } catch (error) {
    console.error("Error in addMessage:", error);
    // Fallback to simple message display
    const messagesContainer = document.getElementById("chatbot-messages");
    if (messagesContainer) {
      const errorLi = document.createElement("li");
      errorLi.classList.add("message", sender);
      errorLi.textContent =
        typeof messageData === "string"
          ? messageData
          : "Error displaying message";
      messagesContainer.appendChild(errorLi);
      scrollToBottom();
    }
  }
}

function scrollToBottom() {
  if (chatbotMessages) {
    // Use requestAnimationFrame for smoother scrolling after DOM update
    requestAnimationFrame(() => {
      chatbotMessages.scrollTo({
        top: chatbotMessages.scrollHeight,
        behavior: "smooth",
      });
    });
  }
}

// --- Chatbot Specific Functions ---

// --- Initialize Chatbot ---
// Global references to chatbot elements
function initializeChatbot() {
  // Assign elements to the globally declared variables
  chatbotContainer = document.getElementById(CHATBOT_CONTAINER_ID);
  chatbotToggle = document.getElementById(CHATBOT_TOGGLE_ID);
  chatbotWindow = document.getElementById(CHATBOT_WINDOW_ID);
  chatbotClose = document.getElementById(CHATBOT_CLOSE_ID);
  chatbotMessages = document.getElementById(CHATBOT_MESSAGES_ID);
  chatbotInput = document.getElementById(CHATBOT_INPUT_ID);
  chatbotSend = document.getElementById(CHATBOT_SEND_ID);
  typingIndicator = document.getElementById(CHATBOT_TYPING_ID);
  chatbotSettingsBtn = document.getElementById(CHATBOT_SETTINGS_ID);
  settingsModal = document.getElementById("chatbotSettingsModal"); // Assuming this ID for the modal
  settingsForm = document.getElementById("chatbotSettingsForm"); // Assuming this ID for the form
  apiKeyInputContainer = document.getElementById("apiKeyInputContainer");
  modelSelect = document.getElementById("chatbotModelSelect");
  clearKeysBtn = document.getElementById("clearApiKeysBtn");

  // Basic check if essential elements exist
  if (
    !chatbotContainer ||
    !chatbotToggle ||
    !chatbotWindow ||
    !chatbotClose ||
    !chatbotMessages ||
    !chatbotInput ||
    !chatbotSend ||
    !typingIndicator
  ) {
    console.warn("Chatbot elements not found. Chatbot functionality disabled.");
    // Optionally hide the toggle button if the chatbot is fundamentally broken
    if (chatbotToggle) chatbotToggle.style.display = "none";
    return; // Stop initialization
  }

  // --- Event Listeners ---
  chatbotToggle.addEventListener("click", toggleChatWindow);
  chatbotClose.addEventListener("click", closeChatWindow);
  chatbotSend.addEventListener("click", handleSendMessage);
  chatbotInput.addEventListener("keypress", (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault(); // Prevent new line on Enter
      handleSendMessage();
    }
  });

  // Settings Button Listener (if exists)
  if (chatbotSettingsBtn) {
    chatbotSettingsBtn.addEventListener("click", () => {
      if (settingsModal && typeof bootstrap !== "undefined") {
        // Populate form before showing
        populateSettingsForm()
          .then(() => {
            const modalInstance = bootstrap.Modal.getInstance(settingsModal);
            if (modalInstance) modalInstance.show();
          })
          .catch((err) => {
            console.error("Error populating settings form:", err);
            // Show an error message to the user?
          });
      } else {
        console.warn("Settings modal or Bootstrap not found.");
      }
    });
  }

  // Settings Form Listener (if exists)
  if (settingsForm) {
    settingsForm.addEventListener("submit", handleSettingsFormSubmit);
  }

  // Model Select Listener (if exists)
  if (modelSelect) {
    modelSelect.addEventListener("change", (event) => {
      renderApiKeyInput(event.target.value); // Show/hide API key input based on selection
    });
  }

  // Clear Keys Button Listener (if exists)
  if (clearKeysBtn) {
    clearKeysBtn.addEventListener("click", handleClearKeysClick);
  }

  // --- Initial State ---
  hideTypingIndicator();
  setInputState(false); // Ensure input is enabled initially

  // Fetch initial state/greeting from backend
  // Fetch initial state/greeting from backend
  fetch("/api/chatbot/init")
    .then((response) => response.json())
    .then((data) => {
      // 'data' here is { message, is_html, action }
      if (data.message) {
        // For init messages, they are simple strings, so the old way is fine
        // or you can adapt addMessage to always take an object.
        // To keep it simple for now, if it's an init message (not analysis_response):
        addMessage("bot", {
          message: data.message,
          is_html: data.is_html || false,
        });
      }
      currentBotAction = data.action || null;
      updateInputPlaceholder();
      console.log("Chatbot initialized. Current action:", currentBotAction);
    })
    .catch((error) => {
      console.error("Error initializing chatbot:", error);
      addMessage("bot", {
        message:
          "Sorry, I couldn't initialize properly. Please try refreshing.",
        is_html: false,
      });
      setInputState(false, "Initialization failed. Try refreshing."); // Keep input enabled, show error in placeholder
    });

  // --- Helper Functions (Specific to Chatbot) ---

  function updateInputPlaceholder() {
    if (!chatbotInput) return;
    switch (currentBotAction) {
      case "choose_model":
        // Pass specific placeholder when disabling
        setInputState(true, "Please select an option above.");
        break;
      case "provide_key":
        // Pass specific placeholder when disabling
        setInputState(true, "Please enter API key in settings.");
        break;
      case "awaiting_query":
        chatbotInput.placeholder =
          "Ask about stocks, analysis, or portfolio...";
        setInputState(false); // Enable input for general queries
        break;
      default:
        chatbotInput.placeholder = "Type your message...";
        setInputState(false); // Default to enabled
    }
  }

  // --- Helper Functions (Core Chat) ---
  function openChatWindow() {
    if (chatbotWindow) chatbotWindow.classList.remove("hidden");
    // CSS handles toggle button visibility
    if (chatbotInput) chatbotInput.focus();
    scrollToBottom();
  }

  function closeChatWindow() {
    if (chatbotWindow) chatbotWindow.classList.add("hidden");
    if (chatbotContainer) chatbotContainer.classList.remove("settings-open"); // Close settings if chat closes
    // CSS handles toggle button visibility
  }
  function toggleChatWindow() {
    // Use chatbotContainer class to check settings state
    const isSettingsOpen = chatbotContainer.classList.contains("settings-open");
    if (isSettingsOpen) {
      chatbotContainer.classList.remove("settings-open"); // Close settings first
    } else if (chatbotWindow && chatbotWindow.classList.contains("hidden")) {
      openChatWindow();
    } else {
      closeChatWindow();
    }
  }

  // --- Replace the existing addMessage function with this version ---
  // --- Replace the existing addMessage function with this version ---
  // --- Replace the existing addMessage function with this enhanced version ---
  // Helper function (ensure it exists or add it)
  // function safeJsFloat(value, defaultValue = null) { // Already defined globally
  //     if (value === null || value === undefined || value === '') return defaultValue;
  //     const num = Number(value);
  //     return isNaN(num) ? defaultValue : num;
  // }

  function showTypingIndicator() {
    if (typingIndicator) {
      typingIndicator.classList.remove("hidden");
      isBotTyping = true;
      scrollToBottom();
    }
  }

  function hideTypingIndicator() {
    if (typingIndicator) {
      typingIndicator.classList.add("hidden");
      isBotTyping = false;
    }
  }

  function setInputState(disabled, forcePlaceholder = null) {
    if (chatbotInput) {
      chatbotInput.disabled = disabled;
      chatbotInput.placeholder =
        forcePlaceholder !== null
          ? forcePlaceholder
          : disabled
            ? "Waiting for response..."
            : "Type your message...";
    }
    if (chatbotSend) {
      chatbotSend.disabled = disabled;
    }
  }

  // --- Dynamic Button Click Handler ---
  async function handleDynamicButtonClick(event) {
    const button = event.target;
    const action = button.dataset.action;
    const value = button.dataset.value;
    const endpoint = button.dataset.endpoint; // Get endpoint from button

    if (!action || !endpoint) {
      console.error("Button missing action or endpoint data attribute");
      return;
    }

    // Disable buttons in the same container after click
    const buttonContainer = button.closest(".dynamic-buttons");
    if (buttonContainer) {
      buttonContainer
        .querySelectorAll("button")
        .forEach((btn) => (btn.disabled = true));
    }

    // Add user message visually (optional, but good UX)
    // addMessage('user', `Selected: ${button.textContent}`);

    // Send choice to backend
    showTypingIndicator();
    setInputState(true); // Disable input while processing

    try {
      await sendChoiceToBackend(endpoint, { action: action, value: value });
    } catch (error) {
      console.error(
        `Error sending dynamic choice (${action}) to ${endpoint}:`,
        error,
      );
      addMessage(
        "bot",
        `Sorry, there was an error processing your choice: ${error.message}`,
        false,
      );
      hideTypingIndicator();
      setInputState(false); // Re-enable input on error
      // Re-enable buttons on error? Maybe not, to prevent resubmission.
    }
  }

  // --- Send Message Handler ---
  async function handleSendMessage() {
    const messageText = chatbotInput.value.trim();
    if (messageText === "" || isBotTyping) {
      return; // Don't send empty messages or while bot is typing
    }

    console.log("Sending message:", messageText); // Debug log

    addMessage("user", messageText);
    chatbotInput.value = ""; // Clear input field
    showTypingIndicator();
    setInputState(true); // Disable input while waiting for response

    try {
      const response = await fetch("/api/chatbot", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({ message: messageText }),
      });

      if (!response.ok) {
        // Try to parse error from JSON response
        let errorMsg = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMsg = errorData.error || errorData.message || errorMsg;
        } catch (e) {
          console.warn("Could not parse error response as JSON");
        }
        throw new Error(errorMsg);
      }

      const data = await response.json();
      console.log("Response received:", data); // Debug log

      hideTypingIndicator();

      // Check if this is a financial data response
      if (data.type === "financial_data") {
        // Handle financial data response
        handleFinancialResponse(data);
      } else {
        // Handle regular response - pass the whole data object
        addMessage("bot", data);
      }

      currentBotAction = data.action_required || data.action || null; // Update required action
      updateInputPlaceholder(); // Update placeholder based on action
      setInputState(false); // Re-enable input after successful response
    } catch (error) {
      console.error("Error sending message:", error);
      hideTypingIndicator();
      addMessage(
        "bot",
        `Sorry, an error occurred: ${error.message}. Please try again.`,
      );
      setInputState(false); // Re-enable input on error
    }
  }

  // --- Send Dynamic Choice to Backend ---
  async function sendChoiceToBackend(url, payload) {
    setInputState(true, "Waiting for assistant...");
    showTypingIndicator();

    try {
      console.log("Sending choice to backend:", { url, payload }); // Debug log

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        let errorMsg = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMsg = errorData.error || errorData.message || errorMsg;
        } catch (e) {
          console.warn("Could not parse error response as JSON");
        }
        throw new Error(errorMsg);
      }

      const data = await response.json();
      console.log("Choice response received:", data); // Debug log

      if (data.message || data.messageType === "analysis_response") {
        const delay = 200 + Math.random() * 400;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      hideTypingIndicator();

      // Pass the entire data object to addMessage for proper handling
      if (data.message || data.messageType === "analysis_response") {
        addMessage("bot", data);
      } else {
        console.log(
          "Backend responded without a message to display (e.g., state update only).",
        );
      }

      currentBotAction = data.action_required || null;

      if (
        currentBotAction === "choose_model" ||
        currentBotAction === "provide_key" ||
        currentBotAction === "key_invalid"
      ) {
        setInputState(true, "Please respond using the buttons above.");
        console.log("Bot requires action:", currentBotAction);
        isBotTyping = false;
      } else if (currentBotAction === "key_valid") {
        setInputState(false);
        isBotTyping = false;
        console.log("API Key valid, input enabled.");
      } else {
        setInputState(false);
        isBotTyping = false;
      }
    } catch (error) {
      console.error(`Chatbot Error (${url}):`, error);
      hideTypingIndicator();
      addMessage("bot", `😥 Sorry, an error occurred: ${error.message}`);
      setInputState(false);
      isBotTyping = false;
      currentBotAction = null;
    }
  }

  // Check if D3.js is available for visualizations
  if (!window.d3) {
    console.warn("D3.js not loaded - charts will be disabled");
  }

  // Check if enhanced rating charts are available
  if (typeof renderAnalysisVisualization !== "function") {
    console.warn("Enhanced rating visualization functions not available");
  }

  console.log("Chatbot UI initialized.");

  // Initialize financial features if function exists
  if (typeof initializeFinancialFeatures === "function") {
    initializeFinancialFeatures();
  } else {
    console.log(
      "Financial features initialization skipped - function not available yet",
    );
  }
} // --- End initializeChatbot ---

// --- Chatbot Settings Functions ---

async function populateSettingsForm() {
  if (!settingsForm || !modelSelect) {
    console.warn("Settings form or model select element not found.");
    return;
  }

  // Show a simple loading state
  modelSelect.disabled = true;
  if (apiKeyInputContainer)
    apiKeyInputContainer.innerHTML = "<p>Loading settings...</p>";

  try {
    const response = await fetch("/api/chatbot/settings");
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    const settings = await response.json();
    console.log("Fetched settings:", settings);

    // Populate Model Select
    modelSelect.innerHTML = ""; // Clear existing options
    if (settings.available_models && settings.available_models.length > 0) {
      settings.available_models.forEach((model) => {
        const option = document.createElement("option");
        option.value = model.key; // Use the key (e.g., 'openai', 'groq') as the value
        option.textContent = model.name; // Display the friendly name
        option.selected = model.key === settings.current_model;
        modelSelect.appendChild(option);
      });
    } else {
      const option = document.createElement("option");
      option.textContent = "No models available";
      option.disabled = true;
      modelSelect.appendChild(option);
    }

    modelSelect.disabled = false;

    // Render API key input based on the *currently selected* model
    renderApiKeyInput(settings.current_model);
    // Pre-fill API key if it exists (indicated by has_key)
    const apiKeyInput = document.getElementById("apiKeyInput");
    if (apiKeyInput && settings.has_key) {
      apiKeyInput.value = "********"; // Show placeholder, not the actual key
      apiKeyInput.dataset.hasExistingKey = "true"; // Mark that a key exists
    } else if (apiKeyInput) {
      apiKeyInput.dataset.hasExistingKey = "false";
    }
  } catch (error) {
    console.error("Error fetching chatbot settings:", error);
    if (apiKeyInputContainer)
      apiKeyInputContainer.innerHTML = `<p class="text-danger">Error loading settings: ${error.message}</p>`;
    // Optionally disable the form
    settingsForm
      .querySelectorAll("input, select, button")
      .forEach((el) => (el.disabled = true));
  }
}

function renderApiKeyInput(selectedModelKey) {
  if (!apiKeyInputContainer) return;

  // Define which models require an API key input field
  const modelsRequiringKey = ["openai", "google", "groq"]; // Add other model keys as needed

  if (modelsRequiringKey.includes(selectedModelKey)) {
    // Check if an input already exists and if it has a stored key
    const existingInput = document.getElementById("apiKeyInput");
    const hasExistingKey =
      existingInput && existingInput.dataset.hasExistingKey === "true";

    apiKeyInputContainer.innerHTML = `
            <label for="apiKeyInput" class="form-label">API Key for ${selectedModelKey}</label>
            <input type="password" class="form-control form-control-sm" id="apiKeyInput" name="api_key"
                   placeholder="${hasExistingKey ? "Key saved - Enter new key to replace" : "Enter your API key"}"
                   aria-describedby="apiKeyHelp"
                   data-has-existing-key="${hasExistingKey}">
             <div id="apiKeyHelp" class="form-text form-text-sm">
                ${hasExistingKey ? "Leave blank to keep the currently saved key." : "Your API key is stored securely."}
             </div>
        `;
    // Re-attach dataset info if input was recreated
    const newInput = document.getElementById("apiKeyInput");
    if (newInput) newInput.dataset.hasExistingKey = String(hasExistingKey);
  } else {
    apiKeyInputContainer.innerHTML = ""; // Clear the input field area if model doesn't need a key
  }
}

async function handleSettingsFormSubmit(event) {
  event.preventDefault();
  if (!settingsForm) return;

  const formData = new FormData(settingsForm);
  const selectedModel = formData.get("model");
  let apiKey = formData.get("api_key"); // May be null or empty string
  const feedbackElement = settingsForm.querySelector(".form-feedback");
  const submitButton = settingsForm.querySelector('button[type="submit"]');

  // Clear previous feedback and disable button
  if (feedbackElement) feedbackElement.textContent = "";
  feedbackElement.style.display = "none";
  if (submitButton) submitButton.disabled = true;
  submitButton.textContent = "Saving...";

  // Determine if we should send the API key
  // Send the key ONLY if the input field exists AND it's not empty
  const apiKeyInput = document.getElementById("apiKeyInput");
  let sendApiKey = false;
  if (apiKeyInput && apiKey && apiKey.trim() !== "" && apiKey !== "********") {
    sendApiKey = true;
  } else {
    apiKey = null; // Ensure null is sent if not providing a new key
  }

  const payload = {
    model: selectedModel,
    // Only include api_key in the payload if sendApiKey is true
    ...(sendApiKey && { api_key: apiKey }),
  };

  console.log("Submitting settings:", {
    model: payload.model,
    apiKeyProvided: sendApiKey,
  });

  try {
    const response = await fetch("/api/chatbot/update_settings", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });

    const result = await response.json();

    if (response.ok) {
      console.log("Settings updated successfully:", result);
      if (feedbackElement) {
        feedbackElement.textContent = "Settings saved successfully!";
        feedbackElement.className = "form-feedback text-success mt-2"; // Use text-success
        feedbackElement.style.display = "block";
      }
      // Optionally close modal after a short delay
      setTimeout(() => {
        if (settingsModal && typeof bootstrap !== "undefined") {
          const modalInstance = bootstrap.Modal.getInstance(settingsModal);
          if (modalInstance) modalInstance.hide();
        }
        // Re-initialize chatbot state if model changed or key was added/removed
        fetch("/api/chatbot/init") // Re-fetch initial state
          .then((res) => res.json())
          .then((data) => {
            currentBotAction = data.action || null;
            updateInputPlaceholder();
            console.log(
              "Chatbot state re-initialized after settings change. Action:",
              currentBotAction,
            );
            // Optionally add a message confirming the change
            // addMessage('bot', `Chatbot model set to ${selectedModel}.`);
          })
          .catch((err) =>
            console.error(
              "Error re-initializing chatbot after settings update:",
              err,
            ),
          );
      }, 1500); // Close after 1.5 seconds

      // Refresh the form display to show placeholder if key was saved
      await populateSettingsForm();
    } else {
      console.error("Error updating settings:", result);
      if (feedbackElement) {
        feedbackElement.textContent = `Error: ${result.error || "Unknown error"}`;
        feedbackElement.className = "form-feedback text-danger mt-2"; // Use text-danger
        feedbackElement.style.display = "block";
      }
    }
  } catch (error) {
    console.error("Network error saving settings:", error);
    if (feedbackElement) {
      feedbackElement.textContent = `Network Error: ${error.message}`;
      feedbackElement.className = "form-feedback text-danger mt-2";
      feedbackElement.style.display = "block";
    }
  } finally {
    // Re-enable button
    if (submitButton) {
      submitButton.disabled = false;
      submitButton.textContent = "Save Settings";
    }
  }
}

async function handleClearKeysClick() {
  if (!clearKeysBtn) return;
  if (
    !confirm(
      "Are you sure you want to remove all saved API keys? This action cannot be undone.",
    )
  ) {
    return;
  }

  const feedbackElement = settingsForm
    ? settingsForm.querySelector(".form-feedback")
    : null; // Find feedback element relative to form
  clearKeysBtn.disabled = true;
  clearKeysBtn.textContent = "Clearing...";
  if (feedbackElement) feedbackElement.textContent = "";
  feedbackElement.style.display = "none";

  try {
    const response = await fetch("/api/chatbot/clear_keys", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      // No body needed, or send an empty object: body: JSON.stringify({})
    });

    const result = await response.json();

    if (response.ok) {
      console.log("API keys cleared successfully:", result);
      if (feedbackElement) {
        feedbackElement.textContent = "All API keys cleared successfully!";
        feedbackElement.className = "form-feedback text-success mt-2"; // Use text-success
        feedbackElement.style.display = "block";
      }
      // Refresh the form to reflect the cleared keys
      await populateSettingsForm();
    } else {
      console.error("Error clearing API keys:", result);
      if (feedbackElement) {
        feedbackElement.textContent = `Error: ${result.error || "Unknown error"}`;
        feedbackElement.className = "form-feedback text-danger mt-2"; // Use text-danger
        feedbackElement.style.display = "block";
      }
    }
  } catch (error) {
    console.error("Network error clearing API keys:", error);
    if (feedbackElement) {
      feedbackElement.textContent = `Network Error: ${error.message}`;
      feedbackElement.className = "form-feedback text-danger mt-2";
      feedbackElement.style.display = "block";
    }
  } finally {
    clearKeysBtn.disabled = false;
    clearKeysBtn.textContent = "Clear All Saved API Keys";
  }
}

// --- General Initialization ---
document.addEventListener("DOMContentLoaded", () => {
  console.log("DOM fully loaded and parsed.");

  // Initialize theme based on localStorage or default
  const savedTheme = localStorage.getItem("theme") || "light";
  setTheme(savedTheme);

  // Setup theme toggle button listener
  const themeToggleSidebar = document.getElementById(
    "themeToggleCheckboxSidebar",
  );
  if (themeToggleSidebar) {
    themeToggleSidebar.addEventListener("change", toggleTheme);
  } else {
    console.warn("Sidebar theme toggle checkbox not found.");
  }

  // Set active sidebar link based on current page
  setActiveSidebarLink();

  // Initialize particles background if the element exists
  if (document.getElementById("particles-js")) {
    initParticles();
  }

  // Check and potentially show/hide the stock ticker banner
  checkAndSetTickerVisibility();

  // Initialize chatbot if its container exists
  if (document.getElementById(CHATBOT_CONTAINER_ID)) {
    initializeChatbot();
  }
  // Temp call for donut chart rendering (for testing)

  // --- Portfolio Page Initializations ---
  if (document.body.classList.contains("portfolio-page")) {
    console.log("Portfolio page detected. Initializing portfolio components.");
    // Add listeners for chart range buttons
    document.querySelectorAll(".chart-range-btn").forEach((button) => {
      button.addEventListener("click", () => updateChartRange(button));
      // Set initial active button
      if (button.dataset.range === currentChartRange) {
        button.classList.add("active");
      }
    });

    // Initial fetch for performance chart (with entry animation)
    fetchChartData(currentChartRange, true);

    // Fetch data for diversification charts
    fetchAndRenderDiversificationCharts();

    // Setup live price updates
    startAutoRefresh();

    // Setup edit modal functionality
    setupModal();

    // Initial calculation of daily change (might run again after first price fetch)
    calculateAndDisplayPortfolioDailyChange();
  }

  // --- DCF Page Initializations ---
  if (document.body.classList.contains("dcf-page")) {
    console.log("DCF page detected.");
    // Add any DCF-specific JS initializations here
    // e.g., form validation, input masks, dynamic field visibility
  }
}); // End DOMContentLoaded

// --- Helper for Plotly Pies (Example) ---
function renderSimplePlotlyPie(id, data, title) {
  const chartDiv = document.getElementById(id);
  if (
    !chartDiv ||
    typeof Plotly === "undefined" ||
    !data ||
    !data.labels ||
    !data.values
  ) {
    console.warn(
      `Cannot render Plotly pie for #${id}: Missing element, Plotly library, or valid data.`,
    );
    if (chartDiv)
      chartDiv.innerHTML = '<p class="chart-info">Chart data unavailable.</p>';
    return;
  }

  const theme = document.body.getAttribute("data-theme") || "light";
  const themeColors = getParticleColors(theme); // Get basic theme colors

  const layout = {
    title: {
      text: title,
      font: { color: theme === "dark" ? "#FFFFFF" : "#111111" },
    },
    showlegend: true,
    legend: {
      bgcolor:
        theme === "dark" ? "rgba(40, 40, 60, 0.7)" : "rgba(255, 255, 255, 0.7)",
      bordercolor:
        theme === "dark" ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.2)",
      font: { color: theme === "dark" ? "#EAEAEA" : "#333333" },
    },
    paper_bgcolor:
      theme === "dark" ? "rgba(18, 18, 31, 0.8)" : "rgba(248, 249, 252, 0.8)",
    plot_bgcolor:
      theme === "dark" ? "rgba(30, 30, 45, 0.8)" : "rgba(233, 236, 239, 0.8)",
    margin: { l: 30, r: 30, t: 50, b: 30 }, // Adjust margins
    height: 300, // Fixed height example
  };

  const trace = [
    {
      labels: data.labels,
      values: data.values,
      type: "pie",
      hole: 0.4, // Make it a donut
      hoverinfo: "label+percent",
      textinfo: "none", // Hide text labels on slices
      marker: {
        // colors: // Optional: Provide specific colors if needed
        line: { color: theme === "dark" ? "#333" : "#FFF", width: 1 }, // Slice border
      },
    },
  ];

  Plotly.newPlot(id, trace, layout, { responsive: true, displaylogo: false });
}

/**
 * Helper function to safely parse a string to a float, returning a default if NaN.
 * @param {any} value - The value to parse.
 * @param {number} defaultValue - The value to return if parsing fails or input is invalid.
 * @returns {number} The parsed float or the default value.
 */
function safeJsFloat(value, defaultValue = 0) {
  const num = parseFloat(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * Helper function to get the value of a CSS custom property (variable).
 * Example: getCssVariable('--bs-primary')
 * @param {string} varName - The name of the CSS variable (e.g., '--my-color').
 * @returns {string} The value of the CSS variable, or an empty string if not found.
 */
function getCssVariable(varName) {
  // Ensure this function is available or implement it if not already in your script.js
  // For example, if it's not already there:
  if (typeof window !== "undefined" && typeof getComputedStyle === "function") {
    return getComputedStyle(document.documentElement)
      .getPropertyValue(varName)
      .trim();
  }
  return ""; // Fallback
}

/**
 * Renders a D3-based SVG score gauge/progress circle.
 * @param {string} targetElementId - The ID of the container div to render the SVG into.
 * @param {object} analysisData - Object containing { score: number (0-100), color: string (hex, rgb, etc.), risk_text?: string }.
 */
function renderScoreGaugeSVG(targetElementId, analysisData) {
  console.log(
    `[renderScoreGaugeSVG] Called for #${targetElementId}. Raw analysisData:`,
    JSON.parse(JSON.stringify(analysisData || {})),
  );

  const container = d3.select(`#${targetElementId}`);
  if (container.empty()) {
    console.error(
      `[renderScoreGaugeSVG] Target element #${targetElementId} not found.`,
    );
    // Optionally, you could try to create the element if it's missing,
    // but it's better if `addMessage` ensures it exists.
    return;
  }
  container.style("border", "2px solid red"); // TEMPORARY TEST
  container.style("min-width", "80px"); // TEMPORARY TEST
  container.style("min-height", "80px"); // TEMPORARY TEST
  container.html(""); // Clear previous content using D3 to prevent multiple charts

  // --- Validate and Prepare Data ---
  // Ensure score is a number between 0 and 100
  const score = Math.max(0, Math.min(100, safeJsFloat(analysisData.score, 0)));

  // Use the color passed from backend; fallback to a default green if invalid or missing
  const scoreColor =
    analysisData.color && CSS.supports("color", analysisData.color)
      ? analysisData.color
      : "#198754"; // Default green (Bootstrap success)

  // In static/script.js
  // Ensure safeJsFloat and getCssVariable are defined globally or within this scope

  function renderScoreGaugeSVG(targetElementId, analysisData) {
    console.log(
      `[renderScoreGaugeSVG] Called for #${targetElementId}. Raw analysisData:`,
      JSON.parse(JSON.stringify(analysisData || {})),
    );

    if (typeof d3 === "undefined") {
      console.error("[renderScoreGaugeSVG] D3.js library is not loaded.");
      const el = document.getElementById(targetElementId);
      if (el)
        el.innerHTML =
          "<small style='color:red; font-weight:bold;'>D3 Missing!</small>";
      return;
    }

    const container = d3.select(`#${targetElementId}`);
    if (container.empty()) {
      console.error(
        `[renderScoreGaugeSVG] Target element #${targetElementId} not found by D3.`,
      );
      // Attempt to find it with vanilla JS for sanity check
      const vanillaEl = document.getElementById(targetElementId);
      if (vanillaEl) {
        console.warn(
          `[renderScoreGaugeSVG] Vanilla JS found #${targetElementId}, but D3 did not. Check D3 selector or timing.`,
        );
        vanillaEl.innerHTML =
          "<small style='color:orange; font-weight:bold;'>D3 Target Error</small>";
      } else {
        console.error(
          `[renderScoreGaugeSVG] Vanilla JS also could not find #${targetElementId}.`,
        );
      }
      return;
    }

    // --- TEMPORARY VISUAL DEBUG ---
    container.style("border", "3px dashed limegreen");
    container.style("min-width", "80px"); // Ensure it has some space
    container.style("min-height", "80px");
    // --- END TEMPORARY ---

    container.html(""); // Clear previous content

    const score = Math.max(
      0,
      Math.min(100, safeJsFloat(analysisData.score, 0)),
    );
    const scoreColor =
      analysisData.color && CSS.supports("color", analysisData.color)
        ? analysisData.color
        : "#198754";
    const neutralColor = getCssVariable("--chatbot-input-bg") || "#e9ecef";

    console.log(
      `[renderScoreGaugeSVG] Using - Score: ${score}, Color: ${scoreColor}, Neutral: ${neutralColor}`,
    );

    const size = 80;
    const strokeWidth = 8; // Matched JS
    const radius = size / 2 - strokeWidth / 2;
    const circumference = 2 * Math.PI * radius;
    const offset = circumference * (1 - score / 100);

    const svg = container
      .append("svg")
      .attr("width", size)
      .attr("height", size)
      .attr("viewBox", `0 0 ${size} ${size}`)
      .classed("score-gauge-svg", true);

    const g = svg
      .append("g")
      .attr("transform", `translate(${size / 2},${size / 2})`);

    g.append("circle")
      .attr("r", radius)
      .attr("fill", "none")
      .attr("stroke", neutralColor)
      .attr("stroke-width", strokeWidth)
      .classed("gauge-background", true);

    const foreground = g
      .append("circle")
      .attr("r", radius)
      .attr("fill", "none")
      .attr("stroke", scoreColor)
      .attr("stroke-width", strokeWidth)
      .attr("stroke-dasharray", circumference)
      .attr("stroke-dashoffset", circumference)
      .attr("stroke-linecap", "round")
      .attr("transform", `rotate(-90)`)
      .classed("gauge-foreground", true);

    const textGroup = g
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle") // For better vertical centering
      .classed("gauge-center-text", true);

    // Score Tspan
    const scoreTspan = textGroup
      .append("tspan")
      .classed("gauge-score", true) // Class from your CSS
      .style("font-size", "18px") // From your CSS
      .style("font-weight", "600") // From your CSS
      .style("fill", scoreColor)
      .text("0"); // Initial text for animation

    // Percent Tspan
    textGroup
      .append("tspan")
      .classed("gauge-percent-sign", true) // Class from your CSS
      .style("font-size", "10px") // From your CSS
      .style("fill", scoreColor)
      .attr("dx", "1px") // From your CSS
      .attr("dy", "-0.3em") // Adjust for vertical alignment with score
      .text("%");

    // Animation for foreground circle
    foreground
      .transition()
      .duration(1000) // Adjust duration as needed
      .ease(d3.easeCubicOut)
      .attr("stroke-dashoffset", offset);

    // Animation for score text count-up
    scoreTspan
      .transition()
      .duration(1000) // Match circle animation duration
      .tween("text", function () {
        const that = d3.select(this);
        const i = d3.interpolateNumber(safeJsFloat(that.text(), 0), score);
        return function (t) {
          that.text(Math.round(i(t)));
        };
      });

    console.log(
      `[renderScoreGaugeSVG] Successfully rendered chart for #${targetElementId}. Score: ${score}, Offset: ${offset}`,
    );
  }

  // D3.js Gauge Chart for Cash Flow to Net Income Ratio
  function createCFtoNetIncomeGauge(containerId, value, title) {
    // Clear previous chart if it exists
    const container = document.getElementById(containerId);
    if (!container) return;
    container.innerHTML = "";

    // Configuration
    const width = container.clientWidth || 400;
    const height = 350;
    const margin = { top: 50, right: 50, bottom: 100, left: 50 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;
    const radius = Math.min(chartWidth, chartHeight) / 2;

    // Gauge specific configurations
    const percentage = value * 100;
    const innerRadius = radius * 0.7;
    const outerRadius = radius * 0.9;

    // Color scale and thresholds - using semantic colors for financial health
    const thresholds = [0, 50, 80, 120];
    const colors = ["#d62728", "#ff7f0e", "#2ca02c", "#1f77b4"]; // red, orange, green, blue

    // Create SVG
    const svg = d3
      .select(`#${containerId}`)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${width / 2}, ${height / 2 - 20})`);

    // Add title with styling - moved further up
    svg
      .append("text")
      .attr("class", "chart-title")
      .attr("x", 0)
      .attr("y", -radius - 40) // Moved further up from -20 to -40
      .attr("text-anchor", "middle")
      .style("font-size", "18px")
      .style("font-weight", "600")
      .text(title);

    // Create circular background for gauge
    const backgroundArc = d3
      .arc()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius)
      .startAngle(-Math.PI / 2)
      .endAngle(Math.PI / 2);

    svg
      .append("path")
      .attr("class", "gauge-background")
      .attr("d", backgroundArc)
      .style("fill", "#e6e6e6");

    // Create colored arcs for different thresholds
    const arcGenerator = d3
      .arc()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius);

    // Scale to convert values to angles
    const scale = d3
      .scaleLinear()
      .domain([0, 100])
      .range([-Math.PI / 2, Math.PI / 2]);

    // Create arcs for each threshold segment
    for (let i = 0; i < thresholds.length - 1; i++) {
      const startAngle = scale(thresholds[i]);
      const endAngle = scale(thresholds[i + 1]);

      svg
        .append("path")
        .attr(
          "d",
          arcGenerator({
            innerRadius: innerRadius,
            outerRadius: outerRadius,
            startAngle: startAngle,
            endAngle: endAngle,
          }),
        )
        .style("fill", colors[i]);
    }

    // Create needle
    const needleLength = radius * 0.8;
    const needleRadius = radius * 0.04;
    const needleAngle = scale(Math.min(Math.max(percentage, 0), 100));

    // Needle container
    const needle = svg
      .append("g")
      .attr("class", "needle")
      .style("transform-origin", "center bottom")
      .style(
        "transform",
        `rotate(${(needleAngle + Math.PI / 2) * (180 / Math.PI)}deg)`,
      );

    // Add needle triangle
    needle
      .append("path")
      .attr(
        "d",
        `M ${-needleRadius} 0 L ${needleRadius} 0 L 0 ${-needleLength} Z`,
      )
      .style("fill", "#464646");

    // Add center circle
    svg
      .append("circle")
      .attr("cx", 0)
      .attr("cy", 0)
      .attr("r", needleRadius * 1.5)
      .style("fill", "#464646")
      .style("stroke", "#e6e6e6")
      .style("stroke-width", "2px");

    // Add percentage text
    svg
      .append("text")
      .attr("x", 0)
      .attr("y", radius / 2.5)
      .attr("text-anchor", "middle")
      .style("font-size", "36px")
      .style("font-weight", "bold")
      .style("fill", getColorForValue(percentage, thresholds, colors))
      .text(`${Math.round(percentage)}%`);

    // Add "High Quality Earnings" text
    svg
      .append("text")
      .attr("x", 0)
      .attr("y", radius / 1.3)
      .attr("text-anchor", "middle")
      .style("font-size", "18px")
      .style("font-weight", "600")
      .text("High Quality Earnings");

    // Add gauge labels at the bottom
    const labelData = [
      { value: 0, text: "0%" },
      { value: 50, text: "50%" },
      { value: 100, text: "100%" },
    ];

    labelData.forEach((d) => {
      const angle = scale(d.value);
      const x = (radius + 20) * Math.cos(angle);
      const y = (radius + 20) * Math.sin(angle);

      svg
        .append("text")
        .attr("x", x)
        .attr("y", y + 35) // Position below the gauge
        .attr("text-anchor", "middle")
        .style("font-size", "12px")
        .style("font-weight", "500")
        .text(d.text);
    });

    // Helper function to determine color based on value
    function getColorForValue(value, thresholds, colors) {
      for (let i = 0; i < thresholds.length - 1; i++) {
        if (value >= thresholds[i] && value < thresholds[i + 1]) {
          return colors[i];
        }
      }
      return colors[colors.length - 1];
    }

    // Add explanatory hover effect - invisible overlay for the entire chart
    svg
      .append("circle")
      .attr("cx", 0)
      .attr("cy", 0)
      .attr("r", radius * 1.2) // Larger than the gauge to ensure easy hovering
      .style("fill", "transparent") // Invisible
      .style("cursor", "help") // Show help cursor on hover
      .on("mouseover", function (event) {
        // Get the tooltip div
        const tooltip = d3
          .select("body")
          .selectAll(".d3-tooltip")
          .data([1])
          .join("div")
          .attr("class", "d3-tooltip")
          .style("position", "absolute")
          .style("visibility", "hidden")
          .style("background-color", "rgba(0,0,0,0.9)")
          .style("color", "white")
          .style("padding", "12px 16px")
          .style("border-radius", "6px")
          .style("font-size", "14px")
          .style("max-width", "300px")
          .style("z-index", "1000")
          .style("pointer-events", "none");

        // Determine the appropriate explanation based on the value
        let explanation;
        if (percentage < 50) {
          explanation =
            "Poor quality earnings: Cash flow is significantly less than reported net income, suggesting earnings may be overstated or cash generation is weak.";
        } else if (percentage < 80) {
          explanation =
            "Moderate quality earnings: Cash flow supports a reasonable portion of reported income, but there's still a gap between accounting profits and cash generation.";
        } else if (percentage < 120) {
          explanation =
            "High quality earnings: Strong cash flow relative to net income indicates reliable earnings with minimal accounting adjustments.";
        } else {
          explanation =
            "Very high quality earnings: Cash flow exceeds reported income, suggesting conservative accounting or strong operational efficiency.";
        }

        // Show the tooltip with explanation
        tooltip
          .html(
            `
          <strong>Cash Flow to Net Income Ratio: ${Math.round(percentage)}%</strong>
          <p style="margin-top: 8px; margin-bottom: 4px;">${explanation}</p>
          <p style="margin-top: 8px; font-size: 12px;">This ratio measures how well a company's reported earnings are backed by actual cash flow. Higher percentages indicate higher quality earnings.</p>
        `,
          )
          .style("visibility", "visible")
          .style("top", event.pageY - 10 + "px")
          .style("left", event.pageX + 15 + "px");
      })
      .on("mousemove", function (event) {
        d3.select(".d3-tooltip")
          .style("top", event.pageY - 10 + "px")
          .style("left", event.pageX + 15 + "px");
      })
      .on("mouseout", function () {
        d3.select(".d3-tooltip").style("visibility", "hidden");
      });

    // Add animation effect for the needle
    needle
      .style("transform", `rotate(${-90}deg)`) // Start at 0
      .transition()
      .duration(1500)
      .ease(d3.easeElastic)
      .style(
        "transform",
        `rotate(${(needleAngle + Math.PI / 2) * (180 / Math.PI)}deg)`,
      );
  }

  // Create a function to initialize the overall score gauge
  function createOverallScoreGauge(containerId, score) {
    // Clear previous chart if it exists
    const container = document.getElementById(containerId);
    if (!container) return;
    container.innerHTML = "";

    // Configuration
    const width = container.clientWidth || 400;
    const height = 350;
    const margin = { top: 50, right: 50, bottom: 70, left: 50 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;
    const radius = Math.min(chartWidth, chartHeight) / 2;

    // Gauge specific configurations
    const innerRadius = radius * 0.7;
    const outerRadius = radius * 0.9;

    // Color scale for the gauge (red -> yellow -> green)
    const colorScale = d3
      .scaleLinear()
      .domain([0, 33, 66, 100])
      .range(["#dc3545", "#ff7f0e", "#ffc107", "#198754"]);

    // Create SVG
    const svg = d3
      .select(`#${containerId}`)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${width / 2}, ${height / 2 - 20})`);

    // Create background arc segments
    const backgroundArc = d3
      .arc()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius);

    // Create a group for the background segments
    const segments = svg.append("g").attr("class", "segments");

    // Create colored segments
    const numSegments = 100;
    const angleRange = Math.PI;
    const angleStep = angleRange / numSegments;

    for (let i = 0; i < numSegments; i++) {
      const startAngle = -Math.PI / 2 + i * angleStep;
      const endAngle = startAngle + angleStep;
      const segmentColor = colorScale(i);

      segments
        .append("path")
        .attr(
          "d",
          backgroundArc({
            startAngle: startAngle,
            endAngle: endAngle,
          }),
        )
        .style("fill", segmentColor);
    }

    // Create needle
    const needleLength = radius * 0.8;
    const needleRadius = radius * 0.04;

    // Scale to convert score to angle
    const scale = d3
      .scaleLinear()
      .domain([0, 100])
      .range([-Math.PI / 2, Math.PI / 2]);

    const needleAngle = scale(score);

    // Needle container with initial position
    const needle = svg
      .append("g")
      .attr("class", "needle")
      .style("transform-origin", "center bottom")
      .style("transform", `rotate(${-90}deg)`);

    // Add needle triangle
    needle
      .append("path")
      .attr(
        "d",
        `M ${-needleRadius} 0 L ${needleRadius} 0 L 0 ${-needleLength} Z`,
      )
      .style("fill", "#464646");

    // Add center circle
    svg
      .append("circle")
      .attr("cx", 0)
      .attr("cy", 0)
      .attr("r", needleRadius * 1.5)
      .style("fill", "#464646")
      .style("stroke", "#e6e6e6")
      .style("stroke-width", "2px");

    // Add score text in the center
    svg
      .append("text")
      .attr("class", "score-value")
      .attr("x", 0)
      .attr("y", 15)
      .attr("text-anchor", "middle")
      .style("font-size", "40px")
      .style("font-weight", "bold")
      .style("fill", colorScale(score))
      .text(score);

    // Add "OVERALL SCORE" text
    svg
      .append("text")
      .attr("x", 0)
      .attr("y", 50)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "500")
      .text("OVERALL SCORE");

    // Add assessment text (LOW, MEDIUM, HIGH)
    const assessment = score < 33 ? "LOW" : score < 66 ? "MEDIUM" : "HIGH";

    svg
      .append("text")
      .attr("x", 0)
      .attr("y", 75)
      .attr("text-anchor", "middle")
      .style("font-size", "20px")
      .style("font-weight", "bold")
      .style("fill", colorScale(score))
      .text(assessment);

    // Add gauge labels
    const labelData = [
      { value: 0, text: "0%" },
      { value: 50, text: "50%" },
      { value: 100, text: "100%" },
    ];

    labelData.forEach((d) => {
      const angle = scale(d.value);
      const x = (radius + 15) * Math.cos(angle);
      const y = (radius + 15) * Math.sin(angle);

      svg
        .append("text")
        .attr("x", x)
        .attr("y", y + 35)
        .attr("text-anchor", "middle")
        .style("font-size", "12px")
        .text(d.text);
    });

    // Animate the needle
    needle
      .transition()
      .duration(1500)
      .ease(d3.easeElastic)
      .style(
        "transform",
        `rotate(${(needleAngle + Math.PI / 2) * (180 / Math.PI)}deg)`,
      );
  }

  // ... existing code ...

  // Add event listener to initialize D3 gauge charts after the DOM is loaded
  document.addEventListener("DOMContentLoaded", function () {
    // Look for cash flow gauge charts that need to be created
    if (document.getElementById("cf-to-income-ratio-chart")) {
      createCFtoNetIncomeGauge(
        "cf-to-income-ratio-chart",
        0.85,
        "Cash Flow to Net Income Ratio",
      );
    }

    // Check for overall score gauge (for financial health page)
    if (document.getElementById("overall-score-gauge")) {
      createOverallScoreGauge("overall-score-gauge", 34);
    }
  });

  // ... existing code ...

  // Financial Features Integration
  function initializeFinancialFeatures() {
    // Add event listeners for financial buttons
    document.addEventListener("click", function (event) {
      const button = event.target.closest(".financial-button");
      if (!button) return;

      const templateContainer = button.closest(".financial-template");
      if (!templateContainer) return;

      const symbol = templateContainer.dataset.symbol;

      // Handle financial statements buttons
      if (button.matches("[data-statement]")) {
        const statementType = button.dataset.statement;

        // Set active button
        templateContainer
          .querySelectorAll("[data-statement]")
          .forEach((btn) => btn.classList.remove("active"));
        button.classList.add("active");

        // Fetch and render financial statements
        fetchFinancialStatements(symbol, statementType);
      }

      // Handle sentiment analysis buttons
      else if (button.matches("[data-sentiment]")) {
        const sentimentType = button.dataset.sentiment;

        // Set active button
        templateContainer
          .querySelectorAll("[data-sentiment]")
          .forEach((btn) => btn.classList.remove("active"));
        button.classList.add("active");

        // Fetch and render sentiment analysis
        fetchSentimentAnalysis(symbol, sentimentType);
      }

      // Handle portfolio tracker buttons
      else if (button.matches("[data-portfolio]")) {
        const portfolioView = button.dataset.portfolio;

        // Set active button
        templateContainer
          .querySelectorAll("[data-portfolio]")
          .forEach((btn) => btn.classList.remove("active"));
        button.classList.add("active");

        // Fetch and render portfolio data
        fetchPortfolioData(portfolioView);
      }
    });
  }

  // Function to handle financial responses from chatbot
  function handleFinancialResponse(response) {
    if (!response || !response.type || response.type !== "financial_data") {
      return false; // Not a financial response
    }

    // Create financial message
    const message = document.createElement("li");
    message.className = "message bot";

    // Add bot avatar
    const avatar = document.createElement("span");
    avatar.className = "bot-avatar";
    avatar.innerHTML = '<i class="fas fa-robot chatbot-icon-small"></i>';
    message.appendChild(avatar);

    // Create message content
    const content = document.createElement("p");

    // Add financial template based on command type
    const templateId = `${response.command}-template`;
    const template = document.getElementById(templateId);

    if (!template) {
      content.innerHTML =
        "Sorry, I could not load the financial visualization.";
      message.appendChild(content);
      return true;
    }

    // Clone template and set it visible
    const templateClone = template.cloneNode(true);
    templateClone.id = `${templateId}-instance`;
    templateClone.style.display = "block";

    // Set symbol data attribute if available
    if (response.params && response.params.symbol) {
      templateClone.dataset.symbol = response.params.symbol;
    }

    // Add intro text based on command
    let introText = "";
    switch (response.command) {
      case "show_financials":
        introText = `Here are the financial statements for ${response.params.symbol || "the company"}. Choose a statement type:`;
        break;
      case "analyze_sentiment":
        introText = `Here's the sentiment analysis for ${response.params.symbol || "the stock"}. Choose a view:`;
        break;
      case "track_portfolio":
        introText = "Here's your portfolio performance. Choose a view:";
        break;
      case "fair_value":
        introText = `Here's the fair value estimation for ${response.params.symbol || "the stock"}. Choose a valuation method:`;
        break;
      default:
        introText = "Here's the financial information you requested:";
    }

    content.innerHTML = `<p>${introText}</p>`;
    content.appendChild(templateClone);

    message.appendChild(content);

    // Add message to chat
    document.getElementById("chatbot-messages").appendChild(message);

    // Scroll to bottom
    scrollToBottom();

    // Click the first option button to load initial data
    const firstButton = templateClone.querySelector(".financial-button");
    if (firstButton) {
      firstButton.click();
    }

    return true; // Handled financial response
  }

  // Financial Statements Functions
  function fetchFinancialStatements(symbol, statementType) {
    if (!symbol) {
      console.error("Symbol is required for financial statements");
      const container = document.getElementById(
        "financial-statements-container",
      );
      if (container)
        container.innerHTML = `<div class="alert alert-danger">Error: No stock symbol provided.</div>`;
      return;
    }

    const container = document.getElementById("financial-statements-container");
    if (!container) {
      console.error("Financial statements container not found");
      return;
    }

    container.innerHTML =
      '<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Loading financial data...</div>';

    // Dynamically build the URL (yearly data only)
    const url = `/financial-reports/api/financial-statement/${symbol}/${statementType}`;
    console.log(`Fetching financial statements from: ${url}`);

    fetch(url)
      .then((response) => {
        if (!response.ok) {
          return response.json().then((err) => {
            throw new Error(err.error || `API Error: ${response.status}`);
          });
        }
        return response.json();
      })
      .then((data) => {
        if (data.error) {
          throw new Error(data.error);
        }

        // Render financial statements using the D3.js visualization
        if (
          window.financialVisualizations &&
          typeof window.financialVisualizations.renderFinancialStatements ===
            "function"
        ) {
          window.financialVisualizations.renderFinancialStatements(
            data,
            "financial-statements-container",
            statementType,
          );
        } else {
          container.innerHTML =
            '<div class="alert alert-warning">Financial visualization module not loaded or function is missing.</div>';
        }
      })
      .catch((error) => {
        console.error("Error fetching financial statements:", error);
        container.innerHTML = `<div class="alert alert-danger">Error loading financial data: ${error.message}</div>`;
      });
  }

  // Sentiment Analysis Functions
  function fetchSentimentAnalysis(symbol, sentimentType) {
    if (!symbol) {
      console.error("Symbol is required for sentiment analysis");
      return;
    }

    const container = document.getElementById("sentiment-analysis-container");
    container.innerHTML =
      '<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Loading sentiment data...</div>';

    fetch(`/api/chatbot/risk-impact-data/${symbol}`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`API Error: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        // Render sentiment analysis using the D3.js visualization
        if (window.financialVisualizations) {
          window.financialVisualizations.renderSentimentAnalysis(
            data,
            "sentiment-analysis-container",
          );
        } else {
          container.innerHTML =
            '<div class="alert alert-warning">Financial visualization module not loaded</div>';
        }
      })
      .catch((error) => {
        console.error("Error fetching sentiment analysis:", error);
        container.innerHTML = `<div class="alert alert-danger">Error loading sentiment data: ${error.message}</div>`;
      });
  }

  // Portfolio Tracker Functions
  function fetchPortfolioData(portfolioView) {
    const container = document.getElementById("portfolio-tracker-container");
    container.innerHTML =
      '<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Loading portfolio data...</div>';

    fetch("/api/portfolio-summary")
      .then((response) => {
        if (!response.ok) {
          throw new Error(`API Error: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        renderPortfolioTracker(data, portfolioView);
      })
      .catch((error) => {
        console.error("Error fetching portfolio data:", error);
        container.innerHTML = `<div class="alert alert-danger">Error loading portfolio data: ${error.message}</div>`;
      });
  }

  function renderPortfolioTracker(data, portfolioView) {
    const container = document.getElementById("portfolio-tracker-container");
    container.innerHTML = "";

    if (!data || !data.holdings || data.holdings.length === 0) {
      container.innerHTML =
        '<div class="alert alert-warning">No portfolio data available</div>';
      return;
    }

    // Create container for portfolio overview
    const overviewDiv = document.createElement("div");
    overviewDiv.className = "portfolio-overview";
    container.appendChild(overviewDiv);

    // Add total value section
    const totalValueDiv = document.createElement("div");
    totalValueDiv.className = "portfolio-metrics";
    totalValueDiv.innerHTML = `
        <h3>Portfolio Value</h3>
        <h4>$${data.total_value.toFixed(2)}</h4>
    `;
    overviewDiv.appendChild(totalValueDiv);

    // Add holdings count section
    const holdingsCountDiv = document.createElement("div");
    holdingsCountDiv.className = "portfolio-metrics";
    holdingsCountDiv.innerHTML = `
        <h3>Holdings</h3>
        <h4>${data.holdings.length} stocks</h4>
    `;
    overviewDiv.appendChild(holdingsCountDiv);

    // Create chart container
    const chartDiv = document.createElement("div");
    chartDiv.className = "portfolio-chart";
    chartDiv.style.height = "300px";
    overviewDiv.appendChild(chartDiv);

    // Create holdings table
    const tableDiv = document.createElement("div");
    tableDiv.className = "portfolio-table-container mt-4";
    container.appendChild(tableDiv);

    const table = document.createElement("table");
    table.className = "portfolio-table";
    tableDiv.appendChild(table);

    // Add table header
    const thead = document.createElement("thead");
    thead.innerHTML = `
        <tr>
            <th>Symbol</th>
            <th>Name</th>
            <th>Shares</th>
            <th>Price</th>
            <th>Value</th>
            <th>Allocation</th>
            <th>Gain/Loss</th>
        </tr>
    `;
    table.appendChild(thead);

    // Add table body
    const tbody = document.createElement("tbody");
    data.holdings.forEach((holding) => {
      const gainLossClass =
        holding.gain_loss >= 0 ? "positive-value" : "negative-value";

      const row = document.createElement("tr");
      row.innerHTML = `
            <td>${holding.symbol}</td>
            <td>${holding.name}</td>
            <td>${holding.shares}</td>
            <td>$${holding.current_price.toFixed(2)}</td>
            <td>$${holding.current_value.toFixed(2)}</td>
            <td>${holding.allocation.toFixed(2)}%</td>
            <td class="${gainLossClass}">$${holding.gain_loss.toFixed(2)} (${holding.gain_loss_percent.toFixed(2)}%)</td>
        `;
      tbody.appendChild(row);
    });
    table.appendChild(tbody);

    // Render the portfolio chart based on the selected view
    if (portfolioView === "portfolio_overview") {
      renderPortfolioAllocationChart(data, chartDiv);
    } else if (portfolioView === "performance_chart") {
      // This would require historical data, which is not included in our API response
      // For now, just show a message
      chartDiv.innerHTML =
        '<div class="alert alert-info">Performance chart requires historical data</div>';
    } else if (portfolioView === "risk_analysis") {
      // This would require risk metrics, which are not included in our API response
      // For now, just show a message
      chartDiv.innerHTML =
        '<div class="alert alert-info">Risk analysis requires additional metrics</div>';
    }
  }

  function renderPortfolioAllocationChart(data, container) {
    // Prepare data for D3 pie chart
    const pieData = data.holdings.map((holding) => ({
      symbol: holding.symbol,
      name: holding.name,
      value: holding.current_value,
      percentage: holding.allocation,
    }));

    // Set dimensions and margins
    const width = container.clientWidth;
    const height = container.clientHeight;
    const margin = { top: 20, right: 20, bottom: 30, left: 40 };
    const radius =
      Math.min(
        width - margin.left - margin.right,
        height - margin.top - margin.bottom,
      ) / 2;

    // Clear container
    d3.select(container).html("");

    // Create SVG
    const svg = d3
      .select(container)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${width / 2}, ${height / 2})`);

    // Define color scale
    const color = d3.scaleOrdinal(d3.schemeCategory10);

    // Create pie generator
    const pie = d3
      .pie()
      .value((d) => d.value)
      .sort(null);

    // Create arc generator
    const arc = d3
      .arc()
      .innerRadius(radius * 0.4)
      .outerRadius(radius * 0.8);

    // Create outer arc for labels
    const outerArc = d3
      .arc()
      .innerRadius(radius * 0.9)
      .outerRadius(radius * 0.9);

    // Create pie slices
    const slices = svg
      .selectAll(".slice")
      .data(pie(pieData))
      .enter()
      .append("g")
      .attr("class", "slice");

    // Add paths
    slices
      .append("path")
      .attr("d", arc)
      .attr("fill", (d, i) => color(i))
      .attr("stroke", "white")
      .style("stroke-width", "2px")
      .style("opacity", 0.7)
      .on("mouseover", function () {
        d3.select(this).style("opacity", 1);
      })
      .on("mouseout", function () {
        d3.select(this).style("opacity", 0.7);
      });

    // Add labels
    slices
      .append("text")
      .attr("transform", (d) => {
        const pos = outerArc.centroid(d);
        const midAngle = d.startAngle + (d.endAngle - d.startAngle) / 2;
        pos[0] = radius * 0.95 * (midAngle < Math.PI ? 1 : -1);
        return `translate(${pos})`;
      })
      .attr("dy", ".35em")
      .style("text-anchor", (d) => {
        const midAngle = d.startAngle + (d.endAngle - d.startAngle) / 2;
        return midAngle < Math.PI ? "start" : "end";
      })
      .text((d) => `${d.data.symbol} (${d.data.percentage.toFixed(1)}%)`);

    // Add polylines for labels
    slices
      .append("polyline")
      .attr("points", (d) => {
        const pos = outerArc.centroid(d);
        const midAngle = d.startAngle + (d.endAngle - d.startAngle) / 2;
        pos[0] = radius * 0.95 * (midAngle < Math.PI ? 1 : -1);
        return [arc.centroid(d), outerArc.centroid(d), pos];
      })
      .style("fill", "none")
      .style("stroke", "gray")
      .style("stroke-width", "1px");
  }
}

// 🌟 SPECTACULAR OVERALL INVESTMENT RATING CHART 🌟
function createSpectacularOverallChart(containerId, rating) {
  console.log(`[SpectacularChart] Creating overall investment rating chart with rating: ${rating}%`);

  if (typeof d3 === "undefined") {
    console.warn("[SpectacularChart] D3.js not available, using fallback");
    const container = document.getElementById(containerId);
    if (container) {
      renderEnhancedFallbackChart(rating, container, "#00d4ff");
    }
    return;
  }

  const container = d3.select(`#${containerId}`);
  if (container.empty()) {
    console.error(`[SpectacularChart] Container #${containerId} not found`);
    return;
  }

  // Clear previous content
  container.html('');

  // Enhanced configuration for spectacular effect
  const size = 200;
  const strokeWidth = 20;
  const radius = (size - strokeWidth) / 2;
  const centerX = size / 2;
  const centerY = size / 2;

  // Create SVG with enhanced overflow handling
  const svg = container
    .append('svg')
    .attr('width', size + 60) // Extra space for glow effects
    .attr('height', size + 60)
    .attr('class', 'spectacular-overall-chart-svg')
    .style('overflow', 'visible')
    .style('transform', 'translate(-30px, -30px)'); // Center the extra space

  // Create enhanced gradient and filter definitions
  const defs = svg.append('defs');

  // Enhanced drop shadow with glow
  const shadowId = `spectacular-shadow-${Math.random().toString(36).substr(2, 9)}`;
  const dropShadow = defs.append('filter')
    .attr('id', shadowId)
    .attr('x', '-100%').attr('y', '-100%')
    .attr('width', '300%').attr('height', '300%');

  dropShadow.append('feGaussianBlur')
    .attr('in', 'SourceAlpha')
    .attr('stdDeviation', 12);

  dropShadow.append('feOffset')
    .attr('dx', 0).attr('dy', 6)
    .attr('result', 'offset');

  dropShadow.append('feFlood')
    .attr('flood-color', '#00d4ff')
    .attr('flood-opacity', 0.6);

  dropShadow.append('feComposite')
    .attr('in2', 'offset')
    .attr('operator', 'in');

  const feMerge = dropShadow.append('feMerge');
  feMerge.append('feMergeNode');
  feMerge.append('feMergeNode').attr('in', 'SourceGraphic');

  // Spectacular gradient for the progress arc
  const gradientId = `spectacular-gradient-${Math.random().toString(36).substr(2, 9)}`;
  const spectacularGradient = defs.append('linearGradient')
    .attr('id', gradientId)
    .attr('x1', '0%').attr('y1', '0%')
    .attr('x2', '100%').attr('y2', '100%');

  spectacularGradient.append('stop')
    .attr('offset', '0%')
    .attr('stop-color', '#00d4ff')
    .attr('stop-opacity', 1);

  spectacularGradient.append('stop')
    .attr('offset', '25%')
    .attr('stop-color', '#0099cc')
    .attr('stop-opacity', 1);

  spectacularGradient.append('stop')
    .attr('offset', '75%')
    .attr('stop-color', '#ff6b35')
    .attr('stop-opacity', 1);

  spectacularGradient.append('stop')
    .attr('offset', '100%')
    .attr('stop-color', '#00ff88')
    .attr('stop-opacity', 1);

  // Create the main group with enhanced positioning
  const mainGroup = svg.append('g')
    .attr('transform', `translate(${centerX + 30}, ${centerY + 30})`);

  // Background track with subtle glow
  const backgroundArc = d3.arc()
    .innerRadius(radius - strokeWidth / 2)
    .outerRadius(radius + strokeWidth / 2)
    .startAngle(0)
    .endAngle(2 * Math.PI);

  mainGroup.append('path')
    .datum({})
    .attr('d', backgroundArc)
    .attr('fill', 'rgba(255, 255, 255, 0.1)')
    .attr('stroke', 'rgba(255, 255, 255, 0.2)')
    .attr('stroke-width', 2);

  // Spectacular progress arc
  const progressArc = d3.arc()
    .innerRadius(radius - strokeWidth / 2)
    .outerRadius(radius + strokeWidth / 2)
    .startAngle(0)
    .cornerRadius(strokeWidth / 2);

  const progressPath = mainGroup.append('path')
    .datum({ endAngle: 0 })
    .attr('d', progressArc)
    .attr('fill', `url(#${gradientId})`)
    .attr('filter', `url(#${shadowId})`)
    .style('opacity', 0);

  // Center text with enhanced styling
  const centerText = mainGroup.append('text')
    .attr('text-anchor', 'middle')
    .attr('dominant-baseline', 'central')
    .style('font-size', '32px')
    .style('font-weight', '900')
    .style('fill', '#ffffff')
    .style('text-shadow', '0 2px 8px rgba(0,0,0,0.8), 0 0 16px rgba(0,212,255,0.6)')
    .text('0%');

  // Spectacular animation sequence
  const targetAngle = (rating / 100) * 2 * Math.PI;

  // Fade in and animate
  progressPath
    .transition()
    .duration(800)
    .ease(d3.easeCubicOut)
    .style('opacity', 1)
    .attrTween('d', function() {
      const interpolate = d3.interpolate(0, targetAngle);
      return function(t) {
        const angle = interpolate(t);
        return progressArc({ endAngle: angle });
      };
    });

  // Animate center text with counting effect
  centerText
    .transition()
    .duration(1200)
    .ease(d3.easeCubicOut)
    .tween('text', function() {
      const interpolate = d3.interpolate(0, rating);
      return function(t) {
        d3.select(this).text(Math.round(interpolate(t)) + '%');
      };
    });

  console.log(`[SpectacularChart] Created successfully for rating: ${rating}%`);
}

// 💎 PREMIUM DONUT CHART WITH ENHANCED EFFECTS 💎
function renderPremiumDonutChart(ratingValue, chartElement, color = "#007bff") {
  if (!chartElement) {
    console.warn(`[PremiumDonutChart] Chart element not provided.`);
    return;
  }

  // Validate and clamp rating value
  const clampedRating = Math.max(0, Math.min(100, ratingValue || 0));
  console.log(`[PremiumDonutChart] Creating premium chart with rating: ${clampedRating}%`);

  // Clear any existing content
  chartElement.innerHTML = '';

  if (typeof d3 === "undefined") {
    console.warn("[PremiumDonutChart] D3 not available, using enhanced fallback");
    renderEnhancedFallbackChart(clampedRating, chartElement, color);
    return;
  }

  // Create premium D3.js circular chart
  const size = 100;
  const strokeWidth = 8;
  const radius = (size - strokeWidth) / 2;

  const svg = d3.select(chartElement)
    .append('svg')
    .attr('width', size)
    .attr('height', size)
    .attr('class', 'premium-donut-chart-svg')
    .style('overflow', 'visible');

  // Enhanced gradient definition
  const defs = svg.append('defs');
  const gradientId = `premium-gradient-${Math.random().toString(36).substr(2, 9)}`;
  const gradient = defs.append('linearGradient')
    .attr('id', gradientId)
    .attr('x1', '0%').attr('y1', '0%')
    .attr('x2', '100%').attr('y2', '100%');

  gradient.append('stop')
    .attr('offset', '0%')
    .attr('stop-color', color)
    .attr('stop-opacity', 1);

  gradient.append('stop')
    .attr('offset', '100%')
    .attr('stop-color', d3.color(color).brighter(0.5))
    .attr('stop-opacity', 1);

  const g = svg.append('g')
    .attr('transform', `translate(${size/2}, ${size/2})`);

  // Background arc
  const backgroundArc = d3.arc()
    .innerRadius(radius - strokeWidth/2)
    .outerRadius(radius + strokeWidth/2)
    .startAngle(0)
    .endAngle(2 * Math.PI);

  g.append('path')
    .datum({})
    .attr('d', backgroundArc)
    .attr('fill', 'rgba(255, 255, 255, 0.1)');

  // Progress arc with animation
  const progressArc = d3.arc()
    .innerRadius(radius - strokeWidth/2)
    .outerRadius(radius + strokeWidth/2)
    .startAngle(0)
    .cornerRadius(strokeWidth/4);

  const progressPath = g.append('path')
    .datum({ endAngle: 0 })
    .attr('d', progressArc)
    .attr('fill', `url(#${gradientId})`)
    .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))');

  // Center text with premium styling
  const centerText = g.append('text')
    .attr('text-anchor', 'middle')
    .attr('dominant-baseline', 'central')
    .style('font-size', '16px')
    .style('font-weight', '900')
    .style('fill', '#2c3e50')
    .style('text-shadow', '0 1px 2px rgba(255,255,255,0.8)')
    .text('0%');

  // Animate the chart
  const targetAngle = (clampedRating / 100) * 2 * Math.PI;

  progressPath
    .transition()
    .duration(1000)
    .ease(d3.easeCubicOut)
    .attrTween('d', function() {
      const interpolate = d3.interpolate(0, targetAngle);
      return function(t) {
        return progressArc({ endAngle: interpolate(t) });
      };
    });

  centerText
    .transition()
    .duration(1000)
    .ease(d3.easeCubicOut)
    .tween('text', function() {
      const interpolate = d3.interpolate(0, clampedRating);
      return function(t) {
        d3.select(this).text(Math.round(interpolate(t)) + '%');
      };
    });
}

// Enhanced fallback for when D3 is not available
function renderEnhancedFallbackChart(ratingValue, chartElement, color) {
  chartElement.style.setProperty(
    "background",
    `conic-gradient(${color} 0deg ${(ratingValue / 100) * 360}deg, rgba(255,255,255,0.1) ${(ratingValue / 100) * 360}deg 360deg)`
  );
  chartElement.style.borderRadius = '50%';
  chartElement.style.width = '100px';
  chartElement.style.height = '100px';
  chartElement.style.display = 'flex';
  chartElement.style.alignItems = 'center';
  chartElement.style.justifyContent = 'center';
  chartElement.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.1)';

  const textSpan = document.createElement("span");
  textSpan.className = "donut-chart-text";
  textSpan.textContent = ratingValue + "%";
  textSpan.style.color = '#2c3e50';
  textSpan.style.fontWeight = '900';
  textSpan.style.fontSize = '16px';
  textSpan.style.textShadow = '0 1px 2px rgba(255,255,255,0.8)';
  chartElement.appendChild(textSpan);
}

// ===== COMPREHENSIVE FINANCIAL METRICS FUNCTIONALITY =====

/**
 * Load and display comprehensive financial metrics for the portfolio
 */
function loadComprehensiveMetrics() {
    const loader = document.getElementById('comprehensive-metrics-loader');
    const error = document.getElementById('comprehensive-metrics-error');
    const grid = document.getElementById('comprehensive-metrics-grid');
    const summary = document.getElementById('comprehensive-metrics-summary');

    // Show loader and hide other elements
    if (loader) loader.style.display = 'block';
    if (error) error.style.display = 'none';
    if (grid) grid.style.display = 'none';
    if (summary) summary.style.display = 'none';

    fetch('/api/portfolio-comprehensive-metrics')
        .then(response => response.json())
        .then(data => {
            if (loader) loader.style.display = 'none';

            if (data.error) {
                showComprehensiveMetricsError(data.error);
                return;
            }

            displayComprehensiveMetrics(data.metrics);
            if (grid) grid.style.display = 'grid';

            // Show summary if we have enough data
            if (Object.keys(data.metrics).length > 5) {
                generatePortfolioSummary(data.metrics);
                if (summary) summary.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error loading comprehensive metrics:', error);
            if (loader) loader.style.display = 'none';
            showComprehensiveMetricsError('Failed to load comprehensive metrics');
        });
}

/**
 * Display comprehensive metrics in the grid
 */
function displayComprehensiveMetrics(metrics) {
    const metricItems = document.querySelectorAll('.metric-item');

    metricItems.forEach(item => {
        const metricName = item.getAttribute('data-metric');
        const valueElement = item.querySelector('.metric-value');
        const interpretationElement = item.querySelector('.metric-interpretation');

        if (metrics[metricName]) {
            const metric = metrics[metricName];
            const formattedValue = formatMetricValue(metricName, metric.value);
            const interpretation = getMetricInterpretation(metricName, metric.value);

            if (valueElement) {
                valueElement.textContent = formattedValue;
                valueElement.style.color = getMetricColor(metricName, metric.value);
            }

            if (interpretationElement) {
                interpretationElement.textContent = interpretation;
            }

            // Add coverage info
            if (metric.coverage) {
                const coverageSpan = document.createElement('span');
                coverageSpan.className = 'metric-coverage';
                coverageSpan.textContent = ` ${metric.coverage}`;
                coverageSpan.style.fontSize = '0.7rem';
                coverageSpan.style.color = 'var(--text-muted)';
                if (valueElement && !valueElement.querySelector('.metric-coverage')) {
                    valueElement.appendChild(coverageSpan);
                }
            }
        } else {
            if (valueElement) valueElement.textContent = '—';
            if (interpretationElement) interpretationElement.textContent = 'No data available';
        }
    });
}

/**
 * Format metric values for display
 */
function formatMetricValue(metricName, value) {
    if (value === null || value === undefined) return '—';

    // Percentage metrics (these are already in percentage form from backend)
    const percentageMetrics = [
        'roe', 'roa', 'roic', 'roce', 'gross_profit_margin',
        'operating_profit_margin', 'net_profit_margin', 'ebitda_margin',
        'pretax_profit_margin', 'return_on_tangible_assets', 'return_on_total_capital',
        'operating_cash_flow_ratio', 'debt_to_capital', 'long_term_debt_to_equity',
        'long_term_debt_to_assets', 'dividend_yield', 'dividend_payout_ratio',
        'retention_ratio', 'buyback_yield', 'shareholder_yield', 'earnings_yield',
        'fcf_yield', 'dividend_coverage_ratio'
    ];

    if (percentageMetrics.includes(metricName)) {
        // Backend already provides percentage values, just add % symbol
        return `${value.toFixed(2)}%`;
    }

    // Ratio metrics (display with 2 decimal places)
    const ratioMetrics = [
        'current_ratio', 'quick_ratio', 'cash_ratio', 'working_capital_ratio',
        'debt_to_equity', 'debt_to_assets', 'interest_coverage', 'financial_leverage',
        'net_debt_to_ebitda', 'pe_ratio', 'forward_pe', 'peg_ratio', 'price_to_book',
        'price_to_tangible_book', 'price_to_sales', 'price_to_free_cash_flow',
        'price_to_operating_cash_flow', 'ev_to_revenue', 'ev_to_ebitda', 'ev_to_ebit',
        'ev_to_fcf', 'asset_turnover', 'fixed_asset_turnover', 'inventory_turnover',
        'receivables_turnover', 'payables_turnover', 'ebitda_interest_coverage',
        'cash_coverage_ratio'
    ];

    if (ratioMetrics.includes(metricName)) {
        return value.toFixed(2);
    }

    // Days metrics (whole numbers)
    const daysMetrics = [
        'days_sales_outstanding', 'days_inventory_outstanding', 'days_payables_outstanding',
        'cash_conversion_cycle', 'operating_cycle'
    ];
    if (daysMetrics.includes(metricName)) {
        return `${Math.round(value)} days`;
    }

    // Per share metrics (currency format)
    const perShareMetrics = [
        'earnings_per_share', 'diluted_eps', 'book_value_per_share', 'tangible_book_value_per_share',
        'revenue_per_share', 'cash_per_share', 'fcf_per_share', 'ocf_per_share'
    ];
    if (perShareMetrics.includes(metricName)) {
        return `$${value.toFixed(2)}`;
    }

    // Large number metrics (format with commas)
    const largeNumberMetrics = ['market_cap', 'enterprise_value', 'total_revenue', 'net_income', 'net_working_capital'];
    if (largeNumberMetrics.includes(metricName)) {
        return formatLargeNumber(value);
    }

    // Default formatting
    return value.toFixed(2);
}

/**
 * Get color for metric value based on its performance
 */
function getMetricColor(metricName, value) {
    if (value === null || value === undefined) return 'var(--text-muted)';

    // Define good/bad thresholds for different metrics
    const metricThresholds = {
        // Profitability (higher is better)
        'roe': { good: 15, bad: 5 },
        'roa': { good: 5, bad: 1 },
        'roic': { good: 10, bad: 3 },
        'roce': { good: 15, bad: 5 },
        'gross_profit_margin': { good: 40, bad: 20 },
        'operating_profit_margin': { good: 15, bad: 5 },
        'net_profit_margin': { good: 10, bad: 2 },

        // Liquidity (higher is better, but not too high)
        'current_ratio': { good: 1.5, bad: 1.0, tooHigh: 3.0 },
        'quick_ratio': { good: 1.0, bad: 0.5, tooHigh: 2.0 },
        'cash_ratio': { good: 0.2, bad: 0.1, tooHigh: 1.0 },

        // Leverage (lower is better)
        'debt_to_equity': { good: 0.3, bad: 1.0, reverse: true },
        'debt_to_assets': { good: 0.3, bad: 0.6, reverse: true },
        'interest_coverage': { good: 5, bad: 2 },

        // Valuation (context dependent, but generally lower P/E is better)
        'pe_ratio': { good: 15, bad: 25, reverse: true },
        'price_to_book': { good: 1.5, bad: 3.0, reverse: true },

        // Efficiency
        'asset_turnover': { good: 1.0, bad: 0.5 },
        'free_cash_flow_yield': { good: 5, bad: 0 },
        'dividend_yield': { good: 3, bad: 0 }
    };

    const threshold = metricThresholds[metricName];
    if (!threshold) return 'var(--primary-color)';

    if (threshold.reverse) {
        // Lower is better
        if (value <= threshold.good) return 'var(--positive-color)';
        if (value >= threshold.bad) return 'var(--negative-color)';
        return 'var(--warning-color)';
    } else {
        // Higher is better
        if (threshold.tooHigh && value >= threshold.tooHigh) return 'var(--warning-color)';
        if (value >= threshold.good) return 'var(--positive-color)';
        if (value <= threshold.bad) return 'var(--negative-color)';
        return 'var(--warning-color)';
    }
}

/**
 * Get interpretation text for metric values
 */
function getMetricInterpretation(metricName, value) {
    if (value === null || value === undefined) return 'No data available';

    // Backend already provides percentage values in correct format, no conversion needed
    const percentValue = value;

    const interpretations = {
        // Profitability Ratios (values are already in percentage format from backend)
        'roe': value >= 15 ? 'Excellent profitability' : value >= 10 ? 'Good profitability' : value >= 5 ? 'Moderate profitability' : 'Poor profitability',
        'roa': value >= 5 ? 'Excellent asset efficiency' : value >= 3 ? 'Good asset efficiency' : value >= 1 ? 'Moderate efficiency' : 'Poor efficiency',
        'roic': value >= 15 ? 'Excellent capital efficiency' : value >= 10 ? 'Good capital efficiency' : value >= 5 ? 'Moderate efficiency' : 'Poor efficiency',
        'roce': value >= 15 ? 'Strong capital efficiency' : value >= 10 ? 'Good capital efficiency' : value >= 5 ? 'Moderate efficiency' : 'Poor efficiency',
        'gross_profit_margin': value >= 40 ? 'Excellent margins' : value >= 30 ? 'Good margins' : value >= 20 ? 'Moderate margins' : 'Thin margins',
        'operating_profit_margin': value >= 20 ? 'Excellent operations' : value >= 10 ? 'Good operations' : value >= 5 ? 'Moderate operations' : 'Weak operations',
        'net_profit_margin': value >= 15 ? 'Excellent profitability' : value >= 10 ? 'Good profitability' : value >= 5 ? 'Moderate profitability' : 'Low profitability',
        'ebitda_margin': value >= 25 ? 'Excellent cash generation' : value >= 15 ? 'Good cash generation' : value >= 10 ? 'Moderate cash generation' : 'Weak cash generation',
        'pretax_profit_margin': value >= 20 ? 'Excellent pre-tax profitability' : value >= 10 ? 'Good profitability' : value >= 5 ? 'Moderate profitability' : 'Low profitability',
        'return_on_tangible_assets': value >= 10 ? 'Excellent tangible asset efficiency' : value >= 5 ? 'Good efficiency' : value >= 2 ? 'Moderate efficiency' : 'Poor efficiency',
        'return_on_total_capital': value >= 12 ? 'Excellent capital returns' : value >= 8 ? 'Good returns' : value >= 4 ? 'Moderate returns' : 'Poor returns',

        // Liquidity Ratios
        'current_ratio': value >= 2 ? 'Strong liquidity' : value >= 1.5 ? 'Good liquidity' : value >= 1 ? 'Adequate liquidity' : 'Poor liquidity',
        'quick_ratio': value >= 1 ? 'Strong short-term liquidity' : value >= 0.5 ? 'Adequate liquidity' : 'Weak liquidity',
        'cash_ratio': value >= 0.5 ? 'Strong cash position' : value >= 0.2 ? 'Good cash position' : value >= 0.1 ? 'Moderate cash position' : 'Weak cash position',
        'operating_cash_flow_ratio': value >= 0.4 ? 'Strong cash flow coverage' : value >= 0.2 ? 'Good coverage' : value >= 0.1 ? 'Moderate coverage' : 'Weak coverage',
        'working_capital_ratio': value >= 0.2 ? 'Strong working capital efficiency' : value >= 0.1 ? 'Good efficiency' : value >= 0 ? 'Adequate efficiency' : 'Poor efficiency',

        // Leverage Ratios
        'debt_to_equity': value <= 0.3 ? 'Conservative debt level' : value <= 0.6 ? 'Moderate debt level' : value <= 1 ? 'High debt level' : 'Very high debt level',
        'debt_to_assets': value <= 0.3 ? 'Low debt burden' : value <= 0.5 ? 'Moderate debt burden' : value <= 0.7 ? 'High debt burden' : 'Very high debt burden',
        'debt_to_capital': value <= 0.3 ? 'Conservative capital structure' : value <= 0.5 ? 'Moderate leverage' : value <= 0.7 ? 'High leverage' : 'Very high leverage',
        'long_term_debt_to_equity': value <= 0.4 ? 'Conservative long-term debt' : value <= 0.8 ? 'Moderate debt' : value <= 1.2 ? 'High debt' : 'Very high debt',
        'long_term_debt_to_assets': value <= 0.2 ? 'Low long-term debt burden' : value <= 0.4 ? 'Moderate burden' : value <= 0.6 ? 'High burden' : 'Very high burden',
        'interest_coverage': value >= 10 ? 'Excellent coverage' : value >= 5 ? 'Good coverage' : value >= 2.5 ? 'Adequate coverage' : 'Poor coverage',
        'ebitda_interest_coverage': value >= 15 ? 'Excellent EBITDA coverage' : value >= 8 ? 'Good coverage' : value >= 4 ? 'Adequate coverage' : 'Poor coverage',
        'cash_coverage_ratio': value >= 5 ? 'Excellent cash coverage' : value >= 2.5 ? 'Good coverage' : value >= 1.5 ? 'Adequate coverage' : 'Poor coverage',
        'financial_leverage': value <= 2 ? 'Conservative leverage' : value <= 3 ? 'Moderate leverage' : value <= 5 ? 'High leverage' : 'Very high leverage',
        'net_debt_to_ebitda': value <= 2 ? 'Conservative debt level' : value <= 4 ? 'Moderate debt' : value <= 6 ? 'High debt' : 'Very high debt',

        // Valuation Ratios
        'pe_ratio': value <= 15 ? 'Potentially undervalued' : value <= 25 ? 'Fairly valued' : value <= 35 ? 'Potentially overvalued' : 'Highly overvalued',
        'forward_pe': value <= 12 ? 'Attractive forward valuation' : value <= 20 ? 'Fair forward valuation' : value <= 30 ? 'High forward valuation' : 'Very high forward valuation',
        'peg_ratio': value <= 1 ? 'Attractive growth valuation' : value <= 1.5 ? 'Fair growth valuation' : value <= 2 ? 'High growth valuation' : 'Very high growth valuation',
        'price_to_book': value <= 1 ? 'Potentially undervalued' : value <= 3 ? 'Fairly valued' : value <= 5 ? 'Potentially overvalued' : 'Highly overvalued',
        'price_to_tangible_book': value <= 1.5 ? 'Attractive tangible book valuation' : value <= 4 ? 'Fair valuation' : value <= 8 ? 'High valuation' : 'Very high valuation',
        'price_to_sales': value <= 2 ? 'Attractive valuation' : value <= 5 ? 'Fair valuation' : value <= 10 ? 'High valuation' : 'Very high valuation',
        'price_to_free_cash_flow': value <= 15 ? 'Attractive FCF valuation' : value <= 25 ? 'Fair FCF valuation' : value <= 40 ? 'High FCF valuation' : 'Very high FCF valuation',
        'price_to_operating_cash_flow': value <= 12 ? 'Attractive OCF valuation' : value <= 20 ? 'Fair OCF valuation' : value <= 35 ? 'High OCF valuation' : 'Very high OCF valuation',
        'ev_to_revenue': value <= 3 ? 'Attractive EV/Revenue' : value <= 6 ? 'Fair EV/Revenue' : value <= 12 ? 'High EV/Revenue' : 'Very high EV/Revenue',
        'ev_to_ebitda': value <= 10 ? 'Attractive EV/EBITDA' : value <= 15 ? 'Fair EV/EBITDA' : value <= 25 ? 'High EV/EBITDA' : 'Very high EV/EBITDA',
        'ev_to_ebit': value <= 12 ? 'Attractive EV/EBIT' : value <= 18 ? 'Fair EV/EBIT' : value <= 30 ? 'High EV/EBIT' : 'Very high EV/EBIT',
        'ev_to_fcf': value <= 15 ? 'Attractive EV/FCF' : value <= 25 ? 'Fair EV/FCF' : value <= 40 ? 'High EV/FCF' : 'Very high EV/FCF',
        'earnings_yield': percentValue >= 8 ? 'High earnings yield' : percentValue >= 5 ? 'Good earnings yield' : percentValue >= 3 ? 'Moderate earnings yield' : 'Low earnings yield',
        'fcf_yield': percentValue >= 8 ? 'High FCF yield' : percentValue >= 5 ? 'Good FCF yield' : percentValue >= 3 ? 'Moderate FCF yield' : 'Low FCF yield',

        // Activity/Efficiency Ratios
        'asset_turnover': value >= 1.5 ? 'Excellent asset efficiency' : value >= 1 ? 'Good efficiency' : value >= 0.5 ? 'Moderate efficiency' : 'Poor efficiency',
        'fixed_asset_turnover': value >= 4 ? 'Excellent fixed asset efficiency' : value >= 2 ? 'Good efficiency' : value >= 1 ? 'Moderate efficiency' : 'Poor efficiency',
        'inventory_turnover': value >= 12 ? 'Excellent inventory management' : value >= 6 ? 'Good management' : value >= 3 ? 'Moderate management' : 'Poor management',
        'receivables_turnover': value >= 12 ? 'Excellent collections' : value >= 8 ? 'Good collections' : value >= 4 ? 'Moderate collections' : 'Poor collections',
        'payables_turnover': value >= 8 ? 'Fast supplier payments' : value >= 4 ? 'Moderate payment speed' : value >= 2 ? 'Slow payments' : 'Very slow payments',
        'days_sales_outstanding': value <= 30 ? 'Excellent collections' : value <= 45 ? 'Good collections' : value <= 60 ? 'Moderate collections' : 'Slow collections',
        'days_inventory_outstanding': value <= 30 ? 'Excellent inventory turnover' : value <= 60 ? 'Good turnover' : value <= 90 ? 'Moderate turnover' : 'Slow turnover',
        'days_payables_outstanding': value >= 45 ? 'Good payment terms' : value >= 30 ? 'Moderate terms' : value >= 15 ? 'Fast payments' : 'Very fast payments',
        'cash_conversion_cycle': value <= 30 ? 'Excellent cash conversion' : value <= 60 ? 'Good conversion' : value <= 90 ? 'Moderate conversion' : 'Slow conversion',
        'operating_cycle': value <= 60 ? 'Efficient operating cycle' : value <= 90 ? 'Moderate cycle' : value <= 120 ? 'Slow cycle' : 'Very slow cycle',

        // Dividend & Shareholder Ratios
        'dividend_yield': value >= 4 ? 'High dividend yield' : value >= 2 ? 'Moderate dividend yield' : value >= 1 ? 'Low dividend yield' : 'No dividend',
        'dividend_payout_ratio': value <= 40 ? 'Conservative payout' : value <= 60 ? 'Moderate payout' : value <= 80 ? 'High payout' : 'Very high payout',
        'dividend_coverage_ratio': value >= 3 ? 'Strong dividend coverage' : value >= 2 ? 'Good coverage' : value >= 1.5 ? 'Adequate coverage' : 'Weak coverage',
        'retention_ratio': value >= 0.7 ? 'High earnings retention' : value >= 0.5 ? 'Moderate retention' : value >= 0.3 ? 'Low retention' : 'Very low retention',
        'buyback_yield': percentValue >= 3 ? 'High buyback activity' : percentValue >= 1 ? 'Moderate buybacks' : percentValue >= 0.5 ? 'Low buybacks' : 'No buybacks',
        'shareholder_yield': percentValue >= 5 ? 'High total shareholder yield' : percentValue >= 3 ? 'Good yield' : percentValue >= 1 ? 'Moderate yield' : 'Low yield',

        // Per Share Metrics (interpretations based on growth and consistency)
        'earnings_per_share': value > 0 ? 'Profitable' : 'Unprofitable',
        'diluted_eps': value > 0 ? 'Profitable (diluted)' : 'Unprofitable (diluted)',
        'book_value_per_share': value > 0 ? 'Positive book value' : 'Negative book value',
        'tangible_book_value_per_share': value > 0 ? 'Positive tangible book value' : 'Negative tangible book value',
        'revenue_per_share': value > 0 ? 'Revenue generating' : 'No revenue',
        'cash_per_share': value >= 5 ? 'Strong cash position' : value >= 2 ? 'Good cash position' : value >= 1 ? 'Moderate cash' : 'Low cash',
        'fcf_per_share': value > 0 ? 'Positive free cash flow' : 'Negative free cash flow',
        'ocf_per_share': value > 0 ? 'Positive operating cash flow' : 'Negative operating cash flow'
    };

    return interpretations[metricName] || 'Analysis not available';
}

/**
 * Show error message for comprehensive metrics
 */
function showComprehensiveMetricsError(message) {
    const error = document.getElementById('comprehensive-metrics-error');
    if (error) {
        error.style.display = 'block';
        const errorSpan = error.querySelector('span');
        if (errorSpan) {
            errorSpan.textContent = message;
        }
    }
}

/**
 * Generate portfolio summary based on comprehensive metrics
 */
function generatePortfolioSummary(metrics) {
    const overallStrength = document.getElementById('overall-strength');
    const riskLevel = document.getElementById('risk-level');
    const growthPotential = document.getElementById('growth-potential');

    // Calculate overall financial strength score
    let strengthScore = 0;
    let strengthCount = 0;

    // Profitability metrics (weight: 40%)
    const profitabilityMetrics = ['roe', 'roa', 'roic', 'net_profit_margin'];
    profitabilityMetrics.forEach(metric => {
        if (metrics[metric]) {
            strengthScore += normalizeMetricScore(metric, metrics[metric].value) * 0.4 / profitabilityMetrics.length;
            strengthCount++;
        }
    });

    // Liquidity metrics (weight: 30%)
    const liquidityMetrics = ['current_ratio', 'quick_ratio'];
    liquidityMetrics.forEach(metric => {
        if (metrics[metric]) {
            strengthScore += normalizeMetricScore(metric, metrics[metric].value) * 0.3 / liquidityMetrics.length;
            strengthCount++;
        }
    });

    // Leverage metrics (weight: 30%)
    const leverageMetrics = ['debt_to_equity', 'interest_coverage'];
    leverageMetrics.forEach(metric => {
        if (metrics[metric]) {
            strengthScore += normalizeMetricScore(metric, metrics[metric].value) * 0.3 / leverageMetrics.length;
            strengthCount++;
        }
    });

    // Display results
    if (strengthCount > 0) {
        const strengthText = strengthScore >= 0.8 ? 'Excellent' :
                           strengthScore >= 0.6 ? 'Good' :
                           strengthScore >= 0.4 ? 'Moderate' : 'Weak';
        if (overallStrength) {
            overallStrength.textContent = strengthText;
            overallStrength.style.color = getScoreColor(strengthScore);
        }
    }

    // Calculate risk level
    let riskScore = 0;
    if (metrics.debt_to_equity) {
        riskScore += metrics.debt_to_equity.value > 1 ? 0.4 : metrics.debt_to_equity.value > 0.5 ? 0.2 : 0;
    }
    if (metrics.current_ratio) {
        riskScore += metrics.current_ratio.value < 1 ? 0.3 : metrics.current_ratio.value < 1.5 ? 0.1 : 0;
    }
    if (metrics.interest_coverage) {
        riskScore += metrics.interest_coverage.value < 2 ? 0.3 : metrics.interest_coverage.value < 5 ? 0.1 : 0;
    }

    const riskText = riskScore >= 0.6 ? 'High' : riskScore >= 0.3 ? 'Moderate' : 'Low';
    if (riskLevel) {
        riskLevel.textContent = riskText;
        riskLevel.style.color = riskScore >= 0.6 ? 'var(--negative-color)' :
                               riskScore >= 0.3 ? 'var(--warning-color)' : 'var(--positive-color)';
    }

    // Calculate growth potential
    let growthScore = 0;
    if (metrics.roe && metrics.roe.value > 15) growthScore += 0.3;
    if (metrics.roic && metrics.roic.value > 10) growthScore += 0.3;
    if (metrics.asset_turnover && metrics.asset_turnover.value > 1) growthScore += 0.2;
    if (metrics.debt_to_equity && metrics.debt_to_equity.value < 0.5) growthScore += 0.2;

    const growthText = growthScore >= 0.7 ? 'High' : growthScore >= 0.4 ? 'Moderate' : 'Low';
    if (growthPotential) {
        growthPotential.textContent = growthText;
        growthPotential.style.color = getScoreColor(growthScore);
    }
}

/**
 * Normalize metric score to 0-1 range
 */
function normalizeMetricScore(metricName, value) {
    if (value === null || value === undefined) return 0;

    const scoreRanges = {
        'roe': { min: 0, max: 25, reverse: false },
        'roa': { min: 0, max: 10, reverse: false },
        'roic': { min: 0, max: 20, reverse: false },
        'net_profit_margin': { min: 0, max: 20, reverse: false },
        'current_ratio': { min: 0.5, max: 3, reverse: false },
        'quick_ratio': { min: 0.2, max: 2, reverse: false },
        'debt_to_equity': { min: 0, max: 2, reverse: true },
        'interest_coverage': { min: 1, max: 10, reverse: false }
    };

    const range = scoreRanges[metricName];
    if (!range) return 0.5; // Default neutral score

    let normalizedScore = (value - range.min) / (range.max - range.min);
    normalizedScore = Math.max(0, Math.min(1, normalizedScore));

    return range.reverse ? 1 - normalizedScore : normalizedScore;
}

/**
 * Get color based on score (0-1 range)
 */
function getScoreColor(score) {
    if (score >= 0.7) return 'var(--positive-color)';
    if (score >= 0.4) return 'var(--warning-color)';
    return 'var(--negative-color)';
}

/**
 * Initialize comprehensive metrics when page loads
 */
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the portfolio page
    if (document.getElementById('comprehensive-metrics-grid')) {
        // Load comprehensive metrics automatically
        loadComprehensiveMetrics();

        // Set up refresh button
        const refreshBtn = document.getElementById('refreshComprehensiveMetrics');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', loadComprehensiveMetrics);
        }

        // Set up toggle view button
        const toggleBtn = document.getElementById('toggleComprehensiveView');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', function() {
                const grid = document.getElementById('comprehensive-metrics-grid');
                const summary = document.getElementById('comprehensive-metrics-summary');

                if (grid && summary) {
                    const isGridVisible = grid.style.display !== 'none';
                    grid.style.display = isGridVisible ? 'none' : 'grid';
                    summary.style.display = isGridVisible ? 'block' : 'none';

                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = isGridVisible ? 'fas fa-compress-alt' : 'fas fa-expand-alt';
                    }
                }
            });
        }
    }
});
