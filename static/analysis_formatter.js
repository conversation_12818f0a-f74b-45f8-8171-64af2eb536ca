/* static/analysis_formatter.js */
/* Advanced text processing and formatting for stock analysis */

/**
 * Main Analysis Formatter Class
 */
class AnalysisFormatter {
    constructor() {
        this.placeholderPatterns = [
            /\+\s*generate\w+HTML\(\)\s*\+/gi,
            /\+\s*\w+Chart\w*\(\)\s*\+/gi,
            /\{\{\s*\w+\s*\}\}/g,
            /\$\{\s*\w+\s*\}/g,
            /<script[^>]*>[\s\S]*?<\/script>/gi,
            /function\s+\w+\([^)]*\)\s*\{[^}]*\}/gi
        ];

        this.inconsistencyPatterns = {
            overallRating: /overall\s+rating:?\s*(\d+(?:\.\d+)?)\s*%?/gi,
            valuationScore: /valuation\s+(?:score|rating):?\s*(\d+(?:\.\d+)?)\s*%?/gi,
            businessQuality: /business\s+(?:quality|moat):?\s*(\d+(?:\.\d+)?)\s*%?/gi,
            financialHealth: /financial\s+(?:health|strength):?\s*(\d+(?:\.\d+)?)\s*%?/gi,
            managementQuality: /management\s+(?:quality|score):?\s*(\d+(?:\.\d+)?)\s*%?/gi
        };

        this.sectionHeaders = {
            'overall': 'Overall Investment Assessment',
            'valuation': 'Valuation Analysis',
            'financial': 'Financial Health Evaluation',
            'business': 'Business Quality & Competitive Moat',
            'management': 'Management Quality Assessment',
            'risks': 'Risk Analysis',
            'recommendation': 'Investment Recommendation',
            'dcf': 'Discounted Cash Flow Analysis',
            'peer': 'Peer Comparison Analysis'
        };

        this.scoreDescriptions = {
            90: 'Exceptional - Outstanding performance across all metrics',
            80: 'Excellent - Strong fundamentals with minimal concerns',
            70: 'Good - Solid investment with manageable risks',
            60: 'Average - Mixed signals requiring careful consideration',
            50: 'Below Average - Significant concerns present',
            40: 'Poor - Multiple red flags identified',
            30: 'Very Poor - High risk investment'
        };
    }

    /**
     * Main formatting function
     */
    formatAnalysis(rawText, metadata = {}) {
        console.log('[Formatter] Processing analysis text...');

        let formatted = rawText;

        // Step 1: Remove placeholders and artifacts
        formatted = this.removePlaceholders(formatted);

        // Step 2: Fix structural issues
        formatted = this.fixStructuralIssues(formatted);

        // Step 3: Ensure data consistency
        formatted = this.ensureDataConsistency(formatted, metadata);

        // Step 4: Improve section organization
        formatted = this.organizeSections(formatted);

        // Step 5: Enhance language and style
        formatted = this.improveLanguageStyle(formatted);

        // Step 6: Add visual elements
        formatted = this.addVisualEnhancements(formatted, metadata);

        console.log('[Formatter] Analysis formatting complete');
        return formatted;
    }

    /**
     * Remove placeholder artifacts and broken code
     */
    removePlaceholders(text) {
        let cleaned = text;

        // Remove code placeholders
        this.placeholderPatterns.forEach(pattern => {
            cleaned = cleaned.replace(pattern, '');
        });

        // Remove broken concatenation artifacts
        cleaned = cleaned.replace(/\s*\+\s*'/g, ' ');
        cleaned = cleaned.replace(/'\s*\+\s*/g, ' ');
        cleaned = cleaned.replace(/\s*\+\s*"/g, ' ');
        cleaned = cleaned.replace(/"\s*\+\s*/g, ' ');

        // Clean up extra whitespace
        cleaned = cleaned.replace(/\s{3,}/g, '  ');
        cleaned = cleaned.replace(/\n{3,}/g, '\n\n');

        return cleaned.trim();
    }

    /**
     * Fix structural and formatting issues
     */
    fixStructuralIssues(text) {
        let fixed = text;

        // Consolidate redundant headings
        fixed = this.consolidateHeadings(fixed);

        // Fix punctuation issues
        fixed = fixed.replace(/\s+([,.!?])/g, '$1');
        fixed = fixed.replace(/([,.!?])\s*([,.!?])/g, '$1$2');

        // Fix paragraph breaks
        fixed = fixed.replace(/\.\s*([A-Z])/g, '. $1');

        // Clean up bullet points
        fixed = fixed.replace(/^\s*[-•*]\s*/gm, '• ');

        return fixed;
    }

    /**
     * Consolidate redundant section headings
     */
    consolidateHeadings(text) {
        const headingGroups = {};
        const lines = text.split('\n');
        const processedLines = [];

        lines.forEach(line => {
            const trimmed = line.trim();

            // Check if line is a heading
            if (this.isHeading(trimmed)) {
                const normalized = this.normalizeHeading(trimmed);

                if (!headingGroups[normalized]) {
                    headingGroups[normalized] = true;
                    processedLines.push(line);
                }
                // Skip duplicate headings
            } else {
                processedLines.push(line);
            }
        });

        return processedLines.join('\n');
    }

    /**
     * Check if a line is a heading
     */
    isHeading(line) {
        return /^#{1,6}\s+/.test(line) ||
               /^[A-Z][^.!?]*:?\s*$/.test(line) ||
               /^(Overall|Valuation|Financial|Business|Management|Risk|Recommendation)/i.test(line);
    }

    /**
     * Normalize heading text for comparison
     */
    normalizeHeading(heading) {
        return heading.toLowerCase()
                     .replace(/^#{1,6}\s+/, '')
                     .replace(/[^a-z0-9\s]/g, '')
                     .replace(/\s+/g, ' ')
                     .trim();
    }

    /**
     * Ensure data consistency across the analysis
     */
    ensureDataConsistency(text, metadata) {
        const scores = this.extractScores(text);
        const consistent = this.validateScoreConsistency(scores);

        if (!consistent.isValid) {
            console.warn('[Formatter] Score inconsistencies detected:', consistent.issues);
            text = this.fixScoreInconsistencies(text, scores, consistent.issues);
        }

        return text;
    }

    /**
     * Extract all numeric scores from the text
     */
    extractScores(text) {
        const scores = {};

        Object.entries(this.inconsistencyPatterns).forEach(([key, pattern]) => {
            const matches = [...text.matchAll(pattern)];
            if (matches.length > 0) {
                scores[key] = matches.map(m => parseFloat(m[1]));
            }
        });

        return scores;
    }

    /**
     * Validate score consistency
     */
    validateScoreConsistency(scores) {
        const issues = [];

        Object.entries(scores).forEach(([metric, values]) => {
            if (values.length > 1) {
                const unique = [...new Set(values)];
                if (unique.length > 1) {
                    issues.push({
                        metric,
                        values: unique,
                        message: `Inconsistent ${metric} scores: ${unique.join(', ')}`
                    });
                }
            }
        });

        return {
            isValid: issues.length === 0,
            issues
        };
    }

    /**
     * Fix score inconsistencies by using the most frequently mentioned value
     */
    fixScoreInconsistencies(text, scores, issues) {
        let fixed = text;

        issues.forEach(issue => {
            const pattern = this.inconsistencyPatterns[issue.metric];
            const mostCommon = this.getMostCommonValue(scores[issue.metric]);

            fixed = fixed.replace(pattern, (match, score) => {
                return match.replace(score, mostCommon.toString());
            });
        });

        return fixed;
    }

    /**
     * Get the most frequently occurring value in an array
     */
    getMostCommonValue(values) {
        const frequency = {};
        values.forEach(val => {
            frequency[val] = (frequency[val] || 0) + 1;
        });

        return Object.keys(frequency).reduce((a, b) =>
            frequency[a] > frequency[b] ? a : b
        );
    }

    /**
     * Organize content into proper sections
     */
    organizeSections(text) {
        const sections = this.identifySections(text);
        const organized = this.restructureSections(sections);

        return this.generateFormattedOutput(organized);
    }

    /**
     * Identify distinct sections in the analysis
     */
    identifySections(text) {
        const sections = {};
        const lines = text.split('\n');
        let currentSection = 'introduction';
        let currentContent = [];

        lines.forEach(line => {
            const trimmed = line.trim();

            if (this.isHeading(trimmed)) {
                // Save previous section
                if (currentContent.length > 0) {
                    sections[currentSection] = currentContent.join('\n').trim();
                }

                // Start new section
                currentSection = this.categorizeSectionHeading(trimmed);
                currentContent = [line];
            } else {
                currentContent.push(line);
            }
        });

        // Save final section
        if (currentContent.length > 0) {
            sections[currentSection] = currentContent.join('\n').trim();
        }

        return sections;
    }

    /**
     * Categorize section headings
     */
    categorizeSectionHeading(heading) {
        const normalized = heading.toLowerCase();

        if (/overall|summary|conclusion/i.test(normalized)) return 'overall';
        if (/valuation|price|dcf|intrinsic/i.test(normalized)) return 'valuation';
        if (/financial|balance|income|cash/i.test(normalized)) return 'financial';
        if (/business|moat|competitive|quality/i.test(normalized)) return 'business';
        if (/management|leadership|governance/i.test(normalized)) return 'management';
        if (/risk|concern|challenge/i.test(normalized)) return 'risks';
        if (/recommendation|verdict|decision/i.test(normalized)) return 'recommendation';
        if (/peer|comparison|competitor/i.test(normalized)) return 'peer';

        return 'other';
    }

    /**
     * Restructure sections in logical order
     */
    restructureSections(sections) {
        const sectionOrder = [
            'introduction', 'overall', 'business', 'financial',
            'valuation', 'management', 'peer', 'risks', 'recommendation'
        ];

        const restructured = {};

        sectionOrder.forEach(key => {
            if (sections[key]) {
                restructured[key] = sections[key];
            }
        });

        // Add any remaining sections
        Object.keys(sections).forEach(key => {
            if (!restructured[key]) {
                restructured[key] = sections[key];
            }
        });

        return restructured;
    }

    /**
     * Generate formatted output from organized sections
     */
    generateFormattedOutput(sections) {
        let output = [];

        Object.entries(sections).forEach(([sectionKey, content]) => {
            if (content && content.trim().length > 0) {
                // Add standardized section header if needed
                if (this.sectionHeaders[sectionKey] && !content.includes(this.sectionHeaders[sectionKey])) {
                    output.push(`\n## ${this.sectionHeaders[sectionKey]}\n`);
                }

                output.push(content);
                output.push('\n');
            }
        });

        return output.join('\n').replace(/\n{3,}/g, '\n\n');
    }

    /**
     * Improve language and style
     */
    improveLanguageStyle(text) {
        let improved = text;

        // Fix passive voice and wordiness
        const passivePatterns = [
            { pattern: /this analysis attempts to/gi, replacement: 'This analysis' },
            { pattern: /it should be noted that/gi, replacement: '' },
            { pattern: /it is important to mention/gi, replacement: 'Notably,' },
            { pattern: /the valuation may be fair but not compelling/gi, replacement: 'The valuation appears fair but lacks compelling upside' }
        ];

        passivePatterns.forEach(({pattern, replacement}) => {
            improved = improved.replace(pattern, replacement);
        });

        // Improve clarity of recommendations
        improved = this.clarifyRecommendations(improved);

        // Add specific context where missing
        improved = this.addMissingContext(improved);

        return improved;
    }

    /**
     * Clarify vague recommendations
     */
    clarifyRecommendations(text) {
        let clarified = text;

        // Replace vague phrases with specific guidance
        const clarifications = [
            {
                pattern: /may be worth considering/gi,
                replacement: 'warrants further analysis'
            },
            {
                pattern: /could be interesting/gi,
                replacement: 'shows potential but requires careful evaluation'
            },
            {
                pattern: /might be a good investment/gi,
                replacement: 'demonstrates solid investment characteristics'
            }
        ];

        clarifications.forEach(({pattern, replacement}) => {
            clarified = clarified.replace(pattern, replacement);
        });

        return clarified;
    }

    /**
     * Add missing context for better understanding
     */
    addMissingContext(text) {
        let enhanced = text;

        // Add context for financial ratios
        enhanced = enhanced.replace(
            /P\/E ratio of ([\d.]+)/gi,
            'P/E ratio of $1 (compared to industry average)'
        );

        // Add context for growth rates
        enhanced = enhanced.replace(
            /growth rate of ([\d.]+)%/gi,
            'annual growth rate of $1% over the next 5 years'
        );

        return enhanced;
    }

    /**
     * Add visual enhancements and formatting
     */
    addVisualEnhancements(text, metadata) {
        let enhanced = text;

        // Add score context
        enhanced = this.addScoreContext(enhanced);

        // Add emphasis for key points
        enhanced = this.addEmphasis(enhanced);

        // Add summary tables where appropriate
        enhanced = this.addSummaryElements(enhanced, metadata);

        return enhanced;
    }

    /**
     * Add context to numeric scores
     */
    addScoreContext(text) {
        let contextual = text;

        // Add descriptive context to scores
        Object.entries(this.scoreDescriptions).forEach(([threshold, description]) => {
            const pattern = new RegExp(`(\\d+)%(?=\\s|\\.|,|$)`, 'g');

            contextual = contextual.replace(pattern, (match, score) => {
                const numScore = parseInt(score);

                if (numScore >= threshold) {
                    const contextDesc = this.getScoreDescription(numScore);
                    return `${score}% (${contextDesc})`;
                }

                return match;
            });
        });

        return contextual;
    }

    /**
     * Get appropriate description for a score
     */
    getScoreDescription(score) {
        if (score >= 90) return 'Exceptional';
        if (score >= 80) return 'Excellent';
        if (score >= 70) return 'Good';
        if (score >= 60) return 'Average';
        if (score >= 50) return 'Below Average';
        if (score >= 40) return 'Poor';
        return 'Very Poor';
    }

    /**
     * Add emphasis to key points
     */
    addEmphasis(text) {
        let emphasized = text;

        // Bold key financial terms
        const keyTerms = [
            'Strong Buy', 'Buy', 'Hold', 'Sell', 'Strong Sell',
            'Undervalued', 'Overvalued', 'Fair Value',
            'High Risk', 'Low Risk', 'Medium Risk',
            'Excellent', 'Good', 'Poor', 'Outstanding'
        ];

        keyTerms.forEach(term => {
            const pattern = new RegExp(`\\b(${term})\\b`, 'gi');
            emphasized = emphasized.replace(pattern, '**$1**');
        });

        return emphasized;
    }

    /**
     * Add summary elements like tables and lists
     */
    addSummaryElements(text, metadata) {
        let enhanced = text;

        // Add ratings summary table if scores are present
        const scores = this.extractScores(text);
        if (Object.keys(scores).length > 0) {
            const summaryTable = this.generateRatingsSummaryTable(scores);
            enhanced = summaryTable + '\n\n' + enhanced;
        }

        // Add key metrics summary
        const metrics = this.extractKeyMetrics(text);
        if (metrics.length > 0) {
            const metricsTable = this.generateMetricsTable(metrics);
            enhanced = enhanced + '\n\n' + metricsTable;
        }

        return enhanced;
    }

    /**
     * Generate ratings summary table
     */
    generateRatingsSummaryTable(scores) {
        let table = '\n## Investment Rating Summary\n\n';
        table += '| Metric | Score | Assessment |\n';
        table += '|--------|-------|------------|\n';

        Object.entries(scores).forEach(([metric, values]) => {
            const score = values[0]; // Use first (or most common) value
            const assessment = this.getScoreDescription(score);
            const displayName = this.formatMetricName(metric);

            table += `| ${displayName} | ${score}% | ${assessment} |\n`;
        });

        return table;
    }

    /**
     * Format metric names for display
     */
    formatMetricName(metric) {
        const names = {
            'overallRating': 'Overall Rating',
            'valuationScore': 'Valuation',
            'businessQuality': 'Business Quality',
            'financialHealth': 'Financial Health',
            'managementQuality': 'Management Quality'
        };

        return names[metric] || metric;
    }

    /**
     * Extract key financial metrics from text
     */
    extractKeyMetrics(text) {
        const metrics = [];
        const patterns = {
            'P/E Ratio': /P\/E.*?(\d+\.?\d*)/gi,
            'Revenue Growth': /revenue.*?growth.*?(\d+\.?\d*)%/gi,
            'ROE': /ROE.*?(\d+\.?\d*)%/gi,
            'Debt/Equity': /debt.*?equity.*?(\d+\.?\d*)/gi,
            'Current Ratio': /current.*?ratio.*?(\d+\.?\d*)/gi
        };

        Object.entries(patterns).forEach(([name, pattern]) => {
            const matches = [...text.matchAll(pattern)];
            if (matches.length > 0) {
                metrics.push({
                    name,
                    value: matches[0][1]
                });
            }
        });

        return metrics;
    }

    /**
     * Generate key metrics table
     */
    generateMetricsTable(metrics) {
        let table = '\n## Key Financial Metrics\n\n';
        table += '| Metric | Value |\n';
        table += '|--------|-------|\n';

        metrics.forEach(({name, value}) => {
            table += `| ${name} | ${value} |\n`;
        });

        return table;
    }

    /**
     * Validate the formatted analysis
     */
    validateFormatting(formattedText) {
        const issues = [];

        // Check for remaining placeholders
        if (this.hasPlaceholders(formattedText)) {
            issues.push('Contains unprocessed placeholders');
        }

        // Check for score consistency
        const scores = this.extractScores(formattedText);
        const consistency = this.validateScoreConsistency(scores);
        if (!consistency.isValid) {
            issues.push('Score inconsistencies remain');
        }

        // Check for proper section structure
        if (!this.hasProperStructure(formattedText)) {
            issues.push('Lacks proper section structure');
        }

        return {
            isValid: issues.length === 0,
            issues
        };
    }

    /**
     * Check if text still contains placeholders
     */
    hasPlaceholders(text) {
        return this.placeholderPatterns.some(pattern => pattern.test(text));
    }

    /**
     * Check if text has proper section structure
     */
    hasProperStructure(text) {
        const hasHeaders = /^#{1,6}\s+/gm.test(text);
        const hasSections = Object.keys(this.sectionHeaders).some(key =>
            new RegExp(this.sectionHeaders[key], 'i').test(text)
        );

        return hasHeaders || hasSections;
    }
}

/**
 * Utility function to format analysis text
 */
function formatStockAnalysis(rawText, metadata = {}) {
    const formatter = new AnalysisFormatter();
    const formatted = formatter.formatAnalysis(rawText, metadata);

    // Validate the result
    const validation = formatter.validateFormatting(formatted);
    if (!validation.isValid) {
        console.warn('[Formatter] Validation issues:', validation.issues);
    }

    return formatted;
}

/**
 * Utility function to clean text for chatbot display
 */
function cleanChatbotText(text) {
    const formatter = new AnalysisFormatter();

    // Quick cleaning for chatbot display
    let cleaned = formatter.removePlaceholders(text);
    cleaned = formatter.fixStructuralIssues(cleaned);
    cleaned = formatter.improveLanguageStyle(cleaned);

    return cleaned;
}

/**
 * Extract and format rating data from analysis text
 */
function extractRatingData(text) {
    const formatter = new AnalysisFormatter();
    const scores = formatter.extractScores(text);

    const ratingData = [];
    Object.entries(scores).forEach(([metric, values]) => {
        if (values.length > 0) {
            ratingData.push({
                label: formatter.formatMetricName(metric),
                value: values[0],
                maxValue: 100
            });
        }
    });

    return ratingData;
}

// Export for global use
window.AnalysisFormatter = AnalysisFormatter;
window.formatStockAnalysis = formatStockAnalysis;
window.cleanChatbotText = cleanChatbotText;
window.extractRatingData = extractRatingData;

console.log('[Formatter] Analysis formatting utilities loaded');
