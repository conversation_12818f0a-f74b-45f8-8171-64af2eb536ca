/**
 * Financial Visualizations Module using D3.js
 * 
 * This module provides interactive financial data visualizations for:
 * - Financial Statements (Balance Sheet, Income Statement, Cash Flow)
 * - Stock Sentiment Analysis
 * - Portfolio Tracking with Risk/Return Visualization
 * - Fair Value Estimation
 * - And more financial features
 */

// Financial Statements Visualization
function renderFinancialStatements(data, containerId, statementType) {
    const container = d3.select(`#${containerId}`);
    if (!container.node()) {
        console.error(`Container with ID ${containerId} not found`);
        return;
    }
    
    container.html(""); // Clear container
    
    if (!data || Object.keys(data).length === 0) {
        container.html("<div class='alert alert-warning'>No financial data available</div>");
        return;
    }
    
    // Check for error in response
    if (data.error) {
        container.html(`<div class='alert alert-danger'>Error: ${data.error}</div>`);
        return;
    }
    
    // Get the financial data based on statement type
    let financialData;
    let title;
    
    switch (statementType) {
        case 'balance_sheet':
        case 'balance-sheet':
            financialData = data.items;
            title = "Balance Sheet";
            break;
        case 'income_statement':
        case 'income-statement':
            financialData = data.items;
            title = "Income Statement";
            break;
        case 'cash_flow':
        case 'cash-flow':
            financialData = data.items;
            title = "Cash Flow Statement";
            break;
        default:
            container.html("<div class='alert alert-warning'>Invalid statement type</div>");
            return;
    }
    
    if (!financialData) {
        container.html("<div class='alert alert-warning'>No financial data available for this statement type</div>");
        return;
    }
    
    // Create header
    const headerDiv = container.append("div")
        .attr("class", "financial-header mb-4");
        
    headerDiv.append("h2")
        .attr("class", "financial-title")
        .text(`${title} - ${data.companyName || "Company"}`);
        
    // Create period toggle
    const toggleDiv = headerDiv.append("div")
        .attr("class", "btn-group mb-3")
        .attr("role", "group");
        
    toggleDiv.append("button")
        .attr("class", "btn btn-outline-primary period-btn active")
        .attr("data-period", "yearly")
        .text("Annual")
        .on("click", function() {
            d3.selectAll(".period-btn").classed("active", false);
            d3.select(this).classed("active", true);
            updateFinancialTable(financialData, tableDiv, "yearly");
        });
        
    toggleDiv.append("button")
        .attr("class", "btn btn-outline-primary period-btn")
        .attr("data-period", "quarterly")
        .text("Quarterly")
        .on("click", function() {
            d3.selectAll(".period-btn").classed("active", false);
            d3.select(this).classed("active", true);
            updateFinancialTable(financialData, tableDiv, "quarterly");
        });
    
    // Create table container
    const tableDiv = container.append("div")
        .attr("class", "financial-table-container");
        
    // Initial render with yearly data
    updateFinancialTable(financialData, tableDiv, "yearly");
    
    // Create visualization
    createFinancialVisualization(financialData, container, statementType);
}

function updateFinancialTable(financialData, container, periodType) {
    if (!financialData || Object.keys(financialData).length === 0) {
        container.html("<div class='alert alert-warning'>No financial data available</div>");
        return;
    }
    
    // Create table
    const table = container.append("table")
        .attr("class", "table table-striped table-hover financial-table");
    
    // Create header row
    const thead = table.append("thead");
    const headerRow = thead.append("tr");
    
    // Add item column header
    headerRow.append("th")
        .attr("class", "item-name")
        .text("Item");
    
    // Add date columns
    const dates = Object.keys(financialData[Object.keys(financialData)[0]].values);
    dates.forEach(date => {
        headerRow.append("th")
            .attr("class", "financial-value")
            .text(formatDate(date));
    });
    
    // Create table body
    const tbody = table.append("tbody");
    
    // Add rows for each financial item
    Object.entries(financialData).forEach(([key, item]) => {
        const row = tbody.append("tr");
        
        // Add item name
        row.append("td")
            .attr("class", "item-name")
            .text(item.displayName);
        
        // Add values for each date
        item.values.forEach(value => {
            row.append("td")
                .attr("class", "financial-value")
                .text(formatFinancialValue(value, true));
        });
    });
}

function createFinancialVisualization(financialData, container, statementType) {
    const visualizationDiv = container.append("div")
        .attr("class", "financial-visualization mt-4");
        
    visualizationDiv.append("h3")
        .attr("class", "visualization-title mb-3")
        .text("Visual Trends");
    
    // Create chart container
    const chartDiv = visualizationDiv.append("div")
        .attr("class", "chart-container")
        .style("height", "400px");
    
    // Define key metrics to visualize based on statement type
    let keyMetrics = [];
    switch (statementType) {
        case 'balance_sheet':
            keyMetrics = ['totalAssets', 'totalLiab', 'totalStockholderEquity'];
            break;
        case 'income_statement':
            keyMetrics = ['totalRevenue', 'grossProfit', 'netIncome'];
            break;
        case 'cash_flow':
            keyMetrics = ['totalCashFromOperatingActivities', 'capitalExpenditures', 'freeCashFlow'];
            break;
    }
    
    // Prepare data for visualization
    const chartData = prepareChartData(financialData, keyMetrics);
    
    // Create D3 line chart
    createLineChart(chartData, chartDiv.node(), keyMetrics);
}

function prepareChartData(financialData, metrics) {
    const chartData = [];
    
    // Use yearly data for visualization
    const yearlyData = financialData.yearly;
    
    if (!yearlyData) return chartData;
    
    // Get all dates sorted chronologically
    const dates = Object.keys(yearlyData).sort();
    
    dates.forEach(date => {
        const dataPoint = { date: new Date(date) };
        
        metrics.forEach(metric => {
            dataPoint[metric] = yearlyData[date][metric] || 0;
        });
        
        chartData.push(dataPoint);
    });
    
    return chartData;
}

function createLineChart(data, container, metrics) {
    // Clear container
    d3.select(container).html("");
    
    if (data.length === 0) {
        d3.select(container).html("<div class='alert alert-warning'>No data available for visualization</div>");
        return;
    }
    
    // Set dimensions and margins
    const margin = { top: 20, right: 80, bottom: 30, left: 50 };
    const width = container.clientWidth - margin.left - margin.right;
    const height = container.clientHeight - margin.top - margin.bottom;
    
    // Create SVG
    const svg = d3.select(container).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);
    
    // Define scales
    const x = d3.scaleTime()
        .domain(d3.extent(data, d => d.date))
        .range([0, width]);
    
    const y = d3.scaleLinear()
        .domain([0, d3.max(data, d => {
            return d3.max(metrics, metric => d[metric] || 0);
        })])
        .range([height, 0]);
    
    // Define color scale
    const color = d3.scaleOrdinal()
        .domain(metrics)
        .range(d3.schemeCategory10);
    
    // Create axes
    const xAxis = d3.axisBottom(x)
        .ticks(5)
        .tickFormat(d3.timeFormat("%Y"));
    
    const yAxis = d3.axisLeft(y)
        .tickFormat(d => formatCurrencyForAxis(d));
    
    // Add axes
    svg.append("g")
        .attr("class", "x-axis")
        .attr("transform", `translate(0,${height})`)
        .call(xAxis);
    
    svg.append("g")
        .attr("class", "y-axis")
        .call(yAxis);
    
    // Create line generator
    const line = d3.line()
        .defined(d => !isNaN(d[1]))
        .x(d => x(d[0]))
        .y(d => y(d[1]))
        .curve(d3.curveMonotoneX);
    
    // Add lines for each metric
    metrics.forEach(metric => {
        const metricData = data.map(d => [d.date, d[metric]]);
        
        svg.append("path")
            .datum(metricData.filter(d => line.defined()(d)))
            .attr("class", "line")
            .attr("d", line)
            .attr("fill", "none")
            .attr("stroke", color(metric))
            .attr("stroke-width", 2);
    });
    
    // Add legend
    const legend = svg.append("g")
        .attr("class", "legend")
        .attr("transform", `translate(${width + 10}, 0)`);
    
    metrics.forEach((metric, i) => {
        const legendRow = legend.append("g")
            .attr("transform", `translate(0, ${i * 20})`);
        
        legendRow.append("rect")
            .attr("width", 10)
            .attr("height", 10)
            .attr("fill", color(metric));
        
        legendRow.append("text")
            .attr("x", 15)
            .attr("y", 10)
            .attr("class", "legend-text")
            .text(formatItemName(metric));
    });
    
    // Add tooltip
    const tooltip = d3.select(container).append("div")
        .attr("class", "tooltip")
        .style("opacity", 0)
        .style("position", "absolute")
        .style("background-color", "white")
        .style("border", "1px solid #ddd")
        .style("border-radius", "4px")
        .style("padding", "10px")
        .style("pointer-events", "none");
    
    // Add tooltip functionality
    const bisect = d3.bisector(d => d.date).left;
    
    // Add focus circle for each line
    const focus = svg.append("g")
        .attr("class", "focus")
        .style("display", "none");
    
    metrics.forEach(metric => {
        focus.append("circle")
            .attr("class", `focus-circle-${metric}`)
            .attr("r", 4)
            .attr("fill", color(metric))
            .attr("stroke", "white")
            .attr("stroke-width", 2);
    });
    
    // Add overlay for mouse events
    svg.append("rect")
        .attr("class", "overlay")
        .attr("width", width)
        .attr("height", height)
        .style("opacity", 0)
        .on("mouseover", () => focus.style("display", null))
        .on("mouseout", () => {
            focus.style("display", "none");
            tooltip.style("opacity", 0);
        })
        .on("mousemove", mousemove);
    
    function mousemove(event) {
        const mouseX = d3.pointer(event)[0];
        const x0 = x.invert(mouseX);
        const i = bisect(data, x0, 1);
        const d0 = data[i - 1];
        const d1 = data[i];
        const d = x0 - d0.date > d1.date - x0 ? d1 : d0;
        
        // Update focus circles
        metrics.forEach(metric => {
            focus.select(`.focus-circle-${metric}`)
                .attr("transform", `translate(${x(d.date)},${y(d[metric])})`);
        });
        
        // Update tooltip
        tooltip.style("opacity", 0.9)
            .style("left", `${event.pageX + 10}px`)
            .style("top", `${event.pageY - 28}px`)
            .html(() => {
                let content = `<strong>${d3.timeFormat("%Y")(d.date)}</strong><br>`;
                metrics.forEach(metric => {
                    content += `${formatItemName(metric)}: ${formatCurrency(d[metric])}<br>`;
                });
                return content;
            });
    }
}

// Stock Sentiment Analysis Visualization
function renderSentimentAnalysis(data, containerId) {
    const container = d3.select(`#${containerId}`);
    container.html(""); // Clear container
    
    if (!data || (!data.sentiment && !data.news)) {
        container.html("<div class='alert alert-warning'>No sentiment data available</div>");
        return;
    }
    
    // Create header
    container.append("h2")
        .attr("class", "sentiment-title mb-4")
        .text("Stock Sentiment Analysis");
    
    // Create sentiment timeline chart
    if (data.sentiment) {
        createSentimentTimeline(data.sentiment, container);
    }
    
    // Create news sentiment section
    if (data.news && data.news.length > 0) {
        createNewsSentiment(data.news, container);
    }
}

function createSentimentTimeline(sentimentData, container) {
    const timelineDiv = container.append("div")
        .attr("class", "sentiment-timeline mb-4");
        
    timelineDiv.append("h3")
        .attr("class", "section-title mb-3")
        .text("Sentiment Timeline");
    
    // Create chart container
    const chartDiv = timelineDiv.append("div")
        .attr("class", "chart-container")
        .style("height", "300px");
    
    // Prepare data for chart
    const chartData = [];
    Object.entries(sentimentData).forEach(([date, sentiment]) => {
        chartData.push({
            date: new Date(date),
            sentiment: sentiment
        });
    });
    
    // Sort data by date
    chartData.sort((a, b) => a.date - b.date);
    
    if (chartData.length === 0) {
        chartDiv.html("<div class='alert alert-warning'>No timeline data available</div>");
        return;
    }
    
    // Create SVG
    const margin = { top: 20, right: 30, bottom: 30, left: 40 };
    const width = chartDiv.node().clientWidth - margin.left - margin.right;
    const height = chartDiv.node().clientHeight - margin.top - margin.bottom;
    
    const svg = chartDiv.append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);
    
    // Define scales
    const x = d3.scaleTime()
        .domain(d3.extent(chartData, d => d.date))
        .range([0, width]);
    
    const y = d3.scaleLinear()
        .domain([-1, 1])
        .range([height, 0]);
    
    // Create axes
    const xAxis = d3.axisBottom(x)
        .ticks(5)
        .tickFormat(d3.timeFormat("%b %d"));
    
    const yAxis = d3.axisLeft(y)
        .tickFormat(d => d === 0 ? "Neutral" : d > 0 ? "Positive" : "Negative");
    
    // Add axes
    svg.append("g")
        .attr("class", "x-axis")
        .attr("transform", `translate(0,${height})`)
        .call(xAxis);
    
    svg.append("g")
        .attr("class", "y-axis")
        .call(yAxis);
    
    // Create line
    const line = d3.line()
        .x(d => x(d.date))
        .y(d => y(d.sentiment))
        .curve(d3.curveMonotoneX);
    
    // Add line path
    svg.append("path")
        .datum(chartData)
        .attr("class", "sentiment-line")
        .attr("fill", "none")
        .attr("stroke", "steelblue")
        .attr("stroke-width", 2)
        .attr("d", line);
    
    // Add zero line
    svg.append("line")
        .attr("class", "zero-line")
        .attr("x1", 0)
        .attr("x2", width)
        .attr("y1", y(0))
        .attr("y2", y(0))
        .attr("stroke", "#999")
        .attr("stroke-dasharray", "4");
    
    // Add area fill
    const area = d3.area()
        .x(d => x(d.date))
        .y0(y(0))
        .y1(d => y(d.sentiment))
        .curve(d3.curveMonotoneX);
    
    svg.append("path")
        .datum(chartData)
        .attr("class", "sentiment-area")
        .attr("fill", "steelblue")
        .attr("fill-opacity", 0.3)
        .attr("d", area);
    
    // Add tooltip
    const tooltip = chartDiv.append("div")
        .attr("class", "tooltip")
        .style("opacity", 0)
        .style("position", "absolute")
        .style("background-color", "white")
        .style("border", "1px solid #ddd")
        .style("border-radius", "4px")
        .style("padding", "10px")
        .style("pointer-events", "none");
    
    // Add focus circle
    const focus = svg.append("g")
        .attr("class", "focus")
        .style("display", "none");
    
    focus.append("circle")
        .attr("r", 4)
        .attr("fill", "steelblue")
        .attr("stroke", "white")
        .attr("stroke-width", 2);
    
    // Add overlay for mouse events
    svg.append("rect")
        .attr("class", "overlay")
        .attr("width", width)
        .attr("height", height)
        .style("opacity", 0)
        .on("mouseover", () => focus.style("display", null))
        .on("mouseout", () => {
            focus.style("display", "none");
            tooltip.style("opacity", 0);
        })
        .on("mousemove", mousemove);
    
    function mousemove(event) {
        const bisect = d3.bisector(d => d.date).left;
        const mouseX = d3.pointer(event)[0];
        const x0 = x.invert(mouseX);
        const i = bisect(chartData, x0, 1);
        
        if (i >= chartData.length) return;
        
        const d0 = chartData[i - 1];
        const d1 = chartData[i];
        const d = x0 - d0.date > d1.date - x0 ? d1 : d0;
        
        focus.attr("transform", `translate(${x(d.date)},${y(d.sentiment)})`);
        
        const sentimentText = d.sentiment > 0.3 ? "Very Positive" :
                              d.sentiment > 0 ? "Positive" :
                              d.sentiment > -0.3 ? "Negative" : "Very Negative";
        
        tooltip.style("opacity", 0.9)
            .style("left", `${event.pageX + 10}px`)
            .style("top", `${event.pageY - 28}px`)
            .html(`
                <strong>${d3.timeFormat("%b %d, %Y")(d.date)}</strong><br>
                Sentiment: ${sentimentText} (${d.sentiment.toFixed(2)})
            `);
    }
    
    // Add summary
    const avgSentiment = d3.mean(chartData, d => d.sentiment);
    const sentimentTrend = chartData[chartData.length - 1].sentiment - chartData[0].sentiment;
    
    let summaryText = "Overall sentiment is ";
    if (avgSentiment > 0.3) summaryText += "very positive";
    else if (avgSentiment > 0) summaryText += "positive";
    else if (avgSentiment > -0.3) summaryText += "negative";
    else summaryText += "very negative";
    
    summaryText += " with a ";
    if (sentimentTrend > 0.1) summaryText += "strong upward";
    else if (sentimentTrend > 0) summaryText += "slight upward";
    else if (sentimentTrend > -0.1) summaryText += "slight downward";
    else summaryText += "strong downward";
    
    summaryText += " trend.";
    
    timelineDiv.append("p")
        .attr("class", "sentiment-summary mt-3")
        .text(summaryText);
}

function createNewsSentiment(newsData, container) {
    const newsDiv = container.append("div")
        .attr("class", "news-sentiment mt-4");
        
    newsDiv.append("h3")
        .attr("class", "section-title mb-3")
        .text("Recent News Sentiment");
    
    // Create news list
    const newsList = newsDiv.append("div")
        .attr("class", "news-list");
    
    newsData.slice(0, 5).forEach(news => {
        const sentiment = news.sentiment || { polarity: 0, neg: 0.5, neu: 0.5, pos: 0.5 };
        const sentimentClass = sentiment.polarity > 0.1 ? "positive" :
                               sentiment.polarity < -0.1 ? "negative" : "neutral";
        
        const newsItem = newsList.append("div")
            .attr("class", `news-item mb-3 p-3 border ${sentimentClass}`);
        
        newsItem.append("h4")
            .attr("class", "news-title")
            .text(news.title);
        
        newsItem.append("p")
            .attr("class", "news-date small text-muted")
            .text(formatDate(news.date));
        
        newsItem.append("p")
            .attr("class", "news-summary")
            .text(news.summary || news.text || "No summary available");
        
        // Create sentiment indicators
        const sentimentIndicator = newsItem.append("div")
            .attr("class", "sentiment-indicator d-flex align-items-center mt-2");
        
        sentimentIndicator.append("span")
            .attr("class", "sentiment-label mr-2")
            .text("Sentiment: ");
        
        const sentimentBar = sentimentIndicator.append("div")
            .attr("class", "sentiment-bar")
            .style("width", "200px")
            .style("height", "20px")
            .style("background-color", "#eee")
            .style("border-radius", "10px")
            .style("overflow", "hidden");
        
        sentimentBar.append("div")
            .attr("class", `sentiment-fill ${sentimentClass}`)
            .style("width", `${(sentiment.pos * 100)}%`)
            .style("height", "100%")
            .style("background-color", sentimentClass === "positive" ? "#4caf50" :
                                      sentimentClass === "negative" ? "#f44336" : "#9e9e9e");
        
        sentimentIndicator.append("span")
            .attr("class", "sentiment-value ml-2")
            .text(sentiment.polarity.toFixed(2));
    });
}

// Helper Functions
function formatDate(dateStr) {
    if (!dateStr) return "";
    
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
}

function formatItemName(name) {
    if (!name) return "";
    
    // Convert camelCase to title case with spaces
    return name
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, str => str.toUpperCase());
}

function formatCurrency(value) {
    if (value === undefined || value === null || isNaN(value)) return "-";
    
    // Format large numbers in millions or billions
    const absValue = Math.abs(value);
    
    if (absValue >= 1_000_000_000) {
        return `${(value / 1_000_000_000).toFixed(2)}B`;
    } else if (absValue >= 1_000_000) {
        return `${(value / 1_000_000).toFixed(2)}M`;
    } else if (absValue >= 1_000) {
        return `${(value / 1_000).toFixed(2)}K`;
    } else {
        return value.toFixed(2);
    }
}

function formatCurrencyForAxis(value) {
    if (value === 0) return "0";
    
    // Format large numbers in millions or billions for axis labels
    const absValue = Math.abs(value);
    
    if (absValue >= 1_000_000_000) {
        return `${(value / 1_000_000_000)}B`;
    } else if (absValue >= 1_000_000) {
        return `${(value / 1_000_000)}M`;
    } else if (absValue >= 1_000) {
        return `${(value / 1_000)}K`;
    } else {
        return value;
    }
} 