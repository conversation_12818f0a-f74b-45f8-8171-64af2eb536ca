/* static/enhanced_charts_test.js */
/* Test Suite for Enhanced Rating Charts Integration */

/**
 * Enhanced Charts Test Suite
 */
class EnhancedChartsTest {
    constructor() {
        this.testResults = {};
        this.debugMode = true;
        this.testContainer = null;
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🧪 Starting Enhanced Charts Test Suite...');

        this.createTestContainer();

        // Core functionality tests
        await this.testDependencies();
        await this.testChartCreation();
        await this.testAnimations();
        await this.testScrollTriggers();
        await this.testResponsiveDesign();
        await this.testChatbotIntegration();
        await this.testDataExtraction();
        await this.testTextFormatting();

        // Integration tests
        await this.testRatingChartOverride();
        await this.testMessageObserver();

        this.displayResults();
        this.cleanup();
    }

    /**
     * Create test container
     */
    createTestContainer() {
        this.testContainer = document.createElement('div');
        this.testContainer.id = 'enhanced-charts-test-container';
        this.testContainer.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            max-height: 80vh;
            background: #fff;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 15px;
            z-index: 10000;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            font-family: 'Inter', sans-serif;
            font-size: 12px;
        `;

        this.testContainer.innerHTML = `
            <h3 style="margin: 0 0 10px 0; color: #1f2937;">📊 Chart Tests</h3>
            <div id="test-results"></div>
            <button id="close-test" style="position: absolute; top: 5px; right: 10px; border: none; background: none; font-size: 16px; cursor: pointer;">×</button>
        `;

        document.body.appendChild(this.testContainer);

        // Close button
        document.getElementById('close-test').onclick = () => {
            this.cleanup();
        };
    }

    /**
     * Test if all dependencies are loaded
     */
    async testDependencies() {
        const tests = [
            { name: 'D3.js', check: () => typeof d3 !== 'undefined' },
            { name: 'EnhancedRatingChart', check: () => typeof EnhancedRatingChart !== 'undefined' },
            { name: 'EnhancedTextCommentary', check: () => typeof EnhancedTextCommentary !== 'undefined' },
            { name: 'createEnhancedRatingVisualization', check: () => typeof createEnhancedRatingVisualization !== 'undefined' },
            { name: 'ChatbotRatingIntegration', check: () => typeof window.ChatbotRatingIntegration !== 'undefined' },
            { name: 'AnalysisFormatter', check: () => typeof AnalysisFormatter !== 'undefined' }
        ];

        let passed = 0;
        tests.forEach(test => {
            const result = test.check();
            this.testResults[`dependency_${test.name}`] = result;
            if (result) passed++;
            this.log(`${result ? '✅' : '❌'} ${test.name}`, result ? 'success' : 'error');
        });

        this.log(`Dependencies: ${passed}/${tests.length} loaded`, passed === tests.length ? 'success' : 'warning');
        return passed === tests.length;
    }

    /**
     * Test chart creation
     */
    async testChartCreation() {
        if (typeof EnhancedRatingChart === 'undefined') {
            this.log('❌ Cannot test chart creation - EnhancedRatingChart not loaded', 'error');
            return false;
        }

        try {
            // Create test container
            const testDiv = document.createElement('div');
            testDiv.style.width = '300px';
            testDiv.style.height = '200px';
            document.body.appendChild(testDiv);

            // Test data
            const testData = [
                { label: 'Test Rating 1', value: 75 },
                { label: 'Test Rating 2', value: 60 },
                { label: 'Test Rating 3', value: 90 }
            ];

            // Create chart
            const chart = new EnhancedRatingChart(testDiv, {
                width: 300,
                height: 200,
                animateOnScroll: false
            });

            chart.render(testData);

            // Check if SVG was created
            const svg = testDiv.querySelector('svg');
            const hasElements = svg && svg.querySelectorAll('.rating-filled-bar').length === 3;

            this.testResults.chartCreation = hasElements;
            this.log(`${hasElements ? '✅' : '❌'} Chart Creation`, hasElements ? 'success' : 'error');

            // Cleanup
            chart.destroy();
            document.body.removeChild(testDiv);

            return hasElements;
        } catch (error) {
            this.log(`❌ Chart Creation Error: ${error.message}`, 'error');
            this.testResults.chartCreation = false;
            return false;
        }
    }

    /**
     * Test animations
     */
    async testAnimations() {
        if (typeof EnhancedRatingChart === 'undefined') {
            this.log('❌ Cannot test animations - EnhancedRatingChart not loaded', 'error');
            return false;
        }

        try {
            const testDiv = document.createElement('div');
            testDiv.style.cssText = 'width: 300px; height: 200px; position: absolute; top: -1000px;';
            document.body.appendChild(testDiv);

            const chart = new EnhancedRatingChart(testDiv, {
                width: 300,
                height: 200,
                animateOnScroll: false
            });

            const testData = [{ label: 'Animation Test', value: 80 }];
            chart.render(testData);

            // Check if bars start at width 0 (for animation)
            const bar = testDiv.querySelector('.rating-filled-bar');
            const initialWidth = parseFloat(bar.getAttribute('width') || '0');

            // Simulate animation
            await new Promise(resolve => {
                setTimeout(() => {
                    const finalWidth = parseFloat(bar.getAttribute('width') || '0');
                    const animated = finalWidth > initialWidth;

                    this.testResults.animations = animated;
                    this.log(`${animated ? '✅' : '❌'} Animations`, animated ? 'success' : 'error');

                    chart.destroy();
                    document.body.removeChild(testDiv);
                    resolve(animated);
                }, 100);
            });

            return this.testResults.animations;
        } catch (error) {
            this.log(`❌ Animation Test Error: ${error.message}`, 'error');
            this.testResults.animations = false;
            return false;
        }
    }

    /**
     * Test scroll triggers
     */
    async testScrollTriggers() {
        if (typeof IntersectionObserver === 'undefined') {
            this.log('❌ IntersectionObserver not supported', 'error');
            this.testResults.scrollTriggers = false;
            return false;
        }

        try {
            // Create observer to test
            let triggered = false;
            const testElement = document.createElement('div');
            testElement.style.cssText = 'width: 100px; height: 100px; position: absolute; top: 0;';
            document.body.appendChild(testElement);

            const observer = new IntersectionObserver((entries) => {
                triggered = true;
            }, { threshold: 0.1 });

            observer.observe(testElement);

            // Wait a moment for observer to trigger
            await new Promise(resolve => setTimeout(resolve, 100));

            observer.disconnect();
            document.body.removeChild(testElement);

            this.testResults.scrollTriggers = triggered;
            this.log(`${triggered ? '✅' : '❌'} Scroll Triggers`, triggered ? 'success' : 'error');
            return triggered;
        } catch (error) {
            this.log(`❌ Scroll Trigger Error: ${error.message}`, 'error');
            this.testResults.scrollTriggers = false;
            return false;
        }
    }

    /**
     * Test responsive design
     */
    async testResponsiveDesign() {
        if (typeof EnhancedRatingChart === 'undefined') {
            this.log('❌ Cannot test responsive design - EnhancedRatingChart not loaded', 'error');
            return false;
        }

        try {
            const testDiv = document.createElement('div');
            testDiv.style.cssText = 'width: 100%; position: absolute; top: -1000px;';
            document.body.appendChild(testDiv);

            const chart = new EnhancedRatingChart(testDiv, {
                responsive: true,
                animateOnScroll: false
            });

            const testData = [{ label: 'Responsive Test', value: 70 }];
            chart.render(testData);

            const svg = testDiv.querySelector('svg');
            const hasViewBox = svg && svg.hasAttribute('viewBox');
            const hasPreserveAspectRatio = svg && svg.hasAttribute('preserveAspectRatio');

            const isResponsive = hasViewBox && hasPreserveAspectRatio;

            this.testResults.responsiveDesign = isResponsive;
            this.log(`${isResponsive ? '✅' : '❌'} Responsive Design`, isResponsive ? 'success' : 'error');

            chart.destroy();
            document.body.removeChild(testDiv);
            return isResponsive;
        } catch (error) {
            this.log(`❌ Responsive Test Error: ${error.message}`, 'error');
            this.testResults.responsiveDesign = false;
            return false;
        }
    }

    /**
     * Test chatbot integration
     */
    async testChatbotIntegration() {
        try {
            const integrationLoaded = typeof window.ChatbotRatingIntegration !== 'undefined';
            const chatbotContainer = document.getElementById('chatbot-messages');
            const containerExists = !!chatbotContainer;

            // Test function override
            const functionsOverridden = typeof window.updateGlobalRatingChart === 'function';

            const integrationWorking = integrationLoaded && functionsOverridden;

            this.testResults.chatbotIntegration = integrationWorking;
            this.log(`${integrationWorking ? '✅' : '❌'} Chatbot Integration`, integrationWorking ? 'success' : 'error');

            if (containerExists) {
                this.log(`✅ Chatbot container found`, 'success');
            } else {
                this.log(`⚠️ Chatbot container not found`, 'warning');
            }

            return integrationWorking;
        } catch (error) {
            this.log(`❌ Integration Test Error: ${error.message}`, 'error');
            this.testResults.chatbotIntegration = false;
            return false;
        }
    }

    /**
     * Test data extraction
     */
    async testDataExtraction() {
        if (typeof extractRatingData === 'undefined') {
            this.log('❌ extractRatingData function not found', 'error');
            this.testResults.dataExtraction = false;
            return false;
        }

        try {
            const testText = `
                Overall Rating: 85%
                Financial Health: 92%
                Valuation Score: 65%
                Business Quality: 78%
            `;

            const extracted = extractRatingData(testText);
            const hasData = extracted && extracted.length > 0;

            this.testResults.dataExtraction = hasData;
            this.log(`${hasData ? '✅' : '❌'} Data Extraction (${extracted?.length || 0} items)`, hasData ? 'success' : 'error');

            return hasData;
        } catch (error) {
            this.log(`❌ Data Extraction Error: ${error.message}`, 'error');
            this.testResults.dataExtraction = false;
            return false;
        }
    }

    /**
     * Test text formatting
     */
    async testTextFormatting() {
        if (typeof AnalysisFormatter === 'undefined') {
            this.log('❌ AnalysisFormatter not found', 'error');
            this.testResults.textFormatting = false;
            return false;
        }

        try {
            const formatter = new AnalysisFormatter();
            const testText = `
                + generateChartHTML() +
                Overall Rating: 85%
                Overall Rating: 87%
                This analysis attempts to evaluate the stock.
            `;

            const formatted = formatter.formatAnalysis(testText);

            const placeholdersRemoved = !formatted.includes('generateChartHTML');
            const inconsistenciesFixed = (formatted.match(/Overall Rating: (\d+)%/g) || []).length <= 1;
            const languageImproved = !formatted.includes('attempts to');

            const allPassed = placeholdersRemoved && inconsistenciesFixed && languageImproved;

            this.testResults.textFormatting = allPassed;
            this.log(`${allPassed ? '✅' : '❌'} Text Formatting`, allPassed ? 'success' : 'error');

            return allPassed;
        } catch (error) {
            this.log(`❌ Text Formatting Error: ${error.message}`, 'error');
            this.testResults.textFormatting = false;
            return false;
        }
    }

    /**
     * Test rating chart function override
     */
    async testRatingChartOverride() {
        try {
            const originalExists = typeof window.updateGlobalRatingChart === 'function';

            if (originalExists) {
                // Test if function has been enhanced
                const functionString = window.updateGlobalRatingChart.toString();
                const isEnhanced = functionString.includes('Integration') || functionString.includes('Enhanced');

                this.testResults.functionOverride = isEnhanced;
                this.log(`${isEnhanced ? '✅' : '❌'} Function Override`, isEnhanced ? 'success' : 'error');
                return isEnhanced;
            } else {
                this.log(`❌ updateGlobalRatingChart not found`, 'error');
                this.testResults.functionOverride = false;
                return false;
            }
        } catch (error) {
            this.log(`❌ Function Override Error: ${error.message}`, 'error');
            this.testResults.functionOverride = false;
            return false;
        }
    }

    /**
     * Test message observer
     */
    async testMessageObserver() {
        try {
            const chatbotContainer = document.getElementById('chatbot-messages');

            if (!chatbotContainer) {
                this.log(`⚠️ Chatbot messages container not found`, 'warning');
                this.testResults.messageObserver = false;
                return false;
            }

            // Create a test message
            const testMessage = document.createElement('li');
            testMessage.className = 'message bot';
            testMessage.innerHTML = `
                <span class="bot-avatar"><i class="fas fa-robot"></i></span>
                <div class="message-content">
                    <div class="rating-charts">Test chart content</div>
                </div>
            `;

            chatbotContainer.appendChild(testMessage);

            // Wait for observer to potentially process
            await new Promise(resolve => setTimeout(resolve, 200));

            // Check if enhancement was attempted
            const enhancedContainer = testMessage.querySelector('.enhanced-rating-container');
            const observerWorking = !!enhancedContainer;

            this.testResults.messageObserver = observerWorking;
            this.log(`${observerWorking ? '✅' : '❌'} Message Observer`, observerWorking ? 'success' : 'error');

            // Cleanup
            chatbotContainer.removeChild(testMessage);

            return observerWorking;
        } catch (error) {
            this.log(`❌ Message Observer Error: ${error.message}`, 'error');
            this.testResults.messageObserver = false;
            return false;
        }
    }

    /**
     * Log test result
     */
    log(message, type = 'info') {
        if (!this.debugMode) return;

        const color = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#6b7280'
        }[type] || '#6b7280';

        console.log(`%c${message}`, `color: ${color}; font-weight: 500;`);

        // Also add to UI
        const resultsDiv = document.getElementById('test-results');
        if (resultsDiv) {
            const logEntry = document.createElement('div');
            logEntry.style.cssText = `
                color: ${color};
                margin: 2px 0;
                font-size: 11px;
                font-family: monospace;
            `;
            logEntry.textContent = message;
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
    }

    /**
     * Display final results
     */
    displayResults() {
        const total = Object.keys(this.testResults).length;
        const passed = Object.values(this.testResults).filter(Boolean).length;
        const percentage = Math.round((passed / total) * 100);

        const summary = `
            📊 Test Summary: ${passed}/${total} passed (${percentage}%)
            ${percentage >= 80 ? '🎉 Good to go!' : percentage >= 60 ? '⚠️ Some issues' : '❌ Needs fixes'}
        `;

        this.log(summary, percentage >= 80 ? 'success' : percentage >= 60 ? 'warning' : 'error');

        // Update UI
        const resultsDiv = document.getElementById('test-results');
        if (resultsDiv) {
            const summaryDiv = document.createElement('div');
            summaryDiv.style.cssText = `
                margin-top: 10px;
                padding: 8px;
                background: ${percentage >= 80 ? '#d1fae5' : percentage >= 60 ? '#fef3c7' : '#fee2e2'};
                border-radius: 4px;
                font-weight: 600;
                font-size: 11px;
            `;
            summaryDiv.textContent = `${passed}/${total} tests passed (${percentage}%)`;
            resultsDiv.appendChild(summaryDiv);
        }
    }

    /**
     * Cleanup test container
     */
    cleanup() {
        if (this.testContainer && this.testContainer.parentNode) {
            this.testContainer.parentNode.removeChild(this.testContainer);
        }
    }
}

/**
 * Quick test function
 */
function testEnhancedCharts() {
    const tester = new EnhancedChartsTest();
    tester.runAllTests();
}

/**
 * Auto-run tests when manually triggered
 */
function runEnhancedChartsTests() {
    console.log('🔧 Running Enhanced Charts Tests...');
    setTimeout(() => {
        testEnhancedCharts();
    }, 1000);
}

// Global access
window.testEnhancedCharts = testEnhancedCharts;
window.runEnhancedChartsTests = runEnhancedChartsTests;

// Add test button to page (for manual testing)
function addTestButton() {
    const button = document.createElement('button');
    button.textContent = '🧪 Test Charts';
    button.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 10000;
        padding: 10px 15px;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    `;
    button.onclick = testEnhancedCharts;
    document.body.appendChild(button);

    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (button.parentNode) {
            button.parentNode.removeChild(button);
        }
    }, 10000);
}

// Auto-add test button when page loads
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(addTestButton, 2000);
});

console.log('🧪 Enhanced Charts Test Suite loaded - Use testEnhancedCharts() to run');
