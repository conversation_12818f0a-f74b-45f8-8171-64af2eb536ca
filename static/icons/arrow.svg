<svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <style>
    #arrow {
        transform-origin: 50% 100%; /* Pivot at the base of the arrow */
        transition: transform 0.3s ease; /* Optional: Smooth rotation */
    }
    #arrow path {
        fill: var(--arrow-color);
        stroke: var(--arrow-stroke-color);
        stroke-width: 0.8;
        stroke-linejoin: round;
    }
  </style>
  <g id="arrow">
    <path d="M16 1L2 28L16 22L30 28L16 1Z"/>
  </g>
</svg>