/* static/enhanced_rating_charts.js */
/* Enhanced D3.js Rating Charts with Scroll-Based Animations */

// Global animation state management
let animationState = {
    chartsAnimated: new Set(),
    textAnimated: new Set(),
    observers: new Map()
};

// Modern color palette
const ENHANCED_COLORS = {
    excellent: '#10B981', // Emerald
    good: '#3B82F6',      // Blue
    average: '#F59E0B',   // Amber
    poor: '#EF4444',      // Red
    neutral: '#6B7280',   // Gray
    background: '#F3F4F6',
    gradients: {
        excellent: ['#10B981', '#059669'],
        good: ['#3B82F6', '#2563EB'],
        average: ['#F59E0B', '#D97706'],
        poor: ['#EF4444', '#DC2626'],
        neutral: ['#6B7280', '#4B5563']
    }
};

// Animation configuration
const ANIMATION_CONFIG = {
    barGrowth: {
        duration: 1200,
        delay: 100,
        ease: d3.easeCubicOut
    },
    textFade: {
        duration: 800,
        delay: 150,
        ease: d3.easeQuadOut
    },
    hover: {
        duration: 200,
        ease: d3.easeQuadInOut
    }
};

/**
 * Enhanced Rating Chart Class
 */
class EnhancedRatingChart {
    constructor(container, options = {}) {
        this.container = d3.select(container);
        this.options = {
            width: options.width || 400,
            height: options.height || 60,
            margin: options.margin || { top: 20, right: 60, bottom: 20, left: 120 },
            barHeight: options.barHeight || 24,
            animateOnScroll: options.animateOnScroll !== false,
            responsive: options.responsive !== false,
            ...options
        };

        this.svg = null;
        this.isAnimated = false;
        this.uniqueId = 'chart-' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Initialize the chart structure
     */
    init() {
        // Clear any existing content
        this.container.selectAll('*').remove();

        // Create responsive wrapper
        const wrapper = this.container
            .append('div')
            .attr('class', 'enhanced-rating-chart-wrapper')
            .style('width', '100%')
            .style('position', 'relative');

        // Create SVG with responsive viewBox
        this.svg = wrapper
            .append('svg')
            .attr('class', 'enhanced-rating-chart')
            .attr('viewBox', `0 0 ${this.options.width} ${this.options.height}`)
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .style('width', '100%')
            .style('height', 'auto')
            .style('max-width', `${this.options.width}px`);

        // Add accessibility attributes
        this.svg
            .attr('role', 'img')
            .attr('aria-label', 'Stock rating visualization');

        // Create gradient definitions
        this.createGradients();

        // Create tooltip
        this.createTooltip();

        return this;
    }

    /**
     * Create gradient definitions for enhanced visuals
     */
    createGradients() {
        const defs = this.svg.append('defs');

        Object.entries(ENHANCED_COLORS.gradients).forEach(([key, colors]) => {
            const gradient = defs.append('linearGradient')
                .attr('id', `gradient-${key}-${this.uniqueId}`)
                .attr('x1', '0%')
                .attr('y1', '0%')
                .attr('x2', '100%')
                .attr('y2', '0%');

            gradient.append('stop')
                .attr('offset', '0%')
                .attr('stop-color', colors[0])
                .attr('stop-opacity', 1);

            gradient.append('stop')
                .attr('offset', '100%')
                .attr('stop-color', colors[1])
                .attr('stop-opacity', 0.8);
        });

        // Add shadow filter
        const filter = defs.append('filter')
            .attr('id', `shadow-${this.uniqueId}`)
            .attr('x', '-20%')
            .attr('y', '-20%')
            .attr('width', '140%')
            .attr('height', '140%');

        filter.append('feDropShadow')
            .attr('dx', 0)
            .attr('dy', 2)
            .attr('stdDeviation', 3)
            .attr('flood-color', '#000000')
            .attr('flood-opacity', 0.1);
    }

    /**
     * Create tooltip for interactive elements
     */
    createTooltip() {
        this.tooltip = d3.select('body')
            .append('div')
            .attr('class', 'enhanced-rating-tooltip')
            .style('position', 'absolute')
            .style('visibility', 'hidden')
            .style('background', 'rgba(0, 0, 0, 0.9)')
            .style('color', 'white')
            .style('padding', '8px 12px')
            .style('border-radius', '6px')
            .style('font-size', '12px')
            .style('font-weight', '500')
            .style('pointer-events', 'none')
            .style('z-index', '1000')
            .style('box-shadow', '0 4px 6px rgba(0, 0, 0, 0.1)');
    }

    /**
     * Render a single rating bar
     */
    renderBar(data, index, total) {
        const { label, value, maxValue = 100, category = 'neutral' } = data;
        const y = this.options.margin.top + (index * (this.options.height - this.options.margin.top - this.options.margin.bottom) / total);

        // Bar group
        const barGroup = this.svg.append('g')
            .attr('class', 'rating-bar-group')
            .attr('transform', `translate(0, ${y})`);

        // Label
        barGroup.append('text')
            .attr('class', 'rating-label')
            .attr('x', this.options.margin.left - 10)
            .attr('y', this.options.barHeight / 2)
            .attr('text-anchor', 'end')
            .attr('dominant-baseline', 'middle')
            .style('font-size', '14px')
            .style('font-weight', '600')
            .style('fill', '#374151')
            .style('opacity', 0)
            .text(label);

        // Background bar
        const bgBar = barGroup.append('rect')
            .attr('class', 'rating-bg-bar')
            .attr('x', this.options.margin.left)
            .attr('y', 0)
            .attr('width', this.options.width - this.options.margin.left - this.options.margin.right)
            .attr('height', this.options.barHeight)
            .attr('rx', this.options.barHeight / 2)
            .attr('ry', this.options.barHeight / 2)
            .style('fill', ENHANCED_COLORS.background)
            .style('stroke', '#E5E7EB')
            .style('stroke-width', 1);

        // Filled bar
        const barWidth = ((value / maxValue) * (this.options.width - this.options.margin.left - this.options.margin.right));
        const filledBar = barGroup.append('rect')
            .attr('class', 'rating-filled-bar')
            .attr('x', this.options.margin.left)
            .attr('y', 0)
            .attr('width', 0) // Start at 0 for animation
            .attr('height', this.options.barHeight)
            .attr('rx', this.options.barHeight / 2)
            .attr('ry', this.options.barHeight / 2)
            .style('fill', `url(#gradient-${category}-${this.uniqueId})`)
            .style('filter', `url(#shadow-${this.uniqueId})`);

        // Value text
        const valueText = barGroup.append('text')
            .attr('class', 'rating-value')
            .attr('x', this.options.width - this.options.margin.right + 10)
            .attr('y', this.options.barHeight / 2)
            .attr('dominant-baseline', 'middle')
            .style('font-size', '14px')
            .style('font-weight', '700')
            .style('fill', ENHANCED_COLORS[category])
            .style('opacity', 0)
            .text(`${Math.round(value)}%`);

        // Store elements for animation
        return {
            barGroup,
            label: barGroup.select('.rating-label'),
            filledBar,
            valueText,
            targetWidth: barWidth,
            data
        };
    }

    /**
     * Animate chart elements
     */
    animateChart(elements) {
        if (this.isAnimated) return;
        this.isAnimated = true;

        elements.forEach((element, index) => {
            const delay = index * ANIMATION_CONFIG.barGrowth.delay;

            // Animate label fade in
            element.label
                .transition()
                .delay(delay)
                .duration(ANIMATION_CONFIG.textFade.duration)
                .ease(ANIMATION_CONFIG.textFade.ease)
                .style('opacity', 1);

            // Animate bar growth
            element.filledBar
                .transition()
                .delay(delay)
                .duration(ANIMATION_CONFIG.barGrowth.duration)
                .ease(ANIMATION_CONFIG.barGrowth.ease)
                .attr('width', element.targetWidth);

            // Animate value text
            element.valueText
                .transition()
                .delay(delay + ANIMATION_CONFIG.barGrowth.duration / 2)
                .duration(ANIMATION_CONFIG.textFade.duration)
                .ease(ANIMATION_CONFIG.textFade.ease)
                .style('opacity', 1);
        });
    }

    /**
     * Add hover interactions
     */
    addInteractivity(elements) {
        elements.forEach(element => {
            const { barGroup, filledBar, data } = element;

            barGroup
                .style('cursor', 'pointer')
                .on('mouseenter', (event) => {
                    // Hover effect
                    filledBar
                        .transition()
                        .duration(ANIMATION_CONFIG.hover.duration)
                        .ease(ANIMATION_CONFIG.hover.ease)
                        .style('filter', `url(#shadow-${this.uniqueId}) brightness(1.1)`);

                    // Show tooltip
                    this.tooltip
                        .style('visibility', 'visible')
                        .html(`<strong>${data.label}</strong><br>${data.value}% - ${this.getRatingText(data.value)}`);
                })
                .on('mousemove', (event) => {
                    this.tooltip
                        .style('top', (event.pageY - 10) + 'px')
                        .style('left', (event.pageX + 10) + 'px');
                })
                .on('mouseleave', () => {
                    // Reset hover effect
                    filledBar
                        .transition()
                        .duration(ANIMATION_CONFIG.hover.duration)
                        .ease(ANIMATION_CONFIG.hover.ease)
                        .style('filter', `url(#shadow-${this.uniqueId})`);

                    // Hide tooltip
                    this.tooltip.style('visibility', 'hidden');
                });
        });
    }

    /**
     * Get rating text from score
     */
    getRatingText(score) {
        if (score >= 80) return 'Excellent';
        if (score >= 60) return 'Good';
        if (score >= 40) return 'Average';
        if (score >= 20) return 'Below Average';
        return 'Poor';
    }

    /**
     * Get category from score
     */
    getCategory(score) {
        if (score >= 80) return 'excellent';
        if (score >= 60) return 'good';
        if (score >= 40) return 'average';
        if (score >= 20) return 'poor';
        return 'poor';
    }

    /**
     * Render complete chart
     */
    render(data) {
        if (!this.svg) this.init();

        // Process data and add categories
        const processedData = data.map(item => ({
            ...item,
            category: this.getCategory(item.value)
        }));

        // Render bars
        const elements = processedData.map((item, index) =>
            this.renderBar(item, index, processedData.length)
        );

        // Add interactivity
        this.addInteractivity(elements);

        // Setup scroll animation if enabled
        if (this.options.animateOnScroll) {
            this.setupScrollAnimation(elements);
        } else {
            this.animateChart(elements);
        }

        return this;
    }

    /**
     * Setup scroll-based animation using Intersection Observer
     */
    setupScrollAnimation(elements) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.isAnimated) {
                    this.animateChart(elements);
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.3,
            rootMargin: '0px 0px -50px 0px'
        });

        observer.observe(this.container.node());
        animationState.observers.set(this.uniqueId, observer);
    }

    /**
     * Cleanup method
     */
    destroy() {
        // Remove tooltip
        if (this.tooltip) {
            this.tooltip.remove();
        }

        // Disconnect observer
        const observer = animationState.observers.get(this.uniqueId);
        if (observer) {
            observer.disconnect();
            animationState.observers.delete(this.uniqueId);
        }

        // Clear container
        this.container.selectAll('*').remove();
    }
}

/**
 * Enhanced Text Commentary Class
 */
class EnhancedTextCommentary {
    constructor(container, options = {}) {
        this.container = d3.select(container);
        this.options = {
            animateOnScroll: options.animateOnScroll !== false,
            staggerDelay: options.staggerDelay || 200,
            ...options
        };
        this.isAnimated = false;
        this.uniqueId = 'text-' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Render enhanced text commentary
     */
    render(content) {
        // Clear existing content
        this.container.selectAll('*').remove();

        // Create main container
        const wrapper = this.container
            .append('div')
            .attr('class', 'enhanced-text-commentary')
            .style('max-width', '800px')
            .style('margin', '0 auto')
            .style('padding', '20px')
            .style('line-height', '1.6')
            .style('font-family', '"Inter", -apple-system, BlinkMacSystemFont, sans-serif');

        // Process content into paragraphs
        const paragraphs = Array.isArray(content) ? content : [content];

        const textElements = paragraphs.map((paragraph, index) => {
            const p = wrapper
                .append('p')
                .attr('class', 'commentary-paragraph')
                .style('margin-bottom', '16px')
                .style('font-size', '16px')
                .style('color', '#374151')
                .style('opacity', 0)
                .style('transform', 'translateY(20px)')
                .html(paragraph);

            return { element: p, index };
        });

        // Setup animation
        if (this.options.animateOnScroll) {
            this.setupScrollAnimation(textElements);
        } else {
            this.animateText(textElements);
        }

        return this;
    }

    /**
     * Animate text elements
     */
    animateText(textElements) {
        if (this.isAnimated) return;
        this.isAnimated = true;

        textElements.forEach(({ element, index }) => {
            element
                .transition()
                .delay(index * this.options.staggerDelay)
                .duration(ANIMATION_CONFIG.textFade.duration)
                .ease(ANIMATION_CONFIG.textFade.ease)
                .style('opacity', 1)
                .style('transform', 'translateY(0px)');
        });
    }

    /**
     * Setup scroll-based animation
     */
    setupScrollAnimation(textElements) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.isAnimated) {
                    this.animateText(textElements);
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.2,
            rootMargin: '0px 0px -30px 0px'
        });

        observer.observe(this.container.node());
        animationState.observers.set(this.uniqueId, observer);
    }

    /**
     * Cleanup method
     */
    destroy() {
        const observer = animationState.observers.get(this.uniqueId);
        if (observer) {
            observer.disconnect();
            animationState.observers.delete(this.uniqueId);
        }
        this.container.selectAll('*').remove();
    }
}

/**
 * Main Enhanced Rating Visualization Function
 */
function createEnhancedRatingVisualization(container, data, options = {}) {
    const wrapper = d3.select(container)
        .append('div')
        .attr('class', 'enhanced-rating-visualization')
        .style('background', '#FFFFFF')
        .style('border-radius', '12px')
        .style('padding', '24px')
        .style('box-shadow', '0 4px 6px rgba(0, 0, 0, 0.05)')
        .style('margin', '20px 0');

    // Title
    if (data.title) {
        wrapper
            .append('h3')
            .attr('class', 'visualization-title')
            .style('margin', '0 0 20px 0')
            .style('font-size', '20px')
            .style('font-weight', '700')
            .style('color', '#1F2937')
            .style('text-align', 'center')
            .text(data.title);
    }

    // Charts container
    const chartsContainer = wrapper
        .append('div')
        .attr('class', 'charts-container');

    // Create rating chart
    const ratingChart = new EnhancedRatingChart(chartsContainer.node(), {
        width: 500,
        height: data.ratings.length * 70,
        ...options.chartOptions
    });

    ratingChart.render(data.ratings);

    // Commentary container
    if (data.commentary) {
        const commentaryContainer = wrapper
            .append('div')
            .attr('class', 'commentary-container')
            .style('margin-top', '30px');

        const textCommentary = new EnhancedTextCommentary(commentaryContainer.node(), {
            ...options.textOptions
        });

        textCommentary.render(data.commentary);
    }

    return { ratingChart, textCommentary: data.commentary };
}

/**
 * Utility function to create stock analysis visualization
 */
function createStockAnalysisVisualization(container, analysisData) {
    const data = {
        title: `${analysisData.symbol} - Stock Analysis Ratings`,
        ratings: [
            { label: 'Overall Rating', value: analysisData.overall || 0 },
            { label: 'Technical Analysis', value: analysisData.technical || 0 },
            { label: 'Financial Health', value: analysisData.financial || 0 },
            { label: 'Valuation', value: analysisData.valuation || 0 },
            { label: 'Growth Potential', value: analysisData.growth || 0 },
            { label: 'Analyst Sentiment', value: analysisData.analyst || 0 }
        ].filter(item => item.value > 0),
        commentary: analysisData.commentary || []
    };

    return createEnhancedRatingVisualization(container, data);
}

/**
 * Global cleanup function
 */
function cleanupEnhancedCharts() {
    // Disconnect all observers
    animationState.observers.forEach(observer => observer.disconnect());
    animationState.observers.clear();

    // Remove all tooltips
    d3.selectAll('.enhanced-rating-tooltip').remove();

    // Reset animation state
    animationState.chartsAnimated.clear();
    animationState.textAnimated.clear();
}

// Export functions for global use
window.EnhancedRatingChart = EnhancedRatingChart;
window.EnhancedTextCommentary = EnhancedTextCommentary;
window.createEnhancedRatingVisualization = createEnhancedRatingVisualization;
window.createStockAnalysisVisualization = createStockAnalysisVisualization;
window.cleanupEnhancedCharts = cleanupEnhancedCharts;

// Clean up on page unload
window.addEventListener('beforeunload', cleanupEnhancedCharts);

console.log('Enhanced Rating Charts loaded successfully');

/**
 * Enhanced Rating Charts JavaScript
 * Fixes alignment issues with rating charts, particularly for valuation ratings
 */

// Function to ensure perfect alignment of percentage values
function ensurePerfectAlignment() {
    // Wait for charts to be rendered
    setTimeout(function() {
        // Get all component rating items
        const componentItems = document.querySelectorAll('.component-rating-item');
        if (componentItems.length < 3) return;
        
        // Get the bottom position of the first card as reference
        const firstCard = componentItems[0];
        const firstScoreDisplay = firstCard.querySelector('.component-score-display');
        
        if (!firstScoreDisplay) return;
        
        // Get the computed bottom position
        const computedStyle = window.getComputedStyle(firstScoreDisplay);
        const referenceBottom = computedStyle.bottom;
        
        // Apply this exact bottom position to all cards
        componentItems.forEach(function(item) {
            const scoreDisplay = item.querySelector('.component-score-display');
            if (scoreDisplay) {
                scoreDisplay.style.bottom = referenceBottom;
                
                // Ensure flex alignment for consistent baseline
                scoreDisplay.style.display = 'flex';
                scoreDisplay.style.alignItems = 'baseline';
                scoreDisplay.style.justifyContent = 'center';
                
                // Fix percentage and number spans
                const spans = scoreDisplay.querySelectorAll('span');
                spans.forEach(function(span) {
                    span.style.verticalAlign = 'baseline';
                });
            }
        });
    }, 500);
}

document.addEventListener('DOMContentLoaded', function() {
    // Fix for valuation rating alignment
    function fixRatingChartAlignment() {
        // Wait for charts to be rendered
        setTimeout(function() {
            // Select all horizontal bar values
            const barValues = document.querySelectorAll('.horizontal-bar-value');
            
            // Apply consistent positioning to all bar values
            barValues.forEach(function(value) {
                value.style.position = 'absolute';
                value.style.right = '10px';
                value.style.top = '50%';
                value.style.transform = 'translateY(-50%)';
                value.style.lineHeight = '1';
                value.style.margin = '0';
                value.style.padding = '0';
            });
            
            // Special fix for valuation rating (typically the third bar)
            const valuationBars = document.querySelectorAll('.rating-visualization .horizontal-bar-container:nth-child(4) .horizontal-bar-value');
            valuationBars.forEach(function(bar) {
                bar.style.top = '50%';
                bar.style.transform = 'translateY(-50%)';
            });

            // Fix component rating items to ensure consistent layout
            const componentItems = document.querySelectorAll('.component-rating-item');
            componentItems.forEach(function(item, index) {
                // Ensure flex layout
                item.style.display = 'flex';
                item.style.flexDirection = 'column';
                item.style.position = 'relative';
                item.style.paddingBottom = '60px';
                
                // Fix chart wrapper
                const chartWrapper = item.querySelector('.component-chart-wrapper');
                if (chartWrapper) {
                    chartWrapper.style.height = '90px';
                    chartWrapper.style.marginBottom = '40px';
                    chartWrapper.style.display = 'flex';
                    chartWrapper.style.alignItems = 'center';
                    chartWrapper.style.justifyContent = 'center';
                }
                
                // Fix score display
                const scoreDisplay = item.querySelector('.component-score-display');
                if (scoreDisplay) {
                    scoreDisplay.style.position = 'absolute';
                    scoreDisplay.style.bottom = '24px';
                    scoreDisplay.style.left = '0';
                    scoreDisplay.style.right = '0';
                    scoreDisplay.style.textAlign = 'center';
                }
                
                // Special fix for valuation component (3rd item)
                if (index === 2) {
                    if (chartWrapper) {
                        chartWrapper.style.marginTop = '0';
                    }
                    if (scoreDisplay) {
                        scoreDisplay.style.bottom = '24px';
                    }
                }
                
                // Fix header height
                const header = item.querySelector('.component-header');
                if (header) {
                    header.style.height = '60px';
                    header.style.marginBottom = '15px';
                    header.style.display = 'flex';
                    header.style.flexDirection = 'column';
                    header.style.justifyContent = 'center';
                }
            });
            
            // Fix for circular charts to ensure they're centered
            const circularCharts = document.querySelectorAll('.component-chart-wrapper svg');
            circularCharts.forEach(function(chart) {
                chart.style.display = 'block';
                chart.style.margin = '0 auto';
            });
            
            // Ensure percentage text is consistently positioned
            const percentageTexts = document.querySelectorAll('.component-score-display');
            percentageTexts.forEach(function(text) {
                text.style.margin = '0';
                text.style.padding = '0';
                text.style.lineHeight = '1';
            });
            
            // Call the perfect alignment function
            ensurePerfectAlignment();
            
        }, 500); // Wait for 500ms to ensure charts are rendered
    }
    
    // Run the fix whenever new content is added to the page
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                fixRatingChartAlignment();
            }
        });
    });
    
    // Start observing the document body for changes
    observer.observe(document.body, { 
        childList: true, 
        subtree: true 
    });
    
    // Initial fix
    fixRatingChartAlignment();
    
    // Run the fix again after a short delay to catch any late-rendered elements
    setTimeout(fixRatingChartAlignment, 1000);
    setTimeout(fixRatingChartAlignment, 2000);
    
    // Run the perfect alignment function at various intervals
    setTimeout(ensurePerfectAlignment, 1000);
    setTimeout(ensurePerfectAlignment, 2000);
    setTimeout(ensurePerfectAlignment, 3000);
});
