/**
 * Perfect Alignment CSS
 * This file contains very specific CSS rules to ensure perfect alignment
 * of percentage values in rating cards
 */

/* Force all component rating items to have identical structure */
.component-rating-item {
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
    padding-bottom: 60px !important;
    height: 100% !important;
    box-sizing: border-box !important;
}

/* Force all score displays to have identical positioning */
.component-score-display {
    position: absolute !important;
    bottom: 24px !important;
    left: 0 !important;
    right: 0 !important;
    text-align: center !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    height: 28px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: baseline !important;
}

/* Force all percentage spans to have identical styling */
.component-score-display span:last-child {
    font-size: 0.8em !important;
    margin-left: 1px !important;
    vertical-align: baseline !important;
}

/* Force all number spans to have identical styling */
.component-score-display span:first-child {
    vertical-align: baseline !important;
}

/* Specific fix for the valuation card (3rd card) */
.component-ratings-grid .component-rating-item:nth-child(3) .component-score-display {
    bottom: 24px !important;
    display: flex !important;
    align-items: baseline !important;
    justify-content: center !important;
}

/* Force all chart wrappers to have identical dimensions */
.component-chart-wrapper {
    height: 90px !important;
    margin-bottom: 40px !important;
    margin-top: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Force all SVG elements to be centered */
.component-chart-wrapper svg {
    display: block !important;
    margin: 0 auto !important;
}

/* Force all headers to have identical dimensions */
.component-header {
    height: 60px !important;
    margin-bottom: 15px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
}

/* Force all horizontal bar values to have identical positioning */
.horizontal-bar-value {
    position: absolute !important;
    right: 10px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    line-height: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Specific fix for the valuation bar (4th bar) */
.horizontal-bar-container:nth-child(4) .horizontal-bar-value {
    top: 50% !important;
    transform: translateY(-50%) !important;
}

/* Force all cards to have the same internal spacing */
.component-rating-item > * {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Ensure all cards have the same padding */
.component-ratings-grid .component-rating-item {
    padding: 24px !important;
    padding-bottom: 60px !important;
} 