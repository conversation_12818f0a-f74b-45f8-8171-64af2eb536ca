/**
 * Rating Charts Alignment Fix
 * This script ensures consistent alignment of rating values across all chart types
 */

document.addEventListener('DOMContentLoaded', function() {
    // Main function to fix all rating chart alignments
    function fixRatingAlignment() {
        console.log("Applying rating alignment fixes...");
        
        // Fix horizontal bar charts
        fixHorizontalBarCharts();
        
        // Fix component rating cards
        fixComponentRatingCards();
        
        // Fix circular charts
        fixCircularCharts();
        
        // Special fix for valuation card (3rd card)
        fixValuationCard();
        
        // Fix the spans containing numbers and percentage signs
        fixPercentageSpans();
    }
    
    // Fix horizontal bar charts alignment
    function fixHorizontalBarCharts() {
        const barValues = document.querySelectorAll('.horizontal-bar-value');
        barValues.forEach(function(value) {
            // Apply consistent positioning
            value.style.position = 'absolute';
            value.style.right = '10px';
            value.style.top = '50%';
            value.style.transform = 'translateY(-50%)';
            value.style.lineHeight = '1';
            value.style.margin = '0';
            value.style.padding = '0';
            value.style.fontWeight = '600';
            value.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.2)';
        });
        
        // Special fix for the valuation bar (usually the 3rd bar)
        const valuationBars = document.querySelectorAll('.horizontal-bar-container:nth-child(4) .horizontal-bar-value');
        valuationBars.forEach(function(bar) {
            bar.style.top = '50%';
            bar.style.transform = 'translateY(-50%)';
        });
    }
    
    // Fix component rating cards alignment
    function fixComponentRatingCards() {
        const componentItems = document.querySelectorAll('.component-rating-item');
        componentItems.forEach(function(item, index) {
            // Ensure consistent layout
            item.style.display = 'flex';
            item.style.flexDirection = 'column';
            item.style.position = 'relative';
            item.style.paddingBottom = '60px';
            
            // Fix header height
            const header = item.querySelector('.component-header');
            if (header) {
                header.style.height = '60px';
                header.style.marginBottom = '15px';
                header.style.display = 'flex';
                header.style.flexDirection = 'column';
                header.style.justifyContent = 'center';
            }
            
            // Fix chart wrapper
            const chartWrapper = item.querySelector('.component-chart-wrapper');
            if (chartWrapper) {
                chartWrapper.style.height = '90px';
                chartWrapper.style.marginBottom = '40px';
                chartWrapper.style.display = 'flex';
                chartWrapper.style.alignItems = 'center';
                chartWrapper.style.justifyContent = 'center';
            }
            
            // Fix score display
            const scoreDisplay = item.querySelector('.component-score-display');
            if (scoreDisplay) {
                scoreDisplay.style.position = 'absolute';
                scoreDisplay.style.bottom = '24px';
                scoreDisplay.style.left = '0';
                scoreDisplay.style.right = '0';
                scoreDisplay.style.textAlign = 'center';
                scoreDisplay.style.margin = '0';
                scoreDisplay.style.padding = '0';
                scoreDisplay.style.lineHeight = '1';
            }
            
            // Special fix for valuation component (3rd item)
            if (index === 2) {
                if (chartWrapper) {
                    chartWrapper.style.marginTop = '0';
                }
                if (scoreDisplay) {
                    scoreDisplay.style.bottom = '24px';
                }
            }
        });
    }
    
    // Fix circular charts alignment
    function fixCircularCharts() {
        const circularCharts = document.querySelectorAll('.component-chart-wrapper svg');
        circularCharts.forEach(function(chart) {
            chart.style.display = 'block';
            chart.style.margin = '0 auto';
        });
    }
    
    // Special fix for valuation card (3rd card)
    function fixValuationCard() {
        // Find all component rating grids
        const grids = document.querySelectorAll('.component-ratings-grid');
        
        grids.forEach(function(grid) {
            // Find all items in the grid
            const items = grid.querySelectorAll('.component-rating-item');
            
            // Check if we have at least 3 items
            if (items.length >= 3) {
                // First, get the baseline position from the first card
                const firstCard = items[0];
                const firstScoreDisplay = firstCard ? firstCard.querySelector('.component-score-display') : null;
                const firstBottom = firstScoreDisplay ? parseInt(getComputedStyle(firstScoreDisplay).bottom) : 24;
                
                // Apply the exact same bottom position to all cards
                items.forEach(function(item, index) {
                    const scoreDisplay = item.querySelector('.component-score-display');
                    if (scoreDisplay) {
                        // Ensure absolute positioning with exact same bottom value
                        scoreDisplay.style.position = 'absolute';
                        scoreDisplay.style.bottom = firstBottom + 'px';
                        scoreDisplay.style.left = '0';
                        scoreDisplay.style.right = '0';
                        scoreDisplay.style.textAlign = 'center';
                        scoreDisplay.style.margin = '0';
                        scoreDisplay.style.padding = '0';
                        scoreDisplay.style.lineHeight = '1';
                        
                        // Force all percentage spans to have same vertical alignment
                        const percentSpan = scoreDisplay.querySelector('span:last-child');
                        if (percentSpan) {
                            percentSpan.style.fontSize = '0.8em';
                            percentSpan.style.marginLeft = '1px';
                            percentSpan.style.verticalAlign = 'baseline';
                        }
                        
                        // Ensure the number spans are also aligned
                        const numberSpan = scoreDisplay.querySelector('span:first-child');
                        if (numberSpan) {
                            numberSpan.style.verticalAlign = 'baseline';
                        }
                    }
                    
                    // Ensure all cards have identical height
                    item.style.height = '100%';
                    
                    // Ensure all chart wrappers have identical height and position
                    const chartWrapper = item.querySelector('.component-chart-wrapper');
                    if (chartWrapper) {
                        chartWrapper.style.height = '90px';
                        chartWrapper.style.marginBottom = '40px';
                        chartWrapper.style.marginTop = '0';
                        chartWrapper.style.display = 'flex';
                        chartWrapper.style.alignItems = 'center';
                        chartWrapper.style.justifyContent = 'center';
                    }
                });
            }
        });
        
        // Also fix the horizontal bar charts
        const horizontalBarContainers = document.querySelectorAll('.horizontal-bar-container');
        horizontalBarContainers.forEach(function(container) {
            const barValue = container.querySelector('.horizontal-bar-value');
            if (barValue) {
                barValue.style.position = 'absolute';
                barValue.style.right = '10px';
                barValue.style.top = '50%';
                barValue.style.transform = 'translateY(-50%)';
            }
        });
    }
    
    // Fix the spans containing numbers and percentage signs
    function fixPercentageSpans() {
        // Get all score displays
        const scoreDisplays = document.querySelectorAll('.component-score-display');
        
        // If we have score displays, use the first one as a reference
        if (scoreDisplays.length > 0) {
            // Make all score displays flex containers with baseline alignment
            scoreDisplays.forEach(function(display) {
                display.style.display = 'flex';
                display.style.justifyContent = 'center';
                display.style.alignItems = 'baseline';
                
                // Fix the spans inside
                const spans = display.querySelectorAll('span');
                if (spans.length >= 2) {
                    // Number span
                    const numberSpan = spans[0];
                    numberSpan.style.verticalAlign = 'baseline';
                    numberSpan.style.lineHeight = '1';
                    
                    // Percentage span
                    const percentSpan = spans[spans.length - 1];
                    percentSpan.style.fontSize = '0.8em';
                    percentSpan.style.marginLeft = '1px';
                    percentSpan.style.verticalAlign = 'baseline';
                    percentSpan.style.lineHeight = '1';
                }
            });
            
            // Special fix for the third card (valuation)
            const valuationDisplays = document.querySelectorAll('.component-ratings-grid .component-rating-item:nth-child(3) .component-score-display');
            valuationDisplays.forEach(function(display) {
                // Force the spans to be at the same baseline
                const spans = display.querySelectorAll('span');
                if (spans.length >= 2) {
                    spans.forEach(function(span) {
                        span.style.verticalAlign = 'baseline';
                        span.style.lineHeight = '1';
                    });
                }
            });
        }
    }
    
    // Set up a mutation observer to apply fixes when new content is added
    function setupMutationObserver() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length > 0) {
                    // Check if any added nodes contain rating elements
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            if (node.classList && 
                                (node.classList.contains('rating-visualization-container') || 
                                 node.classList.contains('component-rating-item') ||
                                 node.classList.contains('horizontal-bar-container'))) {
                                // Apply fixes with a slight delay to ensure rendering is complete
                                setTimeout(fixRatingAlignment, 100);
                            } else if (node.querySelector && 
                                      (node.querySelector('.rating-visualization-container') || 
                                       node.querySelector('.component-rating-item') ||
                                       node.querySelector('.horizontal-bar-container'))) {
                                // Apply fixes with a slight delay to ensure rendering is complete
                                setTimeout(fixRatingAlignment, 100);
                            }
                        }
                    });
                }
            });
        });
        
        // Start observing the document body for changes
        observer.observe(document.body, { 
            childList: true, 
            subtree: true 
        });
        
        return observer;
    }
    
    // Initial fix
    fixRatingAlignment();
    
    // Setup mutation observer
    const observer = setupMutationObserver();
    
    // Apply fixes at intervals to catch any dynamically loaded content
    setTimeout(fixRatingAlignment, 1000);
    setTimeout(fixRatingAlignment, 2000);
    setTimeout(fixRatingAlignment, 3000);
    
    // Expose the fix function globally
    window.fixRatingAlignment = fixRatingAlignment;
}); 