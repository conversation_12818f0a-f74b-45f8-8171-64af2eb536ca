<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Display</title>
    <link rel="stylesheet" href="/static/enhanced_rating_charts.css">
    <script src="/static/enhanced_rating_charts.js"></script>
    <style>
        /* Styles for the content INSIDE the iframe */
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 80px;
            height: 80px;
            background-color: rgba(255,255,255,0.5); /* Light visible background */
            overflow: visible; /* Make overflow visible for debugging */
            position: relative;
            font-family: Arial, sans-serif;
        }
        
        /* <PERSON>uffett-<PERSON><PERSON> enhanced visualization styles */
        .buffett-munger-section {
            width: 100%;
            margin: 15px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 8px;
        }
        
        /* Benchmark comparison styles */
        .benchmark-chart {
            margin: 15px 0;
        }
        
        .benchmark-bar {
            height: 25px;
            background-color: #e9e9e9;
            border-radius: 4px;
            margin: 8px 0;
            position: relative;
        }
        
        .benchmark-fill {
            height: 100%;
            border-radius: 4px;
            position: relative;
            transition: width 0.8s ease-out;
        }
        
        .benchmark-marker {
            position: absolute;
            top: 0;
            width: 2px;
            height: 100%;
            background-color: #000;
        }
        
        .benchmark-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 13px;
        }
        
        /* Financial metrics comparison styles */
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 13px;
        }
        
        .metrics-table th,
        .metrics-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .metrics-table th {
            font-weight: bold;
            color: #555;
            background-color: #f5f5f5;
        }
        
        .metrics-positive {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .metrics-negative {
            color: #F44336;
            font-weight: bold;
        }
        
        .metrics-neutral {
            color: #FFC107;
            font-weight: bold;
        }
        
        /* Valuation scenario table styles */
        .valuation-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 13px;
        }
        
        .valuation-table th,
        .valuation-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        
        .valuation-table th {
            font-weight: bold;
            background-color: #f5f5f5;
        }
        
        .valuation-upside {
            background-color: rgba(76, 175, 80, 0.2);
            color: #1B5E20;
        }
        
        .valuation-fair {
            background-color: rgba(255, 235, 59, 0.2);
            color: #F57F17;
        }
        
        .valuation-downside {
            background-color: rgba(244, 67, 54, 0.2);
            color: #B71C1C;
        }
        
        /* Risk matrix styles */
        .risk-matrix {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 13px;
        }
        
        .risk-matrix th,
        .risk-matrix td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
            vertical-align: middle;
        }
        
        .risk-matrix th {
            font-weight: bold;
            background-color: #f5f5f5;
        }
        
        .risk-bar {
            height: 12px;
            background-color: #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
            width: 100%;
            max-width: 100px;
        }
        
        .risk-bar-fill {
            height: 100%;
            border-radius: 6px;
        }
        
        .risk-high {
            background-color: #F44336;
        }
        
        .risk-medium {
            background-color: #FFC107;
        }
        
        .risk-low {
            background-color: #4CAF50;
        }
        
        /* Growth visualization styles */
        .growth-chart {
            margin: 10px 0;
        }
        
        .growth-bar {
            height: 20px;
            background-color: #e9e9e9;
            border-radius: 4px;
            margin: 5px 0;
            position: relative;
        }
        
        .growth-bar-fill {
            height: 100%;
            border-radius: 4px;
            position: relative;
            transition: width 0.8s ease-out;
        }
        
        .growth-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 12px;
        }
        
        /* Quick reference metrics box styles */
        .metrics-box {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .metric-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .metric-name {
            font-size: 12px;
            color: #757575;
            margin-bottom: 5px;
        }
        
        .metric-value {
            font-size: 16px;
            font-weight: bold;
        }
        
        .trend-indicator {
            display: inline-block;
            margin-left: 5px;
            font-size: 12px;
        }
        
        .trend-up {
            color: #4CAF50;
        }
        
        .trend-down {
            color: #F44336;
        }
        
        .trend-neutral {
            color: #757575;
        }
        
        /* Horizontal bar chart styles */
        .horizontal-bar-container {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .horizontal-bar {
            height: 24px;
            border-radius: 4px;
            background-color: #eee;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 5px;
        }
        
        .horizontal-bar-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 1s ease-in-out;
            position: relative;
        }
        
        .horizontal-bar-label {
            font-weight: bold;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }
        
        .horizontal-bar-value {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
            font-size: 12px;
        }
        
        .horizontal-bar-na {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 24px;
            background-color: #f5f5f5;
            color: #757575;
            font-style: italic;
            font-weight: bold;
            border-radius: 4px;
            border: 1px dashed #ccc;
        }
        
        /* The circular progress container */
        .circular-progress {
            position: relative;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* The circular progress percentage display */
        .progress-value {
            position: absolute;
            font-family: Arial, sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        /* Small percentage sign */
        .progress-value small {
            font-size: 10px;
            font-weight: normal;
            vertical-align: super;
            margin-left: 1px;
        }
        
        /* The actual progress circle */
        .progress-circle {
            position: absolute;
            width: 100%;
            height: 100%;
            clip: rect(0, 70px, 70px, 35px);
            border-radius: 50%;
        }
        
        /* The left half of the progress circle */
        .progress-circle-left {
            clip: rect(0, 35px, 70px, 0);
        }
        
        /* The right half of the progress circle */
        .progress-circle-right {
            clip: rect(0, 70px, 70px, 35px);
        }
        
        /* The circle fill */
        .progress-circle-fill {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            clip: rect(0, 35px, 70px, 0);
            background: #4CAF50; /* Default color, will be set via JS */
            transform: rotate(0deg);
            transition: transform 1s ease-in-out;
        }
        
        /* Animation for entrance */
        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.6);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .chart-animated {
            animation: fadeInScale 0.8s ease-out forwards;
        }
        
        /* Debug header */
        #iframe-debug-header {
            display: none; /* Change to none to hide debugging */
            position: absolute;
            top: -20px;
            left: 0;
            width: 100%;
            font-size: 8px;
            color: black;
            background: lightyellow;
            border: 1px solid orange;
            padding: 1px;
            text-align: center;
            z-index: 9999;
        }

        .rating-visualization {
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .rating-title {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.2em;
            font-weight: 600;
        }

        .horizontal-bar-container {
            margin-bottom: 15px;
        }

        .horizontal-bar-label {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-weight: 500;
            color: #444;
        }

        .horizontal-bar-wrapper {
            background: #f5f5f5;
            border-radius: 4px;
            overflow: hidden;
            height: 24px;
        }

        .horizontal-bar {
            height: 100%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .horizontal-bar-value {
            color: #fff;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .tooltip {
            position: relative;
            display: inline-block;
            margin-left: 5px;
            cursor: help;
        }

        .tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        .technical-term {
            border-bottom: 1px dotted #666;
            cursor: help;
        }
    </style>
</head>
<body>
    <h1 id="iframe-debug-header">IFRAME LOADED! Score: <span id="debug-score">?</span> Color: <span id="debug-color">?</span></h1>
    <div id="chart-container"></div>

    <script>
        // Function to generate horizontal bar chart HTML for ratings
        function generateRatingVisualizationHTML(businessQuality, financialStrength, valuation, management) {
            // Function to get color based on score
            function getColorForScore(score) {
                if (score === null || score === undefined || score === 'N/A') return '#999999';
                
                score = Number(score);
                if (score >= 90) return '#00A651'; // Excellent
                if (score >= 80) return '#4CAF50'; // Very Good
                if (score >= 70) return '#8BC34A'; // Good
                if (score >= 60) return '#CDDC39'; // Above Average
                if (score >= 50) return '#FFEB3B'; // Average
                if (score >= 40) return '#FFC107'; // Below Average
                if (score >= 30) return '#FF9800'; // Weak
                if (score >= 20) return '#FF5722'; // Poor
                if (score >= 10) return '#F44336'; // Very Poor
                return '#D32F2F';                  // Unacceptable
            }
            
            // Function to create a single bar
            function createBarHTML(label, score) {
                let normalizedScore = score;
                let barColor = getColorForScore(score);
                let displayValue = '';
                
                // Handle N/A values
                if (score === null || score === undefined || score === 'N/A') {
                    if (label === 'Management') {
                        displayValue = 'N/A';
                        normalizedScore = 0;
                    } else {
                        displayValue = 'N/A';
                        normalizedScore = 0;
                    }
                } else {
                    normalizedScore = parseInt(score);
                    displayValue = `${normalizedScore}%`;
                }
                
                return `
                <div class="horizontal-bar-container" style="margin-bottom: 15px; display: flex; flex-direction: column;">
                    <div class="horizontal-bar-label" style="margin-bottom: 5px; line-height: 1.5;">${label}</div>
                    <div class="horizontal-bar-wrapper" style="height: 24px; position: relative;">
                        <div class="horizontal-bar" style="width: ${normalizedScore}%; background-color: ${barColor}; position: relative; height: 24px; display: flex; align-items: center;">
                            <span class="horizontal-bar-value" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); line-height: 1; margin: 0; padding: 0;">${displayValue}</span>
                        </div>
                    </div>
                </div>`;
            }
            
            // Generate HTML for all bars
            return `
            <div class="rating-visualization" style="padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h3 class="rating-title" style="margin: 0 0 20px 0; color: #333; font-size: 1.2em; font-weight: 600; text-align: center;">Component Ratings</h3>
                ${createBarHTML('Business Quality/Moat', businessQuality)}
                ${createBarHTML('Financial Strength', financialStrength)}
                ${createBarHTML('Valuation', valuation)}
                ${createBarHTML('Management', management)}
            </div>`;
        }
        
        function getQueryParam(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        // Create a circular progress chart with CSS
        function createCircularProgressChart(container, score, color) {
            // Clear container
            container.innerHTML = '';
            
            // Handle N/A values
            if (score === null || score === undefined || score === 'N/A') {
                renderPlaceholderText(container, 'N/A', '#999999');
                return;
            }
            
            // Create SVG element
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', '100%');
            svg.setAttribute('height', '100%');
            svg.setAttribute('viewBox', '0 0 120 120');
            svg.style.transform = 'rotate(-90deg)'; // Start from top instead of right
            
            // Create background circle
            const bgCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            bgCircle.setAttribute('cx', '60');
            bgCircle.setAttribute('cy', '60');
            bgCircle.setAttribute('r', '54');
            bgCircle.setAttribute('fill', 'none');
            bgCircle.setAttribute('stroke', '#e6e6e6');
            bgCircle.setAttribute('stroke-width', '12');
            svg.appendChild(bgCircle);
            
            // Progress circle (foreground)
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', '60');
            circle.setAttribute('cy', '60');
            circle.setAttribute('r', '54');
            circle.setAttribute('fill', 'none');
            circle.setAttribute('stroke', color);
            circle.setAttribute('stroke-width', '12');
            
            // Calculate the circumference of the circle
            const circumference = 2 * Math.PI * 54;
            circle.setAttribute('stroke-dasharray', circumference);
            
            // For zero values, show a small visible segment (1%) to indicate it's a zero value, not an error
            const effectiveScore = score === 0 ? 1 : score;
            
            // Set the stroke-dashoffset to create the progress effect
            const offset = circumference - (effectiveScore / 100) * circumference;
            circle.setAttribute('stroke-dashoffset', offset);
            
            svg.appendChild(circle);
            
            // Add wrapper div to center the content
            const wrapper = document.createElement('div');
            wrapper.style.position = 'relative';
            wrapper.style.width = '100%';
            wrapper.style.height = '100%';
            wrapper.style.display = 'flex';
            wrapper.style.justifyContent = 'center';
            wrapper.style.alignItems = 'center';
            
            // Text in the center (score percentage)
            const textDiv = document.createElement('div');
            textDiv.style.position = 'absolute';
            textDiv.style.top = '50%';
            textDiv.style.left = '50%';
            textDiv.style.transform = 'translate(-50%, -50%)';
            textDiv.style.fontSize = '24px';
            textDiv.style.fontWeight = 'bold';
            textDiv.style.color = color;
            textDiv.textContent = `${score}%`;
            
            wrapper.appendChild(svg);
            wrapper.appendChild(textDiv);
            container.appendChild(wrapper);
            
            console.log('[createCircularProgressChart] Chart created successfully');
        }

        // Render a placeholder text instead of a chart
        function renderPlaceholderText(parentElement, text, color = '#757575') {
            console.log('[renderPlaceholderText] Rendering text:', text);
            
            // Clear any existing content
            parentElement.innerHTML = '';
            
            // Create a simple placeholder that looks similar to the chart
            const placeholder = document.createElement('div');
            placeholder.className = 'circular-progress';
            placeholder.style.background = '#f0f0f0';
            
            // Create the text display
            const textDisplay = document.createElement('div');
            textDisplay.className = 'progress-value';
            textDisplay.style.color = color;
            textDisplay.textContent = text;
            
            placeholder.appendChild(textDisplay);
            parentElement.appendChild(placeholder);
            
            console.log('[renderPlaceholderText] Placeholder created successfully');
        }

        // Main initialization function when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            console.log('[IFRAME] DOM fully loaded and parsed');
            
            // Get chart container
            const chartContainer = document.getElementById('chart-container');
            if (!chartContainer) {
                console.error('[IFRAME] Chart container not found');
                return;
            }
            
            // Get URL parameters
            const scoreParam = getQueryParam('score');
            const colorParam = getQueryParam('color');
            const decodedColor = colorParam ? decodeURIComponent(colorParam) : '#4CAF50';
            
            // Update debug display if enabled
            const debugScoreEl = document.getElementById('debug-score');
            const debugColorEl = document.getElementById('debug-color');
            if (debugScoreEl) debugScoreEl.textContent = scoreParam || 'NULL';
            if (debugColorEl) debugColorEl.textContent = decodedColor;
            
            console.log('[IFRAME] Parameters - Score:', scoreParam, 'Color:', decodedColor);
            
            // Handle missing score parameter
            if (scoreParam === null || scoreParam === undefined) {
                console.warn('[IFRAME] Missing score parameter. Displaying placeholder.');
                renderPlaceholderText(chartContainer, '---', decodedColor);
                return;
            }
            
            // Handle 'N/A' or '0' score value - use 0 with gray styling
            if (String(scoreParam).toUpperCase() === 'N/A' || scoreParam === '0') {
                console.log('[IFRAME] Score is "N/A" or "0". Displaying as 0.');
                createCircularProgressChart(chartContainer, 0, '#999999'); // Use gray color for 0 values
                return;
            }
            
            // Parse numeric score
            const scoreValue = parseFloat(scoreParam);
            if (isNaN(scoreValue)) {
                console.error('[IFRAME] Invalid score value:', scoreParam);
                renderPlaceholderText(chartContainer, 'ERR', '#dc3545');
                return;
            }
            
            // Create the circular progress chart
            const validScore = Math.max(0, Math.min(100, scoreValue));
            createCircularProgressChart(chartContainer, validScore, decodedColor);
        });

        // Add tooltips for technical terms
        function addTechnicalTermTooltips() {
            const terms = {
                'ROIC': 'Return on Invested Capital - A measure of how efficiently a company uses its capital to generate profits',
                'FCF Yield': 'Free Cash Flow Yield - The ratio of free cash flow to market capitalization, indicating the company\'s ability to generate cash',
                'Buyback Yield': 'The percentage of shares repurchased by the company, indicating management\'s confidence in the business'
            };

            Object.entries(terms).forEach(([term, definition]) => {
                const elements = document.querySelectorAll(`*:not(script):not(style)`);
                elements.forEach(element => {
                    if (element.textContent.includes(term)) {
                        const regex = new RegExp(term, 'g');
                        element.innerHTML = element.innerHTML.replace(
                            regex,
                            `<span class="technical-term" title="${definition}">${term}</span>`
                        );
                    }
                });
            });
        }

        // Call this function when the document is ready
        document.addEventListener('DOMContentLoaded', addTechnicalTermTooltips);
    </script>
</body>
</html>
