{% extends 'base.html' %}

{% block title %}Chatbot Settings - H Trader{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">Chatbot Settings</h1>
    <hr>

    <div class="card mb-4">
        <div class="card-header">
            LLM Configuration
        </div>
        <div class="card-body">
            {# Use a form to potentially POST updates later #}
            <form id="settings-form" action="/api/chatbot/update_settings" method="POST"> {# Placeholder action URL #}
                <div class="mb-3">
                    <label for="default-llm" class="form-label">Default Language Model</label>
                    <select class="form-select" id="default-llm" name="default_llm">
                        {# TODO: Populate options dynamically later if needed, get current setting #}
                        <option value="google">Google Gemini</option>
                        <option value="openai">OpenAI GPT</option>
                        <option value="anthropic">Anthropic Claude</option>
                        <option value="deepseek">DeepSeek</option>
                        {# Add other models as supported #}
                    </select>
                    <div class="form-text">Select the default AI model for analysis.</div>
                </div>

                <hr>
                <h5 class="mb-3">API Keys</h5>
                {# Add input fields for each LLM's API key #}
                {# Example for Google Gemini #}
                <div class="mb-3">
                    <label for="google-api-key" class="form-label">Google Gemini API Key</label>
                    <input type="password" class="form-control" id="google-api-key" name="google_api_key" placeholder="Enter your Google API Key">
                    {# TODO: Load existing key if stored, maybe show 'Key Set' indicator #}
                </div>
                {# Example for OpenAI #}
                <div class="mb-3">
                    <label for="openai-api-key" class="form-label">OpenAI API Key</label>
                    <input type="password" class="form-control" id="openai-api-key" name="openai_api_key" placeholder="Enter your OpenAI API Key">
                </div>
                {# Add inputs for Anthropic, DeepSeek etc. similarly #}
                 <div class="mb-3">
                    <label for="anthropic-api-key" class="form-label">Anthropic Claude API Key</label>
                    <input type="password" class="form-control" id="anthropic-api-key" name="anthropic_api_key" placeholder="Enter your Anthropic API Key">
                </div>
                 <div class="mb-3">
                    <label for="deepseek-api-key" class="form-label">DeepSeek API Key</label>
                    <input type="password" class="form-control" id="deepseek-api-key" name="deepseek_api_key" placeholder="Enter your DeepSeek API Key">
                </div>


                <div class="d-flex justify-content-between mt-4">
                    <button type="submit" class="btn btn-primary">Save Settings</button>
                    {# Add a separate button/form for clearing keys #}
                    <button type="button" id="clear-keys-button" class="btn btn-danger">Clear All Stored Keys</button>
                </div>
            </form>
        </div>
    </div>

    {# Add status message area #}
    <div id="settings-status" class="mt-3"></div>

</div>

{# Add JavaScript for handling clear keys button #}
<script>
document.addEventListener('DOMContentLoaded', () => {
    const clearButton = document.getElementById('clear-keys-button');
    const statusDiv = document.getElementById('settings-status');

    if (clearButton) {
        clearButton.addEventListener('click', async () => {
            if (confirm('Are you sure you want to clear all stored API keys? This action cannot be undone.')) {
                try {
                    const response = await fetch('/api/chatbot/clear_keys', { // Use the correct endpoint
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            // Add CSRF token header if needed by your Flask setup
                        }
                    });
                    const result = await response.json();

                    if (response.ok && result.success) {
                        statusDiv.innerHTML = '<div class="alert alert-success" role="alert">All API keys cleared successfully.</div>';
                        // Optionally clear the input fields
                        document.getElementById('google-api-key').value = '';
                        document.getElementById('openai-api-key').value = '';
                        document.getElementById('anthropic-api-key').value = '';
                        document.getElementById('deepseek-api-key').value = '';
                    } else {
                        statusDiv.innerHTML = `<div class="alert alert-danger" role="alert">Error clearing keys: ${result.message || 'Unknown error'}</div>`;
                    }
                } catch (error) {
                    console.error('Error clearing keys:', error);
                    statusDiv.innerHTML = `<div class="alert alert-danger" role="alert">Failed to send request to clear keys. Check console.</div>`;
                }
            }
        });
    }

    // TODO: Add JS for handling the settings form submission if needed (e.g., via AJAX)
    // const settingsForm = document.getElementById('settings-form');
    // settingsForm.addEventListener('submit', async (event) => { ... });
});
</script>

{% endblock %}