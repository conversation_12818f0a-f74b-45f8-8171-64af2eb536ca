<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>
            {% block title %}H Trader{% endblock %} - Elevate Your Investing
        </title>
        <link
            rel="icon"
            href="{{ url_for('static', filename='icons/home.svg') }}"
        />
        <link
            href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=VT323&display=swap"
            rel="stylesheet"
        />
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />
        {# Updated Font Awesome version #}
        <link
            rel="stylesheet"
            href="{{ url_for('static', filename='enhanced_rating_charts.css') }}"
        />
        {# Enhanced Rating Charts CSS #}
        <link
            rel="stylesheet"
            href="{{ url_for('static', filename='perfect_alignment.css') }}"
        />
        {# Perfect Alignment CSS #}
        <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
        <script src="https://cdn.plot.ly/plotly-2.32.0.min.js"></script>
        {# Add Plotly here for global access #}
        <script src="https://d3js.org/d3.v7.min.js"></script>
        {# ADDED D3.js v7 #}

        <style>
            /* --- Base & Theme Variables --- */
            :root {
                --bg-color: #f8f9fc; /* Lighter background */
                --card-bg-color: #ffffff;
                --nav-bg-color: #ffffff;
                --text-color: #212529; /* Darker text for better contrast */
                --text-muted-color: #6c757d;
                --input-bg-color: #ffffff;
                --border-color: #dee2e6;
                --nav-hover-color: #f1f3f5; /* Subtle hover */
                --icon-color: #495057;
                --tooltip-bg: #343a40;
                --tooltip-text: #ffffff;

                --highlight-color: #007bff;
                --highlight-darker: #0056b3;
                --highlight-gradient: linear-gradient(45deg, #0d6efd, #0056b3);
                --highlight-secondary: #ffc107; /* Yellow accent */
                --highlight-secondary-darker: #d39e00;
                --highlight-color-rgb: 0, 123, 255;

                --positive-color: #198754;
                --negative-color: #dc3545;

                --shadow-color: rgba(0, 0, 0, 0.05);
                --shadow-hover-color: rgba(0, 0, 0, 0.1);
                --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.07);

                --font-family: "Poppins", sans-serif;
                --font-pixel: "VT323", monospace;

                --header-height: 65px;
                --sidebar-width: 70px;
                --ticker-height: 40px; /* Height of the ticker */

                --particle-color: var(--highlight-color);
                --particle-link-color: var(--highlight-color);

                --ticker-bg: #000000;
                --ticker-text: #00ff00;
                --ticker-positive: #00ff00;
                --ticker-negative: #ff0000;

                /* Chatbot derived variables (Light) */
                --chatbot-bg: var(--card-bg-color);
                --chatbot-header-bg: var(--highlight-gradient);
                --chatbot-text-color: var(--text-color);
                --chatbot-muted-text-color: var(--text-muted-color);
                --chatbot-input-bg: var(--input-bg-color);
                --chatbot-border-color: var(--border-color);
                --chatbot-user-msg-bg: var(--highlight-color);
                --chatbot-user-msg-text: #ffffff; /* White text on blue bg */
                --chatbot-bot-msg-bg: var(
                    --nav-hover-color
                ); /* Light gray for bot */
                --chatbot-avatar-bg: var(--highlight-darker);
                --chatbot-avatar-icon-color: #ffffff; /* White icon on dark blue bg */
                --chatbot-shadow: 0 10px 35px rgba(0, 0, 0, 0.15);
            }

            [data-theme="dark"] {
                --bg-color: #12121f; /* Dark blue/purple background */
                --card-bg-color: #1a1a2e; /* Slightly lighter card */
                --nav-bg-color: #1a1a2e;
                --text-color: #e0e0e0; /* Light gray text */
                --text-muted-color: #a0a0b0; /* Lighter muted text */
                --input-bg-color: #2a2a3f; /* Darker input */
                --border-color: #3a3a5a; /* Visible border */
                --nav-hover-color: #2a2a3f; /* Darker hover */
                --icon-color: #e0e0e0; /* Light icons */
                --tooltip-bg: #2a2a3f; /* Dark tooltip background */
                --tooltip-text: #e0e0e0; /* Light tooltip text */

                --highlight-color: #8c4fff; /* Vibrant purple */
                --highlight-darker: #6a1fff;
                --highlight-gradient: linear-gradient(45deg, #a855f7, #6a1fff);
                --highlight-secondary: #facc15; /* Vibrant yellow */
                --highlight-secondary-darker: #eab308;
                --highlight-color-rgb: 140, 79, 255;

                --positive-color: #20c997; /* Teal */
                --negative-color: #f87171; /* Light red */

                --shadow-color: rgba(0, 0, 0, 0.2);
                --shadow-hover-color: rgba(0, 0, 0, 0.3);
                --shadow-lg: 0 12px 35px rgba(0, 0, 0, 0.25);

                --particle-color: var(--highlight-color);
                --particle-link-color: var(--highlight-color);

                --ticker-bg: #1a1a1a;
                --ticker-text: #33ff33; /* Brighter green */
                --ticker-positive: #33ff33;
                --ticker-negative: #ff4444; /* Brighter red */

                /* Chatbot derived variables (Dark) */
                --chatbot-bg: var(--card-bg-color);
                --chatbot-header-bg: var(--highlight-gradient);
                --chatbot-text-color: var(--text-color); /* Use main light text */
                --chatbot-muted-text-color: var(--text-muted-color);
                --chatbot-input-bg: var(--input-bg-color);
                --chatbot-border-color: var(--border-color);
                --chatbot-user-msg-bg: var(--highlight-color); /* Purple user bg */
                --chatbot-user-msg-text: #ffffff; /* White text on purple bg */
                --chatbot-bot-msg-bg: #2f2f4a; /* Slightly different dark bot message */
                --chatbot-avatar-bg: var(--highlight-darker); /* Darker purple avatar bg */
                --chatbot-avatar-icon-color: #ffffff; /* White icon on dark purple bg */
                --chatbot-shadow: 0 10px 35px rgba(0, 0, 0, 0.3);

                /* Enhanced dark mode support for all components */
                --table-header-bg: #2a2a3f;
                --table-row-hover: #2f2f4a;
                --modal-bg: var(--card-bg-color);
                --modal-backdrop: rgba(0, 0, 0, 0.7);
                --dropdown-bg: var(--card-bg-color);
                --dropdown-hover: var(--nav-hover-color);
                --alert-bg: var(--card-bg-color);
                --code-bg: #1e1e2e;
                --code-text: #f8f8f2;
                --selection-bg: rgba(140, 79, 255, 0.3);
                --selection-text: #ffffff;

                /* Portfolio specific dark mode variables */
                --portfolio-health-bg: var(--card-bg-color);
                --portfolio-metric-bg: var(--nav-hover-color);
                --portfolio-hover-bg: var(--card-bg-color);
                --portfolio-hover-text: var(--text-color);

                /* Chart specific dark mode variables */
                --chart-bg: var(--card-bg-color);
                --chart-text: var(--text-color);
                --chart-grid: var(--border-color);

                /* Analysis activity dark mode variables */
                --analysis-activity-bg: var(--card-bg-color);
                --analysis-gradient: linear-gradient(135deg, var(--card-bg-color) 0%, var(--nav-hover-color) 100%);
            }

            /* --- Global Styles --- */
            html {
                scroll-behavior: smooth;
            }
            *,
            *::before,
            *::after {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
                /* Better font rendering to prevent clipping */
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                text-rendering: optimizeLegibility;
            }
            body {
                margin: 0;
                font-family: var(--font-family);
                background-color: var(--bg-color);
                color: var(--text-color);
                transition:
                    background-color 0.3s ease,
                    color 0.3s ease;
                line-height: 1.6;
                padding-left: var(--sidebar-width);
                /* Dynamic padding top based on ticker visibility */
                /* padding-top will be set by JS */
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                /* Fix text rendering and clipping */
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                text-rendering: optimizeLegibility;
            }

            /* === COMPREHENSIVE GLOBAL TEXT COLOR FIX === */
            /* Force proper text colors everywhere to prevent contrast issues */

            /* All text elements - use !important to override any hardcoded colors */
            h1, h2, h3, h4, h5, h6,
            p, span, div, li, td, th,
            label, legend, caption,
            .text, .title, .subtitle,
            .content, .description,
            .value, .label, .metric,
            .analysis-content,
            .health-metric-label,
            .health-metric-value,
            .metric-label,
            .metric-value,
            .section-title,
            .card-title,
            .dashboard-title {
                color: var(--text-color) !important;
            }

            /* Muted text elements */
            .text-muted, .muted, .secondary,
            .subtitle, .description,
            small, .small,
            .text-secondary,
            .metric-change,
            .analysis-subtitle {
                color: var(--text-muted-color) !important;
            }

            /* Links */
            a:not(.btn):not(.button) {
                color: var(--highlight-color) !important;
            }

            a:not(.btn):not(.button):hover {
                color: var(--highlight-secondary) !important;
            }

            /* Form elements */
            input, textarea, select {
                background-color: var(--input-bg-color) !important;
                color: var(--text-color) !important;
                border-color: var(--border-color) !important;
            }

            /* SVG text elements */
            svg text:not(.d3-slice-label):not(#ratingText):not(.keep-white) {
                fill: var(--text-color) !important;
            }

            /* Fix text clipping issues */
            * {
                box-sizing: border-box;
                overflow-wrap: break-word;
            }

            /* Ensure text doesn't get cut off */
            .title, .heading, h1, h2, h3, h4, h5, h6 {
                overflow: visible !important;
                white-space: normal !important;
                word-wrap: break-word !important;
            }
            main {
                flex-grow: 1; /* Ensure main content takes available space */
                padding: 30px 40px;
                max-width: 1600px; /* Consistent max width */
                margin-left: auto; /* Center content */
                margin-right: auto;
                width: 100%;
            }
            ::-webkit-scrollbar {
                width: 8px;
                height: 8px;
            }
            ::-webkit-scrollbar-track {
                background: var(--nav-hover-color);
                border-radius: 4px;
            }
            ::-webkit-scrollbar-thumb {
                background: var(--highlight-color);
                border-radius: 4px;
            }
            ::-webkit-scrollbar-thumb:hover {
                background: var(--highlight-darker);
            }

            /* Utility Classes */
            .card {
                background-color: var(--card-bg-color);
                border-radius: 12px; /* Softer radius */
                padding: 25px 30px; /* More padding */
                margin-bottom: 30px; /* Consistent spacing */
                box-shadow: var(--shadow-lg);
                border: 1px solid var(--border-color);
                transition:
                    background-color 0.3s ease,
                    border-color 0.3s ease,
                    box-shadow 0.3s ease;
            }

            /* Enhanced dark mode support for all components */
            [data-theme="dark"] .card {
                background-color: var(--card-bg-color);
                border-color: var(--border-color);
                color: var(--text-color);
            }

            /* Modal support */
            .modal-content {
                background-color: var(--modal-bg, var(--card-bg-color));
                color: var(--text-color);
                border: 1px solid var(--border-color);
            }

            .modal-backdrop {
                background-color: var(--modal-backdrop, rgba(0, 0, 0, 0.5));
            }

            /* Dropdown support */
            .dropdown-menu {
                background-color: var(--dropdown-bg, var(--card-bg-color));
                border: 1px solid var(--border-color);
                color: var(--text-color);
            }

            .dropdown-item {
                color: var(--text-color);
                transition: background-color 0.2s ease;
            }

            .dropdown-item:hover {
                background-color: var(--dropdown-hover, var(--nav-hover-color));
                color: var(--text-color);
            }

            /* Alert support */
            .alert {
                background-color: var(--alert-bg, var(--card-bg-color));
                border: 1px solid var(--border-color);
                color: var(--text-color);
            }

            /* Code blocks */
            code, pre {
                background-color: var(--code-bg, #f8f9fa);
                color: var(--code-text, #212529);
                border: 1px solid var(--border-color);
            }

            /* Text selection */
            ::selection {
                background-color: var(--selection-bg, rgba(0, 123, 255, 0.3));
                color: var(--selection-text, inherit);
            }

            ::-moz-selection {
                background-color: var(--selection-bg, rgba(0, 123, 255, 0.3));
                color: var(--selection-text, inherit);
            }
            .card h2,
            .card h3 {
                margin-top: 0;
                margin-bottom: 20px;
                color: var(--highlight-color);
                border-bottom: 2px solid var(--highlight-secondary); /* Subtle accent */
                padding-bottom: 10px;
                display: inline-block; /* Fit border to text */
            }
            .card h2 i,
            .card h3 i {
                margin-right: 10px;
            }

            button,
            .button {
                /* Style buttons consistently */
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                padding: 10px 20px;
                font-size: 0.95rem;
                font-weight: 500;
                color: #fff;
                background: var(--highlight-gradient);
                border: none;
                border-radius: 25px; /* Pill shape */
                text-decoration: none;
                cursor: pointer;
                transition:
                    transform 0.2s ease,
                    box-shadow 0.3s ease,
                    background 0.3s ease;
                box-shadow: 0 3px 10px rgba(var(--highlight-color-rgb), 0.2);
            }
            button:hover,
            .button:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(var(--highlight-color-rgb), 0.3);
            }
            button:disabled,
            .button:disabled {
                background: var(--text-muted-color);
                cursor: not-allowed;
                opacity: 0.7;
                box-shadow: none;
                transform: none;
            }
            .delete-btn {
                /* Specific style for delete */
                background: linear-gradient(
                    45deg,
                    var(--negative-color),
                    #a71d2a
                ); /* Red gradient */
                box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
            }
            .delete-btn:hover {
                box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            }

            input[type="text"],
            input[type="number"],
            input[type="date"],
            input[type="email"],
            input[type="password"],
            textarea,
            select {
                display: block;
                width: 100%;
                padding: 12px 15px;
                margin-bottom: 15px;
                font-size: 0.95rem;
                line-height: 1.5;
                color: var(--text-color);
                background-color: var(--input-bg-color);
                background-clip: padding-box;
                border: 1px solid var(--border-color);
                border-radius: 8px; /* Slightly larger radius */
                transition:
                    border-color 0.2s ease-in-out,
                    box-shadow 0.2s ease-in-out,
                    background-color 0.3s ease,
                    color 0.3s ease;
            }
            input:focus,
            textarea:focus,
            select:focus {
                border-color: var(--highlight-color);
                outline: 0;
                box-shadow: 0 0 0 0.2rem rgba(var(--highlight-color-rgb), 0.25);
            }
            label {
                display: block;
                margin-bottom: 5px;
                font-weight: 500;
            }
            label i {
                margin-right: 5px;
                color: var(--highlight-color);
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 1rem;
            }
            th,
            td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid var(--border-color);
                vertical-align: middle;
            }
            th {
                font-weight: 600;
                background-color: var(--table-header-bg, var(--nav-hover-color));
                color: var(--text-color);
            }
            tbody tr:hover {
                background-color: var(--table-row-hover, var(--nav-hover-color));
            }

            /* Enhanced table support for dark mode */
            [data-theme="dark"] th {
                background-color: var(--table-header-bg);
                color: var(--text-color);
            }

            [data-theme="dark"] tbody tr:hover {
                background-color: var(--table-row-hover);
            }

            [data-theme="dark"] td {
                color: var(--text-color);
                border-bottom-color: var(--border-color);
            }
            .gain {
                color: var(--positive-color);
                font-weight: 500;
            }
            .loss {
                color: var(--negative-color);
                font-weight: 500;
            }

            /* --- Sidebar --- */
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100%;
                width: var(--sidebar-width);
                background-color: var(--nav-bg-color);
                box-shadow: 3px 0 10px var(--shadow-color);
                display: flex;
                flex-direction: column;
                align-items: center;
                padding-top: 20px;
                z-index: 1030; /* Higher z-index */
                transition:
                    background-color 0.3s ease,
                    box-shadow 0.3s ease;
            }
            .sidebar a {
                text-decoration: none;
                margin: 15px 0; /* Slightly less margin */
                color: var(--icon-color);
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                width: 44px;
                height: 44px; /* Slightly larger */
                border-radius: 10px; /* Rounded square */
                transition:
                    background-color 0.2s ease,
                    transform 0.2s ease,
                    color 0.2s ease;
            }
            .sidebar a img,
            .sidebar a i {
                /* Support both img and Font Awesome icons */
                width: 24px;
                height: 24px;
                transition: filter 0.3s ease;
                font-size: 20px; /* Size for Font Awesome */
                color: inherit; /* Inherit color from parent 'a' tag */
            }
            /* [data-theme="dark"] .sidebar a img { filter: invert(90%) sepia(10%) saturate(500%) hue-rotate(200deg) brightness(100%) contrast(90%); } */ /* Adjusted potential dark mode filter if needed*/
            .sidebar a:hover,
            .sidebar a.active {
                /* Add active state */
                background-color: var(--nav-hover-color);
                transform: scale(1.05); /* Less aggressive scale */
                color: var(
                    --highlight-color
                ); /* Highlight color on hover/active */
            }
            .tooltip {
                position: absolute;
                top: 50%;
                left: calc(var(--sidebar-width) + 12px); /* More spacing */
                transform: translateY(-50%) translateX(-10px);
                background-color: var(--tooltip-bg);
                color: #ffffff !important; /* Force white text for sidebar tooltips */
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                white-space: nowrap;
                visibility: hidden;
                opacity: 0;
                transition:
                    opacity 0.3s ease,
                    transform 0.3s ease;
                z-index: 1031; /* Above sidebar */
            }

            /* Ensure tooltip text is white in both light and dark modes */
            .sidebar .tooltip {
                color: #ffffff !important;
            }

            [data-theme="dark"] .sidebar .tooltip {
                color: #ffffff !important;
            }

            :root .sidebar .tooltip {
                color: #ffffff !important;
            }
            .sidebar a:hover .tooltip {
                visibility: visible;
                opacity: 1;
                transform: translateY(-50%) translateX(0);
            }

            /* --- Theme Switch (in Sidebar) --- */
            .sidebar .theme-switch-container {
                margin-top: auto; /* Push to bottom */
                margin-bottom: 20px;
                padding: 10px 0;
            }
            .sidebar .theme-switch {
                position: relative;
                display: inline-block;
                width: 50px;
                height: 26px;
            }
            .sidebar .theme-switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }
            .sidebar .slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ccc;
                border-radius: 26px;
                transition: background-color 0.4s ease;
                display: flex;
                align-items: center; /* Center icons */
            }
            .sidebar .slider:before {
                /* The moving circle */
                position: absolute;
                content: "";
                height: 20px;
                width: 20px;
                left: 3px;
                bottom: 3px;
                background-color: white;
                border-radius: 50%;
                transition: transform 0.4s ease;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                z-index: 1;
            }
            .sidebar .slider .icon {
                /* Sun/Moon icons container */
                position: absolute;
                width: 100%;
                display: flex;
                justify-content: space-between;
                padding: 0 5px;
                z-index: 0;
            }
            .sidebar .slider .fa-sun,
            .sidebar .slider .fa-moon {
                color: #f39c12; /* Yellow/Orange */
                font-size: 14px;
                transition: opacity 0.3s ease;
            }
            .sidebar .slider .fa-moon {
                color: #8c4fff;
                opacity: 0.5;
            } /* Initial state: Moon slightly visible */
            .sidebar .slider .fa-sun {
                opacity: 1;
            } /* Initial state: Sun fully visible */

            .sidebar input:checked + .slider {
                background-color: var(--highlight-darker);
            } /* Use darker highlight */
            .sidebar input:checked + .slider:before {
                transform: translateX(24px);
            }
            .sidebar input:checked + .slider .fa-sun {
                opacity: 0.5;
            } /* Dark mode: Sun slightly visible */
            .sidebar input:checked + .slider .fa-moon {
                opacity: 1;
            } /* Dark mode: Moon fully visible */

            /* --- Header --- */
            header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 30px;
                height: var(--header-height);
                background-color: var(--nav-bg-color);
                color: var(--text-color);
                border-bottom: 1px solid var(--border-color);
                position: fixed;
                top: 0; /* Will be adjusted by JS if ticker visible */
                left: var(--sidebar-width);
                right: 0;
                z-index: 1020; /* Below sidebar, above ticker */
                transition:
                    background-color 0.3s ease,
                    box-shadow 0.3s ease,
                    border-color 0.3s ease,
                    top 0.3s ease;
            }
            header h1 {
                font-size: 1.5rem;
                font-weight: 600;
                transition: color 0.3s ease;
                margin: 0;
            }
            body.scrolled header {
                box-shadow: 0 4px 15px var(--shadow-color);
            }

            /* --- Ticker (Only on Index/Home) --- */
            .stock-ticker-container {
                background-color: var(--ticker-bg);
                color: var(--ticker-text);
                padding: 0; /* Padding handled by inner content */
                width: calc(
                    100% - var(--sidebar-width)
                ); /* Full width minus sidebar */
                overflow: hidden;
                white-space: nowrap;
                position: fixed;
                top: 0;
                left: var(--sidebar-width);
                right: 0;
                height: var(--ticker-height);
                line-height: var(--ticker-height); /* Vertical center */
                z-index: 1025; /* Above header, below sidebar */
                font-family: var(--font-pixel);
                font-size: 1.4rem;
                border-bottom: 1px solid var(--nav-hover-color);
                display: none; /* Hidden by default, shown via JS on index */
            }
            .stock-ticker-move {
                display: inline-block; /* Important for animation */
                padding-left: 100%; /* Start fully off-screen left for initial move-in */
                animation: tickerScroll 90s linear infinite; /* Adjust duration for speed */
                will-change: transform; /* Performance hint */
                cursor: default;
            }
            /* Pause on hover */
            .stock-ticker-container:hover .stock-ticker-move {
                animation-play-state: paused;
            }
            .ticker-item {
                margin-right: 50px; /* More spacing */
                display: inline-block;
            }
            .ticker-item .symbol {
                font-weight: bold;
                color: #ffff00 !important; /* Always yellow for symbols - retro style */
            }
            .ticker-item .price {
                margin-left: 8px;
                color: #ffff00 !important; /* Always yellow for price - retro style */
            }
            .ticker-item .change {
                margin-left: 8px;
                font-size: 0.9em;
            } /* Smaller change % */
            .ticker-positive {
                color: #00ff00 !important; /* Always green for positive - retro style */
            }
            .ticker-negative {
                color: #ff0000 !important; /* Always red for negative - retro style */
            }

            /* Ensure retro colors are preserved in dark mode */
            [data-theme="dark"] .ticker-item .symbol {
                color: #ffff00 !important; /* Always yellow for symbols in dark mode */
            }
            [data-theme="dark"] .ticker-item .price {
                color: #ffff00 !important; /* Always yellow for price in dark mode */
            }
            [data-theme="dark"] .ticker-positive {
                color: #00ff00 !important; /* Always green for positive in dark mode */
            }
            [data-theme="dark"] .ticker-negative {
                color: #ff0000 !important; /* Always red for negative in dark mode */
            }
            #ticker-placeholder,
            #ticker-error {
                padding-left: 20px;
                display: inline-block;
                color: var(--text-muted-color);
                font-size: 1rem;
                font-family: var(--font-family);
            }
            /* Continuous scroll animation */
            @keyframes tickerScroll {
                0% {
                    transform: translateX(0%);
                }
                100% {
                    transform: translateX(-50%);
                } /* Move exactly half the duplicated width */
            }

            /* --- Footer --- */
            footer {
                text-align: center;
                padding: 25px 0;
                margin-top: 40px;
                font-size: 0.9rem;
                color: var(--text-muted-color);
                border-top: 1px solid var(--border-color);
                flex-shrink: 0; /* Prevent footer from shrinking */
                transition:
                    color 0.3s ease,
                    border-color 0.3s ease,
                    background-color 0.3s ease;
                /* No margin-left needed if we don't give it a background */
                width: calc(
                    100% - var(--sidebar-width)
                ); /* Align with main content */
                margin-left: var(--sidebar-width);
            }
            [data-theme="dark"] footer {
                background-color: var(--nav-bg-color);
            } /* Optional dark footer bg */

            /* --- Particles Background --- */
            #particles-js {
                position: fixed;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                z-index: -1;
                background-color: var(--bg-color);
                transition: background-color 0.3s ease;
            }

            /* --- Flash Messages --- */
            .flash-messages-container {
                max-width: 1600px;
                margin: -15px auto 15px auto; /* Adjust alignment */
                padding: 0 40px; /* Match main padding */
                position: sticky; /* Keep messages visible */
                top: calc(
                    var(--header-height) + 15px
                ); /* Position below header */
                z-index: 1010; /* Below header */
            }
            .flash-message {
                padding: 15px 20px;
                margin-bottom: 15px;
                border-radius: 8px;
                border: 1px solid transparent;
                background-color: var(--card-bg-color);
                color: var(--text-color);
                box-shadow: var(--shadow-lg);
                display: flex;
                justify-content: space-between;
                align-items: center;
                opacity: 0;
                transform: translateY(-10px);
                animation: fadeInDown 0.5s ease forwards;
            }
            .flash-message.error {
                border-left: 5px solid var(--negative-color);
                background-color: color-mix(
                    in srgb,
                    var(--negative-color) 10%,
                    var(--card-bg-color)
                );
            }
            .flash-message.success {
                border-left: 5px solid var(--positive-color);
                background-color: color-mix(
                    in srgb,
                    var(--positive-color) 10%,
                    var(--card-bg-color)
                );
            }
            .flash-message.warning {
                border-left: 5px solid var(--highlight-secondary);
                background-color: color-mix(
                    in srgb,
                    var(--highlight-secondary) 10%,
                    var(--card-bg-color)
                );
            }
            .flash-message strong {
                margin-right: 8px;
            }
            .flash-message button {
                background: none;
                border: none;
                font-size: 1.4rem;
                line-height: 1;
                cursor: pointer;
                opacity: 0.6;
                color: var(--text-muted-color);
                padding: 0 5px;
                transition: opacity 0.2s ease;
            }
            .flash-message button:hover {
                opacity: 1;
            }
            @keyframes fadeInDown {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* === GLOBAL CONTRAST FIX - FORCE PROPER COLORS EVERYWHERE === */

            /* Universal text color enforcement for dark theme */
            [data-theme="dark"] {
                /* Force all text elements to use proper colors */
                *, *::before, *::after {
                    border-color: var(--border-color) !important;
                }

                /* All text elements */
                h1, h2, h3, h4, h5, h6,
                p, span, div, li, td, th,
                label, legend, caption,
                .text, .title, .subtitle,
                .metric-label, .metric-value,
                .chart-title, .chart-label,
                .dashboard-title, .section-title,
                .analysis-title, .analysis-text,
                .rating-title, .rating-label,
                .performance-title, .performance-subtitle {
                    color: var(--text-color) !important;
                }

                /* Muted text elements */
                .text-muted, .muted, .secondary,
                .subtitle, .description,
                .metric-change, .chart-subtitle,
                small, .small {
                    color: var(--text-muted-color) !important;
                }

                /* Form controls */
                input[type="text"],
                input[type="number"],
                input[type="date"],
                input[type="email"],
                input[type="password"],
                textarea,
                select {
                    background-color: var(--input-bg-color) !important;
                    color: var(--text-color) !important;
                    border-color: var(--border-color) !important;
                }

                /* Buttons - keep white text on colored backgrounds */
                button:not(.secondary-link):not(.outline),
                .button:not(.secondary-link):not(.outline),
                .btn:not(.btn-outline):not(.btn-secondary) {
                    color: #ffffff !important;
                }

                /* Secondary/outline buttons */
                .secondary-link, .outline, .btn-outline, .btn-secondary {
                    color: var(--text-color) !important;
                    background-color: transparent !important;
                    border-color: var(--border-color) !important;
                }

                /* Links */
                a:not(.button):not(.btn) {
                    color: var(--highlight-color) !important;
                }

                a:not(.button):not(.btn):hover {
                    color: var(--highlight-secondary) !important;
                }

                /* SVG text elements */
                svg text:not(.keep-white) {
                    fill: var(--text-color) !important;
                }

                /* Keep white text on colored elements */
                .keep-white,
                .d3-slice-label,
                #ratingText,
                .metric-change.positive,
                .metric-change.negative,
                .ticker-badge,
                .category-icon,
                .verdict-icon,
                .card-icon,
                .gauge-text {
                    color: #ffffff !important;
                    fill: #ffffff !important;
                }

                /* Tables */
                table {
                    background-color: var(--card-bg-color) !important;
                    color: var(--text-color) !important;
                }

                th {
                    background-color: var(--table-header-bg, var(--nav-hover-color)) !important;
                    color: var(--text-color) !important;
                }

                td {
                    color: var(--text-color) !important;
                    border-color: var(--border-color) !important;
                }

                /* Cards and containers */
                .card, .container, .box, .panel {
                    background-color: var(--card-bg-color) !important;
                    color: var(--text-color) !important;
                    border-color: var(--border-color) !important;
                }

                /* Modals and overlays */
                .modal-content, .overlay, .popup {
                    background-color: var(--card-bg-color) !important;
                    color: var(--text-color) !important;
                    border-color: var(--border-color) !important;
                }

                /* Tooltips */
                .tooltip, .tooltip-content, .d3-tooltip {
                    background-color: var(--card-bg-color) !important;
                    color: var(--text-color) !important;
                    border: 1px solid var(--border-color) !important;
                }
            }

            /* === ULTRA-AGGRESSIVE CONTRAST FIX === */
            /* Force proper colors on EVERYTHING in dark mode */
            [data-theme="dark"] * {
                /* Override any hardcoded black text */
                color: var(--text-color) !important;
            }

            /* Specific overrides for elements that should keep their colors */
            [data-theme="dark"] .keep-original-color,
            [data-theme="dark"] .positive-color,
            [data-theme="dark"] .negative-color,
            [data-theme="dark"] .success,
            [data-theme="dark"] .error,
            [data-theme="dark"] .warning,
            [data-theme="dark"] .info {
                color: inherit !important;
            }

            /* Keep white text on colored backgrounds */
            [data-theme="dark"] button,
            [data-theme="dark"] .btn,
            [data-theme="dark"] .badge,
            [data-theme="dark"] .tag,
            [data-theme="dark"] .chip,
            [data-theme="dark"] .metric-change.positive,
            [data-theme="dark"] .metric-change.negative,
            [data-theme="dark"] .ticker-badge,
            [data-theme="dark"] .category-icon,
            [data-theme="dark"] .verdict-icon,
            [data-theme="dark"] .card-icon,
            [data-theme="dark"] th,
            [data-theme="dark"] .table-header {
                color: #ffffff !important;
            }

            /* SVG text elements */
            [data-theme="dark"] svg text {
                fill: var(--text-color) !important;
            }

            /* Keep white on colored SVG elements */
            [data-theme="dark"] svg .keep-white,
            [data-theme="dark"] svg .d3-slice-label,
            [data-theme="dark"] svg #ratingText {
                fill: #ffffff !important;
            }

            /* Portfolio specific fixes */
            [data-theme="dark"] .health-score-text,
            [data-theme="dark"] .health-status-text,
            [data-theme="dark"] .health-metric-label,
            [data-theme="dark"] .health-metric-value,
            [data-theme="dark"] .analysis-content,
            [data-theme="dark"] .analysis-content p,
            [data-theme="dark"] .analysis-content h1,
            [data-theme="dark"] .analysis-content h2,
            [data-theme="dark"] .analysis-content h3,
            [data-theme="dark"] .analysis-content strong,
            [data-theme="dark"] .analysis-content em {
                color: var(--text-color) !important;
                fill: var(--text-color) !important;
            }

            /* DCF result specific fixes */
            [data-theme="dark"] .dcf-hero-title,
            [data-theme="dark"] .dcf-hero-subtitle,
            [data-theme="dark"] .result-title,
            [data-theme="dark"] .result-subtitle,
            [data-theme="dark"] .section-title,
            [data-theme="dark"] .metric-label,
            [data-theme="dark"] .metric-value,
            [data-theme="dark"] .analysis-title,
            [data-theme="dark"] .analysis-subtitle {
                color: var(--text-color) !important;
            }

            /* Chart and visualization fixes */
            [data-theme="dark"] .chart-title,
            [data-theme="dark"] .chart-label,
            [data-theme="dark"] .chart-axis text,
            [data-theme="dark"] .axis text,
            [data-theme="dark"] .tick text {
                fill: var(--text-color) !important;
                color: var(--text-color) !important;
            }

            /* Responsive Adjustments */
            @media (max-width: 768px) {
                body {
                    padding-left: 0;
                    padding-top: var(--header-height);
                } /* Remove sidebar padding, keep header */
                .sidebar {
                    transform: translateX(-100%);
                    width: 250px; /* Allow toggle later if needed */
                }
                header {
                    left: 0;
                    width: 100%;
                }
                main {
                    padding: 20px;
                }
                .flash-messages-container {
                    padding: 0 20px;
                    top: calc(var(--header-height) + 10px);
                }
                footer {
                    width: 100%;
                    margin-left: 0;
                }
                .stock-ticker-container {
                    width: 100%;
                    left: 0;
                }
                header h1 {
                    font-size: 1.3rem;
                }
                /* Add a menu toggle button if sidebar needs to be accessible */

                /* Mobile chatbot adjustments */
                #chatbot-toggle {
                    bottom: 20px;
                    right: 20px;
                    width: 60px;
                    height: 60px;
                }

                #chatbot-window {
                    width: 100vw;
                    left: 0;
                    right: 0;
                }
            }
        </style>
        {% block head_extra %}{% endblock %} {# Block for page-specific CSS/JS
        in head #}
    </head>
    <body data-theme="light">
        {# JS will set theme from localStorage #}

        <div id="particles-js"></div>
        {# Particles Background #} {# Stock Ticker - Structure is here,
        visibility controlled by JS #}
        <div class="stock-ticker-container" id="stockTickerContainer">
            <div class="stock-ticker-move" id="stockTickerMove">
                <span id="ticker-placeholder">Loading ticker...</span>
                {# Ticker items will be injected here by JS #}
            </div>
        </div>

        {# Sidebar #}
        <nav class="sidebar">
            {# Use nav element #} {# Add a Logo/Brand element if desired #} {#
            <a href="{{ url_for('home') }}" class="sidebar-brand">
                <img src="..." alt="Logo" />
            </a>
            #}

            <a href="{{ url_for('home') }}" title="Home">
                <i class="fas fa-home"></i> {# Use Font Awesome #}
                <div class="tooltip">Home</div>
            </a>
            <a href="{{ url_for('portfolio_page') }}" title="Portfolio">
                <i class="fas fa-chart-pie"></i> {# Use Font Awesome #}
                <div class="tooltip">Portfolio</div>
            </a>
            <a href="{{ url_for('dcf_page') }}" title="DCF Calculator">
                <i class="fas fa-calculator"></i> {# Use Font Awesome #}
                <div class="tooltip">DCF Calculator</div>
            </a>
            <a
                href="{{ url_for('financial_reports.index') }}"
                title="Financial Reports"
            >
                <i class="fas fa-file-alt"></i> {# Use Font Awesome #}
                <div class="tooltip">Financial Reports</div>
            </a>
            {# Sidebar Theme Switch #}
            <div class="theme-switch-container">
                <label class="theme-switch" title="Toggle Theme">
                    <input
                        type="checkbox"
                        id="themeToggleCheckboxSidebar"
                        onchange="toggleTheme()"
                    />
                    {# Use onchange #}
                    <span class="slider">
                        <span class="icon">
                            <i class="fas fa-sun"></i>
                            <i class="fas fa-moon"></i>
                        </span>
                    </span>
                </label>
            </div>
        </nav>

        {# Global Header #}
        <header id="pageHeader">
            <h1>{% block header_title %}H Trader{% endblock %}</h1>
            {# Header content if needed, e.g., user profile, search #}
        </header>

        {# Main Content Area - This will be replaced by child templates #}
        <main>
            {# Flash Messages - Improved Styling #} {% with messages =
            get_flashed_messages(with_categories=true) %} {% if messages %}
            <div class="flash-messages-container">
                {% for category, message in messages %}
                <div class="flash-message {{ category }}" role="alert">
                    <span>
                        {# Wrap message and category text #} {% if category ==
                        'error' %}
                        <strong
                            ><i class="fas fa-times-circle"></i> Error:</strong
                        >
                        {% elif category == 'success' %}
                        <strong
                            ><i class="fas fa-check-circle"></i>
                            Success:</strong
                        >
                        {% elif category == 'warning' %}
                        <strong
                            ><i class="fas fa-exclamation-triangle"></i>
                            Warning:</strong
                        >
                        {% else %}
                        <strong
                            ><i class="fas fa-info-circle"></i> Info:</strong
                        >
                        {% endif %} {{ message }}
                    </span>
                    <button
                        type="button"
                        aria-label="Close"
                        onclick="this.parentElement.style.display='none'"
                    >
                        &times;
                    </button>
                </div>
                {% endfor %}
            </div>
            {% endif %} {% endwith %} {% block content %}
            <div class="card">
                {# Default content wrapped in card #}
                <h2>Welcome to H Trader!</h2>
                <p>Select an option from the sidebar to get started.</p>
            </div>
            {% endblock %}
        </main>

        {# Global Footer #}
        <footer>
            <p>
                &copy; {{ current_year if current_year else
                datetime.utcnow().year }} H Trader Finance Tool. All Rights
                Reserved.
            </p>
        </footer>

        {# --- Include Chatbot --- #} {% include 'chatbot.html' %} {# --- Global
        JavaScript --- #}
        <script>
            // --- Theme Handling ---
            const themeToggleSidebar = document.getElementById(
                "themeToggleCheckboxSidebar",
            );

            function setTheme(themeName) {
                document.body.setAttribute("data-theme", themeName);
                if (themeToggleSidebar) {
                    themeToggleSidebar.checked = themeName === "dark";
                }
                localStorage.setItem("theme", themeName);
                updateParticlesTheme(themeName);
                // Update Plotly charts theme if function exists
                if (typeof updatePlotlyChartTheme === "function") {
                    updatePlotlyChartTheme(themeName);
                }
            }

            function toggleTheme() {
                const currentTheme =
                    document.body.getAttribute("data-theme") || "light";
                const newTheme = currentTheme === "dark" ? "light" : "dark";
                setTheme(newTheme);
            }

            // --- Active Sidebar Link ---
            function setActiveSidebarLink() {
                const currentPath = window.location.pathname;
                const sidebarLinks = document.querySelectorAll(".sidebar a");
                sidebarLinks.forEach((link) => {
                    link.classList.remove("active");
                    // Handle root path and specific page paths
                    if (
                        link.getAttribute("href") === currentPath ||
                        (currentPath === "/" &&
                            link.getAttribute("href") ===
                                "{{ url_for('home') }}")
                    ) {
                        link.classList.add("active");
                    }
                    // Special case for portfolio page if URL has parameters
                    if (
                        link.getAttribute("href") ===
                            "{{ url_for('portfolio_page') }}" &&
                        currentPath.startsWith(
                            "{{ url_for('portfolio_page') }}",
                        )
                    ) {
                        link.classList.add("active");
                    }
                });
            }

            // --- Ticker Handling ---
            const tickerContainer = document.getElementById(
                "stockTickerContainer",
            );
            const tickerMove = document.getElementById("stockTickerMove");
            const tickerPlaceholder =
                document.getElementById("ticker-placeholder");
            const pageHeader = document.getElementById("pageHeader");
            let tickerIntervalId = null;
            let isTickerVisible = false;

            function createTickerHTML(data) {
                const validData = data.filter(
                    (item) =>
                        item &&
                        item.symbol &&
                        item.price !== null &&
                        item.price !== "N/A",
                );
                if (validData.length === 0) {
                    return '<span id="ticker-error">Ticker data unavailable.</span>';
                }
                const itemsHTML = validData
                    .map((item) => {
                        const price =
                            typeof item.price === "number"
                                ? item.price.toFixed(2)
                                : "N/A";
                        const changePercent =
                            typeof item.changesPercentage === "number"
                                ? item.changesPercentage
                                : 0;
                        const changeClass =
                            changePercent >= 0
                                ? "ticker-positive"
                                : "ticker-negative";
                        const sign = changePercent >= 0 ? "+" : "";
                        const formattedChange =
                            typeof item.changesPercentage === "number"
                                ? `(${sign}${changePercent.toFixed(2)}%)`
                                : "(N/A)";

                        // Use Font Awesome icons for up/down trend
                        const trendIcon =
                            changePercent > 0
                                ? '<i class="fas fa-arrow-up"></i>'
                                : changePercent < 0
                                  ? '<i class="fas fa-arrow-down"></i>'
                                  : '<i class="fas fa-minus"></i>';

                        return `<span class="ticker-item">
                            <span class="symbol">${item.symbol}</span>
                            <span class="price">$${price}</span>
                            <span class="change ${changeClass}">
                                ${trendIcon} ${formattedChange}
                            </span>
                        </span>`;
                    })
                    .join("");
                // Duplicate content for seamless scroll effect - IMPORTANT!
                return itemsHTML + itemsHTML;
            }

            async function fetchTickerData() {
                if (!tickerContainer || !tickerMove) return;
                try {
                    const response = await fetch(
                        "{{ url_for('get_eodhd_ticker_data') }}",
                    );
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    const data = await response.json();

                    if (Array.isArray(data) && data.length > 0) {
                        const tickerContent = createTickerHTML(data);
                        // Check if content is different before updating to avoid flicker/animation reset
                        // Note: Direct comparison might reset animation. A more complex check might be needed
                        // if frequent identical updates cause issues. For now, just update.
                        tickerMove.innerHTML = tickerContent;
                    } else {
                        throw new Error("No valid ticker data received");
                    }
                } catch (error) {
                    console.error("Error fetching ticker data:", error);
                    if (
                        tickerMove &&
                        !tickerMove.querySelector("#ticker-error")
                    ) {
                        // Avoid adding multiple errors
                        tickerMove.innerHTML = `<span id="ticker-error" title="${error.message}">Market data unavailable. Retrying...</span>`;
                    }
                }
            }

            function showTicker() {
                if (tickerContainer && pageHeader) {
                    tickerContainer.style.display = "block";
                    pageHeader.style.top = `var(--ticker-height)`;
                    document.body.style.paddingTop = `calc(var(--header-height) + var(--ticker-height))`;
                    isTickerVisible = true;
                    fetchTickerData(); // Initial fetch
                    if (tickerIntervalId) clearInterval(tickerIntervalId); // Clear previous interval if any
                    tickerIntervalId = setInterval(fetchTickerData, 60 * 1000); // Refresh every 60 seconds
                }
            }

            function hideTicker() {
                if (tickerContainer && pageHeader) {
                    tickerContainer.style.display = "none";
                    pageHeader.style.top = `0px`;
                    document.body.style.paddingTop = `var(--header-height)`;
                    isTickerVisible = false;
                    if (tickerIntervalId) clearInterval(tickerIntervalId);
                    tickerIntervalId = null;
                }
            }

            function checkAndSetTickerVisibility() {
                const isHomePage =
                    window.location.pathname === "/" ||
                    window.location.pathname === "{{ url_for('home') }}";
                if (isHomePage) {
                    showTicker();
                } else {
                    hideTicker();
                }
            }

            // --- Particles Handling ---
            function getParticleColors(theme) {
                const style = getComputedStyle(document.body);
                if (theme === "dark") {
                    return {
                        color:
                            style
                                .getPropertyValue("--highlight-color")
                                .trim() || "#8c4fff",
                        linkColor:
                            style
                                .getPropertyValue("--highlight-color")
                                .trim() || "#8c4fff",
                        bgColor:
                            style.getPropertyValue("--bg-color").trim() ||
                            "#12121f",
                    };
                } else {
                    return {
                        color:
                            style
                                .getPropertyValue("--highlight-color")
                                .trim() || "#007bff",
                        linkColor:
                            style
                                .getPropertyValue("--highlight-color")
                                .trim() || "#007bff",
                        bgColor:
                            style.getPropertyValue("--bg-color").trim() ||
                            "#f8f9fc", // Match body bg
                    };
                }
            }

            function initParticles() {
                const theme =
                    document.body.getAttribute("data-theme") || "light";
                const colors = getParticleColors(theme);
                const particlesElement =
                    document.getElementById("particles-js");
                if (!particlesElement || typeof particlesJS === "undefined") {
                    console.warn("Particles container or library not found.");
                    return;
                }
                particlesElement.style.backgroundColor = colors.bgColor; // Ensure initial bg matches

                particlesJS("particles-js", {
                    /* Configuration using variables */
                    particles: {
                        number: {
                            value: 60,
                            density: { enable: true, value_area: 900 },
                        },
                        color: { value: colors.color },
                        shape: { type: "circle" },
                        opacity: { value: 0.3, random: true },
                        size: { value: 3, random: true },
                        line_linked: {
                            enable: true,
                            distance: 150,
                            color: colors.linkColor,
                            opacity: 0.15,
                            width: 1,
                        },
                        move: {
                            enable: true,
                            speed: 1.5,
                            direction: "none",
                            random: true,
                            straight: false,
                            out_mode: "out",
                            bounce: false,
                        },
                    },
                    interactivity: {
                        detect_on: "canvas",
                        events: {
                            onhover: { enable: true, mode: "grab" },
                            onclick: { enable: false },
                        },
                        modes: {
                            grab: {
                                distance: 140,
                                line_linked: { opacity: 0.5 },
                            },
                        },
                    },
                    retina_detect: true,
                });
            }

            function updateParticlesTheme(theme) {
                if (window.pJSDom && window.pJSDom[0]) {
                    const colors = getParticleColors(theme);
                    const pJS = window.pJSDom[0].pJS;
                    pJS.particles.color.value = colors.color;
                    pJS.particles.line_linked.color = colors.linkColor;
                    const particlesElement =
                        document.getElementById("particles-js");
                    if (particlesElement) {
                        particlesElement.style.backgroundColor = colors.bgColor;
                    }
                    pJS.fn.particlesRefresh();
                } else {
                    // If particles haven't initialized yet, init them
                    initParticles();
                }
            }

            // --- Scroll listener for header shadow ---
            window.addEventListener(
                "scroll",
                () => {
                    if (window.scrollY > 10) {
                        document.body.classList.add("scrolled");
                    } else {
                        document.body.classList.remove("scrolled");
                    }
                },
                { passive: true },
            );

            // --- Plotly Theme Update ---
            function updatePlotlyChartTheme(theme) {
                const plotlyLayoutUpdate = {
                    paper_bgcolor:
                        theme === "dark"
                            ? "var(--card-bg-color)"
                            : "var(--card-bg-color)", // Use CSS var
                    plot_bgcolor:
                        theme === "dark"
                            ? "var(--card-bg-color)"
                            : "var(--card-bg-color)", // Use CSS var
                    "font.color":
                        theme === "dark"
                            ? "var(--text-color)"
                            : "var(--text-color)", // Use CSS var
                    "xaxis.gridcolor":
                        theme === "dark"
                            ? "var(--border-color)"
                            : "var(--border-color)",
                    "yaxis.gridcolor":
                        theme === "dark"
                            ? "var(--border-color)"
                            : "var(--border-color)",
                    "xaxis.linecolor":
                        theme === "dark"
                            ? "var(--border-color)"
                            : "var(--border-color)",
                    "yaxis.linecolor":
                        theme === "dark"
                            ? "var(--border-color)"
                            : "var(--border-color)",
                    "xaxis.tickfont.color":
                        theme === "dark"
                            ? "var(--text-muted-color)"
                            : "var(--text-muted-color)",
                    "yaxis.tickfont.color":
                        theme === "dark"
                            ? "var(--text-muted-color)"
                            : "var(--text-muted-color)",
                    // Add other layout properties as needed
                };

                // Find all Plotly charts on the page and update their layout
                const plotlyCharts =
                    document.querySelectorAll(".js-plotly-plot");
                plotlyCharts.forEach((chartDiv) => {
                    if (chartDiv.layout) {
                        // Check if layout exists
                        Plotly.relayout(chartDiv, plotlyLayoutUpdate);
                    }
                });
            }

            // --- DOMContentLoaded ---
            document.addEventListener("DOMContentLoaded", () => {
                const savedTheme =
                    localStorage.getItem("theme") ||
                    (window.matchMedia("(prefers-color-scheme: dark)").matches
                        ? "dark"
                        : "light");
                // Call initParticles FIRST
                if (typeof initParticles === "function") {
                    // Ensure function exists (it does, via script.js)
                    initParticles();
                }

                // THEN set theme. Give a tiny timeout for particles to potentially register pJSDom.
                // This is a bit of a hack; a more robust solution would be a callback or promise from particles.js if available.
                setTimeout(() => {
                    setTheme(savedTheme); // Set theme after particles init
                }, 0); // Small timeout often helps with such async library initializations

                setActiveSidebarLink();
                checkAndSetTickerVisibility(); // Check if ticker should be shown

                // Initial Plotly theme update for any charts loaded initially
                setTimeout(() => {
                    if (typeof updatePlotlyChartTheme === "function") {
                        updatePlotlyChartTheme(savedTheme);
                    }
                }, 100);

                // Chatbot JS is included via chatbot.html and initialized by script.js's DOMContentLoaded
            });
        </script>
        {% block scripts_extra %}{% endblock %} {# Block for page-specific JS #}

        <script src="{{ url_for('static', filename='enhanced_rating_charts.js') }}"></script>
        <script src="{{ url_for('static', filename='rating_alignment_fix.js') }}"></script>
        <script src="{{ url_for('static', filename='analysis_formatter.js') }}"></script>
        <script src="{{ url_for('static', filename='buffett_analysis_formatter.js') }}"></script>
        <!-- <script src="{{ url_for('static', filename='chatbot_rating_integration.js') }}"></script> -->
        <script src="{{ url_for('static', filename='script.js') }}"></script>
        <script src="{{ url_for('static', filename='financial_visualizations.js') }}"></script>
        <!-- Development Test Script (Remove in Production) -->
        <script src="{{ url_for('static', filename='enhanced_charts_test.js') }}"></script>
    </body>
</html>
