{% extends "base.html" %}

{% block title %}Portfolio Import{% endblock %}

{% block header_title %}Portfolio Import{% endblock %}

{% block head_extra %}
    <style>
        /* === PORTFOLIO IMPORT STYLES === */
        .import-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 32px;
            min-height: 100vh;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
        }

        .import-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .import-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 16px;
            background: linear-gradient(135deg, var(--highlight-color), var(--accent-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .import-subtitle {
            font-size: 1.2rem;
            color: var(--text-muted);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .import-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 32px;
            margin-bottom: 48px;
        }

        .import-method {
            background: var(--card-bg-color);
            border: 2px solid var(--border-color);
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .import-method:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: var(--highlight-color);
        }

        .import-method::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--highlight-color), var(--accent-blue));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .import-method:hover::before {
            opacity: 1;
        }

        .method-icon {
            font-size: 3rem;
            color: var(--highlight-color);
            margin-bottom: 24px;
            display: block;
        }

        .method-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
        }

        .method-description {
            color: var(--text-muted);
            margin-bottom: 24px;
            line-height: 1.6;
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 48px 24px;
            margin: 24px 0;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            background: var(--bg-secondary);
        }

        .upload-area:hover,
        .upload-area.dragover {
            border-color: var(--highlight-color);
            background: rgba(var(--highlight-color-rgb), 0.05);
            transform: scale(1.02);
        }

        .upload-area.processing {
            border-color: var(--accent-blue);
            background: rgba(var(--accent-blue-rgb), 0.05);
        }

        .upload-icon {
            font-size: 2.5rem;
            color: var(--text-muted);
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .upload-area:hover .upload-icon {
            color: var(--highlight-color);
            transform: scale(1.1);
        }

        .upload-text {
            font-size: 1.1rem;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .upload-subtext {
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .file-input {
            display: none;
        }

        .upload-button {
            background: linear-gradient(135deg, var(--highlight-color), var(--accent-blue));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 16px;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .upload-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Progress Indicator */
        .progress-container {
            margin: 24px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-secondary);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 16px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--highlight-color), var(--accent-blue));
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .progress-text {
            text-align: center;
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        /* Results Section */
        .results-container {
            margin-top: 48px;
            display: none;
        }

        .results-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding: 24px;
            background: var(--card-bg-color);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .results-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .results-summary {
            display: flex;
            gap: 24px;
            color: var(--text-muted);
        }

        .summary-item {
            text-align: center;
        }

        .summary-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--highlight-color);
            display: block;
        }

        .summary-label {
            font-size: 0.9rem;
            margin-top: 4px;
        }

        .preview-table {
            background: var(--card-bg-color);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            margin-bottom: 24px;
        }

        .preview-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .preview-table th,
        .preview-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .preview-table th {
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .preview-table td {
            color: var(--text-muted);
        }

        .preview-table tr:hover {
            background: var(--bg-secondary);
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 32px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--highlight-color), var(--accent-blue));
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            border: 2px solid var(--border-color);
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-secondary:hover {
            border-color: var(--highlight-color);
            color: var(--highlight-color);
        }

        /* Error and Warning Messages */
        .message-container {
            margin: 24px 0;
        }

        .message {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 12px;
        }

        .message.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .message.warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            color: #f59e0b;
        }

        .message.success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }

        .message.info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #3b82f6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .import-methods {
                grid-template-columns: 1fr;
            }
            
            .results-summary {
                flex-direction: column;
                gap: 16px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }

        /* Dark mode adjustments */
        [data-theme="dark"] .upload-area {
            background: var(--bg-tertiary);
        }

        [data-theme="dark"] .upload-area:hover,
        [data-theme="dark"] .upload-area.dragover {
            background: rgba(var(--highlight-color-rgb), 0.1);
        }

        /* Currency Selection Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        .currency-modal {
            background: var(--card-bg-color);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideUp 0.3s ease;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 600;
        }

        .modal-header h3 i {
            color: var(--highlight-color);
            margin-right: 8px;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-muted);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .modal-body {
            padding: 24px;
        }

        .currency-detection-info {
            margin-bottom: 24px;
        }

        .gemini-question {
            margin-bottom: 20px;
            animation: fadeInUp 0.5s ease;
        }

        .ai-message {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1));
            border: 2px solid rgba(147, 51, 234, 0.3);
            border-radius: 12px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .ai-message::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #9333ea, #3b82f6, #10b981);
            animation: shimmer 2s infinite;
        }

        .ai-avatar {
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #9333ea, #3b82f6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            animation: pulse 2s infinite;
        }

        .ai-content h4 {
            margin: 0 0 8px 0;
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .ai-content p {
            margin: 0;
            color: var(--text-primary);
            line-height: 1.5;
        }

        .detection-message {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: var(--text-primary);
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
        }

        .detection-message i {
            color: #3b82f6;
            margin-right: 8px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .currency-analysis {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .currency-stat {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .currency-stat:last-child {
            border-bottom: none;
        }

        .currency-options {
            display: grid;
            gap: 12px;
            margin-bottom: 24px;
        }

        .currency-option {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: var(--bg-secondary);
        }

        .currency-option:hover {
            border-color: var(--highlight-color);
            background: rgba(var(--highlight-color-rgb), 0.1);
        }

        .currency-option.selected {
            border-color: var(--highlight-color);
            background: rgba(var(--highlight-color-rgb), 0.2);
        }

        .currency-option input[type="radio"] {
            margin-right: 12px;
            accent-color: var(--highlight-color);
        }

        .currency-info {
            flex: 1;
        }

        .currency-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .currency-details {
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .currency-explanation {
            margin-top: 16px;
        }

        .explanation-card {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 16px;
        }

        .explanation-card h4 {
            margin: 0 0 12px 0;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .explanation-card h4 i {
            color: var(--highlight-color);
            margin-right: 8px;
        }

        .explanation-card ul {
            margin: 0;
            padding-left: 20px;
            color: var(--text-muted);
        }

        .explanation-card li {
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .modal-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px;
            border-top: 1px solid var(--border-color);
            gap: 16px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="import-container">
    <!-- Header -->
    <div class="import-header">
        <h1 class="import-title">AI-Powered Portfolio Import</h1>
        <p class="import-subtitle">
            🤖 <strong>Enhanced with AI Intelligence!</strong> Our system intelligently understands various formats including "Avg Cost Basis", "Average Price", "Cost Per Share", and more.
            Simply upload a screenshot of your broker statement or export your data as CSV/XLSX - no need for specific formatting!
        </p>
        
        <!-- Debug button for testing currency modal -->
        <div style="margin-top: 20px;">
            <button type="button" onclick="testCurrencyModal()" style="background: #f59e0b; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 0.9rem;">
                🧪 Test Currency Selection Modal
            </button>
        </div>
    </div>

    <!-- Import Methods -->
    <div class="import-methods">
        <!-- Image Upload -->
        <div class="import-method">
            <i class="fas fa-camera method-icon"></i>
            <h3 class="method-title">🤖 AI Image Analysis</h3>
            <p class="method-description">
                Upload a screenshot or photo of your portfolio from any broker platform.
                Our AI intelligently understands various price formats like "Avg Cost Basis", "Cost Per Share", "Average Price" - no specific formatting required!
            </p>

            <div class="upload-area" id="imageUploadArea">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <div class="upload-text">Drop your image here or click to browse</div>
                <div class="upload-subtext">Supports JPEG, PNG formats</div>
                <input type="file" id="imageInput" class="file-input" accept="image/*">
                <button type="button" class="upload-button" id="imageUploadButton">
                    Choose Image
                </button>
            </div>

            <div class="progress-container" id="imageProgress">
                <div class="progress-bar">
                    <div class="progress-fill" id="imageProgressFill"></div>
                </div>
                <div class="progress-text" id="imageProgressText">Processing image...</div>
            </div>
        </div>

        <!-- Spreadsheet Upload -->
        <div class="import-method">
            <i class="fas fa-file-excel method-icon"></i>
            <h3 class="method-title">🧠 Smart Spreadsheet Analysis</h3>
            <p class="method-description">
                Upload your portfolio data as a CSV or Excel file. Our AI intelligently maps columns regardless of naming -
                works with "Stock Symbol", "Avg Cost Basis", "Total Investment", and many other variations!
            </p>

            <div class="upload-area" id="spreadsheetUploadArea">
                <i class="fas fa-file-upload upload-icon"></i>
                <div class="upload-text">Drop your spreadsheet here or click to browse</div>
                <div class="upload-subtext">Supports CSV, XLSX, XLS formats</div>
                <input type="file" id="spreadsheetInput" class="file-input" accept=".csv,.xlsx,.xls">
                <button type="button" class="upload-button" id="spreadsheetUploadButton">
                    Choose File
                </button>
            </div>

            <div class="progress-container" id="spreadsheetProgress">
                <div class="progress-bar">
                    <div class="progress-fill" id="spreadsheetProgressFill"></div>
                </div>
                <div class="progress-text" id="spreadsheetProgressText">Processing spreadsheet...</div>
            </div>
        </div>
    </div>

    <!-- Messages Container -->
    <div class="message-container" id="messageContainer"></div>

    <!-- Results Container -->
    <div class="results-container" id="resultsContainer">
        <div class="results-header">
            <h3 class="results-title">Import Preview</h3>
            <div class="results-summary" id="resultsSummary">
                <div class="summary-item">
                    <span class="summary-value" id="totalEntries">0</span>
                    <span class="summary-label">Entries</span>
                </div>
                <div class="summary-item">
                    <span class="summary-value" id="totalInvested">$0</span>
                    <span class="summary-label">Total Invested</span>
                </div>
                <div class="summary-item">
                    <span class="summary-value" id="uniqueTickers">0</span>
                    <span class="summary-label">Unique Stocks</span>
                </div>
                <div class="summary-item">
                    <span class="summary-value" id="cashPosition">$0</span>
                    <span class="summary-label">Cash Position</span>
                </div>
            </div>
        </div>

        <!-- Currency Selection -->
        <div class="currency-selection" style="margin-bottom: 24px; padding: 20px; background: var(--card-bg-color); border-radius: 12px; border: 1px solid var(--border-color);">
            <h4 style="margin-bottom: 16px; color: var(--text-primary);">
                <i class="fas fa-coins" style="margin-right: 8px; color: var(--highlight-color);"></i>
                Currency Settings
            </h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                <div>
                    <label for="portfolioCurrency" style="display: block; margin-bottom: 8px; color: var(--text-muted); font-weight: 500;">Portfolio Currency:</label>
                    <select id="portfolioCurrency" class="form-control" onchange="portfolioImporter.changeCurrency(this.value)">
                        <option value="USD">🇺🇸 US Dollar ($)</option>
                        <option value="EUR">🇪🇺 Euro (€)</option>
                        <option value="GBP">🇬🇧 British Pound (£)</option>
                        <option value="JPY">🇯🇵 Japanese Yen (¥)</option>
                        <option value="CAD">🇨🇦 Canadian Dollar (C$)</option>
                        <option value="AUD">🇦🇺 Australian Dollar (A$)</option>
                        <option value="CHF">🇨🇭 Swiss Franc (CHF)</option>
                        <option value="CNY">🇨🇳 Chinese Yuan (¥)</option>
                        <option value="SEK">🇸🇪 Swedish Krona (kr)</option>
                        <option value="NOK">🇳🇴 Norwegian Krone (kr)</option>
                        <option value="DKK">🇩🇰 Danish Krone (kr)</option>
                        <option value="PLN">🇵🇱 Polish Zloty (zł)</option>
                        <option value="CZK">🇨🇿 Czech Koruna (Kč)</option>
                        <option value="HUF">🇭🇺 Hungarian Forint (Ft)</option>
                        <option value="RUB">🇷🇺 Russian Ruble (₽)</option>
                        <option value="BRL">🇧🇷 Brazilian Real (R$)</option>
                        <option value="INR">🇮🇳 Indian Rupee (₹)</option>
                        <option value="KRW">🇰🇷 South Korean Won (₩)</option>
                        <option value="SGD">🇸🇬 Singapore Dollar (S$)</option>
                        <option value="HKD">🇭🇰 Hong Kong Dollar (HK$)</option>
                        <option value="NZD">🇳🇿 New Zealand Dollar (NZ$)</option>
                        <option value="MXN">🇲🇽 Mexican Peso ($)</option>
                        <option value="ZAR">🇿🇦 South African Rand (R)</option>
                        <option value="TRY">🇹🇷 Turkish Lira (₺)</option>
                        <option value="ILS">🇮🇱 Israeli Shekel (₪)</option>
                        <option value="THB">🇹🇭 Thai Baht (฿)</option>
                        <option value="MYR">🇲🇾 Malaysian Ringgit (RM)</option>
                        <option value="PHP">🇵🇭 Philippine Peso (₱)</option>
                        <option value="IDR">🇮🇩 Indonesian Rupiah (Rp)</option>
                        <option value="VND">🇻🇳 Vietnamese Dong (₫)</option>
                    </select>
                </div>
                <div>
                    <label style="display: block; margin-bottom: 8px; color: var(--text-muted); font-weight: 500;">Add New Entry:</label>
                    <button type="button" class="btn btn-secondary" onclick="portfolioImporter.addNewEntry()" style="width: 100%;">
                        <i class="fas fa-plus"></i> Add Stock
                    </button>
                </div>
            </div>
        </div>

        <div class="preview-table">
            <table>
                <thead>
                    <tr>
                        <th>Ticker</th>
                        <th>Amount Invested</th>
                        <th>Buy Price</th>
                        <th>Shares</th>
                        <th>Purchase Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="previewTableBody">
                    <!-- Data will be populated here -->
                </tbody>
            </table>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn-primary" id="importButton">
                <i class="fas fa-download"></i> Import to Portfolio
            </button>
            <button type="button" class="btn-secondary" id="cancelButton">
                <i class="fas fa-times"></i> Cancel
            </button>
        </div>
    </div>
</div>

<!-- Currency Selection Modal -->
<div id="currencySelectionModal" class="modal-overlay" style="display: none;">
    <div class="modal-content currency-modal">
        <div class="modal-header">
            <h3><i class="fas fa-coins"></i> Multiple Currencies Detected</h3>
            <button type="button" class="modal-close" onclick="closeCurrencyModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="modal-body">
            <div class="currency-detection-info">
                <div class="gemini-question" id="geminiQuestion" style="display: none;">
                    <div class="ai-message">
                        <div class="ai-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="ai-content">
                            <h4>AI Currency Detection</h4>
                            <p id="geminiQuestionText"></p>
                        </div>
                    </div>
                </div>

                <p class="detection-message" id="defaultDetectionMessage">
                    <i class="fas fa-info-circle"></i>
                    We detected multiple currencies in your portfolio. Please select your preferred display currency:
                </p>

                <div class="currency-analysis" id="currencyAnalysis">
                    <!-- Currency analysis will be populated here -->
                </div>
            </div>

            <div class="currency-options" id="currencyOptions">
                <!-- Currency options will be populated here -->
            </div>

            <div class="currency-explanation">
                <div class="explanation-card">
                    <h4><i class="fas fa-lightbulb"></i> How this works:</h4>
                    <ul>
                        <li>Your original data will be preserved exactly as detected</li>
                        <li>The selected currency will be used for portfolio totals and calculations</li>
                        <li>Individual holdings will show their original currencies</li>
                        <li>You can change this later in portfolio settings</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeCurrencyModal()">
                <i class="fas fa-arrow-left"></i> Go Back
            </button>
            <button type="button" class="btn-primary" id="confirmCurrencyButton" onclick="confirmCurrencySelection()">
                <i class="fas fa-check"></i> Continue with Selected Currency
            </button>
        </div>
    </div>
</div>

<!-- Currency Selection Modal -->
<div id="currencySelectionModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>🌍 Currency Selection Required</h3>
            <span class="close" onclick="portfolioImporter.closeCurrencyModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="currencyQuestion" class="currency-question">
                <p>I detected multiple currencies or was uncertain about the currency in your portfolio. Please select your preferred display currency:</p>
            </div>
            
            <div class="currency-options">
                <div class="currency-grid">
                    <div class="currency-option" data-currency="USD">
                        <div class="currency-flag">🇺🇸</div>
                        <div class="currency-info">
                            <div class="currency-name">US Dollar</div>
                            <div class="currency-code">USD ($)</div>
                        </div>
                    </div>
                    <div class="currency-option" data-currency="EUR">
                        <div class="currency-flag">🇪🇺</div>
                        <div class="currency-info">
                            <div class="currency-name">Euro</div>
                            <div class="currency-code">EUR (€)</div>
                        </div>
                    </div>
                    <div class="currency-option" data-currency="DKK">
                        <div class="currency-flag">🇩🇰</div>
                        <div class="currency-info">
                            <div class="currency-name">Danish Krone</div>
                            <div class="currency-code">DKK (kr)</div>
                        </div>
                    </div>
                    <div class="currency-option" data-currency="GBP">
                        <div class="currency-flag">🇬🇧</div>
                        <div class="currency-info">
                            <div class="currency-name">British Pound</div>
                            <div class="currency-code">GBP (£)</div>
                        </div>
                    </div>
                    <div class="currency-option" data-currency="SEK">
                        <div class="currency-flag">🇸🇪</div>
                        <div class="currency-info">
                            <div class="currency-name">Swedish Krona</div>
                            <div class="currency-code">SEK (kr)</div>
                        </div>
                    </div>
                    <div class="currency-option" data-currency="NOK">
                        <div class="currency-flag">🇳🇴</div>
                        <div class="currency-info">
                            <div class="currency-name">Norwegian Krone</div>
                            <div class="currency-code">NOK (kr)</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="detected-info">
                <p><strong>Detected currencies:</strong> <span id="detectedCurrencies"></span></p>
                <p><strong>AI Analysis:</strong> <span id="currencyAnalysis"></span></p>
            </div>
        </div>
        <div class="modal-footer">
            <button id="confirmCurrencyButton" class="btn btn-primary" disabled onclick="portfolioImporter.confirmCurrencySelection()">
                <i class="fas fa-check"></i> Confirm Selection
            </button>
            <button class="btn btn-secondary" onclick="portfolioImporter.closeCurrencyModal()">
                Cancel
            </button>
        </div>
    </div>
</div>

<style>
/* Currency Selection Modal Styles */
.modal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--card-bg-color);
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 24px 32px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.close {
    color: var(--text-muted);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 32px;
}

.currency-question {
    margin-bottom: 24px;
}

.currency-question p {
    color: var(--text-primary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0;
}

.currency-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.currency-option {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--bg-secondary);
}

.currency-option:hover {
    border-color: var(--highlight-color);
    background: var(--bg-primary);
    transform: translateY(-2px);
}

.currency-option.selected {
    border-color: var(--highlight-color);
    background: linear-gradient(135deg, var(--highlight-color)20, var(--bg-primary));
    box-shadow: 0 4px 12px rgba(var(--highlight-color-rgb), 0.3);
}

.currency-flag {
    font-size: 2rem;
    margin-right: 16px;
}

.currency-info {
    flex: 1;
}

.currency-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.currency-code {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.detected-info {
    background: var(--bg-secondary);
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid var(--highlight-color);
}

.detected-info p {
    margin: 8px 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.detected-info strong {
    color: var(--text-primary);
}

.modal-footer {
    padding: 24px 32px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.modal-footer .btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.modal-footer .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>

<script>
// Portfolio Import JavaScript
class PortfolioImporter {
    constructor() {
        console.log('PortfolioImporter initialized');
        this.currentData = null;
        this.selectedCurrency = 'USD'; // Default currency
        this.initializeEventListeners();
        console.log('Event listeners initialized');
    }

    initializeEventListeners() {
        // Image upload
        const imageInput = document.getElementById('imageInput');
        const imageUploadArea = document.getElementById('imageUploadArea');
        const imageUploadButton = document.getElementById('imageUploadButton');

        console.log('Elements found:', {
            imageInput: !!imageInput,
            imageUploadArea: !!imageUploadArea,
            imageUploadButton: !!imageUploadButton
        });

        if (!imageInput || !imageUploadArea || !imageUploadButton) {
            console.error('Missing required elements for image upload');
            return;
        }

        imageInput.addEventListener('change', (e) => this.handleImageUpload(e));

        // Handle clicks on the upload area (but not on the button)
        imageUploadArea.addEventListener('click', (e) => {
            console.log('Image upload area clicked, target:', e.target);
            // Don't trigger if clicking on the button
            if (e.target !== imageUploadButton && !imageUploadButton.contains(e.target)) {
                console.log('Triggering file input from area click');
                imageInput.click();
            } else {
                console.log('Click was on button, ignoring area click');
            }
        });

        // Handle button clicks specifically
        imageUploadButton.addEventListener('click', (e) => {
            console.log('Image upload button clicked');
            e.stopPropagation(); // Prevent event bubbling
            imageInput.click();
        });

        imageUploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        imageUploadArea.addEventListener('drop', (e) => this.handleImageDrop(e));

        // Spreadsheet upload
        const spreadsheetInput = document.getElementById('spreadsheetInput');
        const spreadsheetUploadArea = document.getElementById('spreadsheetUploadArea');
        const spreadsheetUploadButton = document.getElementById('spreadsheetUploadButton');

        spreadsheetInput.addEventListener('change', (e) => this.handleSpreadsheetUpload(e));

        // Handle clicks on the upload area (but not on the button)
        spreadsheetUploadArea.addEventListener('click', (e) => {
            // Don't trigger if clicking on the button
            if (e.target !== spreadsheetUploadButton && !spreadsheetUploadButton.contains(e.target)) {
                spreadsheetInput.click();
            }
        });

        // Handle button clicks specifically
        spreadsheetUploadButton.addEventListener('click', (e) => {
            console.log('Spreadsheet upload button clicked');
            e.stopPropagation(); // Prevent event bubbling
            spreadsheetInput.click();
        });

        spreadsheetUploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        spreadsheetUploadArea.addEventListener('drop', (e) => this.handleSpreadsheetDrop(e));

        // Action buttons
        document.getElementById('importButton').addEventListener('click', () => this.importToPortfolio());
        document.getElementById('cancelButton').addEventListener('click', () => this.resetImport());
    }

    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    handleImageDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith('image/')) {
            this.processImageFile(files[0]);
        }
    }

    handleSpreadsheetDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            const validTypes = ['.csv', '.xlsx', '.xls'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

            if (validTypes.includes(fileExtension)) {
                this.processSpreadsheetFile(file);
            }
        }
    }

    handleImageUpload(e) {
        console.log('Image file selected:', e.target.files[0]);
        const file = e.target.files[0];
        if (file) {
            console.log('Processing image file:', file.name, file.size, 'bytes');
            this.processImageFile(file);
        } else {
            console.log('No file selected');
        }
    }

    handleSpreadsheetUpload(e) {
        const file = e.target.files[0];
        if (file) {
            this.processSpreadsheetFile(file);
        }
    }

    async processImageFile(file) {
        this.showProgress('image', 'Uploading image...');

        try {
            const formData = new FormData();
            formData.append('image', file);

            this.updateProgress('image', 30, 'Processing with AI...');

            const response = await fetch('/api/import/image', {
                method: 'POST',
                body: formData
            });

            this.updateProgress('image', 70, 'Extracting data...');

            const result = await response.json();

            this.updateProgress('image', 100, 'Complete!');

            setTimeout(() => {
                this.hideProgress('image');
                this.displayResults(result);
            }, 500);

        } catch (error) {
            this.hideProgress('image');
            this.showMessage('Error processing image: ' + error.message, 'error');
        }
    }

    async processSpreadsheetFile(file) {
        this.showProgress('spreadsheet', 'Uploading spreadsheet...');

        try {
            const formData = new FormData();
            formData.append('spreadsheet', file);

            this.updateProgress('spreadsheet', 30, 'Parsing data...');

            const response = await fetch('/api/import/spreadsheet', {
                method: 'POST',
                body: formData
            });

            this.updateProgress('spreadsheet', 70, 'Validating entries...');

            const result = await response.json();

            this.updateProgress('spreadsheet', 100, 'Complete!');

            setTimeout(() => {
                this.hideProgress('spreadsheet');
                this.displayResults(result);
            }, 500);

        } catch (error) {
            this.hideProgress('spreadsheet');
            this.showMessage('Error processing spreadsheet: ' + error.message, 'error');
        }
    }

    showProgress(type, message) {
        const progressContainer = document.getElementById(`${type}Progress`);
        const progressText = document.getElementById(`${type}ProgressText`);

        progressContainer.style.display = 'block';
        progressText.textContent = message;
        this.updateProgress(type, 10);
    }

    updateProgress(type, percentage, message = null) {
        const progressFill = document.getElementById(`${type}ProgressFill`);
        const progressText = document.getElementById(`${type}ProgressText`);

        progressFill.style.width = `${percentage}%`;

        if (message) {
            progressText.textContent = message;
        }
    }

    hideProgress(type) {
        const progressContainer = document.getElementById(`${type}Progress`);
        progressContainer.style.display = 'none';
    }

    displayResults(result) {
        this.currentData = result;

        // Clear previous messages
        this.clearMessages();

        // Show errors and warnings
        if (result.errors && result.errors.length > 0) {
            result.errors.forEach(error => this.showMessage(error, 'error'));
        }

        if (result.warnings && result.warnings.length > 0) {
            result.warnings.forEach(warning => this.showMessage(warning, 'warning'));
        }

        // Show helpful message for Google Vision API issues
        if (result.warnings && result.warnings.some(w => w.includes('mock') || w.includes('demo'))) {
            this.showMessage('📝 Demo Mode: Using sample data for demonstration. In production, this would extract data from your actual image.', 'info');
        }

        if (result.success && result.portfolio.length > 0) {
            this.showMessage(`Successfully extracted ${result.portfolio.length} portfolio entries!`, 'success');

            // Check for mixed currencies and show selection modal if needed
            console.log('Checking currency selection requirement:', result.currency_info);
            
            // CRITICAL: Extract detected currency from result
            const detectedCurrency = result.detected_currency || result.currency || 'USD';
            this.detectedCurrency = detectedCurrency; // Store as class property for use in templates
            
            console.log(`🎯 Detected currency from result: ${detectedCurrency}`);
            console.log('🔍 Full result object:', result);
            
            const shouldForceSelection = detectedCurrency && detectedCurrency !== 'USD' && detectedCurrency !== 'EUR';
            
            if ((result.currency_info && result.currency_info.requires_user_selection) || shouldForceSelection) {
                console.log('Currency selection required! Showing modal...');
                console.log('Detected currency:', detectedCurrency);
                console.log('Should force selection:', shouldForceSelection);
                
                // Ensure currency_info exists
                if (!result.currency_info) {
                    result.currency_info = {
                        detected_currencies: [detectedCurrency],
                        primary_currency: detectedCurrency,
                        requires_user_selection: true,
                        gemini_question: `I detected ${detectedCurrency} currency in your portfolio. Is this correct, or would you prefer to display amounts in a different currency?`
                    };
                }
                
                this.showCurrencySelectionModal(result);
                return; // Don't show results yet, wait for currency selection
            } else {
                console.log('No currency selection required, proceeding with results');
                console.log('Detected currency:', detectedCurrency);
            }

            // CRITICAL FIX: ALWAYS update currency settings regardless of modal
            if (detectedCurrency && detectedCurrency !== 'USD') {
                console.log(`🎯 FORCING currency update from ${this.selectedCurrency} to detected currency: ${detectedCurrency}`);
                this.selectedCurrency = detectedCurrency;

                // Update the portfolio currency dropdown to reflect the detected currency
                const portfolioCurrencySelect = document.getElementById('portfolioCurrency');
                if (portfolioCurrencySelect) {
                    portfolioCurrencySelect.value = detectedCurrency;
                    console.log('✅ Updated portfolio currency dropdown to detected currency:', detectedCurrency);
                } else {
                    console.error('❌ Portfolio currency select element not found!');
                }
            }

            // Check if OCR failed and show helpful guidance
            if (result.raw_data && result.raw_data.ocr_failed) {
                this.showOCRFailureGuidance(result.raw_data.user_guidance);
                return;
            }

            // FINAL CHECK: Ensure currency dropdown shows detected currency
            this.ensureCurrencyDropdownUpdated();

            // Update summary
            this.updateSummary(result);

            // Populate preview table
            this.populatePreviewTable(result.portfolio);

            // Show results container
            document.getElementById('resultsContainer').style.display = 'block';

            // Scroll to results
            document.getElementById('resultsContainer').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        } else {
            this.showMessage('No valid portfolio entries found. Please check your file format.', 'warning');
        }
    }

    updateSummary(result) {
        const summary = result.summary || {};

        // Calculate totals based on current portfolio data (in case values were converted)
        let totalInvested = 0;
        const uniqueTickers = new Set();
        
        if (result.portfolio) {
            result.portfolio.forEach(entry => {
                if (entry.amount_invested) {
                    totalInvested += parseFloat(entry.amount_invested) || 0;
                }
                if (entry.ticker) {
                    uniqueTickers.add(entry.ticker.toUpperCase());
                }
            });
        }

        document.getElementById('totalEntries').textContent = result.portfolio?.length || 0;
        document.getElementById('totalInvested').textContent = this.formatCurrency(totalInvested);
        document.getElementById('uniqueTickers').textContent = uniqueTickers.size;
        document.getElementById('cashPosition').textContent = this.formatCurrency(result.cash_position || 0);
    }

    populatePreviewTable(portfolio) {
        const tbody = document.getElementById('previewTableBody');
        tbody.innerHTML = '';

        portfolio.forEach((entry, index) => {
            // Store original currencies on first render (before any conversions)
            if (!entry.original_buy_price_currency) {
                entry.original_buy_price_currency = entry.buy_price_currency || this.detectedCurrency || 'USD';
            }
            
            // Debug: Log the entry data to see what currencies we have
            console.log(`🔍 Entry ${index} (${entry.ticker}):`, {
                amount_invested: entry.amount_invested,
                amount_invested_currency: entry.amount_invested_currency,
                buy_price: entry.buy_price,
                buy_price_currency: entry.buy_price_currency,
                original_buy_price_currency: entry.original_buy_price_currency,
                currency: entry.currency,
                selectedCurrency: this.selectedCurrency
            });

            // AMOUNT INVESTED: Use currently selected currency (since amount gets converted)
            const displayAmountCurrency = this.selectedCurrency || 'USD';
            
            // BUY PRICE: Use original currency from when the stock was purchased (NOT converted)
            const displayBuyPriceCurrency = entry.original_buy_price_currency || entry.buy_price_currency || this.detectedCurrency || 'USD';
            
            console.log(`💰 Currency display for ${entry.ticker}: amount=${displayAmountCurrency} (selected), buy_price=${displayBuyPriceCurrency} (original)`);
            console.log(`🔍 Currency sources: selectedCurrency=${this.selectedCurrency}, original_buy_currency=${entry.original_buy_price_currency}`);
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="text" class="form-control editable-ticker"
                           value="${entry.ticker}"
                           data-index="${index}"
                           style="min-width: 80px;">
                </td>
                <td>
                    <input type="number" class="form-control editable-amount"
                           value="${parseFloat(entry.amount_invested || 0).toFixed(2)}"
                           data-index="${index}"
                           step="0.01" min="0"
                           style="min-width: 120px;">
                    <small class="text-muted amount-currency-label">${displayAmountCurrency}</small>
                </td>
                <td>
                    <input type="number" class="form-control editable-price"
                           value="${parseFloat(entry.buy_price || 0).toFixed(2)}"
                           data-index="${index}"
                           step="0.01" min="0"
                           style="min-width: 100px;">
                    <small class="text-muted buy-price-currency-label">${displayBuyPriceCurrency}</small>
                </td>
                <td>
                    <input type="number" class="form-control editable-shares"
                           value="${entry.shares ? parseFloat(entry.shares).toFixed(2) : ''}"
                           data-index="${index}"
                           step="0.01" min="0"
                           style="min-width: 100px;">
                </td>
                <td>
                    <input type="date" class="form-control editable-date"
                           value="${entry.purchase_date}"
                           data-index="${index}"
                           style="min-width: 140px;">
                </td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="portfolioImporter.removeEntry(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // Add event listeners for real-time updates
        this.addEditableEventListeners();
    }

    async importToPortfolio() {
        if (!this.currentData || !this.currentData.success) {
            this.showMessage('No valid data to import', 'error');
            return;
        }

        try {
            const importButton = document.getElementById('importButton');
            importButton.disabled = true;
            importButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Importing...';

            // Add currency information to the data
            const dataToSend = {
                ...this.currentData,
                currency: this.selectedCurrency || 'USD',
                selected_currency: this.selectedCurrency || 'USD'
            };
            
            console.log('Importing portfolio with currency:', this.selectedCurrency);
            console.log('Data being sent to backend:', {
                currency: dataToSend.currency,
                selected_currency: dataToSend.selected_currency,
                portfolio_entries: dataToSend.portfolio?.length || 0
            });

            const response = await fetch('/api/import/confirm', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(dataToSend)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('Portfolio imported successfully!', 'success');

                // Update portfolio currency in localStorage for the portfolio page
                const finalCurrency = result.portfolio_currency || this.selectedCurrency || 'USD';
                localStorage.setItem('portfolioCurrency', finalCurrency);
                console.log('Final currency stored for portfolio page:', finalCurrency);

                // Show success message with currency info
                const currencyName = this.getCurrencyDisplayName(finalCurrency);
                this.showMessage(`Portfolio imported successfully in ${currencyName}!`, 'success');

                // Redirect to portfolio page after a short delay
                setTimeout(() => {
                    window.location.href = '/portfolio';
                }, 2000);
            } else {
                this.showMessage('Error importing portfolio: ' + (result.error || 'Unknown error'), 'error');
                importButton.disabled = false;
                importButton.innerHTML = '<i class="fas fa-download"></i> Import to Portfolio';
            }

        } catch (error) {
            this.showMessage('Error importing portfolio: ' + error.message, 'error');
            const importButton = document.getElementById('importButton');
            importButton.disabled = false;
            importButton.innerHTML = '<i class="fas fa-download"></i> Import to Portfolio';
        }
    }

    resetImport() {
        this.currentData = null;

        // Hide results
        document.getElementById('resultsContainer').style.display = 'none';

        // Clear file inputs
        document.getElementById('imageInput').value = '';
        document.getElementById('spreadsheetInput').value = '';

        // Clear messages
        this.clearMessages();

        // Hide progress indicators
        this.hideProgress('image');
        this.hideProgress('spreadsheet');

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    showMessage(message, type) {
        const messageContainer = document.getElementById('messageContainer');

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.innerHTML = `
            <i class="fas fa-${this.getMessageIcon(type)}"></i>
            ${message}
        `;

        messageContainer.appendChild(messageDiv);
    }

    clearMessages() {
        const messageContainer = document.getElementById('messageContainer');
        messageContainer.innerHTML = '';
    }

    getMessageIcon(type) {
        switch (type) {
            case 'error': return 'exclamation-circle';
            case 'warning': return 'exclamation-triangle';
            case 'success': return 'check-circle';
            case 'info': return 'info-circle';
            default: return 'info-circle';
        }
    }

    addEditableEventListeners() {
        // Add event listeners for editable fields
        document.querySelectorAll('.editable-ticker, .editable-amount, .editable-price, .editable-shares, .editable-date').forEach(input => {
            input.addEventListener('change', (e) => {
                this.updatePortfolioEntry(e.target);
            });
        });
    }

    updatePortfolioEntry(input) {
        const index = parseInt(input.dataset.index);
        const field = input.className.split(' ')[1].replace('editable-', '');

        if (!this.currentData || !this.currentData.portfolio[index]) return;

        let value = input.value;

        // Convert numeric fields
        if (['amount', 'price', 'shares'].includes(field)) {
            value = parseFloat(value) || 0;
        }

        // Map field names
        const fieldMap = {
            'amount': 'amount_invested',
            'price': 'buy_price',
            'shares': 'shares',
            'ticker': 'ticker',
            'date': 'purchase_date'
        };

        const actualField = fieldMap[field] || field;
        this.currentData.portfolio[index][actualField] = value;

        // Update summary
        this.updateSummary(this.currentData);
    }

    removeEntry(index) {
        if (!this.currentData || !this.currentData.portfolio) return;

        this.currentData.portfolio.splice(index, 1);
        this.populatePreviewTable(this.currentData.portfolio);
        this.updateSummary(this.currentData);
    }

    changeCurrency(newCurrency) {
        const oldCurrency = this.selectedCurrency;
        this.selectedCurrency = newCurrency;

        // Only convert if we have current data and the currency actually changed
        if (this.currentData && oldCurrency !== newCurrency) {
            console.log(`🔄 Converting currency from ${oldCurrency} to ${newCurrency}`);
            
            // Convert amount_invested values for each portfolio entry
            this.currentData.portfolio.forEach((entry, index) => {
                const originalAmount = entry.amount_invested;
                const originalCurrency = entry.amount_invested_currency || entry.currency || oldCurrency;
                
                // Store original buy price currency if not already stored
                if (!entry.original_buy_price_currency) {
                    entry.original_buy_price_currency = entry.buy_price_currency || this.detectedCurrency || oldCurrency;
                }
                
                if (originalAmount && originalCurrency) {
                    const convertedAmount = this.convertCurrency(originalAmount, originalCurrency, newCurrency);
                    entry.amount_invested = convertedAmount;
                    entry.amount_invested_currency = newCurrency;
                    
                    console.log(`💱 ${entry.ticker}: ${originalAmount} ${originalCurrency} → ${convertedAmount} ${newCurrency}`);
                    console.log(`🔒 Buy price currency preserved: ${entry.original_buy_price_currency}`);
                }
            });

            // Update summary with converted values
            this.updateSummary(this.currentData);
            
            // Refresh the preview table to show converted values
            this.populatePreviewTable(this.currentData.portfolio);
            
            // Show conversion notice
            this.showMessage(`Currency changed to ${this.getCurrencyDisplayName(newCurrency)}. All amounts have been converted.`, 'success');
        } else if (this.currentData) {
            // Just update displays if currency didn't change
            this.updateSummary(this.currentData);
        }
        
        // CRITICAL: Update all currency labels in the table
        this.updateCurrencyLabelsInTable();
    }
    
    updateCurrencyLabelsInTable() {
        console.log('🔄 Updating currency labels in table...');
        
        // Update all "Amount Invested" currency labels to show selected currency
        const amountLabels = document.querySelectorAll('.amount-currency-label');
        amountLabels.forEach(label => {
            const newCurrencyText = this.selectedCurrency || 'USD';
            label.textContent = newCurrencyText;
            console.log(`💰 Updated amount label to: ${newCurrencyText}`);
        });
        
        // Buy price labels stay as original currency (no change needed)
        console.log('🔒 Buy price labels preserved as original currency');
    }

    convertCurrency(amount, fromCurrency, toCurrency) {
        // Currency conversion rates (same as in portfolio_import.py)
        const CURRENCY_RATES = {
            'USD': 1.0,     // Base currency
            'EUR': 1.08,    // Euro
            'GBP': 1.27,    // British Pound
            'JPY': 0.0067,  // Japanese Yen
            'CAD': 0.74,    // Canadian Dollar
            'AUD': 0.66,    // Australian Dollar
            'CHF': 1.10,    // Swiss Franc
            'CNY': 0.14,    // Chinese Yuan
            'SEK': 0.092,   // Swedish Krona
            'NOK': 0.091,   // Norwegian Krone
            'DKK': 0.145,   // Danish Krone
            'PLN': 0.25,    // Polish Zloty
            'CZK': 0.044,   // Czech Koruna
            'HUF': 0.0027,  // Hungarian Forint
            'BRL': 0.20,    // Brazilian Real
            'MXN': 0.059,   // Mexican Peso
            'INR': 0.012,   // Indian Rupee
            'KRW': 0.00076, // South Korean Won
            'SGD': 0.74,    // Singapore Dollar
            'HKD': 0.13,    // Hong Kong Dollar
            'NZD': 0.61,    // New Zealand Dollar
            'ZAR': 0.055,   // South African Rand
            'RUB': 0.011,   // Russian Ruble
            'TRY': 0.034,   // Turkish Lira
            'THB': 0.028,   // Thai Baht
            'MYR': 0.22,    // Malaysian Ringgit
            'IDR': 0.000066,// Indonesian Rupiah
            'PHP': 0.018,   // Philippine Peso
        };

        if (fromCurrency === toCurrency) {
            return amount;
        }

        const fromRate = CURRENCY_RATES[fromCurrency] || 1.0;
        const toRate = CURRENCY_RATES[toCurrency] || 1.0;
        
        // Convert to USD first, then to target currency
        const usdAmount = amount * fromRate;
        const convertedAmount = usdAmount / toRate;
        
        return Math.round(convertedAmount * 100) / 100; // Round to 2 decimal places
    }

    addNewEntry() {
        if (!this.currentData) {
            this.currentData = {
                success: true,
                portfolio: [],
                cash_position: 0,
                summary: { total_entries: 0, total_invested: 0, unique_tickers: 0 }
            };
        }

        const newEntry = {
            ticker: '',
            amount_invested: 0,
            amount_invested_currency: this.selectedCurrency || 'USD',
            buy_price: 0,
            buy_price_currency: this.selectedCurrency || 'USD',
            original_buy_price_currency: this.selectedCurrency || 'USD', // Store original for display
            shares: 0,
            purchase_date: new Date().toISOString().split('T')[0],
            currency: this.selectedCurrency || 'USD'
        };

        this.currentData.portfolio.push(newEntry);
        this.populatePreviewTable(this.currentData.portfolio);
        this.updateSummary(this.currentData);

        // Show results container if hidden
        document.getElementById('resultsContainer').style.display = 'block';

        // Focus on the new ticker input
        setTimeout(() => {
            const newTickerInput = document.querySelector(`input[data-index="${this.currentData.portfolio.length - 1}"].editable-ticker`);
            if (newTickerInput) {
                newTickerInput.focus();
            }
        }, 100);
    }

    formatCurrency(amount, currency = null) {
        const selectedCurrency = currency || this.selectedCurrency || 'USD';
        const currencySymbols = {
            'USD': '$', 'EUR': '€', 'GBP': '£', 'JPY': '¥', 'CAD': 'C$', 'AUD': 'A$',
            'CHF': 'CHF', 'CNY': '¥', 'SEK': 'kr', 'NOK': 'kr', 'DKK': 'kr',
            'PLN': 'zł', 'CZK': 'Kč', 'HUF': 'Ft', 'RUB': '₽', 'BRL': 'R$',
            'INR': '₹', 'KRW': '₩', 'SGD': 'S$', 'HKD': 'HK$', 'NZD': 'NZ$',
            'MXN': '$', 'ZAR': 'R', 'TRY': '₺', 'ILS': '₪', 'THB': '฿',
            'MYR': 'RM', 'PHP': '₱', 'IDR': 'Rp', 'VND': '₫'
        };

        const symbol = currencySymbols[selectedCurrency] || selectedCurrency;
        const formattedAmount = (amount || 0).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        return `${symbol}${formattedAmount}`;
    }

    formatDate(dateString) {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch {
            return dateString;
        }
    }

    // Currency Selection Modal Methods
    showCurrencySelectionModal(result) {
        console.log('showCurrencySelectionModal called with:', result);
        
        const modal = document.getElementById('currencySelectionModal');
        const currencyQuestion = document.getElementById('currencyQuestion');
        const detectedCurrencies = document.getElementById('detectedCurrencies');
        const currencyAnalysis = document.getElementById('currencyAnalysis');
        const confirmButton = document.getElementById('confirmCurrencyButton');
        
        console.log('Modal elements found:', {
            modal: !!modal,
            currencyQuestion: !!currencyQuestion,
            detectedCurrencies: !!detectedCurrencies,
            currencyAnalysis: !!currencyAnalysis,
            confirmButton: !!confirmButton
        });

        // Check if modal exists
        if (!modal) {
            console.error('Currency selection modal not found in DOM!');
            alert('Currency Selection Modal Error: Modal element not found. Please refresh the page.');
            return;
        }

        // Update the question text
        if (result.currency_info && result.currency_info.gemini_question) {
            currencyQuestion.innerHTML = `<p>${result.currency_info.gemini_question}</p>`;
        } else {
            const detectedCurrency = result.detected_currency || result.currency || 'unknown';
            currencyQuestion.innerHTML = `<p>I detected <strong>${detectedCurrency}</strong> currency in your portfolio. Please confirm this is correct or select your preferred display currency:</p>`;
        }

        // Update detected currencies display
        const currencies = result.currency_info?.detected_currencies || [result.detected_currency || result.currency];
        detectedCurrencies.textContent = currencies.join(', ');

        // Update currency analysis
        if (result.currency_info && result.currency_info.currency_analysis) {
            let analysisText = '';
            Object.entries(result.currency_info.currency_analysis).forEach(([currency, stats]) => {
                const percentage = Math.round(stats.percentage || 0);
                analysisText += `${currency}: ${stats.total_usage || 0} fields (${percentage}%); `;
            });
            currencyAnalysis.textContent = analysisText.slice(0, -2); // Remove last "; "
        } else {
            currencyAnalysis.textContent = 'Currency detected from portfolio data patterns';
        }

        // Set up currency option click handlers
        document.querySelectorAll('.currency-option').forEach(option => {
            option.addEventListener('click', () => {
                const currency = option.dataset.currency;
                this.selectCurrencyOption(currency);
            });
        });

        // Set default selection to detected currency
        const primaryCurrency = result.currency_info?.primary_currency || result.detected_currency || result.currency || 'DKK';
        this.selectCurrencyOption(primaryCurrency);

        // Show the modal
        console.log('Showing currency selection modal...');
        modal.style.display = 'block';
        console.log('Modal display set to block');

        // Store the result for later use
        this.pendingImportResult = result;
    }

    selectCurrencyOption(currency) {
        // Remove previous selections
        document.querySelectorAll('.currency-option').forEach(option => {
            option.classList.remove('selected');
        });

        // Select the new option
        const selectedOption = document.querySelector(`[data-currency="${currency}"]`);
        if (selectedOption) {
            selectedOption.classList.add('selected');
            this.selectedCurrency = currency;
            
            // Enable confirm button
            const confirmButton = document.getElementById('confirmCurrencyButton');
            if (confirmButton) {
                confirmButton.disabled = false;
            }
            
            console.log('Selected currency:', currency);
        }
    }

    getCurrencyDisplayName(currency) {
        const currencyNames = {
            'USD': '🇺🇸 US Dollar ($)',
            'EUR': '🇪🇺 Euro (€)',
            'GBP': '🇬🇧 British Pound (£)',
            'DKK': '🇩🇰 Danish Krone (kr)',
            'SEK': '🇸🇪 Swedish Krona (kr)',
            'NOK': '🇳🇴 Norwegian Krone (kr)',
            'JPY': '🇯🇵 Japanese Yen (¥)',
            'CHF': '🇨🇭 Swiss Franc (CHF)',
            'CAD': '🇨🇦 Canadian Dollar (C$)',
            'AUD': '🇦🇺 Australian Dollar (A$)',
            'CNY': '🇨🇳 Chinese Yuan (¥)',
            'PLN': '🇵🇱 Polish Zloty (zł)',
            'CZK': '🇨🇿 Czech Koruna (Kč)',
            'HUF': '🇭🇺 Hungarian Forint (Ft)',
            'RUB': '🇷🇺 Russian Ruble (₽)',
            'BRL': '🇧🇷 Brazilian Real (R$)',
            'INR': '🇮🇳 Indian Rupee (₹)',
            'KRW': '🇰🇷 South Korean Won (₩)',
            'SGD': '🇸🇬 Singapore Dollar (S$)',
            'HKD': '🇭🇰 Hong Kong Dollar (HK$)',
            'NZD': '🇳🇿 New Zealand Dollar (NZ$)',
            'MXN': '🇲🇽 Mexican Peso ($)',
            'ZAR': '🇿🇦 South African Rand (R)',
            'TRY': '🇹🇷 Turkish Lira (₺)',
            'THB': '🇹🇭 Thai Baht (฿)',
            'MYR': '🇲🇾 Malaysian Ringgit (RM)',
            'PHP': '🇵🇭 Philippine Peso (₱)',
            'IDR': '🇮🇩 Indonesian Rupiah (Rp)',
            'VND': '🇻🇳 Vietnamese Dong (₫)'
        };

        return currencyNames[currency] || `${currency}`;
    }

    async confirmCurrencySelection() {
        if (!this.selectedCurrency) {
            this.showMessage('Please select a currency', 'error');
            return;
        }

        console.log('Confirming currency selection:', this.selectedCurrency);

        // Close the modal first
        this.closeCurrencyModal();

        // Use the pending result if available, otherwise use current data
        const result = this.pendingImportResult || this.currentData;
        
        if (!result) {
            this.showMessage('No data available for currency selection', 'error');
            return;
        }

        // PRESERVE ORIGINAL DATA: Only update display preferences, don't modify the actual data
        result.selected_currency = this.selectedCurrency;
        result.user_selected_currency = true;
        result.display_currency = this.selectedCurrency;  // For display purposes only

        console.log('🎯 User selected display currency:', this.selectedCurrency);
        console.log('📊 Preserving original portfolio data exactly as Gemini extracted it');

        // CRITICAL: Do NOT modify the original portfolio data
        // Gemini AI extracted the data correctly - preserve it exactly as is
        // Only set display preferences for the UI
        if (result.portfolio) {
            result.portfolio.forEach(entry => {
                console.log(`💰 ${entry.ticker}: preserving original data - amount=${entry.amount_invested}, currency=${entry.currency || entry.buy_price_currency}`);

                // DO NOT modify entry.currency, entry.buy_price_currency, etc.
                // These contain the correct original data from Gemini AI
                // The UI will handle display formatting based on result.display_currency
            });
        }

        // Send the currency selection to the backend to properly process
        this.sendCurrencySelectionToBackend(result);
    }

    async sendCurrencySelectionToBackend(result) {
        try {
            console.log('🔄 Sending currency selection to backend...');

            const response = await fetch('/api/import/currency-selection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    selected_currency: this.selectedCurrency,
                    original_data: result
                })
            });

            const updatedResult = await response.json();

            if (updatedResult.success) {
                console.log('✅ Backend processed currency selection successfully');

                // Set this as current data
                this.currentData = result;  // Use our updated result, not backend result

                // Ensure currency dropdown is updated
                this.ensureCurrencyDropdownUpdated();

                // Display results with the selected currency
                this.displayResultsWithCurrency(result);

                this.showMessage(`Portfolio currency set to ${this.getCurrencyDisplayName(this.selectedCurrency)}`, 'success');
            } else {
                console.error('❌ Backend error:', updatedResult.error);

                // Still show results with user selection, but warn about backend issue
                this.currentData = result;
                this.ensureCurrencyDropdownUpdated();
                this.displayResultsWithCurrency(result);
                this.showMessage(`Currency set to ${this.getCurrencyDisplayName(this.selectedCurrency)} (backend warning: ${updatedResult.error})`, 'warning');
            }
        } catch (error) {
            console.error('❌ Error communicating with backend:', error);

            // Still show results with user selection
            this.currentData = result;
            this.ensureCurrencyDropdownUpdated();
            this.displayResultsWithCurrency(result);
            this.showMessage(`Currency set to ${this.getCurrencyDisplayName(this.selectedCurrency)} (offline mode)`, 'warning');
        }
    }

    displayResultsWithCurrency(result) {
        // Update the current data with the selected currency
        result.selected_currency = this.selectedCurrency;
        result.currency = this.selectedCurrency;

        // Update the portfolio currency dropdown to reflect the selected currency
        const portfolioCurrencySelect = document.getElementById('portfolioCurrency');
        if (portfolioCurrencySelect) {
            portfolioCurrencySelect.value = this.selectedCurrency;
            console.log('Updated portfolio currency dropdown to:', this.selectedCurrency);
            
            // Trigger change event to ensure any listeners are notified
            portfolioCurrencySelect.dispatchEvent(new Event('change'));
        }
        
        // Also store in localStorage for persistence
        localStorage.setItem('portfolioCurrency', this.selectedCurrency);
        console.log('Stored currency in localStorage:', this.selectedCurrency);

        // Update summary
        this.updateSummary(result);

        // Populate preview table
        this.populatePreviewTable(result.portfolio);

        // Show results container
        document.getElementById('resultsContainer').style.display = 'block';

        // Show a message about the currency selection
        this.showMessage(`Portfolio will be displayed in ${this.getCurrencyDisplayName(this.selectedCurrency)}`, 'info');
    }

    ensureCurrencyDropdownUpdated() {
        console.log('🔄 Ensuring currency dropdown is updated...');
        console.log(`   detectedCurrency: ${this.detectedCurrency}`);
        console.log(`   selectedCurrency: ${this.selectedCurrency}`);
        
        const portfolioCurrencySelect = document.getElementById('portfolioCurrency');
        if (!portfolioCurrencySelect) {
            console.error('❌ Portfolio currency select element not found!');
            return;
        }

        // Use detected currency if available, otherwise keep selected
        const currencyToShow = this.detectedCurrency || this.selectedCurrency || 'USD';
        
        console.log(`🎯 Setting dropdown to: ${currencyToShow}`);
        portfolioCurrencySelect.value = currencyToShow;
        this.selectedCurrency = currencyToShow;
        
        // Update currency labels in the table
        this.updateCurrencyLabelsInTable();
        
        console.log(`✅ Currency dropdown updated to: ${portfolioCurrencySelect.value}`);
    }



    populateCurrencyAnalysis(currencyInfo) {
        const analysisContainer = document.getElementById('currencyAnalysis');
        const analysis = currencyInfo.currency_analysis || {};
        
        let html = '';
        
        if (analysis.currency_counts) {
            html += '<h5 style="margin-bottom: 12px; color: var(--text-primary);">Detected Currencies:</h5>';
            for (const [currency, count] of Object.entries(analysis.currency_counts)) {
                html += `
                    <div class="currency-stat">
                        <span>${currency}</span>
                        <span>${count} occurrence${count > 1 ? 's' : ''}</span>
                    </div>
                `;
            }
        }
        
        if (currencyInfo.selection_reason) {
            html += `
                <div style="margin-top: 16px; padding: 12px; background: rgba(59, 130, 246, 0.1); border-radius: 6px;">
                    <strong>Why selection is needed:</strong> ${currencyInfo.selection_reason}
                </div>
            `;
        }
        
        analysisContainer.innerHTML = html;
    }

    populateCurrencyOptions(currencies) {
        const optionsContainer = document.getElementById('currencyOptions');
        const currencyNames = {
            'USD': '🇺🇸 US Dollar ($)',
            'EUR': '🇪🇺 Euro (€)',
            'DKK': '🇩🇰 Danish Krone (kr)',
            'GBP': '🇬🇧 British Pound (£)',
            'JPY': '🇯🇵 Japanese Yen (¥)',
            'CAD': '🇨🇦 Canadian Dollar (C$)',
            'AUD': '🇦🇺 Australian Dollar (A$)',
            'CHF': '🇨🇭 Swiss Franc (CHF)',
            'SEK': '🇸🇪 Swedish Krona (kr)',
            'NOK': '🇳🇴 Norwegian Krone (kr)'
        };
        
        let html = '';
        currencies.forEach((currency, index) => {
            const isDefault = index === 0;
            const displayName = currencyNames[currency] || `${currency}`;
            
            html += `
                <div class="currency-option ${isDefault ? 'selected' : ''}" onclick="selectCurrency('${currency}')">
                    <input type="radio" name="selectedCurrency" value="${currency}" ${isDefault ? 'checked' : ''}>
                    <div class="currency-info">
                        <div class="currency-name">${displayName}</div>
                        <div class="currency-details">All amounts will be displayed in this currency</div>
                    </div>
                </div>
            `;
        });
        
        optionsContainer.innerHTML = html;
        
        // Set the first currency as selected by default
        if (currencies.length > 0) {
            this.selectedCurrency = currencies[0];
        }
    }

    showOCRFailureGuidance(guidance) {
        if (!guidance || !guidance.show_modal) {
            return;
        }
        
        // Create a custom modal for OCR failure guidance
        const modalHtml = `
            <div id="ocrFailureModal" class="modal-overlay" style="display: flex;">
                <div class="modal-content currency-modal">
                    <div class="modal-header">
                        <h3><i class="${guidance.icon || 'fas fa-exclamation-triangle'}"></i> ${guidance.title}</h3>
                        <button type="button" class="modal-close" onclick="closeOCRFailureModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p class="detection-message">
                            <i class="fas fa-info-circle"></i>
                            ${guidance.message}
                        </p>
                        
                        <div class="currency-explanation">
                            <div class="explanation-card">
                                <h4><i class="fas fa-lightbulb"></i> Suggestions to fix this:</h4>
                                <ul>
                                    ${guidance.suggestions.map(suggestion => `
                                        <li>
                                            <i class="${suggestion.icon}"></i>
                                            <strong>${suggestion.title}:</strong> ${suggestion.description}
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary" onclick="closeOCRFailureModal()">
                            <i class="fas fa-arrow-left"></i> Try Again
                        </button>
                        <button type="button" class="btn-primary" onclick="switchToSpreadsheet()">
                            <i class="fas fa-file-excel"></i> ${guidance.alternative_action?.text || 'Upload Spreadsheet'}
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    getCurrencyDisplayName(currency) {
        const names = {
            'USD': 'US Dollars',
            'EUR': 'Euros',
            'DKK': 'Danish Kroner',
            'GBP': 'British Pounds',
            'JPY': 'Japanese Yen',
            'CAD': 'Canadian Dollars',
            'AUD': 'Australian Dollars',
            'CHF': 'Swiss Francs',
            'SEK': 'Swedish Kronor',
            'NOK': 'Norwegian Kroner'
        };
        return names[currency] || currency;
    }

    closeCurrencyModal() {
        const modal = document.getElementById('currencySelectionModal');
        modal.style.display = 'none';
    }
}

// Initialize the importer when the page loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing PortfolioImporter');
    window.portfolioImporter = new PortfolioImporter();
});

// Global functions for currency modal
function selectCurrency(currency) {
    console.log('Global selectCurrency called with:', currency);
    
    if (window.portfolioImporter) {
        window.portfolioImporter.selectCurrencyOption(currency);
    }
}

function confirmCurrencySelection() {
    if (window.portfolioImporter) {
        window.portfolioImporter.confirmCurrencySelection();
    }
}

function closeCurrencyModal() {
    if (window.portfolioImporter) {
        window.portfolioImporter.closeCurrencyModal();
    }
}

function closeOCRFailureModal() {
    const modal = document.getElementById('ocrFailureModal');
    if (modal) {
        modal.remove();
    }
}

function switchToSpreadsheet() {
    closeOCRFailureModal();
    // Scroll to spreadsheet upload section
    const spreadsheetSection = document.querySelector('.import-method:nth-child(2)');
    if (spreadsheetSection) {
        spreadsheetSection.scrollIntoView({ behavior: 'smooth' });
        // Highlight the spreadsheet upload area
        const uploadArea = spreadsheetSection.querySelector('.upload-area');
        if (uploadArea) {
            uploadArea.style.borderColor = 'var(--highlight-color)';
            uploadArea.style.background = 'rgba(var(--highlight-color-rgb), 0.1)';
            setTimeout(() => {
                uploadArea.style.borderColor = '';
                uploadArea.style.background = '';
            }, 3000);
        }
    }
}

function testCurrencyModal() {
    console.log('Testing currency selection modal...');
    
    // Create test data that should trigger the modal
    const testResult = {
        success: true,
        portfolio: [
            { ticker: 'AAPL', amount_invested: 2462.85, currency: 'DKK' },
            { ticker: 'MSFT', amount_invested: 3250.75, currency: 'DKK' }
        ],
        detected_currency: 'DKK',
        currency: 'DKK',
        currency_info: {
            detected_currencies: ['DKK'],
            primary_currency: 'DKK',
            has_mixed_currencies: false,
            requires_user_selection: true,
            currency_selection_reason: 'Non-standard currency detected',
            gemini_question: 'I detected DKK currency in your portfolio. Is this correct, or would you prefer to display amounts in a different currency?',
            currency_analysis: {
                'DKK': { total_usage: 4, percentage: 100 }
            }
        }
    };
    
    if (window.portfolioImporter) {
        console.log('Calling showCurrencySelectionModal with test data...');
        window.portfolioImporter.showCurrencySelectionModal(testResult);
    } else {
        alert('Portfolio importer not initialized. Please refresh the page.');
    }
}
</script>
{% endblock %}
