{% extends "base.html" %}

{% block title %}DCF Analysis - {{ ticker.split('.')[0] }}{% endblock %}

{% block head_extra %}
    {# Enhanced Libraries for Premium Experience #}
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-transition/3.0.1/d3-transition.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-scale/4.0.2/d3-scale.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <style>
        /* === PREMIUM DCF RESULT PAGE STYLES === */

        /* Root Variables for Premium Theme */
        :root {
            --premium-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --premium-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --premium-gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --premium-gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --premium-gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --premium-glass: rgba(255, 255, 255, 0.1);
            --premium-glass-border: rgba(255, 255, 255, 0.2);
            --premium-shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.1);
            --premium-shadow-medium: 0 16px 64px rgba(0, 0, 0, 0.15);
            --premium-shadow-strong: 0 24px 96px rgba(0, 0, 0, 0.2);
            --premium-blur: blur(20px);
            --premium-border-radius: 24px;
            --premium-border-radius-small: 16px;
            --premium-animation-fast: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --premium-animation-medium: 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            --premium-animation-slow: 1.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Dark Theme Enhancements */
        [data-theme="dark"] {
            --premium-glass: rgba(255, 255, 255, 0.05);
            --premium-glass-border: rgba(255, 255, 255, 0.1);
            --premium-shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.3);
            --premium-shadow-medium: 0 16px 64px rgba(0, 0, 0, 0.4);
            --premium-shadow-strong: 0 24px 96px rgba(0, 0, 0, 0.5);
        }

        /* Page Container with Animated Background */
        .dcf-result-container {
            position: relative;
            min-height: 100vh;
            background: var(--bg-color);
            overflow-x: hidden;
        }

        .dcf-result-container::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundFloat 20s ease-in-out infinite;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        /* Floating Particles Animation */
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: floatParticle 15s linear infinite;
        }

        @keyframes floatParticle {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        /* Premium Hero Section */
        .dcf-hero-section {
            position: relative;
            padding: 60px 0;
            text-align: center;
            background: var(--premium-gradient-primary);
            margin: -25px -25px 40px -25px;
            border-radius: 0 0 var(--premium-border-radius) var(--premium-border-radius);
            box-shadow: var(--premium-shadow-medium);
            overflow: hidden;
        }

        .dcf-hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(10px) translateY(10px); }
        }

        .dcf-hero-title {
            position: relative;
            z-index: 2;
            color: var(--text-color);
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            animation: heroTitleReveal 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        @keyframes heroTitleReveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dcf-hero-subtitle {
            position: relative;
            z-index: 2;
            color: var(--text-muted-color);
            font-size: 1.3rem;
            font-weight: 300;
            margin-bottom: 40px;
            animation: heroSubtitleReveal 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.3s forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        @keyframes heroSubtitleReveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        /* Premium Metric Cards Grid */
        .premium-metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin: 40px 0;
            position: relative;
            z-index: 2;
        }

        .premium-metric-card {
            background: var(--premium-glass);
            backdrop-filter: var(--premium-blur);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            padding: 32px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all var(--premium-animation-medium);
            box-shadow: var(--premium-shadow-soft);
            animation: metricCardReveal 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            opacity: 0;
            transform: translateY(40px) scale(0.95);
        }

        .premium-metric-card:nth-child(1) { animation-delay: 0.1s; }
        .premium-metric-card:nth-child(2) { animation-delay: 0.2s; }
        .premium-metric-card:nth-child(3) { animation-delay: 0.3s; }
        .premium-metric-card:nth-child(4) { animation-delay: 0.4s; }

        @keyframes metricCardReveal {
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .premium-metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--premium-gradient-primary);
            transform: scaleX(0);
            transition: transform var(--premium-animation-medium);
        }

        .premium-metric-card:hover::before {
            transform: scaleX(1);
        }

        .premium-metric-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--premium-shadow-medium);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .metric-icon {
            font-size: 3rem;
            margin-bottom: 16px;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: iconPulse 2s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-muted-color);
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 8px;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .metric-change {
            font-size: 0.85rem;
            font-weight: 600;
            padding: 4px 12px;
            border-radius: 20px;
            display: inline-block;
        }

        .metric-change.positive {
            background: var(--premium-gradient-success);
            color: #ffffff;
        }

        .metric-change.negative {
            background: var(--premium-gradient-danger);
            color: white;
        }

        /* Enhanced Result Box */
        .premium-result-box {
            background: var(--premium-glass);
            backdrop-filter: var(--premium-blur);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            padding: 40px;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
            box-shadow: var(--premium-shadow-medium);
            animation: resultBoxReveal 1s cubic-bezier(0.4, 0, 0.2, 1) 0.5s forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        @keyframes resultBoxReveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .premium-result-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--premium-gradient-primary);
            opacity: 0.05;
            z-index: -1;
        }

        .result-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .result-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: #ffffff !important; /* Force white text on colored background */
            margin-bottom: 12px;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: #ffffff !important; /* Force white text */
            background-clip: text;
        }

        .result-subtitle {
            font-size: 1.1rem;
            color: #ffffff !important; /* Force white text on colored background */
            font-weight: 300;
        }

        .ticker-badge {
            display: inline-block;
            background: var(--premium-gradient-primary);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1.1rem;
            margin: 0 8px;
            box-shadow: var(--premium-shadow-soft);
            animation: badgePulse 3s ease-in-out infinite;
        }

        @keyframes badgePulse {
            0%, 100% { transform: scale(1); box-shadow: var(--premium-shadow-soft); }
            50% { transform: scale(1.05); box-shadow: var(--premium-shadow-medium); }
        }

        /* Interactive Dashboard Layout */
        .premium-dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 60px 0;
            position: relative;
        }

        .dashboard-section {
            background: var(--premium-glass);
            backdrop-filter: var(--premium-blur);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            padding: 32px;
            position: relative;
            overflow: hidden;
            box-shadow: var(--premium-shadow-soft);
            transition: all var(--premium-animation-medium);
            animation: dashboardSectionReveal 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            opacity: 0;
            transform: translateY(40px);
        }

        .dashboard-section:nth-child(1) { animation-delay: 0.2s; }
        .dashboard-section:nth-child(2) { animation-delay: 0.4s; }

        @keyframes dashboardSectionReveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dashboard-section:hover {
            transform: translateY(-4px);
            box-shadow: var(--premium-shadow-medium);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--premium-glass-border);
        }

        .section-icon {
            font-size: 1.8rem;
            margin-right: 12px;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-color);
            margin: 0;
        }

        /* Enhanced Chart Styles */
        .premium-chart-container {
            position: relative;
            height: 400px;
            background: var(--premium-glass);
            border-radius: var(--premium-border-radius-small);
            overflow: hidden;
            border: 1px solid var(--premium-glass-border);
        }

        .premium-chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 70%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
            z-index: 0;
        }

        .premium-chart-container svg {
            position: relative;
            z-index: 1;
            width: 100%;
            height: 100%;
        }

        /* Advanced Chart Elements */
        .chart-line {
            fill: none;
            stroke-width: 3;
            stroke: url(#lineGradient);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .chart-area {
            fill: url(#areaGradient);
            opacity: 0.3;
        }

        .chart-dot {
            fill: white;
            stroke-width: 3;
            stroke: url(#lineGradient);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            transition: all var(--premium-animation-fast);
        }

        .chart-dot:hover {
            r: 6;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .chart-grid {
            stroke: var(--premium-glass-border);
            stroke-width: 1;
            opacity: 0.5;
        }

        .chart-axis {
            stroke: var(--text-muted-color);
            stroke-width: 1;
        }

        .chart-label {
            fill: var(--text-muted-color);
            font-size: 12px;
            font-weight: 500;
        }

        /* Interactive Tooltip */
        .premium-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 500;
            pointer-events: none;
            opacity: 0;
            transform: translateY(10px);
            transition: all var(--premium-animation-fast);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--premium-shadow-soft);
            z-index: 1000;
        }

        .premium-tooltip.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .premium-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
        }

        /* Premium Gauge Visualization */
        .premium-gauge-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px;
            background: var(--premium-glass);
            backdrop-filter: var(--premium-blur);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            box-shadow: var(--premium-shadow-soft);
            margin: 40px 0;
        }

        .gauge-wrapper {
            position: relative;
            width: 300px;
            height: 300px;
            margin-bottom: 30px;
        }

        .gauge-background {
            fill: none;
            stroke: var(--premium-glass-border);
            stroke-width: 20;
            stroke-linecap: round;
        }

        .gauge-progress {
            fill: none;
            stroke: url(#gaugeGradient);
            stroke-width: 20;
            stroke-linecap: round;
            transition: stroke-dasharray 2s cubic-bezier(0.4, 0, 0.2, 1);
            filter: drop-shadow(0 0 10px rgba(120, 119, 198, 0.5));
        }

        .gauge-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .gauge-value {
            font-size: 3rem;
            font-weight: 800;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            margin-bottom: 8px;
        }

        .gauge-label {
            font-size: 1rem;
            color: var(--text-muted-color);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Premium Rating Analysis */
        .premium-rating-analysis {
            background: var(--premium-glass);
            backdrop-filter: var(--premium-blur);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            padding: 40px;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
        }

        .rating-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: center;
        }

        .rating-chart-container {
            position: relative;
            height: 400px;
        }

        .rating-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .rating-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius-small);
            padding: 20px;
            transition: all var(--premium-animation-fast);
        }

        .rating-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(8px);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .rating-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .rating-item-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .rating-item-score {
            font-size: 1.3rem;
            font-weight: 800;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .rating-item-bar {
            height: 8px;
            background: var(--premium-glass-border);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .rating-item-progress {
            height: 100%;
            background: var(--premium-gradient-primary);
            border-radius: 4px;
            transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
        }

        .rating-item-description {
            font-size: 0.9rem;
            color: var(--text-muted-color);
            line-height: 1.4;
        }

        /* Enhanced Investment Rating Styles */
        .section-subtitle {
            font-size: 1rem;
            color: var(--text-muted-color);
            margin-top: 8px;
            font-weight: 400;
        }

        .overall-rating-display {
            display: flex;
            align-items: center;
            gap: 40px;
            margin: 40px 0;
            padding: 30px;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            backdrop-filter: blur(10px);
        }

        .overall-rating-circle {
            position: relative;
            width: 120px;
            height: 120px;
            flex-shrink: 0;
        }

        .rating-circle-svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .rating-circle-bg {
            fill: none;
            stroke: var(--premium-glass-border);
            stroke-width: 8;
            stroke-linecap: round;
        }

        .rating-circle-progress {
            fill: none;
            stroke: url(#overallRatingGradient);
            stroke-width: 8;
            stroke-linecap: round;
            stroke-dasharray: 314;
            stroke-dashoffset: 314;
            transition: stroke-dashoffset 2s cubic-bezier(0.4, 0, 0.2, 1);
            filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.4));
        }

        .overall-rating-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .overall-rating-score {
            font-size: 2rem;
            font-weight: 800;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
        }

        .overall-rating-label {
            font-size: 0.8rem;
            color: var(--text-muted-color);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 4px;
        }

        .overall-rating-description {
            flex: 1;
        }

        .overall-rating-description h4 {
            font-size: 1.3rem;
            margin-bottom: 12px;
            color: var(--text-color);
        }

        .overall-rating-description p {
            font-size: 1rem;
            line-height: 1.6;
            color: var(--text-muted-color);
            margin: 0;
        }

        /* Grade Colors */
        .grade-excellent { color: #10b981; font-weight: 600; }
        .grade-good { color: #3b82f6; font-weight: 600; }
        .grade-fair { color: #f59e0b; font-weight: 600; }
        .grade-below-average { color: #ef4444; font-weight: 600; }
        .grade-poor { color: #dc2626; font-weight: 600; }

        .enhanced-rating-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 40px;
        }

        .radar-chart-section {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .radar-chart-container {
            width: 100%;
            height: 400px;
            margin-bottom: 20px;
        }

        .radar-chart-legend {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: var(--text-muted-color);
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }

        .detailed-ratings-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .rating-category {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius-small);
            padding: 24px;
            transition: all var(--premium-animation-fast);
        }

        .rating-category:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .rating-category-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .category-icon.business-quality { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .category-icon.financial-strength { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .category-icon.valuation { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .category-icon.management-quality { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .category-info {
            flex: 1;
        }

        .category-info h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-color);
            margin: 0 0 4px 0;
        }

        .category-score {
            font-size: 1.4rem;
            font-weight: 800;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .rating-progress-container {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .rating-progress-bar {
            flex: 1;
            height: 12px;
            background: var(--premium-glass-border);
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }

        .rating-progress-fill {
            height: 100%;
            border-radius: 6px;
            transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .rating-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.3) 50%,
                transparent 100%);
            animation: shimmer 2s infinite;
        }

        .business-quality-fill { background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); }
        .financial-strength-fill { background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%); }
        .valuation-fill { background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%); }
        .management-quality-fill { background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%); }

        .rating-percentage {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-color);
            min-width: 40px;
            text-align: right;
        }

        .rating-description {
            font-size: 0.95rem;
            line-height: 1.5;
            color: var(--text-muted-color);
        }

        .rating-description strong {
            color: var(--text-color);
            font-weight: 600;
        }

        /* Enhanced Chart Styles */
        .chart-area {
            opacity: 0.8;
        }

        .chart-line {
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        .chart-axis {
            font-size: 11px;
            color: var(--text-muted-color);
        }

        .chart-axis .domain {
            stroke: rgba(255, 255, 255, 0.2);
            stroke-width: 1;
        }

        .chart-axis .tick text {
            fill: var(--text-muted-color);
            font-size: 10px;
        }

        .value-label-group {
            pointer-events: none;
        }

        .focus-group circle {
            filter: drop-shadow(0 0 6px rgba(102, 126, 234, 0.6));
        }

        .chart-overlay {
            cursor: crosshair;
        }

        .performance-indicators {
            pointer-events: none;
        }

        /* Chart Container Enhancements */
        .premium-chart-container {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .premium-chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(102, 126, 234, 0.05) 0%,
                transparent 50%,
                rgba(118, 75, 162, 0.05) 100%);
            pointer-events: none;
        }

        .premium-chart-container svg {
            position: relative;
            z-index: 1;
        }

        /* Responsive Design Enhancements */
        @media (max-width: 1200px) {
            .enhanced-rating-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .overall-rating-display {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .radar-chart-container {
                height: 300px;
            }
        }

        @media (max-width: 768px) {
            .overall-rating-circle {
                width: 100px;
                height: 100px;
            }

            .overall-rating-score {
                font-size: 1.5rem;
            }

            .category-icon {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .category-score {
                font-size: 1.2rem;
            }

            .rating-category {
                padding: 20px;
            }

            .premium-chart-container {
                padding: 15px;
            }

            .radar-chart-container {
                height: 250px;
            }
        }

        @media (max-width: 480px) {
            .section-header {
                text-align: center;
            }

            .overall-rating-display {
                padding: 20px;
            }

            .rating-category-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .category-info {
                width: 100%;
            }

            .radar-chart-container {
                height: 200px;
            }
        }

        /* Premium Investment Analysis Styles */
        .premium-investment-analysis {
            background: var(--premium-glass);
            backdrop-filter: var(--premium-blur);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            box-shadow: var(--premium-shadow-soft);
            margin: 40px 0;
            overflow: hidden;
        }

        .analysis-header {
            padding: 40px 40px 30px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-bottom: 1px solid var(--premium-glass-border);
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .header-icon {
            position: relative;
        }

        .icon-wrapper {
            position: relative;
            width: 60px;
            height: 60px;
            background: var(--premium-gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .icon-glow {
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: var(--premium-gradient-primary);
            border-radius: 50%;
            opacity: 0.3;
            filter: blur(20px);
            z-index: -1;
            animation: iconPulse 3s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.1); opacity: 0.5; }
        }

        .header-text {
            flex: 1;
        }

        .analysis-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: #ffffff !important; /* Force white text */
            background-clip: text;
            color: #ffffff !important; /* Force white text on colored background */
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .analysis-subtitle {
            font-size: 1.1rem;
            color: #ffffff !important; /* Force white text on colored background */
            margin: 0;
            font-weight: 500;
        }

        /* DCF Hero Section Text Colors */
        .dcf-hero-title {
            color: #ffffff !important; /* Force white text on colored background */
        }

        .dcf-hero-subtitle {
            color: #ffffff !important; /* Force white text on colored background */
        }

        /* Back button styling */
        .button {
            color: #ffffff !important; /* Force white text on button */
            background: var(--highlight-color) !important;
        }

        .button:hover {
            color: #ffffff !important; /* Maintain white text on hover */
        }

        /* Investment Rating Gauge - Preserve Original Colors */
        #ratingText {
            fill: #ffffff !important; /* Keep white text in gauge */
        }

        #ratingCircle circle {
            /* Preserve gauge circle colors - don't override with theme colors */
        }

        /* Price History Chart - Preserve Semantic Colors */
        .chart-line {
            /* Colors are set in JavaScript with semantic green/red values */
        }

        .chart-area {
            /* Colors are set in JavaScript with semantic green/red values */
        }

        .header-decoration {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .decoration-line {
            flex: 1;
            height: 2px;
            background: var(--premium-gradient-primary);
            border-radius: 1px;
        }

        .decoration-dots {
            display: flex;
            gap: 8px;
        }

        .decoration-dots span {
            width: 8px;
            height: 8px;
            background: var(--premium-gradient-primary);
            border-radius: 50%;
            animation: dotPulse 2s ease-in-out infinite;
        }

        .decoration-dots span:nth-child(2) { animation-delay: 0.3s; }
        .decoration-dots span:nth-child(3) { animation-delay: 0.6s; }

        @keyframes dotPulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* Master Score Dashboard */
        .master-score-dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            padding: 40px;
            align-items: center;
        }

        .score-visualization {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .master-score-circle {
            position: relative;
            width: 200px;
            height: 200px;
        }

        .master-score-svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .master-score-bg {
            fill: none;
            stroke: var(--premium-glass-border);
            stroke-width: 12;
            stroke-linecap: round;
        }

        .master-score-progress {
            fill: none;
            stroke: url(#masterScoreGradient);
            stroke-width: 12;
            stroke-linecap: round;
            stroke-dasharray: 502;
            stroke-dashoffset: 502;
            transition: stroke-dashoffset 2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .master-score-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .master-score-value {
            font-size: 2.8rem;
            font-weight: 900;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            margin-bottom: 6px;
        }

        .master-score-label {
            font-size: 0.9rem;
            color: var(--text-muted-color);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .master-score-scale {
            font-size: 1rem;
            color: var(--text-muted-color);
            font-weight: 500;
            margin-top: 2px;
        }

        .score-indicators {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .score-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 0.9rem;
            color: var(--text-muted-color);
        }

        .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .indicator-dot.excellent { background: linear-gradient(135deg, #10b981, #34d399); }
        .indicator-dot.good { background: linear-gradient(135deg, #3b82f6, #60a5fa); }
        .indicator-dot.fair { background: linear-gradient(135deg, #f59e0b, #fbbf24); }
        .indicator-dot.poor { background: linear-gradient(135deg, #ef4444, #f87171); }

        .investment-verdict {
            display: flex;
            align-items: center;
        }

        .verdict-badge {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 30px;
            border-radius: var(--premium-border-radius);
            background: var(--premium-glass);
            border: 1px solid var(--premium-glass-border);
            backdrop-filter: var(--premium-blur);
            width: 100%;
        }

        .verdict-badge.excellent {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(52, 211, 153, 0.1));
            border-color: rgba(16, 185, 129, 0.3);
        }

        .verdict-badge.good {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
            border-color: rgba(59, 130, 246, 0.3);
        }

        .verdict-badge.fair {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1));
            border-color: rgba(245, 158, 11, 0.3);
        }

        .verdict-badge.below-average,
        .verdict-badge.poor {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(248, 113, 113, 0.1));
            border-color: rgba(239, 68, 68, 0.3);
        }

        .verdict-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            flex-shrink: 0;
        }

        .verdict-badge.excellent .verdict-icon { background: linear-gradient(135deg, #10b981, #34d399); }
        .verdict-badge.good .verdict-icon { background: linear-gradient(135deg, #3b82f6, #60a5fa); }
        .verdict-badge.fair .verdict-icon { background: linear-gradient(135deg, #f59e0b, #fbbf24); }
        .verdict-badge.below-average .verdict-icon,
        .verdict-badge.poor .verdict-icon { background: linear-gradient(135deg, #ef4444, #f87171); }

        .verdict-text h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 0 8px 0;
            color: var(--text-color);
        }

        .verdict-text p {
            font-size: 1rem;
            line-height: 1.5;
            color: var(--text-muted-color);
            margin: 0;
        }

        /* Premium Analysis Framework */
        .premium-analysis-framework {
            padding: 40px;
        }

        .radar-visualization-section {
            margin-bottom: 50px;
        }

        .radar-container {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            padding: 30px;
        }

        .radar-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .radar-header h3 {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-color);
            margin: 0 0 8px 0;
        }

        .radar-header p {
            font-size: 1rem;
            color: var(--text-muted-color);
            margin: 0;
        }

        .radar-chart-wrapper {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 40px;
            align-items: center;
        }

        .radar-chart-container {
            height: 400px;
        }

        .radar-metrics {
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-width: 200px;
        }

        .metric-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--premium-glass-border);
            border-radius: 12px;
            transition: all var(--premium-animation-fast);
        }

        .metric-item:hover {
            background: rgba(255, 255, 255, 0.06);
            transform: translateX(4px);
        }

        .metric-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .metric-dot.business { background: linear-gradient(135deg, #667eea, #764ba2); }
        .metric-dot.financial { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .metric-dot.valuation { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .metric-dot.management { background: linear-gradient(135deg, #43e97b, #38f9d7); }

        .metric-item span {
            flex: 1;
            font-size: 0.9rem;
            color: var(--text-muted-color);
        }

        .metric-item strong {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--text-color);
        }

        /* Analysis Cards Grid */
        .analysis-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .analysis-card {
            background: var(--premium-glass);
            backdrop-filter: var(--premium-blur);
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            padding: 30px;
            transition: all var(--premium-animation-medium);
            position: relative;
            overflow: hidden;
        }

        .analysis-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--premium-gradient-primary);
            transform: scaleX(0);
            transition: transform var(--premium-animation-medium);
        }

        .analysis-card:hover::before {
            transform: scaleX(1);
        }

        .analysis-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--premium-shadow-medium);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .card-icon {
            position: relative;
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            flex-shrink: 0;
        }

        .card-icon.business-quality { background: linear-gradient(135deg, #667eea, #764ba2); }
        .card-icon.financial-strength { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .card-icon.valuation { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .card-icon.management-quality { background: linear-gradient(135deg, #43e97b, #38f9d7); }

        .icon-pulse {
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 16px;
            background: inherit;
            opacity: 0.3;
            animation: iconPulse 3s ease-in-out infinite;
        }

        .card-title {
            flex: 1;
        }

        .card-title h4 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-color);
            margin: 0 0 4px 0;
        }

        .card-score {
            font-size: 1.8rem;
            font-weight: 800;
            background: var(--premium-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-score span {
            font-size: 1rem;
            opacity: 0.7;
        }

        .card-progress {
            margin-bottom: 20px;
        }

        .progress-track {
            position: relative;
            height: 8px;
            background: var(--premium-glass-border);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .progress-fill.business-quality-fill { background: linear-gradient(90deg, #667eea, #764ba2); }
        .progress-fill.financial-strength-fill { background: linear-gradient(90deg, #f093fb, #f5576c); }
        .progress-fill.valuation-fill { background: linear-gradient(90deg, #4facfe, #00f2fe); }
        .progress-fill.management-quality-fill { background: linear-gradient(90deg, #43e97b, #38f9d7); }

        .progress-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 100%);
            animation: shimmer 2s infinite;
        }

        .progress-label {
            font-size: 0.85rem;
            color: var(--text-muted-color);
            font-weight: 500;
        }

        .card-analysis {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .analysis-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            align-self: flex-start;
        }

        .analysis-badge.excellent {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(52, 211, 153, 0.2));
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .analysis-badge.good {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(96, 165, 250, 0.2));
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .analysis-badge.fair {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(251, 191, 36, 0.2));
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .analysis-badge.poor {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(248, 113, 113, 0.2));
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .analysis-text {
            font-size: 0.95rem;
            line-height: 1.5;
            color: var(--text-muted-color);
            margin: 0;
        }

        /* Investment Summary Panel */
        .investment-summary-panel {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 1px solid var(--premium-glass-border);
            border-radius: var(--premium-border-radius);
            padding: 30px;
            margin-top: 20px;
        }

        .summary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--premium-glass-border);
        }

        .summary-header h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-color);
            margin: 0;
        }

        .summary-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .summary-indicator.positive {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(52, 211, 153, 0.2));
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .summary-indicator.neutral {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(251, 191, 36, 0.2));
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .summary-indicator.negative {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(248, 113, 113, 0.2));
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .key-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }

        .metric {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-muted-color);
            font-weight: 500;
        }

        .metric-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-color);
        }

        .metric-value.positive { color: #10b981; }
        .metric-value.negative { color: #ef4444; }

        /* === Original Gauge Specific Styles === */
        #gaugeWrapper {
            position: relative;
            width: 400px;
            height: 400px;
            margin: 0 auto;
        }

        #customGauge {
            width: 100%;
            height: 100%;
        }

        #ratingCircle {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 140px;
            height: 140px;
        }

        #ratingCircle circle {
            transition: fill 0.5s ease;
            fill: var(--text-muted-color);
        }

        #arrowContainer {
            position: absolute;
            left: 200px;
            top: 200px;
            width: 0;
            height: 0;
            transform: rotate(-135deg) translate(0, -150px);
        }

        #arrowContainer svg {
            width: 48px;
            height: 48px;
            pointer-events: none;
            transform: translate(-24px, -1.5px);
        }

        .arrow {
            stroke-linejoin: round;
            fill: var(--text-color);
            transition: fill 0.3s ease;
        }

        #ratingText {
            transition: fill 0.3s ease;
            fill: white;
        }

        #ratingDescriptionText {
            transition: color 0.5s ease;
            color: var(--text-muted-color);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .premium-dashboard {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .rating-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .dcf-hero-section {
                padding: 40px 20px;
                margin: -25px -25px 30px -25px;
            }

            .dcf-hero-title {
                font-size: 2.5rem;
            }

            .dcf-hero-subtitle {
                font-size: 1.1rem;
            }

            .premium-metrics-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                margin: 30px 0;
            }

            .premium-metric-card {
                padding: 24px;
            }

            .metric-value {
                font-size: 2rem;
            }

            .premium-result-box {
                padding: 30px 20px;
                margin: 30px 0;
            }

            .result-title {
                font-size: 2rem;
            }

            .dashboard-section {
                padding: 24px;
            }

            .premium-chart-container {
                height: 300px;
            }

            .gauge-wrapper {
                width: 250px;
                height: 250px;
            }

            .gauge-value {
                font-size: 2.5rem;
            }
        }

        @media (max-width: 480px) {
            .dcf-hero-title {
                font-size: 2rem;
            }

            .premium-metric-card {
                padding: 20px;
            }

            .metric-value {
                font-size: 1.8rem;
            }

            .premium-result-box {
                padding: 24px 16px;
            }

            .result-title {
                font-size: 1.8rem;
            }

            .dashboard-section {
                padding: 20px;
            }

            .premium-chart-container {
                height: 250px;
            }

            .gauge-wrapper {
                width: 200px;
                height: 200px;
            }

            .gauge-value {
                font-size: 2rem;
            }
        }

        /* Scroll Animations */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0);
        }

        /* Loading States */
        .loading-shimmer {
            background: linear-gradient(90deg,
                var(--premium-glass) 25%,
                rgba(255, 255, 255, 0.2) 50%,
                var(--premium-glass) 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        .d3-tooltip .logo-container {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }
        .d3-tooltip .stock-logo {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            border-radius: 4px;
            background-color: rgba(128, 128, 128, 0.1);
            object-fit: contain;
        }
        .d3-tooltip .ticker {
            font-weight: bold;
            color: var(--highlight-color);
        }
        .d3-tooltip .tooltip-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        .d3-tooltip .label {
            margin-right: 12px;
            color: var(--text-muted-color);
        }
        .d3-tooltip .value {
            font-weight: 600;
        }
        .d3-tooltip .price.above {
            color: var(--positive-color);
        }
        .d3-tooltip .price.below {
            color: var(--negative-color);
        }
        .chart-buttons { text-align: center; margin-bottom: 15px; }
        .chart-buttons .time-range-btn { /* Keep original button styling if desired */ margin: 0 5px; padding: 6px 12px; font-size: 0.85rem; border-radius: 15px; background: var(--nav-hover-color); color: var(--text-muted-color); border: 1px solid var(--border-color); box-shadow: none; cursor:pointer; transition: all 0.2s ease; }
        .chart-buttons .time-range-btn:hover { background: var(--highlight-color); color: white; border-color: var(--highlight-color); transform: translateY(-1px); box-shadow: 0 2px 5px rgba(var(--highlight-color-rgb), 0.2); }
        .chart-buttons .time-range-btn.active { background: var(--highlight-gradient); color: white; border-color: var(--highlight-darker); font-weight: 600; box-shadow: 0 3px 8px rgba(var(--highlight-color-rgb), 0.3); }

         /* === START: Gauge Specific Styles - EXACTLY as provided === */
         #gaugeWrapper { position: relative; width: 400px; height: 400px; margin: 0 auto; }
         #customGauge { width: 100%; height: 100%; }
         #ratingCircle { position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); width: 100px; height: 100px; }
         #arrowContainer { position: absolute; left: 200px; top: 200px; width: 0; height: 0; }
         #arrowContainer svg { width: 48px; height: 48px; pointer-events: none; transform: translate(-24px, -1.5px); }
         .arrow { stroke-linejoin: round; fill: var(--text-color); transition: fill 0.3s ease; }
         #ratingDescriptionText { position: absolute; left: 50%; transform: translateX(-50%); top: 320px; font-size: 24px; font-weight: bold; width: 100%; text-align: center; filter: url(#text-glow); transition: filter 0.3s ease, color 0.3s ease; }
         .gauge-info { display: none; text-align: center; font-family: 'Inter', sans-serif; color: var(--text-color); }
         .gauge-info span { font-weight: 600; color: var(--text-color); }
         /* === END: Gauge Specific Styles === */

        /* === COMPREHENSIVE CONTRAST FIX FOR DCF RESULT === */
        /* Fix all hardcoded colors for proper dark/light mode support */

        /* FORCE ALL TEXT TO USE THEME COLORS - OVERRIDE EVERYTHING */
        * {
            color: var(--text-color) !important;
        }

        /* Specific overrides for different text types */
        h1, h2, h3, h4, h5, h6,
        p, span, div, li, td, th,
        label, legend, caption,
        .text, .title, .subtitle,
        .content, .description,
        .value, .label, .metric,
        .result-title,
        .result-subtitle,
        .section-title,
        .analysis-title,
        .analysis-subtitle,
        .rating-item-title,
        .rating-item-description,
        .section-subtitle,
        .overall-rating-label,
        .gauge-label,
        .master-score-label,
        .master-score-scale,
        .score-indicator,
        .verdict-text h3,
        .verdict-text p,
        .radar-header h3,
        .radar-header p,
        .metric-item span,
        .metric-item strong,
        .card-title h4,
        .analysis-text,
        .summary-header h3,
        .metric-label,
        .metric-value,
        .chart-axis,
        .loading-indicator,
        .gauge-info,
        .gauge-info span {
            color: var(--text-color) !important;
        }

        /* Muted text elements */
        .text-muted, .muted, .secondary,
        .subtitle, .description,
        small, .small,
        .text-secondary {
            color: var(--text-muted-color) !important;
        }

        /* Keep white text on colored backgrounds */
        .metric-change.positive,
        .metric-change.negative,
        .ticker-badge,
        .category-icon,
        .verdict-icon,
        .card-icon,
        .d3-slice-label,
        #ratingText,
        .btn-primary,
        .btn-success,
        .btn-danger,
        .btn-warning,
        .btn-info {
            color: #ffffff !important;
            fill: #ffffff !important;
        }

        /* Fix tooltip colors */
        .premium-tooltip {
            background: var(--card-bg-color) !important;
            color: var(--text-color) !important;
            border: 1px solid var(--border-color);
        }

        .premium-tooltip::after {
            border-top-color: var(--card-bg-color) !important;
        }

        /* Fix chart elements */
        .chart-dot {
            fill: var(--text-color) !important;
        }

        /* Fix SVG text elements */
        svg text:not(.d3-slice-label):not(#ratingText):not(.keep-white) {
            fill: var(--text-color) !important;
        }

        /* Fix hover borders */
        .premium-metric-card:hover,
        .dashboard-section:hover,
        .rating-item:hover,
        .rating-category:hover,
        .analysis-card:hover {
            border-color: var(--border-color) !important;
        }

        /* Fix chart axis colors */
        .chart-axis .domain {
            stroke: var(--border-color) !important;
        }

        /* Fix error messages */
        .error-message {
            color: var(--negative-color) !important;
        }

        /* Fix hardcoded chart colors in JavaScript */
        .chart-line {
            stroke: var(--highlight-color) !important;
        }

        /* Remove theme-based hover circle styling to preserve semantic colors */
        /* .hover-circle colors are set in JavaScript with semantic values */

        /* === DCF SPECIFIC ULTRA-AGGRESSIVE CONTRAST FIX === */
        /* Force all text to be visible in dark mode */
        [data-theme="dark"] .dcf-container *,
        [data-theme="dark"] .premium-dashboard *,
        [data-theme="dark"] .analysis-framework *,
        [data-theme="dark"] .rating-container *,
        [data-theme="dark"] .chart-container * {
            color: var(--text-color) !important;
        }

        /* DCF hero section */
        [data-theme="dark"] .dcf-hero-title,
        [data-theme="dark"] .dcf-hero-subtitle {
            color: var(--text-color) !important;
        }

        /* Metric cards */
        [data-theme="dark"] .premium-metric-card *,
        [data-theme="dark"] .metric-card *,
        [data-theme="dark"] .dashboard-section * {
            color: var(--text-color) !important;
        }

        /* Rating and analysis sections */
        [data-theme="dark"] .rating-item *,
        [data-theme="dark"] .analysis-card *,
        [data-theme="dark"] .investment-summary * {
            color: var(--text-color) !important;
        }

        /* Chart and gauge elements */
        [data-theme="dark"] .gauge-value,
        [data-theme="dark"] .gauge-label,
        [data-theme="dark"] .chart-axis text,
        [data-theme="dark"] .rating-percentage {
            color: var(--text-color) !important;
            fill: var(--text-color) !important;
        }

        /* Keep white text on colored elements */
        [data-theme="dark"] .metric-change.positive,
        [data-theme="dark"] .metric-change.negative,
        [data-theme="dark"] .ticker-badge,
        [data-theme="dark"] .category-icon,
        [data-theme="dark"] .verdict-icon,
        [data-theme="dark"] .card-icon,
        [data-theme="dark"] #ratingText {
            color: #ffffff !important;
            fill: #ffffff !important;
        }

        /* === END COMPREHENSIVE CONTRAST FIX === */

        /* Layout for Chart and Gauge */
        .ratings-container {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            padding: 10px;
            margin-bottom: 20px;
        }
        
        /* New rating analysis grid layout */
        .ratings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
            align-items: center;
        }
        
        .ratings-details {
            padding: 15px;
            background: var(--card-hover-bg);
            border-radius: 8px;
        }
        
        .ratings-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .ratings-table th,
        .ratings-table td {
            padding: 8px 10px;
            border-bottom: 1px solid var(--border-color);
            text-align: left;
        }
        
        .ratings-table th {
            font-weight: 600;
            color: var(--text-muted-color);
        }
        
        .ratings-table tr.overall-row {
            font-weight: 700;
            background-color: rgba(var(--highlight-color-rgb), 0.05);
        }
        
        /* Make radar chart more mobile responsive */
        @media (max-width: 768px) {
            .ratings-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
{% endblock head_extra %}

{% block content %}
    <div class="dcf-result-container">
        <!-- Floating Particles Background -->
        <div class="floating-particles" id="particles"></div>

        <!-- Premium Hero Section -->
        <div class="dcf-hero-section">
            <div class="result-header">
                <h1 class="dcf-hero-title">
                    DCF Analysis for <span class="ticker-badge">{{ ticker.split('.')[0] }}</span>
                </h1>
                <p class="dcf-hero-subtitle">
                    Comprehensive Discounted Cash Flow Valuation & Investment Analysis
                </p>
            </div>
        </div>

        <main>
            <!-- Premium Metrics Grid -->
            <div class="premium-metrics-grid">
                <div class="premium-metric-card scroll-reveal">
                    <div class="metric-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="metric-label">Intrinsic Value</div>
                    <div class="metric-value" id="intrinsicValueDisplay">
                        ${{ dcf_value_per_share }}
                    </div>
                    <div class="metric-change {{ 'positive' if dcf_value_per_share > current_price else 'negative' }}">
                        {{ 'Undervalued' if dcf_value_per_share > current_price else 'Overvalued' }}
                    </div>
                </div>

                <div class="premium-metric-card scroll-reveal">
                    <div class="metric-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="metric-label">Margin of Safety</div>
                    <div class="metric-value" id="marginValueDisplay">
                        ${{ dcf_value_per_share_margin_of_safety }}
                    </div>
                    <div class="metric-change {{ 'positive' if dcf_value_per_share_margin_of_safety > current_price else 'negative' }}">
                        Conservative Value
                    </div>
                </div>

                <div class="premium-metric-card scroll-reveal">
                    <div class="metric-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="metric-label">Current Price</div>
                    <div class="metric-value" id="currentPriceDisplay">
                        ${{ current_price }}
                    </div>
                    <div class="metric-change">
                        Market Value
                    </div>
                </div>

                <div class="premium-metric-card scroll-reveal">
                    <div class="metric-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="metric-label">Potential Return (Fair Value)</div>
                    <div class="metric-value" id="potentialReturnDisplay">
                        <!-- Will be calculated by JavaScript -->
                    </div>
                    <div class="metric-change" id="potentialReturnChange">
                        <!-- Will be set by JavaScript -->
                    </div>
                </div>

                <div class="premium-metric-card scroll-reveal">
                    <div class="metric-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="metric-label">Conservative Return (w/ MoS)</div>
                    <div class="metric-value" id="conservativeReturnDisplay">
                        <!-- Will be calculated by JavaScript -->
                    </div>
                    <div class="metric-change" id="conservativeReturnChange">
                        <!-- Will be set by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Premium Result Box -->
            <div class="premium-result-box scroll-reveal">
                <div class="result-header">
                    <h2 class="result-title">Investment Summary</h2>
                    <p class="result-subtitle">Based on comprehensive DCF analysis and market conditions</p>
                </div>

                {% if fcf_warning %}
                    <div class="metric-change negative" style="margin: 20px 0; padding: 16px; border-radius: 12px; background: rgba(255, 107, 107, 0.1);">
                        <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                        {{ fcf_warning }}
                    </div>
                {% endif %}
            </div>

            <!-- Premium Dashboard -->
            <div class="premium-dashboard scroll-reveal">
                <!-- Chart Section -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-chart-area"></i>
                        </div>
                        <h3 class="section-title">Price History & Analysis</h3>
                    </div>
                    <div class="premium-chart-container" id="stockChart">
                        <!-- Chart will be rendered here -->
                    </div>
                </div>

                <!-- Original Investment Rating Gauge -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h3 class="section-title">Investment Rating</h3>
                    </div>
                    <div class="gauge-section">
                        {# Keep the exact gauge structure from the original file - DO NOT MODIFY this inner div structure #}
                        <div style="position: relative; width: 400px; height: 420px; margin: 20px auto;">
                           <div id="gaugeWrapper" style="position: relative; width: 400px; height: 400px;">
                               <object id="customGauge" type="image/svg+xml"
                                       data="{{ url_for('static', filename='icons/half_circle.svg') }}"
                                       style="width: 100%; height: 100%;"></object>
                               <svg id="ratingCircle" viewBox="0 0 200 200" style="position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); width: 140px; height: 140px;">
                                   <defs>
                                       <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                                           <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
                                           <feMerge>
                                               <feMergeNode in="coloredBlur"/>
                                               <feMergeNode in="SourceGraphic"/>
                                           </feMerge>
                                       </filter>
                                       <filter id="text-glow" x="-50%" y="-50%" width="200%" height="200%">
                                           <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                                           <feMerge>
                                               <feMergeNode in="coloredBlur"/>
                                               <feMergeNode in="SourceGraphic"/>
                                           </feMerge>
                                       </filter>
                                   </defs>
                                   <circle cx="100" cy="100" r="60" fill="currentColor" filter="url(#glow)"/>
                                   <text id="ratingText" x="100" y="100" text-anchor="middle" dominant-baseline="central" font-size="48" font-weight="bold" fill="white" filter="url(#text-glow)">0.00</text>
                               </svg>
                               <div id="arrowContainer" style="position: absolute; left: 200px; top: 200px; width: 0; height: 0;">
                                   <svg id="arrowObject" viewBox="0 0 32 32" style="width: 48px; height: 48px; pointer-events: none; transform: translate(-24px, -1.5px)">
                                       <path class="arrow" d="M16 1L2 28L16 22L30 28L16 1Z" stroke-linejoin="round" fill="currentColor"/>
                                   </svg>
                               </div>
                           </div>
                           <div id="ratingDescriptionText" style="position: absolute; left: 50%; transform: translateX(-50%); top: 320px; font-size: 24px; font-weight: bold; width: 100%; text-align: center; filter: url(#text-glow); transition: filter 0.3s ease, color 0.3s ease;">
                               <!-- Rating description text will be inserted here by script.js -->
                               Loading Rating...
                           </div>
                       </div>
                       <div class="gauge-info" style="display: none;">
                           <div><span id="rating">{{ rating|default('0.00') }}</span>/5.00</div>
                           <div><span id="difference">{{ percentage_difference|default('0.00') }}</span>%</div>
                       </div>
                        {# --- End of Preserved Gauge Structure --- #}
                    </div>
                </div>
            </div>

            <!-- Premium Investment Quality Analysis -->
            <div class="premium-investment-analysis scroll-reveal">
                <div class="analysis-header">
                    <div class="header-content">
                        <div class="header-icon">
                            <div class="icon-wrapper">
                                <i class="fas fa-chart-radar"></i>
                                <div class="icon-glow"></div>
                            </div>
                        </div>
                        <div class="header-text">
                            <h2 class="analysis-title">Investment Quality Analysis</h2>
                            <p class="analysis-subtitle">Comprehensive multi-dimensional evaluation framework</p>
                        </div>
                    </div>
                    <div class="header-decoration">
                        <div class="decoration-line"></div>
                        <div class="decoration-dots">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                </div>

                <!-- Master Score Dashboard -->
                <div class="master-score-dashboard">
                    <div class="score-visualization">
                        <div class="master-score-circle">
                            <svg class="master-score-svg" viewBox="0 0 200 200">
                                <defs>
                                    <linearGradient id="masterScoreGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                        <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
                                    </linearGradient>
                                    <filter id="masterGlow" x="-50%" y="-50%" width="200%" height="200%">
                                        <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
                                        <feMerge>
                                            <feMergeNode in="coloredBlur"/>
                                            <feMergeNode in="SourceGraphic"/>
                                        </feMerge>
                                    </filter>
                                </defs>
                                <circle cx="100" cy="100" r="80" class="master-score-bg"></circle>
                                <circle cx="100" cy="100" r="80" class="master-score-progress"
                                        data-rating="{{ overall_rating|default(6.5, true) }}" filter="url(#masterGlow)"></circle>
                            </svg>
                            <div class="master-score-content">
                                <div class="master-score-value">{{ '%.1f'|format(overall_rating|default(6.5, true)) }}</div>
                                <div class="master-score-label">Investment Score</div>
                                <div class="master-score-scale">/10</div>
                            </div>
                        </div>
                        <div class="score-indicators">
                            <div class="score-indicator">
                                <div class="indicator-dot excellent"></div>
                                <span>Excellent (8.0+)</span>
                            </div>
                            <div class="score-indicator">
                                <div class="indicator-dot good"></div>
                                <span>Good (7.0+)</span>
                            </div>
                            <div class="score-indicator">
                                <div class="indicator-dot fair"></div>
                                <span>Fair (6.0+)</span>
                            </div>
                            <div class="score-indicator">
                                <div class="indicator-dot poor"></div>
                                <span>Below Average</span>
                            </div>
                        </div>
                    </div>

                    <div class="investment-verdict">
                        {% set overall_score = overall_rating|default(6.5, true) %}
                        <div class="verdict-badge
                            {% if overall_score >= 8.0 %}excellent
                            {% elif overall_score >= 7.0 %}good
                            {% elif overall_score >= 6.0 %}fair
                            {% elif overall_score >= 5.0 %}below-average
                            {% else %}poor{% endif %}">
                            <div class="verdict-icon">
                                {% if overall_score >= 8.0 %}
                                    <i class="fas fa-trophy"></i>
                                {% elif overall_score >= 7.0 %}
                                    <i class="fas fa-thumbs-up"></i>
                                {% elif overall_score >= 6.0 %}
                                    <i class="fas fa-balance-scale"></i>
                                {% elif overall_score >= 5.0 %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% else %}
                                    <i class="fas fa-times-circle"></i>
                                {% endif %}
                            </div>
                            <div class="verdict-text">
                                <h3>
                                    {% if overall_score >= 8.0 %}Exceptional Investment
                                    {% elif overall_score >= 7.0 %}Strong Investment
                                    {% elif overall_score >= 6.0 %}Moderate Investment
                                    {% elif overall_score >= 5.0 %}Risky Investment
                                    {% else %}Poor Investment{% endif %}
                                </h3>
                                <p>
                                    {% if overall_score >= 8.0 %}
                                        Outstanding quality across all dimensions with exceptional fundamentals and attractive valuation.
                                    {% elif overall_score >= 7.0 %}
                                        Solid fundamentals with good growth prospects and reasonable valuation metrics.
                                    {% elif overall_score >= 6.0 %}
                                        Mixed signals with some strengths but notable areas requiring careful monitoring.
                                    {% elif overall_score >= 5.0 %}
                                        Significant concerns outweigh potential benefits. High risk investment.
                                    {% else %}
                                        Multiple red flags across key metrics. Avoid or consider only with extreme caution.
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Premium Analysis Framework -->
                <div class="premium-analysis-framework">
                    <!-- Radar Visualization Section -->
                    <div class="radar-visualization-section">
                        <div class="radar-container">
                            <div class="radar-header">
                                <h3>Multi-Dimensional Analysis</h3>
                                <p>Interactive radar chart showing performance across key investment criteria</p>
                            </div>
                            <div class="radar-chart-wrapper">
                                <div class="radar-chart-container" id="premiumRadarChart">
                                    <!-- Enhanced radar chart will be rendered here -->
                                </div>
                                <div class="radar-metrics">
                                    <div class="metric-item">
                                        <div class="metric-dot business"></div>
                                        <span>Business Quality</span>
                                        <strong>{{ '%.1f'|format(business_quality|default(6, true)) }}</strong>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-dot financial"></div>
                                        <span>Financial Strength</span>
                                        <strong>{{ '%.1f'|format(financial_strength|default(7, true)) }}</strong>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-dot valuation"></div>
                                        <span>Valuation</span>
                                        <strong>{{ '%.1f'|format(valuation|default(6.1, true)) }}</strong>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-dot management"></div>
                                        <span>Management</span>
                                        <strong>{{ '%.1f'|format(management_quality|default(6.5, true)) }}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Analysis Cards -->
                    <div class="analysis-cards-grid">
                        <!-- Business Quality Card -->
                        <div class="analysis-card business-quality-card">
                            <div class="card-header">
                                <div class="card-icon business-quality">
                                    <i class="fas fa-building"></i>
                                    <div class="icon-pulse"></div>
                                </div>
                                <div class="card-title">
                                    <h4>Business Quality</h4>
                                    <div class="card-score">{{ '%.1f'|format(business_quality|default(6, true)) }}<span>/10</span></div>
                                </div>
                            </div>
                            <div class="card-progress">
                                <div class="progress-track">
                                    <div class="progress-fill business-quality-fill"
                                         data-width="{{ (business_quality|default(6, true) / 10 * 100)|round }}%"></div>
                                    <div class="progress-glow"></div>
                                </div>
                                <div class="progress-label">{{ (business_quality|default(6, true) / 10 * 100)|round }}% Score</div>
                            </div>
                            <div class="card-analysis">
                                {% set bq_score = business_quality|default(6, true) %}
                                <div class="analysis-badge
                                    {% if bq_score >= 8.0 %}excellent
                                    {% elif bq_score >= 7.0 %}good
                                    {% elif bq_score >= 6.0 %}fair
                                    {% else %}poor{% endif %}">
                                    {% if bq_score >= 8.0 %}Exceptional
                                    {% elif bq_score >= 7.0 %}Strong
                                    {% elif bq_score >= 6.0 %}Moderate
                                    {% elif bq_score >= 5.0 %}Weak
                                    {% else %}Poor{% endif %}
                                </div>
                                <p class="analysis-text">
                                    {% if bq_score >= 8.0 %}
                                        Strong competitive moat with dominant market position and sustainable advantages.
                                    {% elif bq_score >= 7.0 %}
                                        Good competitive position with solid market share and defendable business model.
                                    {% elif bq_score >= 6.0 %}
                                        Average competitive position with some differentiation but facing pressures.
                                    {% elif bq_score >= 5.0 %}
                                        Limited competitive advantages and struggling to maintain position.
                                    {% else %}
                                        Significant competitive disadvantages and declining market position.
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <!-- Financial Strength Card -->
                        <div class="analysis-card financial-strength-card">
                            <div class="card-header">
                                <div class="card-icon financial-strength">
                                    <i class="fas fa-chart-line"></i>
                                    <div class="icon-pulse"></div>
                                </div>
                                <div class="card-title">
                                    <h4>Financial Strength</h4>
                                    <div class="card-score">{{ '%.1f'|format(financial_strength|default(7, true)) }}<span>/10</span></div>
                                </div>
                            </div>
                            <div class="card-progress">
                                <div class="progress-track">
                                    <div class="progress-fill financial-strength-fill"
                                         data-width="{{ (financial_strength|default(7, true) / 10 * 100)|round }}%"></div>
                                    <div class="progress-glow"></div>
                                </div>
                                <div class="progress-label">{{ (financial_strength|default(7, true) / 10 * 100)|round }}% Score</div>
                            </div>
                            <div class="card-analysis">
                                {% set fs_score = financial_strength|default(7, true) %}
                                <div class="analysis-badge
                                    {% if fs_score >= 8.0 %}excellent
                                    {% elif fs_score >= 7.0 %}good
                                    {% elif fs_score >= 6.0 %}fair
                                    {% else %}poor{% endif %}">
                                    {% if fs_score >= 8.0 %}Excellent
                                    {% elif fs_score >= 7.0 %}Good
                                    {% elif fs_score >= 6.0 %}Fair
                                    {% elif fs_score >= 5.0 %}Weak
                                    {% else %}Poor{% endif %}
                                </div>
                                <p class="analysis-text">
                                    {% if fs_score >= 8.0 %}
                                        Outstanding financial health with strong balance sheet and high profitability.
                                    {% elif fs_score >= 7.0 %}
                                        Solid financial metrics with healthy cash flow and manageable debt levels.
                                    {% elif fs_score >= 6.0 %}
                                        Adequate financial position but some areas requiring monitoring.
                                    {% elif fs_score >= 5.0 %}
                                        Financial stress indicators with elevated debt or declining profitability.
                                    {% else %}
                                        Significant financial distress with high risk of liquidity issues.
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <!-- Valuation Card -->
                        <div class="analysis-card valuation-card">
                            <div class="card-header">
                                <div class="card-icon valuation">
                                    <i class="fas fa-dollar-sign"></i>
                                    <div class="icon-pulse"></div>
                                </div>
                                <div class="card-title">
                                    <h4>Valuation</h4>
                                    <div class="card-score">{{ '%.1f'|format(valuation|default(6.1, true)) }}<span>/10</span></div>
                                </div>
                            </div>
                            <div class="card-progress">
                                <div class="progress-track">
                                    <div class="progress-fill valuation-fill"
                                         data-width="{{ (valuation|default(6.1, true) / 10 * 100)|round }}%"></div>
                                    <div class="progress-glow"></div>
                                </div>
                                <div class="progress-label">{{ (valuation|default(6.1, true) / 10 * 100)|round }}% Score</div>
                            </div>
                            <div class="card-analysis">
                                {% set val_score = valuation|default(6.1, true) %}
                                <div class="analysis-badge
                                    {% if val_score >= 8.0 %}excellent
                                    {% elif val_score >= 7.0 %}good
                                    {% elif val_score >= 6.0 %}fair
                                    {% else %}poor{% endif %}">
                                    {% if val_score >= 8.0 %}Excellent Value
                                    {% elif val_score >= 7.0 %}Good Value
                                    {% elif val_score >= 6.0 %}Fair Value
                                    {% elif val_score >= 5.0 %}Overvalued
                                    {% else %}Significantly Overvalued{% endif %}
                                </div>
                                <p class="analysis-text">
                                    {% if val_score >= 8.0 %}
                                        Significantly undervalued with high margin of safety and attractive entry point.
                                    {% elif val_score >= 7.0 %}
                                        Reasonably priced with moderate upside potential and acceptable risk-reward ratio.
                                    {% elif val_score >= 6.0 %}
                                        Trading near intrinsic value with limited upside but reasonable downside protection.
                                    {% elif val_score >= 5.0 %}
                                        Trading above fair value with limited upside and elevated downside risk.
                                    {% else %}
                                        Substantial premium to intrinsic value with high downside risk.
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <!-- Management Quality Card -->
                        <div class="analysis-card management-quality-card">
                            <div class="card-header">
                                <div class="card-icon management-quality">
                                    <i class="fas fa-users"></i>
                                    <div class="icon-pulse"></div>
                                </div>
                                <div class="card-title">
                                    <h4>Management Quality</h4>
                                    <div class="card-score">{{ '%.1f'|format(management_quality|default(6.5, true)) }}<span>/10</span></div>
                                </div>
                            </div>
                            <div class="card-progress">
                                <div class="progress-track">
                                    <div class="progress-fill management-quality-fill"
                                         data-width="{{ (management_quality|default(6.5, true) / 10 * 100)|round }}%"></div>
                                    <div class="progress-glow"></div>
                                </div>
                                <div class="progress-label">{{ (management_quality|default(6.5, true) / 10 * 100)|round }}% Score</div>
                            </div>
                            <div class="card-analysis">
                                {% set mq_score = management_quality|default(6.5, true) %}
                                <div class="analysis-badge
                                    {% if mq_score >= 8.0 %}excellent
                                    {% elif mq_score >= 7.0 %}good
                                    {% elif mq_score >= 6.0 %}fair
                                    {% else %}poor{% endif %}">
                                    {% if mq_score >= 8.0 %}Exceptional
                                    {% elif mq_score >= 7.0 %}Strong
                                    {% elif mq_score >= 6.0 %}Adequate
                                    {% elif mq_score >= 5.0 %}Concerning
                                    {% else %}Poor{% endif %}
                                </div>
                                <p class="analysis-text">
                                    {% if mq_score >= 8.0 %}
                                        Outstanding leadership with proven track record of value creation and strategic execution.
                                    {% elif mq_score >= 7.0 %}
                                        Competent management team with good capital allocation and operational efficiency.
                                    {% elif mq_score >= 6.0 %}
                                        Reasonable management performance but some areas for improvement in execution.
                                    {% elif mq_score >= 5.0 %}
                                        Management decisions raising questions about strategic direction and execution capability.
                                    {% else %}
                                        Significant management issues with poor capital allocation and strategic missteps.
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Investment Summary Panel -->
                    <div class="investment-summary-panel">
                        <div class="summary-header">
                            <h3>Investment Summary</h3>
                            <div class="summary-indicator
                                {% set overall_score = overall_rating|default(6.5, true) %}
                                {% if overall_score >= 7.0 %}positive
                                {% elif overall_score >= 6.0 %}neutral
                                {% else %}negative{% endif %}">
                                {% if overall_score >= 7.0 %}
                                    <i class="fas fa-arrow-up"></i> Recommended
                                {% elif overall_score >= 6.0 %}
                                    <i class="fas fa-minus"></i> Neutral
                                {% else %}
                                    <i class="fas fa-arrow-down"></i> Not Recommended
                                {% endif %}
                            </div>
                        </div>
                        <div class="summary-content">
                            <div class="key-metrics">
                                <div class="metric">
                                    <span class="metric-label">Overall Score</span>
                                    <span class="metric-value">{{ '%.1f'|format(overall_rating|default(6.5, true)) }}/10</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Upside Potential</span>
                                    <span class="metric-value {{ 'positive' if percentage_difference > 0 else 'negative' }}">
                                        {{ percentage_difference|default('N/A') }}%
                                    </span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Risk Level</span>
                                    <span class="metric-value">
                                        {% if overall_score >= 7.0 %}Low-Medium
                                        {% elif overall_score >= 6.0 %}Medium
                                        {% else %}High{% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Legacy content for compatibility -->
            <div style="display: none;">
                <div class="result-box card">
                    <h2>DCF Result: <span class="ticker-symbol">{{ ticker.split('.')[0] }}</span></h2>

            {# Group the core calculated values #}
            <div class="result-values">
                <div class="result-item">
                    <span class="result-label">Intrinsic Value / Share:</span>
                    <span id="dcfValuePerShare" class="value {{ 'green' if dcf_value_per_share > current_price else 'red' }}">
                        ${{ dcf_value_per_share }}
                    </span>
                </div>
                <div class="result-item">
                    <span class="result-label">Value (with Margin of Safety):</span>
                    <span id="dcfValueWithMargin" class="value {{ 'green' if dcf_value_per_share_margin_of_safety > current_price else 'red' }}">
                        ${{ dcf_value_per_share_margin_of_safety }}
                    </span>
                </div>
                <div class="result-item current-price-item"> {# Specific class for current price #}
                    <span class="result-label">Current Market Price:</span>
                    <span id="currentPrice" class="value current-price"> {# Keep current-price class if needed by JS #}
                        ${{ current_price }}
                    </span>
                </div>
            </div>



            {# Percentage Difference Section #}
            <div id="percentage-difference-tab" class="result-summary">
                <span class="result-label">Implied Upside / Downside:</span>
                <span class="percentage {{ 'red' if percentage_difference < 0 else 'green' }}">
                    {{ percentage_difference | default('N/A') }}%
                </span>
            </div>

            {# Keep FCF Warning if present #}
            {% if fcf_warning %}
            <p class="fcf-warning"><i class="fas fa-exclamation-triangle"></i> {{ fcf_warning }}</p>
            {% endif %}
        </div>



            <!-- Right Column: Chart (moved from left) -->
            <div class="chart-section" data-ticker="{{ ticker }}"> <!-- Keep data-ticker -->
                 {# Add card class and icon from previous attempt #}
                 <div class="card">
                     {# --- MODIFIED H2 --- #}
                     <h2>
                         {# Add the logo image - uses the full ticker for the API call #}
                         <img class="company-logo"
                              src="{{ url_for('get_logo_endpoint', ticker_with_exchange=ticker) }}"
                              alt="{{ ticker.split('.')[0] }} Logo"
                              {# Add onerror handler to hide if logo fails #}
                              onerror="this.style.display='none'; this.onerror=null;">
                         {# Keep the icon #}
                         <i class="fas fa-chart-line"></i>
                         {# Display only the symbol part of the ticker #}
                         Stock Price History for {{ ticker.split('.')[0] }}
                     </h2>
                     {# --- END MODIFIED H2 --- #}

                     <div class="chart-buttons">
                        <button class="time-range-btn" data-range="1W">1W</button>
                        <button class="time-range-btn" data-range="1M">1M</button>
                        <button class="time-range-btn" data-range="6M">6M</button>
                        <button class="time-range-btn active" data-range="1Y">1Y</button>
                        <button class="time-range-btn" data-range="5Y">5Y</button>
                        <button class="time-range-btn" data-range="Max">Max</button>
                     </div>
                     <!-- D3 Stock Chart -->
                     <div id="dcf-stock-chart" class="chart">
                        <!-- Chart will be rendered here -->
                        <div class="loading-indicator" style="text-align: center; padding-top: 150px; color: var(--text-muted-color);">Loading chart data...</div>
                        <div class="error-message" style="display: none; color: var(--negative-color); text-align: center; padding-top: 150px;"></div>
                     </div>
                     
                     <!-- Custom tooltip for hover effects (positioned outside chart for better z-index handling) -->
                     <div class="d3-tooltip">
                         <div class="logo-container">
                             <img class="stock-logo" src="" alt="">
                             <span class="ticker"></span>
                         </div>
                         <div class="tooltip-row">
                             <span class="label">Date:</span>
                             <span class="value date"></span>
                         </div>
                         <div class="tooltip-row">
                             <span class="label">Price:</span>
                             <span class="value price"></span>
                         </div>
                     </div>
                  </div>
             </div>
        </div>

         {# Keep original Back button #}
         <div style="text-align: center; margin-top: 40px;"> {# Add wrapper div #}
             <button onclick="goToDcfPage();" class="button"> {# Use url_for #}
                 <i class="fas fa-arrow-left"></i> Back to DCF Calculator
             </button>
         </div>

    </main> {# End of original <main> tag if it existed #}

    {# Remove the <footer> element that was here #}
    {# Remove the <div style="height: 100px;"> padding element #}

    {# --- END: Original Body Content --- #}
{% endblock content %}

{% block scripts_extra %}
    <script>
        // === PREMIUM DCF RESULT PAGE JAVASCRIPT ===

        // GSAP Registration
        gsap.registerPlugin(ScrollTrigger);

        // Global variables
        let particleSystem = null;
        let chartData = null;
        let gaugeAnimation = null;

        // Initialize premium features with performance optimization
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling
            document.documentElement.style.scrollBehavior = 'smooth';

            // Initialize core features immediately
            requestAnimationFrame(() => {
                initializeParticleSystem();
                initializePremiumAnimations();
                initializeScrollAnimations();

                // Start animations immediately for better user experience
                setTimeout(() => {
                    calculateAndDisplayMetrics();
                    initializePremiumCharts();
                    initializeRatingBars();
                    setupInteractiveElements();
                }, 10);
            });

            // Enhanced scroll performance
            let ticking = false;
            function updateScrollAnimations() {
                // Update any scroll-dependent animations here
                ticking = false;
            }

            window.addEventListener('scroll', () => {
                if (!ticking) {
                    requestAnimationFrame(updateScrollAnimations);
                    ticking = true;
                }
            }, { passive: true });
        });

        // Particle System
        function initializeParticleSystem() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;

            for (let i = 0; i < 50; i++) {
                createParticle(particlesContainer);
            }
        }

        function createParticle(container) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 15 + 's';
            particle.style.animationDuration = (15 + Math.random() * 10) + 's';
            container.appendChild(particle);
        }

        // Premium Animations
        function initializePremiumAnimations() {
            // Animate metric cards with stagger
            gsap.fromTo('.premium-metric-card',
                {
                    opacity: 0,
                    y: 50,
                    scale: 0.9,
                    rotationX: 20
                },
                {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    rotationX: 0,
                    duration: 0.8,
                    stagger: 0.1,
                    ease: "power3.out",
                    delay: 0.5
                }
            );

            // Animate dashboard sections
            gsap.fromTo('.dashboard-section',
                {
                    opacity: 0,
                    x: -50,
                    rotationY: 10
                },
                {
                    opacity: 1,
                    x: 0,
                    rotationY: 0,
                    duration: 1,
                    stagger: 0.2,
                    ease: "power3.out",
                    delay: 1
                }
            );
        }

        // Scroll Animations
        function initializeScrollAnimations() {
            const scrollElements = document.querySelectorAll('.scroll-reveal');

            scrollElements.forEach(element => {
                gsap.fromTo(element,
                    {
                        opacity: 0,
                        y: 30,
                        scale: 0.95
                    },
                    {
                        opacity: 1,
                        y: 0,
                        scale: 1,
                        duration: 0.8,
                        ease: "power3.out",
                        scrollTrigger: {
                            trigger: element,
                            start: "top 80%",
                            end: "bottom 20%",
                            toggleActions: "play none none reverse"
                        }
                    }
                );
            });
        }

        // Calculate and Display Metrics
        function calculateAndDisplayMetrics() {
            const intrinsicValue = parseFloat("{{ dcf_value_per_share }}");
            const currentPrice = parseFloat("{{ current_price }}");
            const marginValue = parseFloat("{{ dcf_value_per_share_margin_of_safety }}");

            // Calculate both potential returns
            const potentialReturn = ((intrinsicValue - currentPrice) / currentPrice * 100);
            const conservativeReturn = ((marginValue - currentPrice) / currentPrice * 100);

            const potentialReturnDisplay = document.getElementById('potentialReturnDisplay');
            const potentialReturnChange = document.getElementById('potentialReturnChange');
            const conservativeReturnDisplay = document.getElementById('conservativeReturnDisplay');
            const conservativeReturnChange = document.getElementById('conservativeReturnChange');

            // Initialize all displays to 0 before animation
            const intrinsicValueDisplay = document.getElementById('intrinsicValueDisplay');
            const marginValueDisplay = document.getElementById('marginValueDisplay');
            const currentPriceDisplay = document.getElementById('currentPriceDisplay');

            if (intrinsicValueDisplay) intrinsicValueDisplay.textContent = '$0.00';
            if (marginValueDisplay) marginValueDisplay.textContent = '$0.00';
            if (currentPriceDisplay) currentPriceDisplay.textContent = '$0.00';
            if (potentialReturnDisplay) potentialReturnDisplay.textContent = '0.0%';
            if (conservativeReturnDisplay) conservativeReturnDisplay.textContent = '0.0%';

            // Set up potential return (fair value)
            if (potentialReturnDisplay && potentialReturnChange) {
                potentialReturnChange.textContent = potentialReturn > 0 ? 'Upside Potential' : 'Downside Risk';
                potentialReturnChange.className = 'metric-change ' + (potentialReturn > 0 ? 'positive' : 'negative');
            }

            // Set up conservative return (margin of safety)
            if (conservativeReturnDisplay && conservativeReturnChange) {
                conservativeReturnChange.textContent = conservativeReturn > 0 ? 'Conservative Upside' : 'Conservative Risk';
                conservativeReturnChange.className = 'metric-change ' + (conservativeReturn > 0 ? 'positive' : 'negative');
            }

            // Animate the numbers counting up immediately
            animateCountUp('intrinsicValueDisplay', 0, intrinsicValue, '$');
            animateCountUp('marginValueDisplay', 0, marginValue, '$');
            animateCountUp('currentPriceDisplay', 0, currentPrice, '$');
            animateCountUp('potentialReturnDisplay', 0, potentialReturn, '', '%');
            animateCountUp('conservativeReturnDisplay', 0, conservativeReturn, '', '%');
        }

        // Animate count up effect
        function animateCountUp(elementId, start, end, prefix = '', suffix = '') {
            const element = document.getElementById(elementId);
            if (!element) return;

            gsap.to({ value: start }, {
                value: end,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    const currentValue = this.targets()[0].value;
                    // For percentage values, show 1 decimal place, for currency show 2
                    const decimals = suffix === '%' ? 1 : 2;
                    const formattedValue = currentValue.toFixed(decimals);
                    element.textContent = prefix + formattedValue + suffix;
                },
                delay: 0.1  // Small delay to ensure DOM is ready, but start almost immediately
            });
        }

        // Premium Charts Initialization
        function initializePremiumCharts() {
            const chartContainer = document.getElementById('stockChart');
            if (!chartContainer) return;

            // Create SVG with gradients
            const svg = d3.select(chartContainer)
                .append('svg')
                .attr('width', '100%')
                .attr('height', '100%')
                .attr('viewBox', '0 0 800 400');

            // Add gradient definitions
            const defs = svg.append('defs');

            const lineGradient = defs.append('linearGradient')
                .attr('id', 'lineGradient')
                .attr('gradientUnits', 'userSpaceOnUse')
                .attr('x1', 0).attr('y1', 0)
                .attr('x2', 800).attr('y2', 0);

            lineGradient.append('stop')
                .attr('offset', '0%')
                .attr('stop-color', '#667eea');

            lineGradient.append('stop')
                .attr('offset', '100%')
                .attr('stop-color', '#764ba2');

            const areaGradient = defs.append('linearGradient')
                .attr('id', 'areaGradient')
                .attr('gradientUnits', 'userSpaceOnUse')
                .attr('x1', 0).attr('y1', 0)
                .attr('x2', 0).attr('y2', 400);

            areaGradient.append('stop')
                .attr('offset', '0%')
                .attr('stop-color', '#667eea')
                .attr('stop-opacity', 0.3);

            areaGradient.append('stop')
                .attr('offset', '100%')
                .attr('stop-color', '#764ba2')
                .attr('stop-opacity', 0.1);

            // Load and render chart data
            loadChartData(svg);
        }

        // Original Half Gauge is handled by the existing gauge logic at the bottom of the file

        // Initialize Enhanced Rating System
        function initializeRatingBars() {
            // Animate master score circle
            const masterScoreCircle = document.querySelector('.master-score-progress');
            if (masterScoreCircle) {
                const rating = parseFloat(masterScoreCircle.getAttribute('data-rating'));
                const circumference = 502; // 2 * π * 80
                const offset = circumference - (rating / 10) * circumference;

                gsap.to(masterScoreCircle, {
                    strokeDashoffset: offset,
                    duration: 2.5,
                    ease: "power3.out",
                    delay: 1
                });
            }

            // Animate analysis card progress bars
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach((bar, index) => {
                const width = bar.getAttribute('data-width');

                gsap.fromTo(bar,
                    { width: '0%' },
                    {
                        width: width,
                        duration: 1.8,
                        ease: "power3.out",
                        delay: 2 + (index * 0.3)
                    }
                );
            });

            // Animate analysis cards
            const analysisCards = document.querySelectorAll('.analysis-card');
            gsap.fromTo(analysisCards,
                {
                    opacity: 0,
                    y: 40,
                    scale: 0.95
                },
                {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    duration: 0.8,
                    stagger: 0.2,
                    ease: "power3.out",
                    delay: 1.5
                }
            );

            // Initialize premium radar chart
            initializePremiumRadarChart();

            // Note: Original gauge is handled by the existing gauge logic at the bottom of the file
            // The original gauge animation and functionality is preserved in the main script section
        }

        // Premium Radar Chart
        function initializePremiumRadarChart() {
            const container = document.getElementById('premiumRadarChart');
            if (!container) return;

            const data = [
                { axis: 'Business Quality', value: parseFloat("{{ business_quality|default(6, true) }}") },
                { axis: 'Financial Strength', value: parseFloat("{{ financial_strength|default(7, true) }}") },
                { axis: 'Valuation', value: parseFloat("{{ valuation|default(6.1, true) }}") },
                { axis: 'Management Quality', value: parseFloat("{{ management_quality|default(6.5, true) }}") }
            ];

            const benchmarkData = [
                { axis: 'Business Quality', value: 7.0 },
                { axis: 'Financial Strength', value: 7.0 },
                { axis: 'Valuation', value: 7.0 },
                { axis: 'Management Quality', value: 7.0 }
            ];

            renderPremiumRadarChart(container, data, benchmarkData);
        }

        // Render Premium Radar Chart
        function renderPremiumRadarChart(container, data, benchmarkData) {
            const width = 400;
            const height = 400;
            const margin = 60;
            const radius = Math.min(width, height) / 2 - margin;

            const svg = d3.select(container)
                .append('svg')
                .attr('width', width)
                .attr('height', height);

            const g = svg.append('g')
                .attr('transform', `translate(${width/2}, ${height/2})`);

            // Create scales
            const angleScale = d3.scaleLinear()
                .domain([0, data.length])
                .range([0, 2 * Math.PI]);

            const radiusScale = d3.scaleLinear()
                .domain([0, 10])
                .range([0, radius]);

            // Draw grid circles
            const gridLevels = [2, 4, 6, 8, 10];
            gridLevels.forEach(level => {
                g.append('circle')
                    .attr('r', radiusScale(level))
                    .attr('fill', 'none')
                    .attr('stroke', 'rgba(255, 255, 255, 0.1)')
                    .attr('stroke-width', 1);

                // Add level labels
                g.append('text')
                    .attr('x', 5)
                    .attr('y', -radiusScale(level))
                    .attr('font-size', '10px')
                    .attr('fill', 'rgba(255, 255, 255, 0.6)')
                    .text(level);
            });

            // Draw axis lines and labels
            data.forEach((d, i) => {
                const angle = angleScale(i) - Math.PI / 2;
                const x = Math.cos(angle) * radius;
                const y = Math.sin(angle) * radius;

                // Axis line
                g.append('line')
                    .attr('x1', 0)
                    .attr('y1', 0)
                    .attr('x2', x)
                    .attr('y2', y)
                    .attr('stroke', 'rgba(255, 255, 255, 0.2)')
                    .attr('stroke-width', 1);

                // Axis label
                const labelX = Math.cos(angle) * (radius + 20);
                const labelY = Math.sin(angle) * (radius + 20);

                g.append('text')
                    .attr('x', labelX)
                    .attr('y', labelY)
                    .attr('text-anchor', 'middle')
                    .attr('dominant-baseline', 'middle')
                    .attr('font-size', '12px')
                    .attr('font-weight', '500')
                    .attr('fill', 'var(--text-color)')
                    .text(d.axis);
            });

            // Create line generator
            const line = d3.lineRadial()
                .angle((d, i) => angleScale(i))
                .radius(d => radiusScale(d.value))
                .curve(d3.curveLinearClosed);

            // Draw benchmark area (light)
            const benchmarkPath = g.append('path')
                .datum(benchmarkData)
                .attr('d', line)
                .attr('fill', 'rgba(102, 126, 234, 0.1)')
                .attr('stroke', 'rgba(102, 126, 234, 0.3)')
                .attr('stroke-width', 2)
                .attr('opacity', 0);

            // Draw current ratings area
            const currentPath = g.append('path')
                .datum(data)
                .attr('d', line)
                .attr('fill', 'rgba(102, 126, 234, 0.2)')
                .attr('stroke', '#667eea')
                .attr('stroke-width', 3)
                .attr('opacity', 0);

            // Add data points
            const points = g.selectAll('.radar-point')
                .data(data)
                .enter()
                .append('circle')
                .attr('class', 'radar-point')
                .attr('cx', (d, i) => {
                    const angle = angleScale(i) - Math.PI / 2;
                    return Math.cos(angle) * radiusScale(d.value);
                })
                .attr('cy', (d, i) => {
                    const angle = angleScale(i) - Math.PI / 2;
                    return Math.sin(angle) * radiusScale(d.value);
                })
                .attr('r', 0)
                .attr('fill', '#667eea')
                .attr('stroke', 'white')
                .attr('stroke-width', 2);

            // Animate the chart
            gsap.timeline({ delay: 3 })
                .to(benchmarkPath.node(), { opacity: 1, duration: 0.5 })
                .to(currentPath.node(), { opacity: 1, duration: 0.8 }, "-=0.3")
                .to(points.nodes(), {
                    attr: { r: 6 },
                    duration: 0.5,
                    stagger: 0.1
                }, "-=0.5");
        }

        // Setup Interactive Elements
        function setupInteractiveElements() {
            // Add hover effects to metric cards
            const metricCards = document.querySelectorAll('.premium-metric-card');
            metricCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    gsap.to(this, {
                        scale: 1.05,
                        rotationY: 5,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });

                card.addEventListener('mouseleave', function() {
                    gsap.to(this, {
                        scale: 1,
                        rotationY: 0,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });
            });

            // Add hover effects to dashboard sections
            const dashboardSections = document.querySelectorAll('.dashboard-section');
            dashboardSections.forEach(section => {
                section.addEventListener('mouseenter', function() {
                    gsap.to(this, {
                        y: -5,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });

                section.addEventListener('mouseleave', function() {
                    gsap.to(this, {
                        y: 0,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });
            });
        }

        // Load Chart Data
        function loadChartData(svg) {
            const ticker = "{{ ticker }}";
            const currentPrice = parseFloat("{{ current_price }}");
            const intrinsicValue = parseFloat("{{ dcf_value_per_share }}");

            // Generate sample data for demonstration
            const data = generateSampleChartData(currentPrice);
            renderPremiumChart(svg, data, currentPrice, intrinsicValue);
        }

        // Generate Sample Chart Data
        function generateSampleChartData(currentPrice) {
            const data = [];
            const startDate = new Date();
            startDate.setFullYear(startDate.getFullYear() - 1);

            let price = currentPrice * 0.8;
            const volatility = 0.02;
            const totalDays = 252;

            for (let i = 0; i < totalDays; i++) {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);

                // Apply random walk
                price *= (1 + (Math.random() - 0.5) * volatility);
                price = Math.max(price, currentPrice * 0.5);

                // Gradually converge to current price in the last 20 days
                if (i >= totalDays - 20) {
                    const convergenceRatio = (i - (totalDays - 20)) / 20;
                    price = price * (1 - convergenceRatio) + currentPrice * convergenceRatio;
                }

                data.push({
                    date: date,
                    price: price
                });
            }

            // Smooth final adjustment to ensure exact current price
            const lastFewPoints = 5;
            for (let i = data.length - lastFewPoints; i < data.length; i++) {
                const ratio = (i - (data.length - lastFewPoints)) / (lastFewPoints - 1);
                const smoothedPrice = data[data.length - lastFewPoints - 1].price * (1 - ratio) + currentPrice * ratio;
                data[i].price = smoothedPrice;
            }

            return data;
        }

        // Enhanced Premium Chart with Value Comparisons
        function renderPremiumChart(svg, data, currentPrice, intrinsicValue) {
            const margin = { top: 30, right: 120, bottom: 50, left: 70 };
            const width = 800 - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            const marginOfSafetyValue = parseFloat("{{ dcf_value_per_share_margin_of_safety }}");

            const g = svg.append('g')
                .attr('transform', `translate(${margin.left}, ${margin.top})`);

            // Enhanced scales with better domain calculation
            const xScale = d3.scaleTime()
                .domain(d3.extent(data, d => d.date))
                .range([0, width]);

            const allValues = [
                ...data.map(d => d.price),
                currentPrice,
                intrinsicValue,
                marginOfSafetyValue
            ];
            const yDomain = d3.extent(allValues);
            const yPadding = (yDomain[1] - yDomain[0]) * 0.1;

            const yScale = d3.scaleLinear()
                .domain([yDomain[0] - yPadding, yDomain[1] + yPadding])
                .nice()
                .range([height, 0]);

            // Create gradient definitions
            const defs = svg.select('defs');

            // Price area gradient
            const priceAreaGradient = defs.append('linearGradient')
                .attr('id', 'priceAreaGradient')
                .attr('gradientUnits', 'userSpaceOnUse')
                .attr('x1', 0).attr('y1', 0)
                .attr('x2', 0).attr('y2', height);

            priceAreaGradient.append('stop')
                .attr('offset', '0%')
                .attr('stop-color', '#667eea')
                .attr('stop-opacity', 0.4);

            priceAreaGradient.append('stop')
                .attr('offset', '100%')
                .attr('stop-color', '#764ba2')
                .attr('stop-opacity', 0.1);

            // Value zone gradient (between margin of safety and intrinsic value)
            const valueZoneGradient = defs.append('linearGradient')
                .attr('id', 'valueZoneGradient')
                .attr('gradientUnits', 'userSpaceOnUse')
                .attr('x1', 0).attr('y1', yScale(intrinsicValue))
                .attr('x2', 0).attr('y2', yScale(marginOfSafetyValue));

            valueZoneGradient.append('stop')
                .attr('offset', '0%')
                .attr('stop-color', '#10b981')
                .attr('stop-opacity', 0.2);

            valueZoneGradient.append('stop')
                .attr('offset', '100%')
                .attr('stop-color', '#10b981')
                .attr('stop-opacity', 0.1);

            // Add value zone background
            g.append('rect')
                .attr('x', 0)
                .attr('y', yScale(intrinsicValue))
                .attr('width', width)
                .attr('height', yScale(marginOfSafetyValue) - yScale(intrinsicValue))
                .attr('fill', 'url(#valueZoneGradient)')
                .attr('opacity', 0)
                .transition()
                .delay(1500)
                .duration(1000)
                .attr('opacity', 1);

            // Line and area generators
            const line = d3.line()
                .x(d => xScale(d.date))
                .y(d => yScale(d.price))
                .curve(d3.curveMonotoneX);

            const area = d3.area()
                .x(d => xScale(d.date))
                .y0(height)
                .y1(d => yScale(d.price))
                .curve(d3.curveMonotoneX);

            // Add price area with enhanced styling
            const priceArea = g.append('path')
                .datum(data)
                .attr('class', 'chart-area')
                .attr('d', area)
                .attr('fill', 'url(#priceAreaGradient)')
                .attr('opacity', 0);

            // Add price line with enhanced styling
            const priceLine = g.append('path')
                .datum(data)
                .attr('class', 'chart-line')
                .attr('d', line)
                .attr('fill', 'none')
                .attr('stroke', 'url(#lineGradient)')
                .attr('stroke-width', 3)
                .attr('filter', 'drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3))');

            // Animate area and line
            priceArea.transition()
                .delay(500)
                .duration(1500)
                .attr('opacity', 1);

            const totalLength = priceLine.node().getTotalLength();
            priceLine
                .attr('stroke-dasharray', totalLength + ' ' + totalLength)
                .attr('stroke-dashoffset', totalLength)
                .transition()
                .duration(2000)
                .ease(d3.easeLinear)
                .attr('stroke-dashoffset', 0);

            // Enhanced axes with better styling
            const xAxis = g.append('g')
                .attr('class', 'chart-axis')
                .attr('transform', `translate(0, ${height})`)
                .call(d3.axisBottom(xScale)
                    .tickFormat(d3.timeFormat('%b %Y'))
                    .tickSize(-height)
                    .tickPadding(10));

            const yAxis = g.append('g')
                .attr('class', 'chart-axis')
                .call(d3.axisLeft(yScale)
                    .tickFormat(d => '$' + d.toFixed(2))
                    .tickSize(-width)
                    .tickPadding(10));

            // Style grid lines
            xAxis.selectAll('.tick line')
                .attr('stroke', 'rgba(255, 255, 255, 0.1)')
                .attr('stroke-width', 1);

            yAxis.selectAll('.tick line')
                .attr('stroke', 'rgba(255, 255, 255, 0.1)')
                .attr('stroke-width', 1);

            // Value reference lines with enhanced styling
            const valueLines = [
                { value: intrinsicValue, label: 'Intrinsic Value', color: '#667eea', dash: '8,4' },
                { value: marginOfSafetyValue, label: 'Margin of Safety', color: '#10b981', dash: '4,4' },
                { value: currentPrice, label: 'Current Price', color: '#f59e0b', dash: 'none' }
            ];

            valueLines.forEach((line, index) => {
                // Reference line
                const referenceLine = g.append('line')
                    .attr('x1', 0)
                    .attr('x2', width)
                    .attr('y1', yScale(line.value))
                    .attr('y2', yScale(line.value))
                    .attr('stroke', line.color)
                    .attr('stroke-width', 2)
                    .attr('stroke-dasharray', line.dash)
                    .attr('opacity', 0)
                    .attr('filter', `drop-shadow(0 0 4px ${line.color})`);

                // Animate line appearance
                referenceLine.transition()
                    .delay(2000 + index * 300)
                    .duration(800)
                    .attr('opacity', 0.8);

                // Value label with enhanced styling
                const labelGroup = g.append('g')
                    .attr('class', 'value-label-group')
                    .attr('opacity', 0);

                labelGroup.append('rect')
                    .attr('x', width + 10)
                    .attr('y', yScale(line.value) - 12)
                    .attr('width', 100)
                    .attr('height', 24)
                    .attr('fill', line.color)
                    .attr('rx', 4)
                    .attr('opacity', 0.9);

                labelGroup.append('text')
                    .attr('x', width + 15)
                    .attr('y', yScale(line.value) - 2)
                    .attr('font-size', '11px')
                    .attr('font-weight', '600')
                    .attr('fill', 'white')
                    .text(line.label);

                labelGroup.append('text')
                    .attr('x', width + 15)
                    .attr('y', yScale(line.value) + 10)
                    .attr('font-size', '10px')
                    .attr('font-weight', '500')
                    .attr('fill', 'white')
                    .text(`$${line.value.toFixed(2)}`);

                // Animate label appearance
                labelGroup.transition()
                    .delay(2300 + index * 300)
                    .duration(500)
                    .attr('opacity', 1);
            });

            // Enhanced interactive elements
            const focusGroup = g.append('g')
                .attr('class', 'focus-group')
                .style('display', 'none');

            focusGroup.append('circle')
                .attr('r', 6)
                .attr('fill', '#667eea')
                .attr('stroke', 'white')
                .attr('stroke-width', 2);

            focusGroup.append('rect')
                .attr('class', 'tooltip-bg')
                .attr('width', 120)
                .attr('height', 50)
                .attr('x', 10)
                .attr('y', -25)
                .attr('fill', 'rgba(0, 0, 0, 0.8)')
                .attr('rx', 4);

            focusGroup.append('text')
                .attr('class', 'tooltip-date')
                .attr('x', 15)
                .attr('y', -10)
                .attr('font-size', '11px')
                .attr('font-weight', '600')
                .attr('fill', 'white');

            focusGroup.append('text')
                .attr('class', 'tooltip-price')
                .attr('x', 15)
                .attr('y', 5)
                .attr('font-size', '12px')
                .attr('font-weight', '700')
                .attr('fill', '#667eea');

            // Interactive overlay
            g.append('rect')
                .attr('class', 'chart-overlay')
                .attr('width', width)
                .attr('height', height)
                .attr('fill', 'none')
                .attr('pointer-events', 'all')
                .on('mouseover', () => focusGroup.style('display', null))
                .on('mouseout', () => focusGroup.style('display', 'none'))
                .on('mousemove', function(event) {
                    const [mouseX] = d3.pointer(event);
                    const x0 = xScale.invert(mouseX);
                    const bisectDate = d3.bisector(d => d.date).left;
                    const i = bisectDate(data, x0, 1);
                    const d0 = data[i - 1];
                    const d1 = data[i];
                    const d = x0 - d0.date > d1.date - x0 ? d1 : d0;

                    focusGroup.attr('transform', `translate(${xScale(d.date)}, ${yScale(d.price)})`);
                    focusGroup.select('.tooltip-date').text(d3.timeFormat('%b %d, %Y')(d.date));
                    focusGroup.select('.tooltip-price').text(`$${d.price.toFixed(2)}`);
                });

            // Performance indicators - Fixed to match main metrics calculation
            const performanceData = [
                {
                    label: 'vs Intrinsic Value',
                    value: ((intrinsicValue - currentPrice) / currentPrice * 100).toFixed(1) + '%',
                    color: intrinsicValue > currentPrice ? '#10b981' : '#ef4444'
                },
                {
                    label: 'vs Margin of Safety',
                    value: ((marginOfSafetyValue - currentPrice) / currentPrice * 100).toFixed(1) + '%',
                    color: marginOfSafetyValue > currentPrice ? '#10b981' : '#ef4444'
                }
            ];

            const performanceGroup = g.append('g')
                .attr('class', 'performance-indicators')
                .attr('transform', `translate(10, 10)`);

            performanceData.forEach((perf, index) => {
                const perfGroup = performanceGroup.append('g')
                    .attr('transform', `translate(0, ${index * 25})`)
                    .attr('opacity', 0);

                perfGroup.append('rect')
                    .attr('width', 140)
                    .attr('height', 20)
                    .attr('fill', 'rgba(0, 0, 0, 0.7)')
                    .attr('rx', 4);

                perfGroup.append('text')
                    .attr('x', 5)
                    .attr('y', 8)
                    .attr('font-size', '10px')
                    .attr('fill', 'white')
                    .text(perf.label);

                perfGroup.append('text')
                    .attr('x', 5)
                    .attr('y', 17)
                    .attr('font-size', '11px')
                    .attr('font-weight', '600')
                    .attr('fill', perf.color)
                    .text(perf.value);

                perfGroup.transition()
                    .delay(3000 + index * 200)
                    .duration(500)
                    .attr('opacity', 1);
            });
        }

        // Function to navigate back to DCF page
        function goToDcfPage() {
            window.location.href = "{{ url_for('dcf_page') }}";
        }

        // === ORIGINAL GAUGE INITIALIZATION - SEPARATE FROM CHART LOGIC ===
        document.addEventListener('DOMContentLoaded', function() {
            console.log("Initializing original gauge...");

            // --- Custom SVG Gauge Logic (Original Implementation) ---
            const arrowContainer = document.getElementById("arrowContainer");
            const ratingCircle = document.getElementById('ratingCircle');
            const ratingCircleElement = ratingCircle ? ratingCircle.querySelector('circle') : null;
            const ratingText = document.getElementById('ratingText');
            const ratingDescriptionElement = document.getElementById("ratingDescriptionText");
            const ratingSpan = document.getElementById("rating"); // Hidden span
            const differenceSpan = document.getElementById("difference"); // Hidden span
            const percentageDifference = parseFloat("{{ percentage_difference | tojson }}"); // From Flask

            console.log("Gauge elements found:", {
                arrowContainer: !!arrowContainer,
                ratingCircle: !!ratingCircle,
                ratingCircleElement: !!ratingCircleElement,
                ratingText: !!ratingText,
                ratingDescriptionElement: !!ratingDescriptionElement,
                ratingSpan: !!ratingSpan,
                differenceSpan: !!differenceSpan,
                percentageDifference: percentageDifference
            });

            if (!arrowContainer || !ratingCircle || !ratingCircleElement || !ratingText || !ratingDescriptionElement) {
                console.error("Essential gauge elements are missing from the DOM!");
                return;
            }

            // Helper function to get rating color
            function getRatingColor(rating) {
                if (rating >= 4) return '#33CCCC';
                if (rating >= 3) return '#8CE99A';
                if (rating >= 2) return '#FFD700'; // Brighter Yellow
                if (rating >= 1) return '#FF9C19'; // Orange
                return '#FF4D4D'; // Brighter Red
            }

            // Helper function to get rating description text
            function getRatingDescription(rating) {
                if (rating < 1) return "Extremely Overvalued";
                if (rating < 2) return "Overvalued";
                if (rating < 3) return "Fair Valued";
                if (rating < 4) return "Undervalued";
                return "Extremely Undervalued";
            }

            let clampedRating = 0;
            let computedAngle = -135;

            // Function to animate gauge when it becomes visible
            function animateGauge() {
                if (isNaN(percentageDifference)) {
                    console.warn("Percentage difference is NaN. Setting gauge to minimum.");
                    ratingDescriptionElement.textContent = "N/A";
                    ratingDescriptionElement.style.color = 'var(--text-muted-color)';
                    ratingCircleElement.style.fill = 'var(--text-muted-color)';
                    ratingText.textContent = 'N/A';
                    ratingText.style.fill = 'white';
                    if (ratingSpan) ratingSpan.textContent = 'N/A';
                    if (differenceSpan) differenceSpan.textContent = 'N/A';
                } else {
                    const rawRating = ((percentageDifference + 50) / 100) * 5;
                    clampedRating = Math.min(Math.max(rawRating, 0), 5);
                    computedAngle = -135 + (clampedRating / 5) * 270;

                    if (ratingSpan) ratingSpan.textContent = clampedRating.toFixed(2);
                    if (differenceSpan) differenceSpan.textContent = percentageDifference.toFixed(2);

                    const ratingColor = getRatingColor(clampedRating);
                    const ratingDescText = getRatingDescription(clampedRating);

                    // Set circle background color
                    ratingCircleElement.style.fill = ratingColor;

                    // Set text content and color
                    ratingText.textContent = clampedRating.toFixed(2);
                    ratingText.style.fill = 'white'; // White text on colored background

                    // Set description text
                    ratingDescriptionElement.textContent = ratingDescText;
                    ratingDescriptionElement.style.color = ratingColor;

                    console.log("Gauge values set:", {
                        rating: clampedRating.toFixed(2),
                        angle: computedAngle,
                        color: ratingColor,
                        description: ratingDescText
                    });
                }

                // Animate Arrow with smooth transition
                arrowContainer.style.transform = `rotate(-135deg) translate(0, -150px)`;
                arrowContainer.style.transition = "none";

                setTimeout(() => {
                    arrowContainer.style.transition = "transform 1.2s cubic-bezier(0.4, 0, 0.2, 1)";
                    arrowContainer.style.transform = `rotate(${computedAngle}deg) translate(0, -150px)`;
                    console.log("Arrow animated to angle:", computedAngle);
                }, 200);
            }

            // Set up Intersection Observer for scroll-triggered animation
            const gaugeWrapper = document.getElementById('gaugeWrapper');
            if (gaugeWrapper && 'IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting && entry.intersectionRatio > 0.3) {
                            console.log("Gauge is visible, starting animation...");
                            animateGauge();
                            observer.unobserve(entry.target); // Only animate once
                        }
                    });
                }, {
                    threshold: 0.3,
                    rootMargin: '0px 0px -50px 0px'
                });

                observer.observe(gaugeWrapper);
            } else {
                // Fallback for browsers without IntersectionObserver
                setTimeout(animateGauge, 500);
            }
        });

        // --- Stock Chart Logic (Adapted from previous attempt) ---
         document.addEventListener('DOMContentLoaded', function () {
             console.log("DCF Result page script executing."); // Check script runs

             const tickerElement = document.querySelector('.chart-section[data-ticker]');
             if (!tickerElement) {
                  console.error("Cannot find ticker element for chart.");
                  return;
             }
             const ticker = tickerElement.dataset.ticker;
             const chartContainer = document.getElementById('dcf-stock-chart');
             const timeRangeButtons = document.querySelectorAll('.chart-section .time-range-btn');

             // --- Essential Data from Flask (for chart annotations) ---
             const intrinsicValue = parseFloat("{{ dcf_value_per_share_margin_of_safety | tojson }}");
             const currentPrice = parseFloat("{{ current_price | tojson }}");
             const intrinsicLineColor = currentPrice > intrinsicValue ? 'var(--negative-color)' : 'var(--positive-color)';

              function plotStockChart(tickerSymbol, range = '1Y') {
                if (!tickerSymbol) {
                    console.error("Plot Stock Chart: Ticker symbol is missing.");
                    if(chartContainer) chartContainer.innerHTML = `<p style="color: var(--negative-color); text-align: center; padding-top: 50px;">Ticker information missing.</p>`;
                    return;
                }
                if (!chartContainer) {
                    console.error("Plot Stock Chart: Chart container not found.");
                    return;
                }
                
                // Show loading indicator
                const loadingIndicator = chartContainer.querySelector('.loading-indicator');
                const errorMessage = chartContainer.querySelector('.error-message');
                if (loadingIndicator) loadingIndicator.style.display = 'block';
                if (errorMessage) errorMessage.style.display = 'none';
                
                // Remove any existing SVG before redrawing
                d3.select(chartContainer).selectAll('svg').remove();
                
                console.log(`Fetching stock chart data for ${tickerSymbol}, range: ${range}`);

                fetch(`/api/stock-history/${tickerSymbol}/${range}`)
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(err => { throw new Error(err.error || `HTTP error ${response.status}`) });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (!data || data.error || !data.dates || !data.prices || data.dates.length === 0) {
                            if (errorMessage) {
                                errorMessage.textContent = `Error loading chart data: ${data.error || 'No data available for this range.'}`;
                                errorMessage.style.display = 'block';
                            }
                            if (loadingIndicator) loadingIndicator.style.display = 'none';
                            return;
                        }
                        
                        // Hide loading indicator
                        if (loadingIndicator) loadingIndicator.style.display = 'none';
                        
                        // Process data for D3
                        const chartData = data.dates.map((date, i) => ({
                            date: new Date(date),
                            price: data.prices[i]
                        }));
                        
                        // Create D3 Chart
                        createD3Chart(chartData, tickerSymbol);
                    })
                    .catch(error => {
                        console.error("Error fetching/plotting stock chart:", error);
                        if (errorMessage) {
                            errorMessage.textContent = `Error loading chart data: ${error.message}`;
                            errorMessage.style.display = 'block';
                        }
                        if (loadingIndicator) loadingIndicator.style.display = 'none';
                    });
              }
              
              function createD3Chart(data, tickerSymbol) {
                  // Get theme colors from CSS variables
                  const getColor = (varName) => getComputedStyle(document.documentElement).getPropertyValue(varName).trim();
                  
                  // Define chart dimensions and margins
                  const margin = {top: 30, right: 30, bottom: 50, left: 60};
                  const width = chartContainer.clientWidth - margin.left - margin.right;
                  const height = chartContainer.clientHeight - margin.top - margin.bottom;
                  
                  // Define chart colors using semantic colors (preserve green/red regardless of theme)
                  const startPrice = data[0].price;
                  const endPrice = data[data.length - 1].price;
                  const chartLineColor = endPrice >= startPrice ? '#10b981' : '#ef4444'; // Green for gains, red for losses
                  const areaColor = chartLineColor;
                  const intrinsicLineColor = currentPrice > intrinsicValue ? '#ef4444' : '#10b981'; // Red if overvalued, green if undervalued
                  const currentPriceColor = getColor('--text-muted-color');
                  
                  // Create SVG element
                  const svg = d3.select(chartContainer)
                      .append('svg')
                      .attr('width', width + margin.left + margin.right)
                      .attr('height', height + margin.top + margin.bottom)
                      .append('g')
                      .attr('transform', `translate(${margin.left},${margin.top})`);
                  
                  // Add gradient for area fill
                  const defs = svg.append('defs');
                  const gradient = defs.append('linearGradient')
                      .attr('id', 'area-gradient')
                      .attr('x1', '0%').attr('y1', '0%')
                      .attr('x2', '0%').attr('y2', '100%');
                      
                  gradient.append('stop')
                      .attr('offset', '0%')
                      .attr('stop-color', areaColor)
                      .attr('stop-opacity', 0.6);
                      
                  gradient.append('stop')
                      .attr('offset', '100%')
                      .attr('stop-color', areaColor)
                      .attr('stop-opacity', 0.1);
                  
                  // Create scales
                  const xScale = d3.scaleTime()
                      .domain(d3.extent(data, d => d.date))
                      .range([0, width]);
                      
                  const yMin = d3.min([d3.min(data, d => d.price), intrinsicValue, currentPrice]) * 0.9;
                  const yMax = d3.max([d3.max(data, d => d.price), intrinsicValue, currentPrice]) * 1.1;
                  
                  const yScale = d3.scaleLinear()
                      .domain([yMin, yMax])
                      .range([height, 0]);
                  
                  // Create axes
                  const xAxis = d3.axisBottom(xScale)
                      .ticks(width > 600 ? 10 : 5)
                      .tickSize(-height)  // extend ticks across chart
                      .tickFormat(d3.timeFormat(width > 600 ? '%b %Y' : '%b'));
                      
                  const yAxis = d3.axisLeft(yScale)
                      .ticks(5)
                      .tickSize(-width)  // extend ticks across chart
                      .tickFormat(d => `$${d3.format(',.2f')(d)}`);
                  
                  // Add X axis
                  svg.append('g')
                      .attr('class', 'axis-x')
                      .attr('transform', `translate(0,${height})`)
                      .call(xAxis)
                      .selectAll('text')
                      .style('text-anchor', 'middle');
                  
                  // Add Y axis
                  svg.append('g')
                      .attr('class', 'axis-y')
                      .call(yAxis);
                  
                  // Style grid lines
                  svg.selectAll('.tick line')
                      .style('stroke', getColor('--border-color'))
                      .style('stroke-opacity', 0.3);
                  
                  // Create line and area generators
                  const line = d3.line()
                      .x(d => xScale(d.date))
                      .y(d => yScale(d.price))
                      .curve(d3.curveMonotoneX);  // smooth curve
                  
                  const area = d3.area()
                      .x(d => xScale(d.date))
                      .y0(height)  // baseline
                      .y1(d => yScale(d.price))
                      .curve(d3.curveMonotoneX);  // match line curve
                  
                  // Add area path
                  svg.append('path')
                      .datum(data)
                      .attr('class', 'area')
                      .attr('d', area)
                      .style('fill', 'url(#area-gradient)');
                  
                  // Add line path with animation
                  const path = svg.append('path')
                      .datum(data)
                      .attr('class', 'line')
                      .attr('d', line)
                      .style('stroke', chartLineColor)
                      .style('stroke-width', 3)
                      .style('fill', 'none');
                  
                  // Animate the line drawing
                  const pathLength = path.node().getTotalLength();
                  path.style('stroke-dasharray', pathLength)
                      .style('stroke-dashoffset', pathLength)
                      .transition()
                      .duration(1500)
                      .ease(d3.easeLinear)
                      .style('stroke-dashoffset', 0);
                  
                  // Add intrinsic value line
                  svg.append('line')
                      .attr('x1', 0)
                      .attr('y1', yScale(intrinsicValue))
                      .attr('x2', width)
                      .attr('y2', yScale(intrinsicValue))
                      .style('stroke', intrinsicLineColor)
                      .style('stroke-width', 2)
                      .style('stroke-dasharray', '6,4');
                  
                  // Add current price line
                  svg.append('line')
                      .attr('x1', 0)
                      .attr('y1', yScale(currentPrice))
                      .attr('x2', width)
                      .attr('y2', yScale(currentPrice))
                      .style('stroke', currentPriceColor)
                      .style('stroke-width', 1.5)
                      .style('stroke-dasharray', '3,3');
                  
                  // Add labels for reference lines
                  svg.append('text')
                      .attr('x', width - 5)
                      .attr('y', yScale(intrinsicValue) - 8)
                      .style('text-anchor', 'end')
                      .style('fill', intrinsicLineColor)
                      .style('font-size', '12px')
                      .style('font-weight', 'bold')
                      .text(`Fair Value: $${intrinsicValue.toFixed(2)}`);
                      
                  svg.append('text')
                      .attr('x', width - 5)
                      .attr('y', yScale(currentPrice) - 8)
                      .style('text-anchor', 'end')
                      .style('fill', currentPriceColor)
                      .style('font-size', '12px')
                      .style('font-weight', 'bold')
                      .text(`Current: $${currentPrice.toFixed(2)}`);
                  
                  // Add data points with hover effects
                  const tooltipPadding = 10;
                  const tooltip = d3.select('.d3-tooltip');
                  const tooltipLogo = tooltip.select('.stock-logo');
                  const tooltipTicker = tooltip.select('.ticker');
                  const tooltipDate = tooltip.select('.date');
                  const tooltipPrice = tooltip.select('.price');
                  
                  // Add tooltip mouseover effects
                  const bisect = d3.bisector(d => d.date).left;
                  
                  // Set the logo src for the tooltip
                  tooltipLogo.attr('src', `${window.location.origin}/api/logo/${tickerSymbol}`);
                  tooltipTicker.text(tickerSymbol.split('.')[0]);
                  
                  // Create invisible overlay for tracking mouse movement
                  svg.append('rect')
                      .attr('width', width)
                      .attr('height', height)
                      .style('fill', 'none')
                      .style('pointer-events', 'all')
                      .on('mousemove', function(event) {
                          // Get mouse x position and find nearest data point
                          const mouseX = d3.pointer(event)[0];
                          const x0 = xScale.invert(mouseX);
                          const i = bisect(data, x0, 1);
                          const d0 = data[i - 1];
                          const d1 = data[i] || d0;
                          const d = x0 - d0.date > d1.date - x0 ? d1 : d0;
                          
                          // Position the crosshair
                          crosshairX
                              .attr('x1', xScale(d.date))
                              .attr('x2', xScale(d.date))
                              .style('opacity', 1);
                              
                          crosshairY
                              .attr('y1', yScale(d.price))
                              .attr('y2', yScale(d.price))
                              .style('opacity', 1);
                          
                          // Show tooltip with data
                          tooltip
                              .style('left', `${event.pageX + tooltipPadding}px`)
                              .style('top', `${event.pageY - 100}px`)
                              .classed('visible', true);
                          
                          // Set tooltip content
                          tooltipDate.text(d3.timeFormat('%b %d, %Y')(d.date));
                          tooltipPrice
                              .text(`$${d.price.toFixed(2)}`)
                              .classed('above', d.price >= currentPrice)
                              .classed('below', d.price < currentPrice);
                          
                          // Add the hover circle
                          hoverCircle
                              .attr('cx', xScale(d.date))
                              .attr('cy', yScale(d.price))
                              .style('opacity', 1);
                      })
                      .on('mouseout', function() {
                          // Hide tooltip and crosshair
                          tooltip.classed('visible', false);
                          crosshairX.style('opacity', 0);
                          crosshairY.style('opacity', 0);
                          hoverCircle.style('opacity', 0);
                      });
                  
                  // Add crosshair
                  const crosshairX = svg.append('line')
                      .attr('class', 'crosshair-x')
                      .attr('y1', 0)
                      .attr('y2', height)
                      .style('stroke', getColor('--text-muted-color'))
                      .style('stroke-width', 1)
                      .style('stroke-dasharray', '3,3')
                      .style('opacity', 0);
                      
                  const crosshairY = svg.append('line')
                      .attr('class', 'crosshair-y')
                      .attr('x1', 0)
                      .attr('x2', width)
                      .style('stroke', getColor('--text-muted-color'))
                      .style('stroke-width', 1)
                      .style('stroke-dasharray', '3,3')
                      .style('opacity', 0);
                  
                  // Add hover circle
                  const hoverCircle = svg.append('circle')
                      .attr('r', 6)
                      .style('fill', 'white')
                      .style('stroke', chartLineColor)
                      .style('stroke-width', 3)
                      .style('opacity', 0);
                      
                  // Add a subtle glow to the hover circle
                  const circleFilter = defs.append('filter')
                      .attr('id', 'glow-hover')
                      .attr('x', '-50%')
                      .attr('y', '-50%')
                      .attr('width', '200%')
                      .attr('height', '200%');
                      
                  circleFilter.append('feGaussianBlur')
                      .attr('stdDeviation', '3')
                      .attr('result', 'coloredBlur');
                      
                  const circleMerge = circleFilter.append('feMerge');
                  circleMerge.append('feMergeNode').attr('in', 'coloredBlur');
                  circleMerge.append('feMergeNode').attr('in', 'SourceGraphic');
                  
                  hoverCircle.style('filter', 'url(#glow-hover)');
                  
                  // Add legend
                  const legend = svg.append('g')
                      .attr('class', 'legend')
                      .attr('transform', `translate(${width/2}, ${height + 35})`);
                  
                  // Stock Price legend item
                  const legendItemWidth = 120;
                  const legendSpacing = 40;
                  
                  const priceItem = legend.append('g')
                      .attr('transform', `translate(${-legendItemWidth - legendSpacing}, 0)`);
                      
                  priceItem.append('line')
                      .attr('x1', 0)
                      .attr('y1', 0)
                      .attr('x2', 20)
                      .attr('y2', 0)
                      .style('stroke', chartLineColor)
                      .style('stroke-width', 3);
                      
                  priceItem.append('text')
                      .attr('x', 25)
                      .attr('y', 4)
                      .style('fill', getColor('--text-muted-color'))
                      .style('font-size', '12px')
                      .text(`${tickerSymbol.split('.')[0]} Price`);
                  
                  // Fair Value legend item
                  const fairValueItem = legend.append('g')
                      .attr('transform', `translate(${-legendItemWidth/2}, 0)`);
                      
                  fairValueItem.append('line')
                      .attr('x1', 0)
                      .attr('y1', 0)
                      .attr('x2', 20)
                      .attr('y2', 0)
                      .style('stroke', intrinsicLineColor)
                      .style('stroke-width', 2)
                      .style('stroke-dasharray', '6,4');
                      
                  fairValueItem.append('text')
                      .attr('x', 25)
                      .attr('y', 4)
                      .style('fill', getColor('--text-muted-color'))
                      .style('font-size', '12px')
                      .text('Fair Value');
                  
                  // Current Price legend item
                  const currentItem = legend.append('g')
                      .attr('transform', `translate(${legendItemWidth/2 + legendSpacing/2}, 0)`);
                      
                  currentItem.append('line')
                      .attr('x1', 0)
                      .attr('y1', 0)
                      .attr('x2', 20)
                      .attr('y2', 0)
                      .style('stroke', currentPriceColor)
                      .style('stroke-width', 1.5)
                      .style('stroke-dasharray', '3,3');
                      
                  currentItem.append('text')
                      .attr('x', 25)
                      .attr('y', 4)
                      .style('fill', getColor('--text-muted-color'))
                      .style('font-size', '12px')
                      .text('Current Price');
                      
                  console.log("D3 chart drawn successfully");
              }

             timeRangeButtons.forEach(button => {
                 button.addEventListener('click', function() {
                     timeRangeButtons.forEach(btn => btn.classList.remove('active'));
                     this.classList.add('active');
                     plotStockChart(ticker, this.dataset.range);
                 });
             });

             if (ticker && chartContainer) {
                 plotStockChart(ticker, '1Y'); // Initial load
             } else {
                 console.error("Initial chart load failed: Ticker or container missing.");
                  if(chartContainer) chartContainer.innerHTML = `<p style="color: var(--negative-color); text-align: center; padding-top: 50px;">Chart could not be loaded.</p>`;
             }


             // Note: Gauge logic is now handled by the independent gauge initialization above

             // Result Text Coloring (using calculated values from above)
             const dcfValuePerShareElement = document.getElementById('dcfValuePerShare');
             const dcfValueWithMarginElement = document.getElementById('dcfValueWithMargin');
             const dcfValuePerShare = parseFloat("{{ dcf_value_per_share | tojson }}");
             // 'intrinsicValue' and 'currentPrice' already defined for chart logic

             if (dcfValuePerShareElement) {
                 dcfValuePerShareElement.classList.remove('green', 'red');
                 if (!isNaN(dcfValuePerShare) && !isNaN(currentPrice)){
                      dcfValuePerShareElement.classList.add(dcfValuePerShare > currentPrice ? 'green' : 'red');
                 }
             }
             if (dcfValueWithMarginElement) {
                 dcfValueWithMarginElement.classList.remove('green', 'red');
                  if (!isNaN(intrinsicValue) && !isNaN(currentPrice)){
                       dcfValueWithMarginElement.classList.add(intrinsicValue > currentPrice ? 'green' : 'red');
                  }
             }

         // Radar Chart Implementation for Investment Ratings
         function createRadarChart() {
             // Set up dimensions
             const margin = {top: 50, right: 80, bottom: 50, left: 80};
             const width = 500 - margin.left - margin.right;
             const height = 400 - margin.top - margin.bottom;
             
             // Get rating values from Flask or use defaults
              // These values are passed from the Flask backend or use sensible defaults
              const businessQuality = parseFloat("{{ business_quality | default(6, true) }}") / 10;
              const financialStrength = parseFloat("{{ financial_strength | default(7, true) }}") / 10;
              const valuation = parseFloat("{{ valuation | default(6.1, true) }}") / 10;
              const managementQuality = parseFloat("{{ management_quality | default(6.5, true) }}") / 10;
              // Calculate overall if not provided, or use the provided value
              const overallRating = parseFloat("{{ overall_rating | default(6.4, true) }}") / 10;
              
              // Update the text values in the ratings table
              document.getElementById('biz-quality-score').textContent = (businessQuality * 10).toFixed(1);
              document.getElementById('fin-strength-score').textContent = (financialStrength * 10).toFixed(1);
              document.getElementById('valuation-score').textContent = (valuation * 10).toFixed(1);
              document.getElementById('mgmt-quality-score').textContent = (managementQuality * 10).toFixed(1);
              document.getElementById('overall-score').textContent = (overallRating * 10).toFixed(1);
              
              // Update the assessment text based on scores
              function updateAssessmentText(id, score) {
                  const element = document.getElementById(id);
                  if (!element) return;
                  
                  if (id === 'biz-quality-text') {
                      element.textContent = score >= 7 ? 'Strong' : (score >= 5 ? 'Moderate' : 'Limited');
                  } else if (id === 'fin-strength-text') {
                      element.textContent = score >= 7 ? 'Strong' : (score >= 5 ? 'Adequate' : 'Weak');
                  } else if (id === 'valuation-text') {
                      element.textContent = score >= 7 ? 'Undervalued' : (score >= 5 ? 'Fair' : 'Overvalued');
                  } else if (id === 'mgmt-quality-text') {
                      element.textContent = score >= 7 ? 'Excellent' : (score >= 5 ? 'Competent' : 'Questionable');
                  } else if (id === 'overall-text') {
                      element.textContent = score >= 7 ? 'Buy' : (score >= 5 ? 'Hold' : 'Avoid');
                  }
              }
              
              updateAssessmentText('biz-quality-text', businessQuality * 10);
              updateAssessmentText('fin-strength-text', financialStrength * 10);
              updateAssessmentText('valuation-text', valuation * 10);
              updateAssessmentText('mgmt-quality-text', managementQuality * 10);
              updateAssessmentText('overall-text', overallRating * 10);
             
             // Build data
             const data = [
                 {axis: "Business Quality/Moat", value: businessQuality},
                 {axis: "Financial Strength", value: financialStrength},
                 {axis: "Valuation", value: valuation},
                 {axis: "Management Quality", value: managementQuality},
                 {axis: "Overall Rating", value: overallRating}
             ];

             // Number of axes (categories)
             const axisCount = data.length;
             
             // Angle between each axis
             const angleSlice = (Math.PI * 2) / axisCount;
             
             // Radius of the outermost circle
             const radius = Math.min(width/2, height/2);
             
             // Scale for the radius
             const rScale = d3.scaleLinear()
                 .range([0, radius])
                 .domain([0, 1]);
             
             // Create the SVG container
             const svg = d3.select("#radar-chart").append("svg")
                 .attr("width", width + margin.left + margin.right)
                 .attr("height", height + margin.top + margin.bottom)
                 .append("g")
                 .attr("transform", `translate(${margin.left + width/2}, ${margin.top + height/2})`);
             
             // Create circular grid lines
             const levels = 5;
             const gridColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color').trim();
             const axisColor = getComputedStyle(document.documentElement).getPropertyValue('--text-muted-color').trim();
             
             // Draw the circular grid lines
             for (let j = 0; j < levels; j++) {
                 const levelFactor = radius * ((j + 1) / levels);
                 
                 // Draw the circles
                 svg.append("circle")
                     .attr("cx", 0)
                     .attr("cy", 0)
                     .attr("r", levelFactor)
                     .style("fill", "none")
                     .style("stroke", gridColor)
                     .style("opacity", 0.5);
                 
                 // Add level labels
                 if (j === levels - 1) {
                     svg.append("text")
                         .attr("x", 0)
                         .attr("y", -levelFactor - 10)
                         .attr("text-anchor", "middle")
                         .style("font-size", "10px")
                         .style("fill", axisColor)
                         .text("10/10");
                 }
             }
             
             // Draw the axes (spokes)
             const axis = svg.selectAll(".axis")
                 .data(Array(axisCount).fill(0))
                 .enter()
                 .append("g")
                 .attr("class", "axis");
             
             axis.append("line")
                 .attr("x1", 0)
                 .attr("y1", 0)
                 .attr("x2", (d, i) => radius * Math.cos(angleSlice * i - Math.PI/2))
                 .attr("y2", (d, i) => radius * Math.sin(angleSlice * i - Math.PI/2))
                 .style("stroke", gridColor)
                 .style("stroke-width", "1px");
             
             // Draw axis labels
             axis.append("text")
                 .attr("class", "legend")
                 .attr("text-anchor", "middle")
                 .attr("dy", "0.35em")
                 .attr("x", (d, i) => (radius + 20) * Math.cos(angleSlice * i - Math.PI/2))
                 .attr("y", (d, i) => (radius + 20) * Math.sin(angleSlice * i - Math.PI/2))
                 .text((d, i) => data[i].axis)
                 .style("font-size", "11px")
                 .style("fill", getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim());
             
             // Draw the radar chart blobs
             const radarLine = d3.lineRadial()
                 .radius(d => rScale(d.value))
                 .angle((d, i) => i * angleSlice)
                 .curve(d3.curveLinearClosed);
             
             // Create the radar area
             svg.append("path")
                 .datum(data)
                 .attr("class", "radar-area")
                 .attr("d", radarLine)
                 .style("fill", getComputedStyle(document.documentElement).getPropertyValue('--highlight-color').trim())
                 .style("fill-opacity", 0.3)
                 .style("stroke", getComputedStyle(document.documentElement).getPropertyValue('--highlight-color').trim())
                 .style("stroke-width", "2px");
             
             // Add circles at each data point
             svg.selectAll(".radar-circle")
                 .data(data)
                 .enter()
                 .append("circle")
                 .attr("class", "radar-circle")
                 .attr("cx", (d, i) => rScale(d.value) * Math.cos(angleSlice * i - Math.PI/2))
                 .attr("cy", (d, i) => rScale(d.value) * Math.sin(angleSlice * i - Math.PI/2))
                 .attr("r", 5)
                 .style("fill", getComputedStyle(document.documentElement).getPropertyValue('--highlight-color').trim())
                 .style("stroke", "#fff")
                 .style("stroke-width", "2px")
                 .append("title")
                 .text(d => `${d.axis}: ${(d.value * 10).toFixed(1)}/10`);
             
             // Add value labels at each data point
             svg.selectAll(".radar-value")
                 .data(data)
                 .enter()
                 .append("text")
                 .attr("class", "radar-value")
                 .attr("x", (d, i) => (rScale(d.value) + 10) * Math.cos(angleSlice * i - Math.PI/2))
                 .attr("y", (d, i) => (rScale(d.value) + 10) * Math.sin(angleSlice * i - Math.PI/2))
                 .style("font-size", "12px")
                 .style("font-weight", "bold")
                 .style("fill", getComputedStyle(document.documentElement).getPropertyValue('--highlight-color').trim())
                 .text(d => (d.value * 10).toFixed(1));
             
             // Create a legend
             const legend = d3.select("#radar-legend");
             legend.html(""); // Clear any existing content
             
             const legendItem = legend.append("div")
                 .attr("class", "radar-legend-item");
             
             legendItem.append("div")
                 .attr("class", "radar-legend-color")
                 .style("background-color", getComputedStyle(document.documentElement).getPropertyValue('--highlight-color').trim());
             
             legendItem.append("span")
                 .text("Investment Quality Ratings (0-10 scale)");
         }
         
         // Call the radar chart function
         createRadarChart();

         // === Enhanced Page Loading Animation ===
         // Add a subtle loading overlay that fades out
         const loadingOverlay = document.createElement('div');
         loadingOverlay.style.cssText = `
             position: fixed;
             top: 0;
             left: 0;
             width: 100%;
             height: 100%;
             background: linear-gradient(135deg, var(--bg-color) 0%, var(--card-bg-color) 100%);
             z-index: 9999;
             display: flex;
             align-items: center;
             justify-content: center;
             opacity: 1;
             transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
             pointer-events: none;
         `;

         // Add loading spinner
         const spinner = document.createElement('div');
         spinner.style.cssText = `
             width: 40px;
             height: 40px;
             border: 3px solid var(--border-color);
             border-top: 3px solid var(--highlight-color);
             border-radius: 50%;
             animation: spin 1s linear infinite;
         `;

         // Add spinner animation
         const spinnerStyle = document.createElement('style');
         spinnerStyle.textContent = `
             @keyframes spin {
                 0% { transform: rotate(0deg); }
                 100% { transform: rotate(360deg); }
             }
         `;
         document.head.appendChild(spinnerStyle);

         loadingOverlay.appendChild(spinner);
         document.body.appendChild(loadingOverlay);

         // Fade out loading overlay after a short delay
         setTimeout(() => {
             loadingOverlay.style.opacity = '0';
             setTimeout(() => {
                 if (loadingOverlay.parentNode) {
                     loadingOverlay.parentNode.removeChild(loadingOverlay);
                 }
             }, 800);
         }, 500);

         // Add staggered reveal animation to elements
         const animatedElements = document.querySelectorAll('.result-box, .chart-section .card, .investment-ratings, .dcf-inputs-summary');
         animatedElements.forEach((element, index) => {
             element.style.opacity = '0';
             element.style.transform = 'translateY(20px)';
             element.style.transition = 'opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1), transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)';

             setTimeout(() => {
                 element.style.opacity = '1';
                 element.style.transform = 'translateY(0)';
             }, 600 + (index * 150)); // Stagger by 150ms
         });

         }); // End DOMContentLoaded

        // --- END: Original Script Block ---
    </script>
{% endblock scripts_extra %}
