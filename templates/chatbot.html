<!-- templates/chatbot.html -->
<div id="chatbot-container">
    {# Main container for positioning #}

    <!-- Settings Panel (Slides from Left) -->
    <div id="chatbot-settings-panel">
        {# Initially hidden via CSS transform #}
        <div id="settings-header">
            <h4><i class="fas fa-sliders-h"></i> Chatbot Settings</h4>
            <button id="settings-close" aria-label="Close Settings">×</button>
        </div>
        <div id="settings-content">
            <div
                id="settings-loading"
                style="text-align: center; padding: 20px; display: none"
            >
                {# Initially hidden #}
                <i class="fas fa-spinner fa-spin"></i> Loading...
            </div>
            <div
                id="settings-status"
                class="mt-3 small p-2"
                style="text-align: center"
            ></div>
            <form id="settings-form" style="display: none">
                {# Initially hidden until loaded #}
                <div class="form-section mb-3">
                    <label for="settings-default-llm" class="form-label"
                        >Default LLM</label
                    >
                    <select
                        class="form-select form-select-sm"
                        id="settings-default-llm"
                        name="default_llm"
                    >
                        <!-- Options populated by JS -->
                    </select>
                    <div class="form-text small">
                        Default model for summaries & general chat fallback.
                    </div>
                </div>
                <hr />
                <div class="form-section mb-3">
                    <label
                        id="settings-api-key-label"
                        for="settings-api-key-input"
                        class="form-label"
                        >API Key</label
                    >
                    <!-- API Key input group will be dynamically inserted here by JS -->
                    <div id="settings-api-key-container">
                        <p class="text-muted small">
                            Select a Default LLM above to manage its API key.
                        </p>
                    </div>
                </div>
                <div
                    class="settings-actions d-flex justify-content-between mt-4 pt-3 border-top"
                >
                    <button type="submit" class="btn btn-primary btn-sm">
                        Save Settings
                    </button>
                    <button
                        type="button"
                        id="clear-keys-button"
                        class="btn btn-danger btn-sm"
                    >
                        Clear All Keys
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Chat Window (Slides with Settings Panel) -->
    <div id="chatbot-window" class="hidden">
        {# Initially hidden via CSS transform and hidden class #}
        <div id="chatbot-header">
            <span class="bot-avatar-header">
                <i class="fas fa-robot chatbot-icon-small"></i>
            </span>
            <h3>H Trader Assistant</h3>
            <button id="chatbot-settings" aria-label="Chatbot Settings">
                {# Settings Cog Icon #}
                <i class="fas fa-cog"></i>
            </button>
            <button id="chatbot-close" aria-label="Close Chatbot">×</button>
        </div>
        <ul id="chatbot-messages">
            <li class="message bot initial-wave">
                <span class="bot-avatar"
                    ><i class="fas fa-robot chatbot-icon-small"></i
                ></span>
                <p>
                    Hello! How can I help you with stock info or site navigation
                    today? Ask away!<br /><br /><small
                        ><i
                            ><b>Disclaimer:</b> I am an AI assistant.
                            Information provided may not always be accurate or
                            up-to-date. Always do your own research before
                            making financial decisions.</i
                        ></small
                    >
                    ✨
                </p>
            </li>
            <!-- RATING CHARTS ARE NOW DYNAMICALLY INSERTED INTO MESSAGES -->
        </ul>
        <div id="chatbot-input-area-wrapper">
            <div id="typing-indicator" class="hidden">
                <span class="bot-avatar-typing"
                    ><i class="fas fa-robot chatbot-icon-small"></i
                ></span>
                <div class="dots"><span></span><span></span><span></span></div>
            </div>
            <div id="chatbot-input-area">
                <input
                    type="text"
                    id="chatbot-input"
                    placeholder="Type your message..."
                    autocomplete="off"
                />
                <button id="chatbot-send" aria-label="Send Message">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Toggle Button (Remains Fixed) -->
    <button id="chatbot-toggle" aria-label="Open Chatbot">
        <i class="fas fa-comment-dots chatbot-icon"></i>
        <span class="pulse-ring-outer"></span>
        <span class="pulse-ring-inner"></span>
    </button>

    <!-- Financial Feature Templates -->
    <div id="financial-templates" style="display: none">
        <!-- Interactive Financial Statements Template -->
        <div
            id="interactive-financial-statements-template"
            class="financial-template"
        >
            <div class="financial-options">
                <h4>Interactive Financial Statement Explorer</h4>
                <div class="statement-controls">
                    <div class="statement-type-buttons financial-buttons">
                        <button
                            class="financial-button statement-type-btn active"
                            data-statement-type="balance-sheet"
                        >
                            <i class="fas fa-file-alt"></i> Balance Sheet
                        </button>
                        <button
                            class="financial-button statement-type-btn"
                            data-statement-type="income-statement"
                        >
                            <i class="fas fa-chart-line"></i> Income Statement
                        </button>
                        <button
                            class="financial-button statement-type-btn"
                            data-statement-type="cash-flow"
                        >
                            <i class="fas fa-money-bill-wave"></i> Cash Flow
                        </button>
                    </div>
                </div>
            </div>
            <div
                class="financial-container"
                id="interactive-financial-statements-container"
            >
                <div class="highlights-section mb-3" id="highlights-section">
                    <h5>Financial Highlights</h5>
                    <div class="highlights-container" id="highlights-container">
                        <!-- Highlights will be populated dynamically -->
                    </div>
                    <div id="highlights-loading" style="display: none">
                        <div
                            class="spinner-border spinner-border-sm text-primary"
                            role="status"
                        >
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>

                <!-- Statement Table Container -->
                <div class="table-responsive position-relative">
                    <table
                        class="table table-bordered table-sm"
                        id="statement-table"
                    >
                        <thead>
                            <tr id="table-header-row">
                                <th>Item</th>
                                <!-- Date columns will be added dynamically -->
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            <!-- Table rows will be added dynamically -->
                        </tbody>
                    </table>
                    <div id="table-loading" style="display: none">
                        <div
                            class="spinner-border spinner-border-sm text-primary"
                            role="status"
                        >
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>

                <div class="chart-section mt-4">
                    <h5>Visual Comparison</h5>
                    <div class="form-group mb-3">
                        <select
                            class="form-select form-select-sm"
                            id="chart-item-selector"
                        >
                            <option value="">
                                Select an item to visualize
                            </option>
                        </select>
                    </div>
                    <div class="chart-container" style="height: 300px">
                        <!-- D3.js visualization will be rendered here -->
                        <!-- Canvas fallback if D3 is not available -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Statements Template -->
        <div id="financial-statements-template" class="financial-template">
            <div class="financial-options">
                <h4>Choose statement type:</h4>
                <div class="financial-buttons">
                    <button
                        class="financial-button"
                        data-statement="balance_sheet"
                    >
                        <i class="fas fa-file-alt"></i> Balance Sheet
                    </button>
                    <button
                        class="financial-button"
                        data-statement="income_statement"
                    >
                        <i class="fas fa-chart-line"></i> Income Statement
                    </button>
                    <button class="financial-button" data-statement="cash_flow">
                        <i class="fas fa-money-bill-wave"></i> Cash Flow
                    </button>
                </div>
            </div>
            <div
                class="financial-container"
                id="financial-statements-container"
            ></div>
        </div>

        <!-- Sentiment Analysis Template -->
        <div id="sentiment-analysis-template" class="financial-template">
            <div class="financial-options">
                <h4>Choose sentiment view:</h4>
                <div class="financial-buttons">
                    <button
                        class="financial-button"
                        data-sentiment="news_sentiment"
                    >
                        <i class="fas fa-newspaper"></i> News Sentiment
                    </button>
                    <button
                        class="financial-button"
                        data-sentiment="social_sentiment"
                    >
                        <i class="fas fa-users"></i> Social Media
                    </button>
                    <button
                        class="financial-button"
                        data-sentiment="overall_sentiment"
                    >
                        <i class="fas fa-chart-pie"></i> Overall Tone
                    </button>
                </div>
            </div>
            <div
                class="financial-container"
                id="sentiment-analysis-container"
            ></div>
        </div>

        <!-- Portfolio Tracker Template -->
        <div id="portfolio-tracker-template" class="financial-template">
            <div class="financial-options">
                <h4>Choose portfolio view:</h4>
                <div class="financial-buttons">
                    <button
                        class="financial-button"
                        data-portfolio="portfolio_overview"
                    >
                        <i class="fas fa-chart-pie"></i> Portfolio Overview
                    </button>
                    <button
                        class="financial-button"
                        data-portfolio="performance_chart"
                    >
                        <i class="fas fa-chart-line"></i> Performance
                    </button>
                    <button
                        class="financial-button"
                        data-portfolio="risk_analysis"
                    >
                        <i class="fas fa-exclamation-triangle"></i> Risk
                        Analysis
                    </button>
                </div>
            </div>
            <div
                class="financial-container"
                id="portfolio-tracker-container"
            ></div>
        </div>

        <!-- Fair Value Template -->
        <div id="fair-value-template" class="financial-template">
            <div class="financial-options">
                <h4>Choose valuation method:</h4>
                <div class="financial-buttons">
                    <button
                        class="financial-button"
                        data-valuation="dcf_valuation"
                    >
                        <i class="fas fa-calculator"></i> DCF Valuation
                    </button>
                    <button
                        class="financial-button"
                        data-valuation="peer_comparison"
                    >
                        <i class="fas fa-users"></i> Peer Comparison
                    </button>
                    <button
                        class="financial-button"
                        data-valuation="overall_valuation"
                    >
                        <i class="fas fa-star-half-alt"></i> Overall Rating
                    </button>
                </div>
            </div>
            <div class="financial-container" id="fair-value-container"></div>
        </div>

        <!-- Stock Screener Template -->
        <div id="stock-screener-template" class="financial-template">
            <div class="financial-options">
                <h4>Choose screen type:</h4>
                <div class="financial-buttons">
                    <button class="financial-button" data-screen="value_stocks">
                        <i class="fas fa-dollar-sign"></i> Value Stocks
                    </button>
                    <button
                        class="financial-button"
                        data-screen="growth_stocks"
                    >
                        <i class="fas fa-chart-line"></i> Growth Stocks
                    </button>
                    <button
                        class="financial-button"
                        data-screen="dividend_stocks"
                    >
                        <i class="fas fa-hand-holding-usd"></i> Dividend Stocks
                    </button>
                    <button
                        class="financial-button"
                        data-screen="custom_screen"
                    >
                        <i class="fas fa-sliders-h"></i> Custom Screen
                    </button>
                </div>
            </div>
            <div
                class="financial-container"
                id="stock-screener-container"
            ></div>
        </div>

        <!-- Earnings Calendar Template -->
        <div id="earnings-calendar-template" class="financial-template">
            <div class="financial-options">
                <h4>Choose view:</h4>
                <div class="financial-buttons">
                    <button
                        class="financial-button"
                        data-earnings="upcoming_earnings"
                    >
                        <i class="fas fa-calendar-alt"></i> Upcoming Earnings
                    </button>
                    <button
                        class="financial-button"
                        data-earnings="recent_earnings"
                    >
                        <i class="fas fa-history"></i> Recent Reports
                    </button>
                    <button
                        class="financial-button"
                        data-earnings="earnings_surprises"
                    >
                        <i class="fas fa-exclamation"></i> Earnings Surprises
                    </button>
                </div>
            </div>
            <div
                class="financial-container"
                id="earnings-calendar-container"
            ></div>
        </div>

        <!-- Sector Performance Template -->
        <div id="sector-performance-template" class="financial-template">
            <div class="financial-options">
                <h4>Choose view:</h4>
                <div class="financial-buttons">
                    <button class="financial-button" data-sector="sector_map">
                        <i class="fas fa-th-large"></i> Sector Map
                    </button>
                    <button
                        class="financial-button"
                        data-sector="sector_leaders"
                    >
                        <i class="fas fa-trophy"></i> Sector Leaders
                    </button>
                    <button
                        class="financial-button"
                        data-sector="sector_laggards"
                    >
                        <i class="fas fa-arrow-down"></i> Sector Laggards
                    </button>
                </div>
            </div>
            <div
                class="financial-container"
                id="sector-performance-container"
            ></div>
        </div>

        <!-- Investment Simulator Template -->
        <div id="investment-simulator-template" class="financial-template">
            <div class="financial-options">
                <h4>Choose calculation:</h4>
                <div class="financial-buttons">
                    <button
                        class="financial-button"
                        data-simulator="calculate_returns"
                    >
                        <i class="fas fa-calculator"></i> Calculate Returns
                    </button>
                    <button
                        class="financial-button"
                        data-simulator="compare_to_index"
                    >
                        <i class="fas fa-balance-scale"></i> Compare to Index
                    </button>
                    <button
                        class="financial-button"
                        data-simulator="with_dividends"
                    >
                        <i class="fas fa-hand-holding-usd"></i> With Dividends
                    </button>
                </div>
            </div>
            <div
                class="financial-container"
                id="investment-simulator-container"
            ></div>
        </div>

        <!-- Stock Comparison Template -->
        <div id="stock-comparison-template" class="financial-template">
            <div class="financial-options">
                <h4>Choose comparison:</h4>
                <div class="financial-buttons">
                    <button
                        class="financial-button"
                        data-comparison="fundamentals"
                    >
                        <i class="fas fa-file-alt"></i> Fundamentals
                    </button>
                    <button
                        class="financial-button"
                        data-comparison="performance"
                    >
                        <i class="fas fa-chart-line"></i> Performance
                    </button>
                    <button
                        class="financial-button"
                        data-comparison="valuation"
                    >
                        <i class="fas fa-balance-scale"></i> Valuation
                    </button>
                </div>
            </div>
            <div
                class="financial-container"
                id="stock-comparison-container"
            ></div>
        </div>
    </div>
</div>

<style>
    /* === PROFESSIONAL TRADING ASSISTANT INTERFACE === */

    /* --- Advanced Keyframes --- */
    @keyframes terminalBoot {
        0% {
            opacity: 0;
            transform: scale(0.8) rotateX(10deg);
            filter: blur(10px);
        }
        50% {
            opacity: 0.7;
            transform: scale(0.95) rotateX(5deg);
            filter: blur(2px);
        }
        100% {
            opacity: 1;
            transform: scale(1) rotateX(0deg);
            filter: blur(0px);
        }
    }

    @keyframes holographicShimmer {
        0% {
            background-position: -200% 0;
        }
        100% {
            background-position: 200% 0;
        }
    }

    @keyframes dataStream {
        0% {
            transform: translateY(100%);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(-100%);
            opacity: 0;
        }
    }

    @keyframes neuralPulse {
        0%, 100% {
            box-shadow:
                0 0 0 0 rgba(0, 255, 136, 0.7),
                0 0 0 0 rgba(0, 212, 255, 0.5),
                0 0 0 0 rgba(255, 107, 53, 0.3);
        }
        50% {
            box-shadow:
                0 0 0 10px rgba(0, 255, 136, 0),
                0 0 0 20px rgba(0, 212, 255, 0),
                0 0 0 30px rgba(255, 107, 53, 0);
        }
    }

    @keyframes quantumFloat {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        25% {
            transform: translateY(-8px) rotate(1deg);
        }
        50% {
            transform: translateY(-4px) rotate(-1deg);
        }
        75% {
            transform: translateY(-12px) rotate(0.5deg);
        }
    }

    @keyframes matrixRain {
        0% {
            transform: translateY(-100%);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(100vh);
            opacity: 0;
        }
    }

    @keyframes aiThinking {
        0%, 100% {
            transform: scale(1);
            opacity: 0.7;
        }
        33% {
            transform: scale(1.2);
            opacity: 1;
        }
        66% {
            transform: scale(0.8);
            opacity: 0.5;
        }
    }

    @keyframes terminalGlow {
        0%, 100% {
            filter: drop-shadow(0 0 5px rgba(0, 255, 136, 0.3));
        }
        50% {
            filter: drop-shadow(0 0 20px rgba(0, 255, 136, 0.8));
        }
    }
    
    /* --- Keyframes for Enhanced Animations --- */
    @keyframes floatUp {
        from {
            transform: translateY(40px) scale(0.95);
            opacity: 0;
        }
        to {
            transform: translateY(0) scale(1);
            opacity: 1;
        }
    }

    @keyframes shimmer {
        0% {
            transform: translateX(-100%);
        }
        100% {
            transform: translateX(100%);
        }
    }

    @keyframes counterRoll {
        from {
            transform: rotateX(90deg);
            opacity: 0;
        }
        to {
            transform: rotateX(0deg);
            opacity: 1;
        }
    }

    @keyframes backgroundFloat {
        0% {
            transform: translate(0, 0);
        }
        25% {
            transform: translate(5px, -10px);
        }
        50% {
            transform: translate(-5px, 0px);
        }
        75% {
            transform: translate(3px, -15px);
        }
        100% {
            transform: translate(0, 0);
        }
    }

    @keyframes celebrationFloat {
        0% {
            transform: translateY(0) rotate(0deg) scale(0);
            opacity: 0;
        }
        10% {
            transform: translateY(-10px) rotate(36deg) scale(1);
            opacity: 1;
        }
        90% {
            transform: translateY(-100px) rotate(360deg) scale(1);
            opacity: 1;
        }
        100% {
            transform: translateY(-120px) rotate(360deg) scale(0);
            opacity: 0;
        }
    }

    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    /* --- Enhanced Rating Chart Styles --- */
    .rating-visualization-container {
        font-family:
            -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
            "Helvetica Neue", Arial, sans-serif;
        opacity: 0;
        transform: translateY(20px);
        transition:
            opacity 0.6s ease-out,
            transform 0.6s ease-out;
    }

    .rating-visualization-container.visible {
        opacity: 1;
        transform: translateY(0);
    }

    .enhanced-rating-charts-container {
        margin: 20px 0;
        padding: 25px;
        background: linear-gradient(
            135deg,
            rgba(var(--highlight-color-rgb, 0, 123, 255), 0.08),
            rgba(var(--highlight-color-rgb, 0, 123, 255), 0.03)
        );
        border-radius: 24px;
        border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.25);
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.12),
            0 8px 20px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.15);
        position: relative;
        overflow: hidden;
        opacity: 0;
        transform: translateY(40px) scale(0.95);
        transition: all 1.2s cubic-bezier(0.34, 1.56, 0.64, 1);
        will-change: transform, opacity;
        backdrop-filter: blur(20px);
    }

    .rating-particles-canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        opacity: 0.4;
        z-index: 0;
        filter: blur(1px);
    }

    .shimmer-overlay {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.12),
            transparent
        );
        pointer-events: none;
        z-index: 1;
        animation: shimmer 2.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        will-change: transform;
    }

    .enhanced-rating-charts-container .content-wrapper {
        position: relative;
        z-index: 2;
    }

    .overall-rating-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 25px;
        margin-bottom: 30px;
        padding: 40px;
        background: transparent; /* Remove background for seamless floating effect */
        border-radius: 24px;
        border: none; /* Remove all borders - no visual cages */
        backdrop-filter: none; /* Remove backdrop filter that creates boundaries */
        box-shadow: none; /* Remove box shadows that create container feel */
        transform: translateZ(0);
        transition:
            transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
            filter 0.4s ease; /* Use filter instead of box-shadow */
        position: relative;
        overflow: visible; /* Allow content to flow naturally */
        cursor: pointer;
        min-height: 220px;
        /* Ensure no visual boundaries */
        clip-path: none;
        mask: none;
    }

    /* Remove the ::before pseudo-element that creates visual boundaries */
    .overall-rating-section::before {
        display: none; /* Completely remove the border glow effect */
    }

    /* Removed borderGlow animation - no longer needed */

    .overall-rating-section:hover {
        transform: translateZ(15px) scale(1.03);
        filter: drop-shadow(0 0 30px rgba(0, 212, 255, 0.4))
                drop-shadow(0 0 60px rgba(255, 107, 53, 0.2))
                drop-shadow(0 10px 40px rgba(0, 0, 0, 0.15)); /* Ambient glow instead of box shadows */
    }

    /* Remove hover effects that reference the removed ::before element */

    .overall-rating-section.exceptional-rating {
        animation: exceptionalPulse 3s ease-in-out infinite;
    }

    /* Remove exceptional rating ::before effects */

    @keyframes exceptionalPulse {
        0%, 100% {
            transform: translateZ(0) scale(1);
            filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.3))
                    drop-shadow(0 0 40px rgba(255, 107, 53, 0.15));
        }
        50% {
            transform: translateZ(5px) scale(1.01);
            filter: drop-shadow(0 0 35px rgba(0, 212, 255, 0.5))
                    drop-shadow(0 0 60px rgba(255, 107, 53, 0.3))
                    drop-shadow(0 0 80px rgba(0, 255, 136, 0.2));
        }
    }

    /* Interactive Metric Tiles */
    .metric-tiles-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-top: 20px;
        width: 100%;
    }

    .metric-tile {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 15px 10px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .metric-tile:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(0, 212, 255, 0.5);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
    }

    .metric-tile-label {
        font-size: 0.8rem;
        color: rgba(0, 0, 0, 0.8);
        margin-bottom: 5px;
        font-weight: 500;
    }

    .metric-tile-value {
        font-size: 1.2rem;
        color: #000000;
        font-weight: 700;
    }

    /* Remove shimmer effect that might create visual boundaries */
    .overall-rating-section .shimmer {
        display: none; /* Remove shimmer effect for cleaner look */
    }

    .overall-chart-wrapper,
    .overall-rating-details {
        position: relative;
        z-index: 2;
    }

    /* Ensure overall chart wrapper has no visual boundaries */
    .overall-chart-wrapper {
        background: transparent;
        border: none;
        box-shadow: none;
        overflow: visible;
        clip-path: none;
        mask: none;
    }

    /* Ensure overall chart wrapper SVG has no boundaries */
    .overall-chart-wrapper svg {
        background: transparent;
        border: none;
        box-shadow: none;
        clip-path: none;
        mask: none;
        overflow: visible;
    }

    .overall-rating-details {
        text-align: center;
    }

    .rating-category {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 15px;
        color: #ffffff !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.8), 0 0 8px rgba(0,212,255,0.4) !important;
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }

    .rating-score-text {
        font-size: 3.5rem;
        font-weight: 800;
        line-height: 1;
        margin-bottom: 10px;
        color: white; /* Set text color to white */
        opacity: 0;
        transform: scale(0.5);
        transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        position: relative;
        display: inline-flex;
        align-items: baseline;
        background: transparent; /* Ensure background is transparent */
        border: none; /* Remove any border */
        filter: none; /* Remove any filter that might create a boundary */
    }

    .percent-sign {
        font-size: 2rem;
        opacity: 0.9;
        margin-left: 3px;
        /* Ensure percent sign inherits the same text shadow */
    }

    .rating-description {
        font-size: 1rem;
        font-weight: 500;
        color: #ffffff !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.8) !important;
        margin-top: 8px;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.6s ease-out 0.3s;
    }

    /* Component rating items alignment fix */
    .component-ratings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
        margin-top: 30px;
        align-items: stretch;
    }
    
    /* Ensure consistent card structure */
    .component-rating-item {
        background: rgba(255, 255, 255, 0.05); /* Subtle background that doesn't create boundaries */
        padding: 24px;
        border-radius: 20px; /* Slightly more rounded for modern look */
        text-align: center;
        border: none; /* No visible borders */
        backdrop-filter: blur(8px); /* Subtle blur for depth without boundaries */
        box-shadow: none; /* No hard shadows that create container feel */
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        cursor: pointer;
        position: relative;
        overflow: visible; /* Prevent any clipping */
        opacity: 0;
        transform: scale(0.7) translateY(40px) rotateX(20deg) rotateY(5deg);
        perspective: 1200px;
        transform-style: preserve-3d;
        /* Alignment fixes */
        display: flex;
        flex-direction: column;
        height: 100%;
        padding-bottom: 45px; /* Reduced space for the percentage at bottom */
        /* Ensure no visual boundaries */
        clip-path: none;
        mask: none;
    }
    
    /* Fixed positioning for percentage values */
    .component-score-display {
        font-size: 1.4rem;
        font-weight: 700;
        background: transparent;
        border: none;
        display: inline-flex;
        align-items: baseline;
        justify-content: center;
        width: 100%;
        /* Fixed positioning */
        position: absolute;
        bottom: 15px;
        left: 0;
        right: 0;
        margin: 0 !important;
    }
    
    /* Consistent header height */
    .component-header {
        height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-bottom: 15px;
    }

    /* Component name styling - proper contrast for dark container background */
    .component-name {
        color: #ffffff !important; /* White text for dark container background */
        font-weight: 600;
        font-size: 0.9rem;
        margin-top: 4px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* Subtle shadow for clarity */
    }

    /* Component icon styling */
    .component-icon {
        font-size: 1.8rem;
        margin-bottom: 2px;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)); /* Subtle shadow for icons */
    }

    /* Light theme adjustments - if container background is light */
    [data-theme="light"] .enhanced-rating-charts-container .component-name {
        color: #2c3e50 !important; /* Dark text for light backgrounds */
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }
    
    /* Fixed height for chart wrapper */
    .component-chart-wrapper {
        height: 80px;
        margin-bottom: 25px !important;
        display: flex;
        align-items: center;
        justify-content: center;
        /* Ensure no visual boundaries around charts */
        background: transparent;
        border: none;
        box-shadow: none;
        overflow: visible;
        clip-path: none;
        mask: none;
    }
    
    /* Specific fix for valuation card */
    .component-ratings-grid .component-rating-item:nth-child(3) .component-score-display {
        bottom: 24px;
    }
    
    /* Ensure SVG charts are centered and boundary-free */
    .component-chart-wrapper svg {
        display: block;
        margin: 0 auto;
        /* Remove any potential visual boundaries */
        background: transparent;
        border: none;
        box-shadow: none;
        clip-path: none;
        mask: none;
        overflow: visible;
    }

    .component-rating-item .hover-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
    }

    .component-rating-item:hover {
        transform: scale(1.15) translateY(-15px) rotateX(-10deg) rotateY(-3deg);
        box-shadow: none; /* Remove traditional box shadow */
        filter: drop-shadow(0 0 25px var(--comp-color-transparent, #007bff40))
                drop-shadow(0 8px 32px rgba(0, 0, 0, 0.15)); /* Enhanced glow effect */
        animation: backgroundFloat 3s ease-in-out infinite;
    }

    .component-rating-item:hover .hover-overlay {
        opacity: 1;
    }

    .component-header,
    .component-chart-wrapper,
    .component-score-display {
        position: relative;
        z-index: 2;
    }

    .success-celebration {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 10;
        overflow: hidden;
    }

    .success-celebration div {
        position: absolute;
        font-size: 1.5rem;
        animation: celebrationFloat 3s ease-out forwards;
    }
    [data-theme="dark"] .enhanced-rating-charts-container {
        background: linear-gradient(
            135deg,
            rgba(var(--highlight-color-rgb, 121, 40, 202), 0.12),
            rgba(var(--highlight-color-rgb, 121, 40, 202), 0.05)
        );
        border-color: rgba(var(--highlight-color-rgb, 121, 40, 202), 0.3);
    }
    [data-theme="dark"] .component-rating-item {
        background: rgba(255, 255, 255, 0.08); /* Slightly more visible in dark theme */
        border: none; /* No borders for seamless look */
    }

    /* === PROFESSIONAL ASSISTANT FAB === */
    #chatbot-toggle {
        position: fixed;
        bottom: 30px;
        right: 30px; /* Always positioned on the right */
        width: 68px;
        height: 68px;
        background: linear-gradient(135deg,
            var(--highlight-color) 0%,
            var(--highlight-darker) 100%);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1051;
        backdrop-filter: blur(15px);
        box-shadow:
            0 8px 32px rgba(var(--highlight-color-rgb), 0.3),
            0 4px 16px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    /* Dark mode support for chatbot toggle */
    [data-theme="dark"] #chatbot-toggle {
        background: linear-gradient(135deg,
            var(--highlight-color) 0%,
            var(--highlight-darker) 100%);
        box-shadow:
            0 8px 32px rgba(var(--highlight-color-rgb), 0.4),
            0 4px 16px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    #chatbot-toggle::before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        background: linear-gradient(135deg,
            rgba(var(--highlight-color-rgb), 0.8) 0%,
            var(--highlight-darker) 100%);
        border-radius: 50%;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    #chatbot-toggle:hover::before {
        opacity: 1;
    }

    .chatbot-icon {
        font-size: 24px;
        line-height: 1;
        position: relative;
        z-index: 2;
        transition: transform 0.3s ease;
    }

    .chatbot-icon::after {
        content: "PRO";
        position: absolute;
        top: -10px;
        right: -10px;
        font-size: 8px;
        font-weight: 700;
        background: rgba(255, 255, 255, 0.9);
        color: var(--highlight-darker);
        padding: 2px 4px;
        border-radius: 3px;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }

    #chatbot-toggle:hover {
        transform: scale(1.08);
        box-shadow:
            0 12px 40px rgba(var(--highlight-color-rgb), 0.4),
            0 6px 20px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    /* Dark mode adjustments for PRO badge */
    [data-theme="dark"] .chatbot-icon::after {
        background: rgba(255, 255, 255, 0.95);
        color: var(--highlight-darker);
    }

    #chatbot-toggle:hover .chatbot-icon {
        transform: scale(1.1);
    }
    .pulse-ring-outer,
    .pulse-ring-inner {
        content: "";
        position: absolute;
        border-radius: 50%;
        animation: sophisticatedPulse 2.2s infinite cubic-bezier(0.66, 0, 0, 1);
        box-shadow: 0 0 1px rgba(var(--highlight-color-rgb), 0.3);
        z-index: -1;
        border: 2px solid rgba(var(--highlight-color-rgb), 0.2);
    }
    .pulse-ring-outer {
        width: 100%;
        height: 100%;
    }
    .pulse-ring-inner {
        width: 100%;
        height: 100%;
        animation-delay: -0.6s;
    }
    @keyframes sophisticatedPulse {
        to {
            opacity: 0;
            transform: scale(2);
        }
    }
    #chatbot-window:not(.hidden) ~ #chatbot-toggle .pulse-ring-outer,
    #chatbot-window:not(.hidden) ~ #chatbot-toggle .pulse-ring-inner {
        animation: none;
        opacity: 0;
    }
    #chatbot-window:not(.hidden) ~ #chatbot-toggle {
        transform: scale(0);
        opacity: 0;
        pointer-events: none;
    }

    #chatbot-window {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        width: min(50vw, 800px);
        height: 100%;
        background: var(--chatbot-bg);
        backdrop-filter: blur(20px) saturate(1.1);
        -webkit-backdrop-filter: blur(20px) saturate(1.1);
        border-radius: 0;
        box-shadow: var(--chatbot-shadow);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        z-index: 1050;
        transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        transform-origin: right center;
        border-left: 1px solid var(--chatbot-border-color);
        transform: translateX(100%);
        opacity: 0;
        color: var(--chatbot-text-color);
    }

    #chatbot-window:not(.hidden) {
        transform: translateX(0%);
        opacity: 1;
    }

    #chatbot-window.hidden {
        transform: translateX(100%);
        opacity: 0;
        pointer-events: none;
    }

    /* Dark theme adjustments */
    [data-theme="dark"] #chatbot-window {
        background: var(--chatbot-bg);
        box-shadow: var(--chatbot-shadow);
        border-left: 1px solid var(--chatbot-border-color);
        color: var(--chatbot-text-color);
    }
    #chatbot-header {
        background: var(--chatbot-header-bg);
        color: white;
        padding: 18px 24px;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        box-shadow:
            0 4px 20px rgba(var(--highlight-color-rgb), 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        z-index: 2;
    }

    /* Dark theme header */
    [data-theme="dark"] #chatbot-header {
        background: var(--chatbot-header-bg);
        box-shadow:
            0 4px 20px rgba(var(--highlight-color-rgb), 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    #chatbot-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.1),
            transparent);
        animation: headerShimmer 4s ease-in-out infinite;
        z-index: 1;
    }

    @keyframes headerShimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }
    .bot-avatar-header {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        padding: 6px;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 36px;
        height: 36px;
        backdrop-filter: blur(10px);
        transition: transform 0.3s ease;
    }

    .bot-avatar-header:hover {
        transform: scale(1.05);
    }

    .chatbot-icon-small {
        font-size: 16px;
        color: white;
        line-height: 1;
        z-index: 2;
        position: relative;
    }

    #chatbot-header h3 {
        margin: 0;
        font-size: 1.3rem;
        font-weight: 700;
        flex-grow: 1;
        display: flex;
        align-items: center;
        gap: 10px;
        position: relative;
        z-index: 2;
    }

    #chatbot-header h3::before {
        content: "💼";
        font-size: 1.1rem;
    }

    #chatbot-header h3::after {
        content: "PROFESSIONAL";
        font-size: 0.7rem;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 3px 6px;
        border-radius: 4px;
        margin-left: auto;
        font-weight: 600;
        letter-spacing: 0.5px;
        backdrop-filter: blur(10px);
    }
    #chatbot-close {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.8);
        font-size: 1.4rem;
        line-height: 1;
        cursor: pointer;
        padding: 6px;
        border-radius: 6px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    #chatbot-close:hover {
        background: rgba(220, 53, 69, 0.9);
        border-color: rgba(220, 53, 69, 1);
        color: white;
        transform: rotate(90deg) scale(1.05);
    }
    #chatbot-messages {
        flex-grow: 1;
        padding: 20px 20px 15px 20px;
        overflow-y: auto;
        list-style: none;
        margin: 0;
        background: var(--chatbot-bg);
        scrollbar-width: thin;
        scrollbar-color: rgba(var(--highlight-color-rgb), 0.4) transparent;
        min-height: 0;
        position: relative;
        z-index: 1;
        color: var(--chatbot-text-color);
    }

    /* Dark theme messages background */
    [data-theme="dark"] #chatbot-messages {
        background: var(--chatbot-bg);
        scrollbar-color: rgba(var(--highlight-color-rgb), 0.4) transparent;
        color: var(--chatbot-text-color);
    }

    /* Professional scrollbar */
    #chatbot-messages::-webkit-scrollbar {
        width: 6px;
    }
    #chatbot-messages::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 3px;
    }
    #chatbot-messages::-webkit-scrollbar-thumb {
        background: rgba(var(--highlight-color-rgb), 0.4);
        border-radius: 3px;
        transition: background 0.3s ease;
    }
    #chatbot-messages::-webkit-scrollbar-thumb:hover {
        background: rgba(var(--highlight-color-rgb), 0.6);
    }

    /* Dark theme scrollbar */
    [data-theme="dark"] #chatbot-messages::-webkit-scrollbar-thumb {
        background: rgba(var(--highlight-color-rgb), 0.4);
    }
    [data-theme="dark"] #chatbot-messages::-webkit-scrollbar-thumb:hover {
        background: rgba(var(--highlight-color-rgb), 0.6);
    }

    .message {
        display: flex;
        margin-bottom: 20px;
        max-width: 85%;
        opacity: 0;
        transform: translateY(20px) scale(0.95);
        animation: terminalBoot 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        position: relative;
        z-index: 2;
    }

    .message.initial-wave {
        animation: none;
        opacity: 1;
        transform: none;
    }

    .message p {
        margin: 0;
        padding: 16px 20px;
        border-radius: 18px;
        max-width: 100%;
        line-height: 1.6;
        word-wrap: break-word;
        position: relative;
        font-size: 0.95rem;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        font-weight: 400;
    }
    .message.user {
        align-self: flex-end;
        flex-direction: row-reverse;
    }
    .message.user .bot-avatar {
        margin-left: 10px;
        margin-right: 0;
    }
    .message.bot {
        align-self: flex-start;
    }
    .message.bot .bot-avatar {
        margin-right: 10px;
    }
    .message p strong {
        color: var(--chatbot-accent-color);
        font-weight: 600;
    }
    .message.bot p {
        background: var(--chatbot-bot-msg-bg);
        color: var(--chatbot-text-color);
        border-top-left-radius: 4px;
        border: 1px solid var(--chatbot-border-color);
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.08),
            0 0 0 1px rgba(var(--highlight-color-rgb), 0.1);
        position: relative;
    }

    /* Dark theme bot messages */
    [data-theme="dark"] .message.bot p {
        background: var(--chatbot-bot-msg-bg);
        color: var(--chatbot-text-color);
        border: 1px solid var(--chatbot-border-color);
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(var(--highlight-color-rgb), 0.2);
    }

    .message.bot p::before {
        content: "💼";
        position: absolute;
        top: -8px;
        left: 12px;
        font-size: 12px;
        background: rgba(0, 123, 255, 0.9);
        color: white;
        padding: 2px 5px;
        border-radius: 4px;
        line-height: 1;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    /* Dark theme bot badge */
    [data-theme="dark"] .message.bot p::before {
        background: rgba(140, 79, 255, 0.9);
        box-shadow: 0 2px 8px rgba(140, 79, 255, 0.3);
    }

    .message.user p {
        background: var(--chatbot-user-msg-bg);
        color: var(--chatbot-user-msg-text);
        border-top-right-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow:
            0 4px 15px rgba(var(--highlight-color-rgb), 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    /* Dark theme user messages */
    [data-theme="dark"] .message.user p {
        background: var(--chatbot-user-msg-bg);
        color: var(--chatbot-user-msg-text);
        box-shadow:
            0 4px 15px rgba(var(--highlight-color-rgb), 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    
    .message.bot.hint p {
        background-color: rgba(var(--highlight-color-rgb, 0, 123, 255), 0.1);
        border: 1px dashed rgba(var(--highlight-color-rgb, 0, 123, 255), 0.3);
        font-style: italic;
    }

    @keyframes messageFadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    #chatbot-input-area-wrapper {
        position: relative;
        flex-shrink: 0;
        background: var(--chatbot-bg);
        border-top: 1px solid var(--chatbot-border-color);
        z-index: 2;
    }

    /* Dark theme input wrapper */
    [data-theme="dark"] #chatbot-input-area-wrapper {
        background: var(--chatbot-bg);
        border-top: 1px solid var(--chatbot-border-color);
    }

    #chatbot-input-area-wrapper::before {
        content: "🔒 Professional Financial Analysis • Powered by Advanced AI";
        position: absolute;
        top: -25px;
        left: 20px;
        right: 20px;
        font-size: 10px;
        color: rgba(var(--highlight-color-rgb), 0.7);
        text-align: center;
        font-weight: 600;
        letter-spacing: 0.5px;
        padding: 4px 0;
        border-top: 1px solid var(--chatbot-border-color);
    }

    /* Dark theme input wrapper text */
    [data-theme="dark"] #chatbot-input-area-wrapper::before {
        color: rgba(var(--highlight-color-rgb), 0.7);
        border-top: 1px solid var(--chatbot-border-color);
    }

    #chatbot-input-area {
        padding: 20px 25px;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        gap: 15px;
        position: relative;
        z-index: 2;
    }
    #chatbot-input {
        flex-grow: 1;
        border: 1px solid var(--chatbot-border-color);
        border-radius: 20px;
        padding: 12px 18px;
        font-size: 0.95rem;
        background: var(--chatbot-input-bg);
        color: var(--chatbot-text-color);
        outline: none;
        transition: all 0.3s ease;
        resize: none;
        height: 44px;
        backdrop-filter: blur(10px);
        font-weight: 400;
    }

    /* Dark theme input */
    [data-theme="dark"] #chatbot-input {
        background: var(--chatbot-input-bg);
        color: var(--chatbot-text-color);
        border: 1px solid var(--chatbot-border-color);
    }

    #chatbot-input:focus {
        border-color: rgba(var(--highlight-color-rgb), 0.6);
        box-shadow:
            0 0 0 3px rgba(var(--highlight-color-rgb), 0.15),
            0 4px 12px rgba(var(--highlight-color-rgb), 0.2);
    }

    /* Dark theme input focus */
    [data-theme="dark"] #chatbot-input:focus {
        border-color: rgba(var(--highlight-color-rgb), 0.6);
        box-shadow:
            0 0 0 3px rgba(var(--highlight-color-rgb), 0.15),
            0 4px 12px rgba(var(--highlight-color-rgb), 0.2);
    }

    #chatbot-input::placeholder {
        color: var(--chatbot-muted-text-color);
        opacity: 1;
        font-weight: 400;
    }

    /* Dark theme placeholder */
    [data-theme="dark"] #chatbot-input::placeholder {
        color: var(--chatbot-muted-text-color);
    }

    #chatbot-send {
        background: var(--chatbot-header-bg);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        flex-shrink: 0;
        font-weight: 600;
        font-size: 1rem;
        backdrop-filter: blur(10px);
        box-shadow:
            0 4px 12px rgba(var(--highlight-color-rgb), 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    /* Dark theme send button */
    [data-theme="dark"] #chatbot-send {
        background: var(--chatbot-header-bg);
        box-shadow:
            0 4px 12px rgba(var(--highlight-color-rgb), 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    #chatbot-send:hover {
        transform: scale(1.05);
        box-shadow:
            0 6px 16px rgba(var(--highlight-color-rgb), 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    /* Dark theme send button hover */
    [data-theme="dark"] #chatbot-send:hover {
        box-shadow:
            0 6px 16px rgba(var(--highlight-color-rgb), 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    #chatbot-send:active {
        transform: rotate(45deg) translateX(100%);
    }

    #chatbot-send:active {
        transform: scale(0.95) rotate(0deg);
        box-shadow:
            0 0 15px rgba(0, 255, 136, 0.4),
            inset 0 0 10px rgba(0, 0, 0, 0.2);
    }
    #chatbot-send:disabled {
        background-color: var(--text-muted-color);
        cursor: not-allowed;
        opacity: 0.7;
    }
    #chatbot-send i {
        font-size: 1rem;
        margin-left: 2px;
    }
    #typing-indicator {
        display: flex;
        align-items: center;
        padding: 8px 18px;
        position: absolute;
        bottom: 100%;
        left: 0;
        right: 0;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        margin-bottom: 5px;
    }
    #typing-indicator:not(.hidden) {
        opacity: 1;
    }
    .bot-avatar-typing {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        background-color: var(--chatbot-avatar-bg);
        border-radius: 50%;
        margin-right: 10px;
    }
    .dots {
        display: flex;
        align-items: center;
        margin-left: 0;
    }
    .dots span {
        width: 7px;
        height: 7px;
        background-color: var(--chatbot-muted-text-color);
        border-radius: 50%;
        margin: 0 3px;
        display: inline-block;
        animation: typingBounce 1.4s infinite ease-in-out both;
    }
    .dots span:nth-child(1) {
        animation-delay: -0.32s;
    }
    .dots span:nth-child(2) {
        animation-delay: -0.16s;
    }
    @keyframes typingBounce {
        0%,
        80%,
        100% {
            transform: scale(0);
            opacity: 0.5;
        }
        40% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* --- Button Styling Fix & Enhancement --- */
    .button-group {
        display: flex !important;
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 10px !important;
        margin: 15px 0 !important;
        padding: 15px !important;
        width: 100% !important;
        background: rgba(
            var(--highlight-color-rgb, 0, 123, 255),
            0.04
        ) !important;
        border-radius: 12px !important;
        border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.1) !important;
        box-sizing: border-box !important;
    }

    .chatbot-button {
        all: unset !important;
        box-sizing: border-box !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 10px 18px !important;
        font-size: 0.9rem !important;
        font-weight: 600 !important;
        line-height: 1.4 !important;
        color: #fff !important;
        border-radius: 8px !important;
        text-align: center !important;
        cursor: pointer !important;
        transition:
            transform 0.2s ease,
            box-shadow 0.2s ease,
            background 0.3s ease !important;
        position: relative !important;
        z-index: 1 !important;
    }

    .chatbot-button:disabled {
        cursor: not-allowed !important;
        opacity: 0.6 !important;
        transform: none !important;
        box-shadow: none !important;
    }

    .chatbot-button:hover:not(:disabled) {
        transform: translateY(-2px) !important;
    }

    .llm-model-button {
        background: linear-gradient(
            135deg,
            #007bff 0%,
            #0056b3 100%
        ) !important;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3) !important;
    }
    .llm-model-button:hover:not(:disabled) {
        background: linear-gradient(
            135deg,
            #0069d9 0%,
            #004a99 100%
        ) !important;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
    }

    .skip-button {
        background: linear-gradient(
            135deg,
            #6c757d 0%,
            #5a6268 100%
        ) !important;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3) !important;
    }
    .skip-button:hover:not(:disabled) {
        background: linear-gradient(
            135deg,
            #5a6268 0%,
            #495057 100%
        ) !important;
        box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4) !important;
    }

    /* --- Settings Panel --- */
    #chatbot-settings {
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.85);
        font-size: 1.2rem;
        cursor: pointer;
        padding: 5px;
        margin: 0 8px;
        transition:
            transform 0.3s ease,
            color 0.2s ease;
    }
    #chatbot-settings:hover {
        color: white;
        transform: rotate(15deg);
    }
    #chatbot-settings-panel {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 300px;
        max-width: 80%;
        background-color: var(--chatbot-bg);
        border-right: 1px solid var(--chatbot-border-color);
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        z-index: 1060;
        display: flex;
        flex-direction: column;
        transform: translateX(-100%);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
    }
    #chatbot-container.settings-open #chatbot-settings-panel {
        transform: translateX(0%);
    }
    #chatbot-container.settings-open #chatbot-window {
        margin-left: 300px;
    }
    #settings-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 18px;
        background: var(--chatbot-header-bg);
        color: white;
        flex-shrink: 0;
        border-bottom: 1px solid var(--chatbot-border-color);
    }
    #settings-header h4 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }
    #settings-header h4 i {
        margin-right: 8px;
        font-size: 1rem;
    }
    #settings-close {
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        font-size: 1.8rem;
        line-height: 1;
        cursor: pointer;
        padding: 0 5px;
        transition:
            color 0.2s ease,
            transform 0.2s ease;
    }
    #settings-close:hover {
        color: white;
        transform: rotate(90deg);
    }

    #settings-content {
        padding: 15px 18px;
        flex-grow: 1;
        overflow-y: auto;
        background-color: var(--chatbot-bg);
        color: var(--chatbot-text-color);
    }

    /* Settings form elements dark mode support */
    #settings-content input,
    #settings-content select,
    #settings-content textarea {
        background-color: var(--chatbot-input-bg);
        color: var(--chatbot-text-color);
        border: 1px solid var(--chatbot-border-color);
    }

    #settings-content label {
        color: var(--chatbot-text-color);
    }

    #settings-content .form-text {
        color: var(--chatbot-muted-text-color);
    }

    #settings-content button {
        background: var(--chatbot-header-bg);
        color: white;
        border: none;
    }

    #settings-content button:hover {
        opacity: 0.9;
    }

    /* Dark theme adjustments for settings */
    [data-theme="dark"] #settings-content {
        background-color: var(--chatbot-bg);
        color: var(--chatbot-text-color);
    }

    /* Advanced Visualization Containers */
    .valuation-sensitivity-matrix-wrapper,
    .peer-valuation-comparison-wrapper,
    .capital-allocation-breakdown-wrapper,
    .revenue-margin-history-wrapper,
    .risk-impact-map-wrapper {
        margin: 15px 0;
        padding: 20px;
        background: linear-gradient(135deg,
            rgba(26, 26, 26, 0.95) 0%,
            rgba(45, 45, 45, 0.95) 100%);
        border-radius: 16px;
        border: 1px solid rgba(0, 212, 255, 0.3);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-height: auto;
    }

    .valuation-sensitivity-matrix-wrapper svg,
    .peer-valuation-comparison-wrapper svg,
    .capital-allocation-breakdown-wrapper svg,
    .revenue-margin-history-wrapper svg,
    .risk-impact-map-wrapper svg {
        max-width: 100%;
        height: auto;
        display: block;
    }

    .valuation-sensitivity-matrix-wrapper::before,
    .peer-valuation-comparison-wrapper::before,
    .capital-allocation-breakdown-wrapper::before,
    .revenue-margin-history-wrapper::before,
    .risk-impact-map-wrapper::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(0, 212, 255, 0.1),
            transparent);
        animation: chartShimmer 4s infinite;
    }

    @keyframes chartShimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* Executive Summary Section */
    .message-text-content h3:contains("EXECUTIVE SUMMARY"),
    .message-text-content h3[id*="executive"],
    .message-text-content h3[id*="summary"] {
        background: linear-gradient(135deg,
            rgba(0, 212, 255, 0.2) 0%,
            rgba(255, 107, 53, 0.2) 100%);
        padding: 20px;
        border-radius: 16px;
        border: 2px solid rgba(0, 212, 255, 0.3);
        margin: 30px 0 20px 0;
        color: #ffffff !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.8);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
    }

    .message-text-content h3:contains("EXECUTIVE SUMMARY")::before,
    .message-text-content h3[id*="executive"]::before,
    .message-text-content h3[id*="summary"]::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.1),
            transparent);
        animation: summaryShimmer 3s infinite;
    }

    @keyframes summaryShimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    @media (max-width: 576px) {
        #chatbot-window {
            width: 100vw;
            border-left: none;
            height: 100%;
            bottom: 0;
            right: 0;
            top: 0;
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
        }
        #chatbot-container.settings-open #chatbot-window {
            margin-left: 0;
        }
        #chatbot-settings-panel {
            width: 85%;
        }
    }
</style>

<!-- Include D3.js library for advanced visualizations -->
<script src="https://d3js.org/d3.v7.min.js"></script>

<!-- Particle.js for enhanced backgrounds -->
<script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>

<script>
    document.addEventListener("DOMContentLoaded", () => {
        const container = document.getElementById("chatbot-container");
        const chatbotToggle = container.querySelector("#chatbot-toggle");
        const chatbotWindow = container.querySelector("#chatbot-window");
        const chatbotClose = container.querySelector("#chatbot-close");
        const chatbotMessages = container.querySelector("#chatbot-messages");
        const chatbotInput = container.querySelector("#chatbot-input");
        const chatbotSend = container.querySelector("#chatbot-send");
        const typingIndicator = container.querySelector("#typing-indicator");

        const chatbotSettingsButton =
            container.querySelector("#chatbot-settings");
        const chatbotSettingsPanel = container.querySelector(
            "#chatbot-settings-panel",
        );
        const settingsCloseButton = container.querySelector("#settings-close");
        const settingsForm = container.querySelector("#settings-form");
        const settingsLoading = container.querySelector("#settings-loading");
        const settingsStatus = container.querySelector("#settings-status");
        const clearKeysButton = container.querySelector("#clear-keys-button");
        const settingsFormContent = settingsForm
            ? settingsForm.querySelector(".form-section")
            : null;

        let isBotTyping = false;
        let isOpen = !chatbotWindow.classList.contains("hidden");
        let currentBotAction = null;
        
        // Add tracking for the current analysis state
        let currentAnalysisState = {
            active: false,
            ticker: null,
            modelSelected: false,
            modelType: null
        };

        // Function to reset analysis state
        function resetAnalysisState() {
            currentAnalysisState = {
                active: false,
                ticker: null,
                modelSelected: false,
                modelType: null
            };
            console.log("Analysis state reset");
        }

        // Function to detect if a message is an analysis question about a specific ticker
        function isAnalysisQuestion(message, ticker) {
            if (!ticker) return false;
            
            // This checks if the message contains the ticker symbol and analysis-related terms
            const tickerRegex = new RegExp(`\\b${ticker}\\b`, 'i');
            if (!tickerRegex.test(message)) return false;
            
            const analysisTerms = [
                'opinion', 'thoughts', 'think about', 'analysis', 'evaluate',
                'assessment', 'outlook', 'prospects', 'future', 'performance',
                'financials', 'financial health', 'balance sheet', 'income', 'revenue',
                'earnings', 'profit', 'growth', 'valuation', 'price', 'worth', 'value',
                'recommendation', 'buy', 'sell', 'hold', 'invest', 'investment',
                'strength', 'weakness', 'opportunity', 'threat', 'risk', 'potential',
                'company', 'business', 'market', 'industry', 'sector', 'competition',
                'competitive', 'advantage', 'moat', 'management', 'leadership',
                'strategy', 'plan', 'innovation', 'product', 'service', 'customer'
            ];
            
            return analysisTerms.some(term => message.toLowerCase().includes(term.toLowerCase()));
        }

        // Function to handle analysis commands
        function handleAnalysisCommand(message) {
            // This regex specifically matches only basic analysis commands, not questions about the stock
            const analysisRegex = /^(?:analyse|analyze|analysis of|stock analysis for|give me an analysis of)\s+([A-Z0-9\.\-]{1,10}\b)$/i;
            const match = message.match(analysisRegex);

            if (!match) {
                // Check if this is an actual analysis question about a ticker we're already analyzing
                if (currentAnalysisState.active &&
                    currentAnalysisState.modelSelected &&
                    isAnalysisQuestion(message, currentAnalysisState.ticker)) {
                    // This is a proper analysis question, let it through to the LLM
                    console.log(`Detected analysis question about ${currentAnalysisState.ticker}`);
                    return false;
                }
                return false;
            }

            const ticker = match[1].toUpperCase();

            // Check if we have an active analysis with model selected for the same ticker
            if (currentAnalysisState.active &&
                currentAnalysisState.modelSelected &&
                currentAnalysisState.ticker === ticker) {

                // IMPORTANT CHANGE: If the message is exactly "analyse [ticker]" after model selection,
                // treat it as a valid analysis request and pass it through to the LLM
                console.log("Treating 'analyse " + ticker + "' as a valid analysis request");

                // We'll return false here to allow the message to go through to the normal processing
                // and get sent to the LLM for full analysis
                return false;
            }

            // Different ticker or no active analysis - start a new one
            if (currentAnalysisState.active && currentAnalysisState.ticker !== ticker) {
                // Different ticker - reset state
                resetAnalysisState();
            }

            // IMPORTANT: Don't set currentAnalysisState here as it causes the loop
            // Let the backend handle the state and only update frontend state when we get responses
            console.log(`Analysis command detected for ${ticker} - letting backend handle state`);
            return false; // Continue with regular message processing
        }

        if (
            !container ||
            !chatbotToggle ||
            !chatbotWindow ||
            !chatbotClose ||
            !chatbotMessages ||
            !chatbotInput ||
            !chatbotSend ||
            !typingIndicator
        ) {
            console.error(
                "One or more core chatbot elements are missing! Chatbot disabled.",
            );
            if (chatbotToggle) chatbotToggle.style.display = "none";
            return;
        }

        if (
            !chatbotSettingsButton ||
            !chatbotSettingsPanel ||
            !settingsCloseButton ||
            !settingsForm ||
            !settingsLoading ||
            !settingsStatus ||
            !clearKeysButton ||
            !settingsFormContent
        ) {
            console.warn(
                "Chatbot Settings Panel elements missing! Disabling settings button.",
            );
            if (chatbotSettingsButton)
                chatbotSettingsButton.style.display = "none";
        }

        chatbotToggle.addEventListener("click", toggleChatWindow);
        chatbotClose.addEventListener("click", closeChatWindow);
        chatbotSend.addEventListener("click", handleSendMessage);
        chatbotInput.addEventListener("keypress", (e) => {
            if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
            }
        });
        document.addEventListener("keydown", (e) => {
            if (e.key === "Escape" && isOpen) {
                closeChatWindow();
            }
        });

        if (
            chatbotSettingsButton &&
            chatbotSettingsPanel &&
            settingsCloseButton &&
            settingsForm &&
            clearKeysButton &&
            settingsFormContent
        ) {
            chatbotSettingsButton.addEventListener("click", () => {
                container.classList.add("settings-open");
                populateSettingsForm();
            });

            settingsCloseButton.addEventListener("click", () => {
                container.classList.remove("settings-open");
            });

            settingsForm.addEventListener("submit", async (event) => {
                event.preventDefault();
                settingsStatus.innerHTML =
                    '<div class="alert alert-info small p-2 text-center mb-0">Saving...</div>';
                settingsStatus.className = "mt-3";

                const finalPayload = {};
                const defaultLlmSelect = settingsForm.querySelector(
                    "#settings-default-llm",
                );
                if (defaultLlmSelect) {
                    finalPayload["default_llm"] = defaultLlmSelect.value;
                } else {
                    settingsStatus.innerHTML = `<div class="alert alert-danger small p-2 mb-0">Error: Could not find LLM selection.</div>`;
                    settingsStatus.className = "mt-3 error";
                    return;
                }

                const apiKeyContainer = document.getElementById(
                    "settings-api-key-container",
                );
                const apiKeyInput = apiKeyContainer
                    ? apiKeyContainer.querySelector(".api-key-input")
                    : null;

                if (apiKeyInput) {
                    const apiKeyName = apiKeyInput.name;
                    const apiKeyValue = apiKeyInput.value.trim();
                    if (apiKeyName && apiKeyValue) {
                        finalPayload[apiKeyName] = apiKeyValue;
                    }
                }

                try {
                    const response = await fetch(
                        "/api/chatbot/update_settings",
                        {
                            method: "POST",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify(finalPayload),
                        },
                    );
                    const result = await response.json();
                    if (response.ok && result.success) {
                        settingsStatus.innerHTML = `<div class="alert alert-success small p-2 text-center mb-0">${result.message}</div>`;
                        settingsStatus.className = "mt-3 success";
                        await populateSettingsForm();
                    } else {
                        let errorDetails = "";
                        if (
                            result.details &&
                            typeof result.details === "object"
                        ) {
                            errorDetails = '<ul class="list-unstyled mb-0">';
                            for (const key in result.details) {
                                errorDetails += `<li><small>${result.details[key]}</small></li>`;
                            }
                            errorDetails += "</ul>";
                        } else if (result.details) {
                            errorDetails = `<p class="mb-0"><small>${result.details}</small></p>`;
                        }
                        settingsStatus.innerHTML = `<div class="alert alert-danger small p-2 mb-0">${result.error || "Failed to save settings."}${errorDetails}</div>`;
                        settingsStatus.className = "mt-3 error";
                    }
                } catch (error) {
                    console.error("Error saving settings:", error);
                    settingsStatus.innerHTML = `<div class="alert alert-danger small p-2 mb-0">Error: ${error.message}</div>`;
                    settingsStatus.className = "mt-3 error";
                }
            });

            clearKeysButton.addEventListener("click", async () => {
                if (
                    !confirm(
                        "Are you sure you want to clear all stored API keys? This action cannot be undone.",
                    )
                ) {
                    return;
                }
                settingsStatus.innerHTML =
                    '<div class="alert alert-info small p-2 text-center mb-0">Clearing keys...</div>';
                settingsStatus.className = "mt-3";

                try {
                    const response = await fetch("/api/chatbot/clear_keys", {
                        method: "POST",
                    });
                    const result = await response.json();
                    if (response.ok && result.success) {
                        settingsStatus.innerHTML =
                            '<div class="alert alert-success small p-2 text-center mb-0">API keys cleared successfully!</div>';
                        settingsStatus.className = "mt-3 success";
                        populateSettingsForm();
                    } else {
                        settingsStatus.innerHTML = `<div class="alert alert-danger small p-2 text-center mb-0">${result.message || "Error clearing keys."}</div>`;
                        settingsStatus.className = "mt-3 error";
                    }
                } catch (error) {
                    console.error("Error clearing keys:", error);
                    settingsStatus.innerHTML =
                        '<div class="alert alert-danger small p-2 text-center mb-0">An error occurred while clearing keys.</div>';
                    settingsStatus.className = "mt-3 error";
                }
            });
        }

        let availableModelsData = [];

        function renderApiKeyInput(modelKey, availableModels) {
            const container = document.getElementById(
                "settings-api-key-container",
            );
            if (!container || !availableModels) {
                if (container)
                    container.innerHTML =
                        '<p class="text-muted small">Could not render API key input.</p>';
                return;
            }
            const modelData = availableModels.find((m) => m.key === modelKey);
            if (!modelData) {
                container.innerHTML =
                    '<p class="text-muted small">Select a valid Default LLM to manage its API key.</p>';
                return;
            }
            const keyName = `api_key_${modelKey}`;
            const keyExists = modelData.api_key_set;
            const placeholder = keyExists
                ? "Key Set (Enter new to replace)"
                : `Enter ${modelData.name} API Key`;
            const statusClass = keyExists ? "set" : "not-set";
            const statusText = keyExists ? "Key Set" : "Key Not Set";
            const inputHtml = `
                <div class="mb-3 dynamic-api-key-section" data-model="${modelKey}">
                    <label for="${modelKey}-settings-api-key" class="form-label">${modelData.name} API Key</label>
                    <div class="input-group input-group-sm">
                        <input type="password" class="form-control api-key-input" id="${modelKey}-settings-api-key" name="${keyName}" placeholder="${placeholder}" autocomplete="new-password">
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('${modelKey}-settings-api-key')"><i class="fas fa-eye"></i></button>
                    </div>
                    <small class="api-key-status ${statusClass}">(${statusText})</small>
                </div>
            `;
            container.innerHTML = inputHtml;
        }

        async function populateSettingsForm() {
            const defaultLlmSelect = settingsForm.querySelector(
                "#settings-default-llm",
            );
            if (
                !defaultLlmSelect ||
                !settingsLoading ||
                !settingsStatus ||
                !settingsForm
            ) {
                return;
            }

            settingsLoading.style.display = "block";
            settingsForm.style.display = "none";
            settingsStatus.innerHTML = "";
            settingsStatus.className = "mt-3";

            try {
                const response = await fetch("/api/chatbot/get_settings");
                if (!response.ok)
                    throw new Error(`HTTP error! status: ${response.status}`);
                const settings = await response.json();

                availableModelsData = settings.available_models || [];
                defaultLlmSelect.innerHTML = "";

                if (availableModelsData.length > 0) {
                    availableModelsData.forEach((model) => {
                        const option = document.createElement("option");
                        option.value = model.key;
                        option.textContent = model.name;
                        if (model.key === settings.current_llm_choice) {
                            option.selected = true;
                        }
                        defaultLlmSelect.appendChild(option);
                    });
                } else {
                    const option = document.createElement("option");
                    option.textContent = "No models available";
                    option.disabled = true;
                    defaultLlmSelect.appendChild(option);
                }

                renderApiKeyInput(
                    settings.current_llm_choice,
                    availableModelsData,
                );

                defaultLlmSelect.addEventListener("change", (event) => {
                    renderApiKeyInput(event.target.value, availableModelsData);
                });

                settingsLoading.style.display = "none";
                settingsForm.style.display = "block";
            } catch (error) {
                console.error("Error populating settings form:", error);
                settingsLoading.style.display = "none";
                settingsStatus.innerHTML =
                    '<div class="alert alert-danger small p-2 text-center mb-0">Error loading settings.</div>';
                settingsStatus.className = "mt-3 error";
            }
        }

        const togglePasswordVisibility = function (inputId) {
            const input = document.getElementById(inputId);
            const button = input
                ? input.closest(".input-group").querySelector("button")
                : null;
            const icon = button ? button.querySelector("i") : null;
            if (input && icon) {
                if (input.type === "password") {
                    input.type = "text";
                    icon.classList.remove("fa-eye");
                    icon.classList.add("fa-eye-slash");
                } else {
                    input.type = "password";
                    icon.classList.remove("fa-eye-slash");
                    icon.classList.add("fa-eye");
                }
            }
        };
        window.togglePasswordVisibility = togglePasswordVisibility;

        // --- API KEY VALIDATION FUNCTIONS ---
        function validateApiKey(apiKey, modelKey) {
            if (!apiKey || typeof apiKey !== "string") {
                return { valid: false, message: "API key is required" };
            }

            const trimmedKey = apiKey.trim();
            if (trimmedKey.length === 0) {
                return { valid: false, message: "API key cannot be empty" };
            }

            // Basic format validation based on model
            switch (modelKey) {
                case "openai":
                    if (
                        !trimmedKey.startsWith("sk-") ||
                        trimmedKey.length < 20
                    ) {
                        return {
                            valid: false,
                            message:
                                "OpenAI API keys should start with 'sk-' and be at least 20 characters",
                        };
                    }
                    break;
                case "anthropic":
                    if (
                        !trimmedKey.startsWith("sk-ant-") ||
                        trimmedKey.length < 30
                    ) {
                        return {
                            valid: false,
                            message:
                                "Anthropic API keys should start with 'sk-ant-' and be at least 30 characters",
                        };
                    }
                    break;
                case "google":
                    if (trimmedKey.length < 20) {
                        return {
                            valid: false,
                            message:
                                "Google Gemini API keys should be at least 20 characters",
                        };
                    }
                    break;
                case "deepseek":
                    if (
                        !trimmedKey.startsWith("sk-") ||
                        trimmedKey.length < 20
                    ) {
                        return {
                            valid: false,
                            message:
                                "DeepSeek API keys should start with 'sk-' and be at least 20 characters",
                        };
                    }
                    break;
            }

            return { valid: true, message: "API key format looks valid" };
        }

        function findApiKeyElements(button) {
            console.log("=== FINDING API KEY ELEMENTS ===");
            console.log("Button:", button);

            // Try multiple methods to find the API key input
            let promptDiv = null;
            let apiKeyInput = null;
            let modelKey = null;

            // Method 1: Closest .api-key-prompt
            promptDiv = button.closest(".api-key-prompt");
            console.log("Method 1 - Closest .api-key-prompt:", promptDiv);

            // Method 2: Go up to message and find within message
            if (!promptDiv) {
                const messageContainer = button.closest(".message");
                if (messageContainer) {
                    promptDiv =
                        messageContainer.querySelector(".api-key-prompt");
                    console.log("Method 2 - Within message:", promptDiv);
                }
            }

            // Method 3: Search in the entire chat messages area
            if (!promptDiv) {
                const allPrompts = document.querySelectorAll(".api-key-prompt");
                if (allPrompts.length > 0) {
                    promptDiv = allPrompts[allPrompts.length - 1]; // Get the last one
                    console.log("Method 3 - Last available prompt:", promptDiv);
                }
            }

            if (promptDiv) {
                // Find the input within the prompt div
                apiKeyInput =
                    promptDiv.querySelector(".api-key-input") ||
                    promptDiv.querySelector('input[type="password"]') ||
                    promptDiv.querySelector('input[type="text"]') ||
                    promptDiv.querySelector("input");

                // Get model key from data attribute or other sources
                modelKey =
                    promptDiv.dataset.modelKey ||
                    promptDiv.getAttribute("data-model-key") ||
                    button.dataset.modelKey ||
                    button.getAttribute("data-model-key");

                console.log("Found API key input:", apiKeyInput);
                console.log("Found model key:", modelKey);
                console.log(
                    "Input value:",
                    apiKeyInput ? apiKeyInput.value : "N/A",
                );
            }

            return { promptDiv, apiKeyInput, modelKey };
        }

        // --- ENHANCED RATING CHART FUNCTIONS ---

        function getRatingColor(score) {
            if (score >= 80) return "#28a745"; // Success green
            if (score >= 60) return "#ffc107"; // Warning yellow
            if (score >= 40) return "#fd7e14"; // Orange
            return "#dc3545"; // Danger red
        }

        function createAnimatedDonutChart(
            container,
            size,
            strokeWidth,
            score,
            color,
            delay = 0,
        ) {
            const radius = size / 2 - strokeWidth / 2;
            const circumference = 2 * Math.PI * radius;
            const svg = d3
                .select(container)
                .append("svg")
                .attr("width", size)
                .attr("height", size);
            const g = svg
                .append("g")
                .attr("transform", `translate(${size / 2}, ${size / 2})`);

            // Add gradient definitions for enhanced visual appeal
            const defs = svg.append("defs");

            // Create gradient for the progress arc
            const gradient = defs.append("linearGradient")
                .attr("id", `gradient-${Math.random().toString(36).substr(2, 9)}`)
                .attr("gradientUnits", "userSpaceOnUse")
                .attr("x1", "0%").attr("y1", "0%")
                .attr("x2", "100%").attr("y2", "100%");

            gradient.append("stop")
                .attr("offset", "0%")
                .attr("stop-color", color)
                .attr("stop-opacity", 1);

            gradient.append("stop")
                .attr("offset", "100%")
                .attr("stop-color", color)
                .attr("stop-opacity", 0.7);

            // Add glow filter
            const filter = defs.append("filter")
                .attr("id", `glow-${Math.random().toString(36).substr(2, 9)}`)
                .attr("x", "-50%").attr("y", "-50%")
                .attr("width", "200%").attr("height", "200%");

            filter.append("feGaussianBlur")
                .attr("stdDeviation", "3")
                .attr("result", "coloredBlur");

            const feMerge = filter.append("feMerge");
            feMerge.append("feMergeNode").attr("in", "coloredBlur");
            feMerge.append("feMergeNode").attr("in", "SourceGraphic");

            // Background circle with subtle glow
            g.append("circle")
                .attr("r", radius)
                .attr("fill", "none")
                .attr("stroke", "rgba(255,255,255,0.1)")
                .attr("stroke-width", strokeWidth)
                .style("filter", "drop-shadow(0 0 5px rgba(255,255,255,0.1))");

            // Foreground progress circle with enhanced styling
            const foreground = g
                .append("circle")
                .attr("r", radius)
                .attr("fill", "none")
                .attr("stroke", `url(#${gradient.attr("id")})`)
                .attr("stroke-width", strokeWidth)
                .attr("stroke-dasharray", circumference)
                .attr("stroke-dashoffset", circumference)
                .attr("stroke-linecap", "round")
                .attr("transform", "rotate(-90)")
                .style("filter", `url(#${filter.attr("id")})`);

            // Add pulsing animation for high scores
            if (score >= 75) {
                foreground
                    .transition()
                    .delay(delay + 2000)
                    .duration(2000)
                    .ease(d3.easeSinInOut)
                    .attr("stroke-width", strokeWidth + 2)
                    .transition()
                    .duration(2000)
                    .ease(d3.easeSinInOut)
                    .attr("stroke-width", strokeWidth)
                    .on("end", function repeat() {
                        d3.select(this)
                            .transition()
                            .duration(2000)
                            .ease(d3.easeSinInOut)
                            .attr("stroke-width", strokeWidth + 2)
                            .transition()
                            .duration(2000)
                            .ease(d3.easeSinInOut)
                            .attr("stroke-width", strokeWidth)
                            .on("end", repeat);
                    });
            }

            // Main progress animation
            foreground
                .transition()
                .delay(delay + 500)
                .duration(1500)
                .ease(d3.easeCubicOut)
                .attr(
                    "stroke-dashoffset",
                    circumference - (score / 100) * circumference,
                );
        }

        function animateCounterEnhanced(
            element,
            start,
            end,
            duration,
            delay = 0,
            suffix = "%",
        ) {
            setTimeout(() => {
                d3.select(element)
                    .transition()
                    .duration(duration)
                    .ease(d3.easeCubicOut)
                    .tween("text", function () {
                        const i = d3.interpolate(start, end);
                        return function (t) {
                            this.textContent = Math.round(i(t)) + suffix;
                        };
                    });
            }, delay);
        }

        function createEnhancedRatingCharts(ratingsData) {
            const container = document.createElement("div");
            container.className = "enhanced-rating-charts-container";

            const canvas = document.createElement("canvas");
            canvas.className = "rating-particles-canvas";
            container.appendChild(canvas);
            initializeParticleBackground(canvas, ratingsData.overall);

            const shimmer = document.createElement("div");
            shimmer.className = "shimmer-overlay";
            container.appendChild(shimmer);

            const contentWrapper = document.createElement("div");
            contentWrapper.className = "content-wrapper";

            const overallSection = document.createElement("div");
            overallSection.className = "overall-rating-section";
            const shimmerEffect = document.createElement("div");
            shimmerEffect.className = "shimmer";
            overallSection.appendChild(shimmerEffect);
            const overallChartWrapper = document.createElement("div");
            overallChartWrapper.className = "overall-chart-wrapper";
            const overallDetails = document.createElement("div");
            overallDetails.className = "overall-rating-details";
                const categoryLabel = document.createElement("div");
    categoryLabel.className = "rating-category";
    categoryLabel.textContent = "Overall Investment Rating";
    categoryLabel.style.color = "#000000";
    categoryLabel.style.textShadow = "none";
                const scoreContainer = document.createElement("div");
    scoreContainer.className = "rating-score-text";
    
    // Get the color for the rating
    const ratingColor = getRatingColor(ratingsData.overall);
    
    // Convert hex color to RGB for text shadow
    const hexToRgb = (hex) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    };
    
    const rgbColor = hexToRgb(ratingColor);
    const rgbString = rgbColor ? `${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}` : "0, 123, 255";
    
    // Create the text shadow effect for both elements
    const glowEffect = `
        0 0 10px rgba(${rgbString}, 0.8),
        0 0 20px rgba(${rgbString}, 0.6),
        0 0 30px rgba(${rgbString}, 0.4)
    `;
    
    // Create individual spans for better control
    const scoreText = document.createElement("span");
    scoreText.style.color = "#000000"; // Black text
    scoreText.style.textShadow = "none";

    const percentSign = document.createElement("span");
    percentSign.className = "percent-sign";
    percentSign.textContent = "%";
    percentSign.style.color = "#000000"; // Black text
    percentSign.style.textShadow = "none";
    
    // Add to container with no background/shadow/border
    scoreContainer.append(scoreText, percentSign);
            const ratingDesc = document.createElement("div");
            ratingDesc.className = "rating-description";
            const getDesc = (s) =>
                s >= 85
                    ? "🌟 Exceptional"
                    : s >= 75
                      ? "⭐ Strong Buy"
                      : s >= 65
                        ? "📈 Good"
                        : s >= 50
                          ? "⚖️ Hold"
                          : "⚠️ High Risk";
            ratingDesc.textContent = getDesc(ratingsData.overall);
            ratingDesc.style.color = "#000000";
            ratingDesc.style.textShadow = "none";

            // Create interactive metric tiles
            const metricTilesContainer = document.createElement("div");
            metricTilesContainer.className = "metric-tiles-container";

            const metrics = [
                { label: "Business", value: ratingsData.business || 0, suffix: "%" },
                { label: "Financial", value: ratingsData.financial || 0, suffix: "%" },
                { label: "Valuation", value: ratingsData.valuation || 0, suffix: "%" },
                { label: "Management", value: ratingsData.management || 0, suffix: "%" }
            ];

            metrics.forEach((metric, index) => {
                const tile = document.createElement("div");
                tile.className = "metric-tile";
                tile.onclick = () => {
                    // Scroll to corresponding component section
                    const componentItems = document.querySelectorAll('.component-rating-item');
                    if (componentItems[index]) {
                        componentItems[index].scrollIntoView({ behavior: 'smooth', block: 'center' });
                        componentItems[index].style.transform = 'scale(1.1)';
                        setTimeout(() => {
                            componentItems[index].style.transform = '';
                        }, 1000);
                    }
                };

                const label = document.createElement("div");
                label.className = "metric-tile-label";
                label.textContent = metric.label;

                const value = document.createElement("div");
                value.className = "metric-tile-value";
                value.textContent = metric.value + metric.suffix;

                tile.append(label, value);
                metricTilesContainer.appendChild(tile);
            });

            overallDetails.append(categoryLabel, scoreContainer, ratingDesc);
            overallSection.append(overallChartWrapper, overallDetails, metricTilesContainer);

            const componentsGrid = document.createElement("div");
            componentsGrid.className = "component-ratings-grid";

            const componentData = [
                {
                    name: "Business Quality",
                    score: ratingsData.business || 0,
                    color: "#17a2b8",
                    icon: "🏰",
                },
                {
                    name: "Financial Strength",
                    score: ratingsData.financial || 0,
                    color: "#28a745",
                    icon: "💪",
                },
                {
                    name: "Valuation",
                    score: ratingsData.valuation || 0,
                    color: "#fd7e14",
                    icon: "💰",
                },
                {
                    name: "Management",
                    score: ratingsData.management || 0,
                    color: "#6f42c1",
                    icon: "👥",
                },
            ];

            componentData.forEach((comp, index) => {
                const item = document.createElement("div");
                item.className = "component-rating-item";
                item.style.setProperty("--comp-color", `${comp.color}`);
                item.style.setProperty(
                    "--comp-color-transparent",
                    `${comp.color}30`,
                );

                const hoverOverlay = document.createElement("div");
                hoverOverlay.className = "hover-overlay";
                hoverOverlay.style.background = `linear-gradient(135deg, ${comp.color}1A, ${comp.color}0D)`;

                const header = document.createElement("div");
                header.className = "component-header";
                header.innerHTML = `<div class="component-icon">${comp.icon}</div><div class="component-name">${comp.name}</div>`;

                const chartWrapper = document.createElement("div");
                chartWrapper.className = "component-chart-wrapper";

                const scoreDisplay = document.createElement("div");
                scoreDisplay.className = "component-score-display";
                scoreDisplay.style.color = comp.color;

                item.append(hoverOverlay, header, chartWrapper, scoreDisplay);
                componentsGrid.appendChild(item);

                setTimeout(
                    () => {
                        createAnimatedDonutChart(
                            chartWrapper,
                            90,
                            8,
                            comp.score,
                            comp.color,
                            index * 150,
                        );
                        
                        // Create span elements for number and % with individual glows
                        scoreDisplay.innerHTML = '';
                        const numberSpan = document.createElement("span");
                        const pctSpan = document.createElement("span");
                        pctSpan.textContent = "%";
                        pctSpan.style.fontSize = "0.8em";
                        pctSpan.style.marginLeft = "1px";
                        
                        // Apply glow to both elements separately
                        const compGlow = `0 0 8px ${comp.color}, 0 0 12px ${comp.color}40`;
                        numberSpan.style.textShadow = compGlow;
                        pctSpan.style.textShadow = compGlow;
                        
                        scoreDisplay.appendChild(numberSpan);
                        scoreDisplay.appendChild(pctSpan);
                        
                        // Animate the counter with the number span only
                        animateCounterEnhanced(
                            numberSpan,
                            0,
                            comp.score,
                            1500,
                            index * 150 + 400,
                            "",
                        );
                    },
                    1000 + index * 200,
                );
            });

            contentWrapper.append(overallSection, componentsGrid);
            container.appendChild(contentWrapper);

            // Trigger animations
            setTimeout(() => {
                createAnimatedDonutChart(
                    overallChartWrapper,
                    140,
                    12,
                    ratingsData.overall,
                    getRatingColor(ratingsData.overall),
                );
                animateCounterEnhanced(
                    scoreText,
                    0,
                    ratingsData.overall,
                    2000,
                    600,
                    "",
                );
                [
                    container,
                    categoryLabel,
                    scoreContainer,
                    ratingDesc,
                    ...componentsGrid.children,
                ].forEach((el, i) => {
                    setTimeout(
                        () => {
                            el.style.opacity = 1;
                            el.style.transform = "translateY(0) scale(1)";
                        },
                        200 + i * 80,
                    );
                });

                // Enhanced effects for exceptional ratings
                if (ratingsData.overall >= 85) {
                    // Add premium border glow for exceptional ratings
                    setTimeout(() => {
                        overallSection.style.setProperty('--glow-intensity', '1');
                        overallSection.classList.add('exceptional-rating');
                    }, 1500);

                    setTimeout(
                        () =>
                            addCelebrationEffects(
                                container,
                                "premium",
                            ),
                        2500,
                    );
                } else if (ratingsData.overall >= 75) {
                    setTimeout(
                        () =>
                            addCelebrationEffects(
                                container,
                                "standard",
                            ),
                        2500,
                    );
                }
            }, 400);

            return container;
        }

        function initializeParticleBackground(canvas, score = 70) {
            // Wait for the canvas to be properly sized
            setTimeout(() => {
                const ctx = canvas.getContext("2d");
                const dpr = window.devicePixelRatio || 1;
                const rect = canvas.getBoundingClientRect();

                // Ensure canvas has dimensions
                if (rect.width === 0 || rect.height === 0) {
                    console.warn("Canvas has no dimensions, skipping particle background");
                    return;
                }

                canvas.width = rect.width * dpr;
                canvas.height = rect.height * dpr;
                ctx.scale(dpr, dpr);
                canvas.style.width = `${rect.width}px`;
                canvas.style.height = `${rect.height}px`;

            const particleCount = Math.max(25, Math.floor(score / 1.2));
            let particles = [];
            let connections = [];

            const getParticleColor = () => {
                if (score >= 80)
                    return `hsla(${140 + Math.random() * 40}, 80%, 65%, ${Math.random() * 0.5 + 0.3})`;
                if (score >= 60)
                    return `hsla(${40 + Math.random() * 30}, 75%, 65%, ${Math.random() * 0.4 + 0.2})`;
                return `hsla(${Math.random() * 30}, 70%, 60%, ${Math.random() * 0.3 + 0.15})`;
            };

            class Particle {
                constructor() {
                    this.reset();
                    this.originalRadius = this.radius;
                    this.pulsePhase = Math.random() * Math.PI * 2;
                }
                reset() {
                    this.x = Math.random() * rect.width;
                    this.y = Math.random() * rect.height;
                    this.radius = Math.random() * 3 + (score >= 80 ? 1.5 : 0.8);
                    this.vx = (Math.random() - 0.5) * (0.4 + score / 150);
                    this.vy = (Math.random() - 0.5) * (0.4 + score / 150);
                    this.opacity = Math.random() * 0.4 + 0.15;
                    this.color = getParticleColor();
                }
                update() {
                    this.x += this.vx;
                    this.y += this.vy;
                    this.pulsePhase += 0.02;
                    this.radius = this.originalRadius + Math.sin(this.pulsePhase) * 0.5;

                    if (this.x < 0 || this.x > rect.width) this.vx *= -1;
                    if (this.y < 0 || this.y > rect.height) this.vy *= -1;
                }
                draw() {
                    // Draw particle with glow effect
                    const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.radius * 3);
                    gradient.addColorStop(0, this.color);
                    gradient.addColorStop(1, 'transparent');

                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                    ctx.fillStyle = gradient;
                    ctx.globalAlpha = this.opacity;
                    ctx.fill();

                    // Draw inner bright core
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.radius * 0.4, 0, Math.PI * 2);
                    ctx.fillStyle = this.color.replace(/hsla\((\d+),\s*(\d+)%,\s*(\d+)%,\s*[\d.]+\)/, 'hsla($1, $2%, 85%, 0.8)');
                    ctx.globalAlpha = this.opacity * 1.5;
                    ctx.fill();
                }
            }

            for (let i = 0; i < particleCount; i++) particles.push(new Particle());

            function drawConnections() {
                for (let i = 0; i < particles.length; i++) {
                    for (let j = i + 1; j < particles.length; j++) {
                        const dx = particles[i].x - particles[j].x;
                        const dy = particles[i].y - particles[j].y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < 100) {
                            const opacity = (100 - distance) / 100 * 0.3;
                            ctx.beginPath();
                            ctx.moveTo(particles[i].x, particles[i].y);
                            ctx.lineTo(particles[j].x, particles[j].y);
                            ctx.strokeStyle = `rgba(${score >= 80 ? '40, 200, 120' : score >= 60 ? '255, 193, 7' : '108, 117, 125'}, ${opacity})`;
                            ctx.lineWidth = 1;
                            ctx.stroke();
                        }
                    }
                }
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.globalCompositeOperation = 'lighter';

                drawConnections();
                particles.forEach(p => { p.update(); p.draw(); });

                ctx.globalCompositeOperation = 'source-over';
                requestAnimationFrame(animate);
            }
            animate();
            }, 100); // Close the setTimeout from initializeParticleBackground
        }

        function addCelebrationEffects(container, level = 'standard') {
            const celebrationContainer = document.createElement("div");
            celebrationContainer.className = "success-celebration";
            container.appendChild(celebrationContainer);

            const indicators = level === 'premium' ?
                ["⭐", "🎉", "💎", "🚀", "🏆", "💰", "✨", "🌟", "🎊", "🎈"] :
                ["⭐", "🎉", "💰", "✨", "🌟"];
            const count = level === 'premium' ? 15 : 8;

            // Create ripple effect
            const ripple = document.createElement("div");
            ripple.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(40, 200, 120, 0.3), transparent);
                animation: ripple 1.5s ease-out;
                transform: translate(-50%, -50%);
            `;
            celebrationContainer.appendChild(ripple);

            for (let i = 0; i < count; i++) {
                setTimeout(() => {
                    const indicator = document.createElement("div");
                    indicator.textContent = indicators[i % indicators.length];
                    indicator.style.cssText = `
                        position: absolute;
                        left: ${Math.random() * 100}%;
                        top: ${Math.random() * 100}%;
                        font-size: ${1.2 + Math.random() * 0.8}rem;
                        animation: celebrationFloat ${2.5 + Math.random()}s ease-out forwards;
                        z-index: 1000;
                        pointer-events: none;
                    `;
                    celebrationContainer.appendChild(indicator);
                    setTimeout(() => indicator.remove(), 3500);
                }, i * 100);
            }

            setTimeout(() => {
                ripple.remove();
                celebrationContainer.remove();
            }, 4000);
        }

        let animationObserver;
        try {
            if (typeof IntersectionObserver !== "undefined") {
                animationObserver = new IntersectionObserver(
                    (entries, observer) => {
                        entries.forEach((entry) => {
                            if (entry.isIntersecting) {
                                const el = entry.target;
                                if (
                                    el.classList.contains(
                                        "rating-visualization-container",
                                    ) &&
                                    !el.dataset.rendered
                                ) {
                                    try {
                                        const ratingsData = JSON.parse(
                                            el.dataset.ratings || "{}",
                                        );
                                        if (
                                            Object.keys(ratingsData).length > 0
                                        ) {
                                            el.classList.add("visible");
                                            const chartNode =
                                                createEnhancedRatingCharts(
                                                    ratingsData,
                                                );
                                            el.innerHTML = "";
                                            el.appendChild(chartNode);
                                            el.dataset.rendered = "true";
                                        }
                                    } catch (error) {
                                        console.error(
                                            "Error rendering chart:",
                                            error,
                                        );
                                    }
                                }
                                observer.unobserve(el);
                            }
                        });
                    },
                    { threshold: 0.2 },
                );
            }
        } catch (e) {
            console.error("Failed to create IntersectionObserver", e);
        }

        // --- LABEL COLLISION PREVENTION SYSTEM ---

        function preventLabelCollisions(container, labelSelector = 'text', minDistance = 20) {
            const labels = container.selectAll(labelSelector);
            const labelNodes = labels.nodes();

            labelNodes.forEach((label, i) => {
                const bbox1 = label.getBBox();
                const x1 = parseFloat(label.getAttribute('x')) || 0;
                const y1 = parseFloat(label.getAttribute('y')) || 0;

                labelNodes.forEach((otherLabel, j) => {
                    if (i !== j) {
                        const bbox2 = otherLabel.getBBox();
                        const x2 = parseFloat(otherLabel.getAttribute('x')) || 0;
                        const y2 = parseFloat(otherLabel.getAttribute('y')) || 0;

                        const distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));

                        if (distance < minDistance) {
                            // Adjust position to prevent overlap
                            const angle = Math.atan2(y2 - y1, x2 - x1);
                            const newX = x1 + Math.cos(angle) * minDistance;
                            const newY = y1 + Math.sin(angle) * minDistance;

                            d3.select(otherLabel)
                                .transition()
                                .duration(300)
                                .attr('x', newX)
                                .attr('y', newY);
                        }
                    }
                });
            });
        }

        function adjustLabelPositions(labels, containerWidth, containerHeight) {
            labels.each(function() {
                const label = d3.select(this);
                const bbox = this.getBBox();
                let x = parseFloat(label.attr('x')) || 0;
                let y = parseFloat(label.attr('y')) || 0;

                // Keep labels within container bounds
                if (x + bbox.width > containerWidth) {
                    x = containerWidth - bbox.width - 5;
                }
                if (x < 0) {
                    x = 5;
                }
                if (y + bbox.height > containerHeight) {
                    y = containerHeight - bbox.height - 5;
                }
                if (y < bbox.height) {
                    y = bbox.height + 5;
                }

                label.attr('x', x).attr('y', y);
            });
        }

        // --- ADVANCED D3.JS VISUALIZATION FUNCTIONS ---

        function createValuationSensitivityMatrix(container, data, ticker) {
            // Prevent duplicate chart creation
            if (container.querySelector('svg')) {
                console.log("Valuation sensitivity matrix already exists in container");
                return;
            }

            const margin = { top: 40, right: 80, bottom: 60, left: 80 };
            const width = 500 - margin.left - margin.right;
            const height = 350 - margin.top - margin.bottom;

            const svg = d3.select(container)
                .append("svg")
                .attr("width", width + margin.left + margin.right)
                .attr("height", height + margin.top + margin.bottom);

            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);

            // Function to fetch and render real data
            const fetchAndRenderData = async () => {
                console.log("DCF Chart - received ticker:", ticker);
                let dcfData = data;

                // Always try to fetch real data if no data provided
                if (!dcfData) {
                    // Try with provided ticker first, then fallback to common tickers
                    const tickersToTry = ticker ? [ticker, 'AAPL', 'MSFT', 'GOOGL'] : ['AAPL', 'MSFT', 'GOOGL'];

                    for (const testTicker of tickersToTry) {
                        console.log("DCF Chart - trying to fetch data for ticker:", testTicker);
                        try {
                            const response = await fetch(`/api/chatbot/dcf-sensitivity-data/${testTicker}`);
                            if (response.ok) {
                                dcfData = await response.json();
                                console.log("Fetched real DCF sensitivity data for", testTicker, ":", dcfData);
                                break; // Success, stop trying
                            } else {
                                console.warn(`Failed to fetch DCF data for ${testTicker}, status:`, response.status);
                            }
                        } catch (error) {
                            console.error(`Error fetching DCF sensitivity data for ${testTicker}:`, error);
                        }
                    }

                    if (!dcfData) {
                        console.log("DCF Chart - all fetch attempts failed, using default data");
                    }
                } else {
                    console.log("DCF Chart - using provided data:", data);
                }

                // Use fetched data or show error if no data available
                const defaultData = dcfData || {
                    terminalGrowthRates: [1.0, 2.0, 3.0, 4.0, 5.0],
                    discountRates: [7.5, 8.5, 9.5, 10.5, 11.5],
                    currentPrice: 0,
                    values: [
                        [0, 0, 0, 0, 0],
                        [0, 0, 0, 0, 0],
                        [0, 0, 0, 0, 0],
                        [0, 0, 0, 0, 0],
                        [0, 0, 0, 0, 0]
                    ],
                    error: dcfData ? null : "Unable to fetch DCF data for this ticker"
                };

                renderDCFMatrix(defaultData);
            };

            const renderDCFMatrix = (defaultData) => {

                // Check if we have an error or all zero values
                if (defaultData.error || (defaultData.values && defaultData.values.every(row => row.every(val => val === 0)))) {
                    // Show error message instead of chart
                    const errorDiv = d3.select(container)
                        .append("div")
                        .style("text-align", "center")
                        .style("padding", "20px")
                        .style("color", "#666")
                        .style("font-style", "italic");

                    errorDiv.append("p")
                        .text(defaultData.error || "DCF sensitivity analysis requires complete financial data");

                    errorDiv.append("p")
                        .style("font-size", "12px")
                        .text("This analysis will be available when sufficient cash flow data is present.");

                    return;
                }

                // Create scales
                const xScale = d3.scaleBand()
                    .domain(defaultData.terminalGrowthRates.map(d => d + "%"))
                    .range([0, width])
                    .padding(0.1);

                const yScale = d3.scaleBand()
                    .domain(defaultData.discountRates.map(d => d + "%"))
                    .range([0, height])
                    .padding(0.1);

            // Color scale based on value relative to current price
            const colorScale = d3.scaleSequential(d3.interpolateRdYlGn)
                .domain([0.7, 1.5]); // 70% to 150% of current price

            // Create cells
            const cells = g.selectAll(".cell")
                .data(defaultData.values.flatMap((row, i) =>
                    row.map((value, j) => ({
                        row: i,
                        col: j,
                        value: value,
                        ratio: value / defaultData.currentPrice,
                        terminalGrowth: defaultData.terminalGrowthRates[j],
                        discountRate: defaultData.discountRates[i]
                    }))
                ))
                .enter().append("g")
                .attr("class", "cell");

            // Create tooltip
            const tooltip = d3.select("body").append("div")
                .attr("class", "d3-tooltip")
                .style("position", "absolute")
                .style("background", "rgba(0, 0, 0, 0.9)")
                .style("color", "#ffffff")
                .style("padding", "12px")
                .style("border-radius", "8px")
                .style("font-size", "12px")
                .style("pointer-events", "none")
                .style("opacity", 0)
                .style("z-index", "10000")
                .style("border", "1px solid rgba(0, 212, 255, 0.5)");

            cells.append("rect")
                .attr("x", d => xScale(d.terminalGrowth + "%"))
                .attr("y", d => yScale(d.discountRate + "%"))
                .attr("width", xScale.bandwidth())
                .attr("height", yScale.bandwidth())
                .attr("fill", d => colorScale(d.ratio))
                .attr("stroke", "#fff")
                .attr("stroke-width", 1)
                .style("opacity", 0)
                .style("cursor", "pointer")
                .on("mouseover", function(event, d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr("stroke-width", 3)
                        .style("filter", "brightness(1.2)");

                    tooltip.transition()
                        .duration(200)
                        .style("opacity", 1);

                    const upside = ((d.value / defaultData.currentPrice - 1) * 100).toFixed(1);
                    tooltip.html(`
                        <strong>Fair Value: $${d.value}</strong><br/>
                        Terminal Growth: ${d.terminalGrowth}%<br/>
                        Discount Rate: ${d.discountRate}%<br/>
                        <em>Current: $${defaultData.currentPrice}</em><br/>
                        <span style="color: ${upside > 0 ? '#4CAF50' : '#f44336'}">
                            ${upside > 0 ? '+' : ''}${upside}% upside
                        </span>
                    `)
                        .style("left", (event.pageX + 10) + "px")
                        .style("top", (event.pageY - 10) + "px");
                })
                .on("mouseout", function(d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr("stroke-width", 1)
                        .style("filter", "brightness(1)");

                    tooltip.transition()
                        .duration(200)
                        .style("opacity", 0);
                })
                .transition()
                .duration(1000)
                .delay((d, i) => i * 50)
                .style("opacity", 1);

            cells.append("text")
                .attr("x", d => xScale(d.terminalGrowth + "%") + xScale.bandwidth() / 2)
                .attr("y", d => yScale(d.discountRate + "%") + yScale.bandwidth() / 2)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .style("font-size", "12px")
                .style("font-weight", "bold")
                .style("fill", d => d.ratio > 1.2 ? "#000000" : "#ffffff")
                .style("text-shadow", d => d.ratio > 1.2 ? "1px 1px 2px rgba(255,255,255,0.8)" : "1px 1px 2px rgba(0,0,0,0.8)")
                .text(d => "$" + d.value)
                .style("opacity", 0)
                .transition()
                .duration(1000)
                .delay((d, i) => i * 50 + 500)
                .style("opacity", 1);

            // Add axes with proper styling
            const xAxis = g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale));

            xAxis.selectAll("text")
                .style("fill", "#ffffff")
                .style("font-size", "12px");

            xAxis.selectAll("path, line")
                .style("stroke", "#ffffff")
                .style("opacity", 0.3);

            xAxis.append("text")
                .attr("x", width / 2)
                .attr("y", 40)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#ffffff")
                .style("font-weight", "bold")
                .text("Terminal Growth Rate");

            const yAxis = g.append("g")
                .call(d3.axisLeft(yScale));

            yAxis.selectAll("text")
                .style("fill", "#ffffff")
                .style("font-size", "12px");

            yAxis.selectAll("path, line")
                .style("stroke", "#ffffff")
                .style("opacity", 0.3);

            yAxis.append("text")
                .attr("transform", "rotate(-90)")
                .attr("y", -50)
                .attr("x", -height / 2)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#ffffff")
                .style("font-weight", "bold")
                .text("Discount Rate (WACC)");

            // Add professional title
            const titleGroup = svg.append("g");

            titleGroup.append("text")
                .attr("x", (width + margin.left + margin.right) / 2)
                .attr("y", 20)
                .attr("text-anchor", "middle")
                .style("font-size", "16px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .text("DCF Valuation Sensitivity Matrix");

            titleGroup.append("text")
                .attr("x", (width + margin.left + margin.right) / 2)
                .attr("y", 35)
                .attr("text-anchor", "middle")
                .style("font-size", "10px")
                .style("fill", "rgba(255, 255, 255, 0.7)")
                .text("Professional Financial Analysis");

            // Add current price indicator
            svg.append("text")
                .attr("x", width + margin.left + 10)
                .attr("y", margin.top + 20)
                .style("font-size", "12px")
                .style("fill", "#ffffff")
                .text(`Current: $${defaultData.currentPrice}`);
            };

            // Call the fetch and render function
            fetchAndRenderData();
        }

        function createPeerComparisonChart(container, data, ticker) {
            // Prevent duplicate chart creation
            if (container.querySelector('svg')) {
                console.log("Peer comparison chart already exists in container");
                return;
            }

            const margin = { top: 40, right: 120, bottom: 60, left: 100 };
            const width = 600 - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;

            const svg = d3.select(container)
                .append("svg")
                .attr("width", width + margin.left + margin.right)
                .attr("height", height + margin.top + margin.bottom);

            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);

            // Function to fetch and render real data
            const fetchAndRenderData = async () => {
                let peerData = data;

                // Always try to fetch real data if no data provided
                if (!peerData) {
                    // Try with provided ticker first, then fallback to common tickers
                    const tickersToTry = ticker ? [ticker, 'AAPL', 'MSFT', 'GOOGL'] : ['AAPL', 'MSFT', 'GOOGL'];

                    for (const testTicker of tickersToTry) {
                        console.log("Peer Chart - trying to fetch data for ticker:", testTicker);
                        try {
                            const response = await fetch(`/api/chatbot/peer-comparison-data/${testTicker}`);
                            if (response.ok) {
                                peerData = await response.json();
                                console.log("Fetched real peer comparison data for", testTicker, ":", peerData);
                                break; // Success, stop trying
                            } else {
                                console.warn(`Failed to fetch peer data for ${testTicker}, status:`, response.status);
                            }
                        } catch (error) {
                            console.error(`Error fetching peer comparison data for ${testTicker}:`, error);
                        }
                    }

                    if (!peerData) {
                        console.log("Peer Chart - all fetch attempts failed, using default data");
                    }
                }

                // Fallback to default data if fetch failed
                const defaultData = peerData || [
                    { company: "GOOGL", pe: 18.95, pfcf: 28.18, yield: 3.6, roic: 29, growth: 15, target: true },
                    { company: "MSFT", pe: 37.1, pfcf: 34.2, yield: 2.8, roic: 27, growth: 13, target: false },
                    { company: "META", pe: 21.3, pfcf: 24.6, yield: 4.0, roic: 32, growth: 12, target: false },
                    { company: "AMZN", pe: 46.7, pfcf: 29.7, yield: 2.6, roic: 12, growth: 16, target: false },
                    { company: "AAPL", pe: 28.5, pfcf: 25.1, yield: 3.2, roic: 45, growth: 8, target: false }
                ];

                renderPeerChart(defaultData);
            };

            const renderPeerChart = (defaultData) => {

            // Create scales
            const xScale = d3.scaleLinear()
                .domain([0, d3.max(defaultData, d => d.pe) * 1.1])
                .range([0, width]);

            const yScale = d3.scaleLinear()
                .domain([0, d3.max(defaultData, d => d.pfcf) * 1.1])
                .range([height, 0]);

            const radiusScale = d3.scaleLinear()
                .domain(d3.extent(defaultData, d => d.roic))
                .range([8, 25]);

            // Create bubbles
            const bubbles = g.selectAll(".bubble")
                .data(defaultData)
                .enter().append("g")
                .attr("class", "bubble");

            // Create tooltip for peer comparison
            const peerTooltip = d3.select("body").append("div")
                .attr("class", "d3-peer-tooltip")
                .style("position", "absolute")
                .style("background", "rgba(0, 0, 0, 0.9)")
                .style("color", "#ffffff")
                .style("padding", "12px")
                .style("border-radius", "8px")
                .style("font-size", "12px")
                .style("pointer-events", "none")
                .style("opacity", 0)
                .style("z-index", "10000")
                .style("border", "1px solid rgba(0, 212, 255, 0.5)");

            bubbles.append("circle")
                .attr("cx", d => xScale(d.pe))
                .attr("cy", d => yScale(d.pfcf))
                .attr("r", 0)
                .attr("fill", d => d.target ? "#007bff" : "#28a745")
                .attr("stroke", d => d.target ? "#0056b3" : "#1e7e34")
                .attr("stroke-width", d => d.target ? 3 : 1)
                .style("opacity", 0.8)
                .style("cursor", "pointer")
                .on("mouseover", function(event, d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr("stroke-width", d => d.target ? 5 : 3)
                        .style("filter", "brightness(1.2)");

                    peerTooltip.transition()
                        .duration(200)
                        .style("opacity", 1);

                    peerTooltip.html(`
                        <strong>${d.company}</strong> ${d.target ? '(Target)' : ''}<br/>
                        P/E Ratio: ${d.pe}<br/>
                        P/FCF Ratio: ${d.pfcf}<br/>
                        ROIC: ${d.roic}%<br/>
                        Div Yield: ${d.yield}%<br/>
                        Growth: ${d.growth}%
                    `)
                        .style("left", (event.pageX + 10) + "px")
                        .style("top", (event.pageY - 10) + "px");
                })
                .on("mouseout", function(d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr("stroke-width", d => d.target ? 3 : 1)
                        .style("filter", "brightness(1)");

                    peerTooltip.transition()
                        .duration(200)
                        .style("opacity", 0);
                })
                .transition()
                .duration(1000)
                .delay((d, i) => i * 200)
                .attr("r", d => radiusScale(d.roic));

            // Add company labels with collision prevention
            const companyLabels = bubbles.append("text")
                .attr("x", d => xScale(d.pe))
                .attr("y", d => yScale(d.pfcf) - radiusScale(d.roic) - 8)
                .attr("text-anchor", "middle")
                .style("font-size", "11px")
                .style("font-weight", d => d.target ? "bold" : "600")
                .style("fill", "#ffffff")
                .style("text-shadow", "1px 1px 2px rgba(0,0,0,0.8)")
                .text(d => d.company)
                .style("opacity", 0)
                .transition()
                .duration(1000)
                .delay((d, i) => i * 200 + 500)
                .style("opacity", 1)
                .on("end", function() {
                    // Apply collision prevention after animation completes
                    setTimeout(() => {
                        adjustLabelPositions(companyLabels, width, height);
                        preventLabelCollisions(g, '.company-label', 25);
                    }, 100);
                });

            companyLabels.attr("class", "company-label");

            // Add ROIC labels inside bubbles with better visibility
            bubbles.append("text")
                .attr("x", d => xScale(d.pe))
                .attr("y", d => yScale(d.pfcf) + 4)
                .attr("text-anchor", "middle")
                .style("font-size", "9px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .style("text-shadow", "1px 1px 1px rgba(0,0,0,0.8)")
                .text(d => d.roic + "%")
                .style("opacity", 0)
                .transition()
                .duration(1000)
                .delay((d, i) => i * 200 + 1000)
                .style("opacity", 1);

            // Add axes with proper styling
            const xAxis = g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale).tickFormat(d => d.toFixed(1)));

            xAxis.selectAll("text")
                .style("fill", "#ffffff")
                .style("font-size", "11px");

            xAxis.selectAll("path, line")
                .style("stroke", "#ffffff")
                .style("opacity", 0.3);

            xAxis.append("text")
                .attr("x", width / 2)
                .attr("y", 40)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#ffffff")
                .style("font-weight", "bold")
                .text("P/E Ratio");

            const yAxis = g.append("g")
                .call(d3.axisLeft(yScale).tickFormat(d => d.toFixed(1)));

            yAxis.selectAll("text")
                .style("fill", "#ffffff")
                .style("font-size", "11px");

            yAxis.selectAll("path, line")
                .style("stroke", "#ffffff")
                .style("opacity", 0.3);

            yAxis.append("text")
                .attr("transform", "rotate(-90)")
                .attr("y", -60)
                .attr("x", -height / 2)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#ffffff")
                .style("font-weight", "bold")
                .text("P/FCF Ratio");

            // Add professional title
            const titleGroup = svg.append("g");

            titleGroup.append("text")
                .attr("x", (width + margin.left + margin.right) / 2)
                .attr("y", 20)
                .attr("text-anchor", "middle")
                .style("font-size", "16px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .text("Peer Valuation Comparison");

            titleGroup.append("text")
                .attr("x", (width + margin.left + margin.right) / 2)
                .attr("y", 35)
                .attr("text-anchor", "middle")
                .style("font-size", "10px")
                .style("fill", "rgba(255, 255, 255, 0.7)")
                .text("Competitive Analysis Framework");

            // Add comprehensive legend
            const legend = svg.append("g")
                .attr("transform", `translate(${width + margin.left + 10}, ${margin.top})`);

            // Legend title
            legend.append("text")
                .attr("x", 0)
                .attr("y", 0)
                .style("font-size", "12px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .text("Legend");

            // Bubble size explanation
            legend.append("text")
                .attr("x", 0)
                .attr("y", 20)
                .style("font-size", "10px")
                .style("fill", "rgba(255, 255, 255, 0.8)")
                .text("Bubble Size = ROIC");

            // Target company indicator
            legend.append("circle")
                .attr("cx", 10)
                .attr("cy", 40)
                .attr("r", 8)
                .attr("fill", "#007bff")
                .attr("stroke", "#0056b3")
                .attr("stroke-width", 2);

            legend.append("text")
                .attr("x", 25)
                .attr("y", 45)
                .style("font-size", "10px")
                .style("fill", "#ffffff")
                .text("Target Company");

            // Peer companies indicator
            legend.append("circle")
                .attr("cx", 10)
                .attr("cy", 60)
                .attr("r", 6)
                .attr("fill", "#28a745")
                .attr("stroke", "#1e7e34")
                .attr("stroke-width", 1);

            legend.append("text")
                .attr("x", 25)
                .attr("y", 65)
                .style("font-size", "10px")
                .style("fill", "#ffffff")
                .text("Peer Companies");
            };

            // Call the fetch and render function
            fetchAndRenderData();
        }

        function createCapitalAllocationChart(container, data, ticker) {
            // Prevent duplicate chart creation
            if (container.querySelector('svg')) {
                console.log("Capital allocation chart already exists in container");
                return;
            }

            const margin = { top: 40, right: 120, bottom: 60, left: 80 };
            const width = 600 - margin.left - margin.right;
            const height = 350 - margin.top - margin.bottom;

            const svg = d3.select(container)
                .append("svg")
                .attr("width", width + margin.left + margin.right)
                .attr("height", height + margin.top + margin.bottom);

            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);

            // Function to fetch and render real data
            const fetchAndRenderData = async () => {
                let capitalData = data;

                // Always try to fetch real data if no data provided
                if (!capitalData) {
                    // Try with provided ticker first, then fallback to common tickers
                    const tickersToTry = ticker ? [ticker, 'AAPL', 'MSFT', 'GOOGL'] : ['AAPL', 'MSFT', 'GOOGL'];

                    for (const testTicker of tickersToTry) {
                        console.log("Capital Chart - trying to fetch data for ticker:", testTicker);
                        try {
                            const response = await fetch(`/api/chatbot/capital-allocation-data/${testTicker}`);
                            if (response.ok) {
                                capitalData = await response.json();
                                console.log("Fetched real capital allocation data for", testTicker, ":", capitalData);
                                break; // Success, stop trying
                            } else {
                                console.warn(`Failed to fetch capital data for ${testTicker}, status:`, response.status);
                            }
                        } catch (error) {
                            console.error(`Error fetching capital allocation data for ${testTicker}:`, error);
                        }
                    }

                    if (!capitalData) {
                        console.log("Capital Chart - all fetch attempts failed, using default data");
                    }
                }

                // Fallback to default data if fetch failed - use recent years (2021-2025)
                const defaultData = capitalData || [
                    { year: "2021", capex: 11.2, buybacks: 43.5, dividends: 14.5, rd: 24.8, ma: 2.1 },
                    { year: "2022", capex: 12.8, buybacks: 47.9, dividends: 15.2, rd: 27.3, ma: 2.8 },
                    { year: "2023", capex: 14.1, buybacks: 52.6, dividends: 15.9, rd: 30.1, ma: 3.2 },
                    { year: "2024", capex: 15.7, buybacks: 57.8, dividends: 16.7, rd: 33.2, ma: 3.9 },
                    { year: "2025", capex: 17.3, buybacks: 63.5, dividends: 17.5, rd: 36.5, ma: 4.3 }
                ];

                renderCapitalChart(defaultData);
            };

            const renderCapitalChart = (defaultData) => {

            const categories = ["capex", "buybacks", "dividends", "rd", "ma"];
            const categoryNames = {
                capex: "CapEx",
                buybacks: "Share Buybacks",
                dividends: "Dividends",
                rd: "R&D",
                ma: "M&A"
            };
            const colors = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"];

            // Stack the data
            const stack = d3.stack()
                .keys(categories)
                .order(d3.stackOrderNone)
                .offset(d3.stackOffsetNone);

            const stackedData = stack(defaultData);

            // Create scales
            const xScale = d3.scaleBand()
                .domain(defaultData.map(d => d.year))
                .range([0, width])
                .padding(0.2);

            const yScale = d3.scaleLinear()
                .domain([0, d3.max(stackedData[stackedData.length - 1], d => d[1])])
                .range([height, 0]);

            const colorScale = d3.scaleOrdinal()
                .domain(categories)
                .range(colors);

            // Create stacked bars
            const layers = g.selectAll(".layer")
                .data(stackedData)
                .enter().append("g")
                .attr("class", "layer")
                .style("fill", d => colorScale(d.key));

            // Create tooltip for capital allocation
            const capitalTooltip = d3.select("body").append("div")
                .attr("class", "d3-capital-tooltip")
                .style("position", "absolute")
                .style("background", "rgba(0, 0, 0, 0.9)")
                .style("color", "#ffffff")
                .style("padding", "12px")
                .style("border-radius", "8px")
                .style("font-size", "12px")
                .style("pointer-events", "none")
                .style("opacity", 0)
                .style("z-index", "10000")
                .style("border", "1px solid rgba(0, 212, 255, 0.5)");

            layers.selectAll("rect")
                .data(d => d)
                .enter().append("rect")
                .attr("x", d => xScale(d.data.year))
                .attr("y", height)
                .attr("height", 0)
                .attr("width", xScale.bandwidth())
                .style("cursor", "pointer")
                .on("mouseover", function(event, d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .style("filter", "brightness(1.2)");

                    capitalTooltip.transition()
                        .duration(200)
                        .style("opacity", 1);

                    const category = d3.select(this.parentNode).datum().key;
                    const value = d.data[category];
                    const total = categories.reduce((sum, cat) => sum + d.data[cat], 0);
                    const percentage = ((value / total) * 100).toFixed(1);

                    capitalTooltip.html(`
                        <strong>${categoryNames[category]}</strong><br/>
                        Year: ${d.data.year}<br/>
                        Amount: $${value.toFixed(1)}B<br/>
                        Share: ${percentage}% of total
                    `)
                        .style("left", (event.pageX + 10) + "px")
                        .style("top", (event.pageY - 10) + "px");
                })
                .on("mouseout", function(d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .style("filter", "brightness(1)");

                    capitalTooltip.transition()
                        .duration(200)
                        .style("opacity", 0);
                })
                .transition()
                .duration(1000)
                .delay((d, i) => i * 200)
                .attr("y", d => yScale(d[1]))
                .attr("height", d => yScale(d[0]) - yScale(d[1]));

            // Add axes with proper formatting
            const xAxis = g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale));

            xAxis.selectAll("text")
                .style("fill", "#ffffff")
                .style("font-size", "12px");

            xAxis.selectAll("path, line")
                .style("stroke", "#ffffff")
                .style("opacity", 0.3);

            xAxis.append("text")
                .attr("x", width / 2)
                .attr("y", 40)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#ffffff")
                .style("font-weight", "bold")
                .text("Year");

            const yAxis = g.append("g")
                .call(d3.axisLeft(yScale).tickFormat(d => "$" + d.toFixed(0) + "B"));

            yAxis.selectAll("text")
                .style("fill", "#ffffff")
                .style("font-size", "11px");

            yAxis.selectAll("path, line")
                .style("stroke", "#ffffff")
                .style("opacity", 0.3);

            yAxis.append("text")
                .attr("transform", "rotate(-90)")
                .attr("y", -50)
                .attr("x", -height / 2)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#ffffff")
                .style("font-weight", "bold")
                .text("Capital Allocation ($B)");

            // Add title
            svg.append("text")
                .attr("x", (width + margin.left + margin.right) / 2)
                .attr("y", 25)
                .attr("text-anchor", "middle")
                .style("font-size", "16px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .text("Capital Allocation Breakdown (5-Year Trend)");

            // Add compact legend
            const legend = svg.append("g")
                .attr("transform", `translate(${width + margin.left + 10}, ${margin.top})`);

            // Legend title
            legend.append("text")
                .attr("x", 0)
                .attr("y", 0)
                .style("font-size", "12px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .text("Capital Allocation");

            const legendItems = legend.selectAll(".legend-item")
                .data(categories)
                .enter().append("g")
                .attr("class", "legend-item")
                .attr("transform", (d, i) => `translate(0, ${i * 18 + 15})`);

            legendItems.append("rect")
                .attr("width", 12)
                .attr("height", 12)
                .style("fill", d => colorScale(d));

            legendItems.append("text")
                .attr("x", 18)
                .attr("y", 10)
                .style("font-size", "10px")
                .style("fill", "#ffffff")
                .text(d => categoryNames[d]);
            };

            // Call the fetch and render function
            fetchAndRenderData();
        }

        function createRevenueMarginChart(container, data, ticker) {
            // Prevent duplicate chart creation
            if (container.querySelector('svg')) {
                console.log("Revenue margin chart already exists in container");
                return;
            }

            const margin = { top: 40, right: 80, bottom: 60, left: 80 };
            const width = 600 - margin.left - margin.right;
            const height = 350 - margin.top - margin.bottom;

            const svg = d3.select(container)
                .append("svg")
                .attr("width", width + margin.left + margin.right)
                .attr("height", height + margin.top + margin.bottom);

            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);

            // Function to fetch and render real data
            const fetchAndRenderData = async () => {
                let revenueData = data;

                // Always try to fetch real data if no data provided
                if (!revenueData) {
                    // Try with provided ticker first, then fallback to common tickers
                    const tickersToTry = ticker ? [ticker, 'AAPL', 'MSFT', 'GOOGL'] : ['AAPL', 'MSFT', 'GOOGL'];

                    for (const testTicker of tickersToTry) {
                        console.log("Revenue Chart - trying to fetch data for ticker:", testTicker);
                        try {
                            const response = await fetch(`/api/chatbot/revenue-margin-data/${testTicker}`);
                            if (response.ok) {
                                revenueData = await response.json();
                                console.log("Fetched real revenue margin data for", testTicker, ":", revenueData);
                                break; // Success, stop trying
                            } else {
                                console.warn(`Failed to fetch revenue data for ${testTicker}, status:`, response.status);
                            }
                        } catch (error) {
                            console.error(`Error fetching revenue margin data for ${testTicker}:`, error);
                        }
                    }

                    if (!revenueData) {
                        console.log("Revenue Chart - all fetch attempts failed, using default data");
                    }
                }

                // Fallback to default data if fetch failed - use recent years (2021-2025)
                const defaultData = revenueData || [
                    { year: "2021", revenue: 85.5, operatingMargin: 18.2 },
                    { year: "2022", revenue: 92.3, operatingMargin: 19.1 },
                    { year: "2023", revenue: 99.8, operatingMargin: 20.5 },
                    { year: "2024", revenue: 107.2, operatingMargin: 21.8 },
                    { year: "2025", revenue: 115.1, operatingMargin: 22.9 }
                ];

                renderRevenueChart(defaultData);
            };

            const renderRevenueChart = (defaultData) => {

            // Create scales
            const xScale = d3.scaleBand()
                .domain(defaultData.map(d => d.year))
                .range([0, width])
                .padding(0.2);

            const yScaleRevenue = d3.scaleLinear()
                .domain([0, d3.max(defaultData, d => d.revenue) * 1.1])
                .range([height, 0]);

            const yScaleMargin = d3.scaleLinear()
                .domain([0, d3.max(defaultData, d => d.operatingMargin) * 1.2])
                .range([height, 0]);

            // Create line generator for margin
            const line = d3.line()
                .x(d => xScale(d.year) + xScale.bandwidth() / 2)
                .y(d => yScaleMargin(d.operatingMargin))
                .curve(d3.curveMonotoneX);

            // Create tooltip for revenue chart
            const revenueTooltip = d3.select("body").append("div")
                .attr("class", "d3-revenue-tooltip")
                .style("position", "absolute")
                .style("background", "rgba(0, 0, 0, 0.9)")
                .style("color", "#ffffff")
                .style("padding", "12px")
                .style("border-radius", "8px")
                .style("font-size", "12px")
                .style("pointer-events", "none")
                .style("opacity", 0)
                .style("z-index", "10000")
                .style("border", "1px solid rgba(0, 212, 255, 0.5)");

            // Create revenue bars
            g.selectAll(".revenue-bar")
                .data(defaultData)
                .enter().append("rect")
                .attr("class", "revenue-bar")
                .attr("x", d => xScale(d.year))
                .attr("y", height)
                .attr("width", xScale.bandwidth())
                .attr("height", 0)
                .attr("fill", "#4CAF50")
                .attr("opacity", 0.7)
                .style("cursor", "pointer")
                .on("mouseover", function(event, d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .style("filter", "brightness(1.2)");

                    revenueTooltip.transition()
                        .duration(200)
                        .style("opacity", 1);

                    revenueTooltip.html(`
                        <strong>${d.year} Performance</strong><br/>
                        Revenue: $${d.revenue.toFixed(1)}B<br/>
                        Operating Margin: ${d.operatingMargin.toFixed(1)}%<br/>
                        Operating Income: $${(d.revenue * d.operatingMargin / 100).toFixed(1)}B
                    `)
                        .style("left", (event.pageX + 10) + "px")
                        .style("top", (event.pageY - 10) + "px");
                })
                .on("mouseout", function(d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .style("filter", "brightness(1)");

                    revenueTooltip.transition()
                        .duration(200)
                        .style("opacity", 0);
                })
                .transition()
                .duration(1000)
                .delay((d, i) => i * 200)
                .attr("y", d => yScaleRevenue(d.revenue))
                .attr("height", d => height - yScaleRevenue(d.revenue));

            // Create margin line
            const marginPath = g.append("path")
                .datum(defaultData)
                .attr("class", "margin-line")
                .attr("fill", "none")
                .attr("stroke", "#FF5722")
                .attr("stroke-width", 3)
                .attr("d", line);

            // Animate the line
            const totalLength = marginPath.node().getTotalLength();
            marginPath
                .attr("stroke-dasharray", totalLength + " " + totalLength)
                .attr("stroke-dashoffset", totalLength)
                .transition()
                .duration(2000)
                .delay(1000)
                .attr("stroke-dashoffset", 0);

            // Add margin data points
            g.selectAll(".margin-dot")
                .data(defaultData)
                .enter().append("circle")
                .attr("class", "margin-dot")
                .attr("cx", d => xScale(d.year) + xScale.bandwidth() / 2)
                .attr("cy", d => yScaleMargin(d.operatingMargin))
                .attr("r", 0)
                .attr("fill", "#FF5722")
                .transition()
                .duration(500)
                .delay((d, i) => i * 200 + 1500)
                .attr("r", 5);

            // Add value labels on bars
            g.selectAll(".revenue-label")
                .data(defaultData)
                .enter().append("text")
                .attr("class", "revenue-label")
                .attr("x", d => xScale(d.year) + xScale.bandwidth() / 2)
                .attr("y", d => yScaleRevenue(d.revenue) - 5)
                .attr("text-anchor", "middle")
                .style("font-size", "11px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .text(d => "$" + d.revenue + "B")
                .style("opacity", 0)
                .transition()
                .duration(500)
                .delay((d, i) => i * 200 + 1000)
                .style("opacity", 1);

            // Add margin labels
            g.selectAll(".margin-label")
                .data(defaultData)
                .enter().append("text")
                .attr("class", "margin-label")
                .attr("x", d => xScale(d.year) + xScale.bandwidth() / 2)
                .attr("y", d => yScaleMargin(d.operatingMargin) - 10)
                .attr("text-anchor", "middle")
                .style("font-size", "11px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .text(d => d.operatingMargin + "%")
                .style("opacity", 0)
                .transition()
                .duration(500)
                .delay((d, i) => i * 200 + 2000)
                .style("opacity", 1);

            // Add axes with proper formatting
            const xAxis = g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale));

            xAxis.selectAll("text")
                .style("fill", "#ffffff")
                .style("font-size", "12px");

            xAxis.selectAll("path, line")
                .style("stroke", "#ffffff")
                .style("opacity", 0.3);

            const yAxisLeft = g.append("g")
                .call(d3.axisLeft(yScaleRevenue).tickFormat(d => "$" + d.toFixed(0) + "B"));

            yAxisLeft.selectAll("text")
                .style("fill", "#4CAF50")
                .style("font-size", "11px");

            yAxisLeft.selectAll("path, line")
                .style("stroke", "#4CAF50")
                .style("opacity", 0.5);

            yAxisLeft.append("text")
                .attr("transform", "rotate(-90)")
                .attr("y", -50)
                .attr("x", -height / 2)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#4CAF50")
                .style("font-weight", "bold")
                .text("Revenue ($B)");

            const yAxisRight = g.append("g")
                .attr("transform", `translate(${width},0)`)
                .call(d3.axisRight(yScaleMargin).tickFormat(d => d.toFixed(1) + "%"));

            yAxisRight.selectAll("text")
                .style("fill", "#FF5722")
                .style("font-size", "11px");

            yAxisRight.selectAll("path, line")
                .style("stroke", "#FF5722")
                .style("opacity", 0.5);

            yAxisRight.append("text")
                .attr("transform", "rotate(-90)")
                .attr("y", 50)
                .attr("x", -height / 2)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#FF5722")
                .style("font-weight", "bold")
                .text("Operating Margin (%)");

            // Add title
            svg.append("text")
                .attr("x", (width + margin.left + margin.right) / 2)
                .attr("y", 25)
                .attr("text-anchor", "middle")
                .style("font-size", "16px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .text("Revenue & Operating Margin History");
            };

            // Call the fetch and render function
            fetchAndRenderData();
        }

        function createRiskImpactMap(container, data, ticker) {
            // Prevent duplicate chart creation
            if (container.querySelector('svg')) {
                console.log("Risk impact map already exists in container");
                return;
            }

            const margin = { top: 40, right: 120, bottom: 60, left: 80 };
            const width = 500 - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;

            const svg = d3.select(container)
                .append("svg")
                .attr("width", width + margin.left + margin.right)
                .attr("height", height + margin.top + margin.bottom);

            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);

            // Function to fetch and render real data
            const fetchAndRenderData = async () => {
                let riskData = data;

                // Always try to fetch real data if no data provided
                if (!riskData) {
                    // Try with provided ticker first, then fallback to common tickers
                    const tickersToTry = ticker ? [ticker, 'AAPL', 'MSFT', 'GOOGL'] : ['AAPL', 'MSFT', 'GOOGL'];

                    for (const testTicker of tickersToTry) {
                        console.log("Risk Chart - trying to fetch data for ticker:", testTicker);
                        try {
                            const response = await fetch(`/api/chatbot/risk-impact-data/${testTicker}`);
                            if (response.ok) {
                                riskData = await response.json();
                                console.log("Fetched real risk impact data for", testTicker, ":", riskData);
                                break; // Success, stop trying
                            } else {
                                console.warn(`Failed to fetch risk data for ${testTicker}, status:`, response.status);
                            }
                        } catch (error) {
                            console.error(`Error fetching risk impact data for ${testTicker}:`, error);
                        }
                    }

                    if (!riskData) {
                        console.log("Risk Chart - all fetch attempts failed, using default data");
                    }
                }

                // Fallback to default data if fetch failed
                const defaultData = riskData || [
                    { risk: "AI Disruption", likelihood: 7, impact: 8, severity: "High" },
                    { risk: "Regulatory Changes", likelihood: 6, impact: 6, severity: "Medium" },
                    { risk: "Competition", likelihood: 8, impact: 5, severity: "Medium" },
                    { risk: "Economic Downturn", likelihood: 4, impact: 7, severity: "Medium" },
                    { risk: "Key Personnel Loss", likelihood: 3, impact: 6, severity: "Low" },
                    { risk: "Cyber Security", likelihood: 5, impact: 8, severity: "High" },
                    { risk: "Supply Chain", likelihood: 4, impact: 4, severity: "Low" },
                    { risk: "Currency Fluctuation", likelihood: 6, impact: 3, severity: "Low" }
                ];

                renderRiskChart(defaultData);
            };

            const renderRiskChart = (defaultData) => {

            // Create scales
            const xScale = d3.scaleLinear()
                .domain([0, 10])
                .range([0, width]);

            const yScale = d3.scaleLinear()
                .domain([0, 10])
                .range([height, 0]);

            const colorScale = d3.scaleOrdinal()
                .domain(["Low", "Medium", "High"])
                .range(["#4CAF50", "#FF9800", "#F44336"]);

            const radiusScale = d3.scaleLinear()
                .domain([0, 10])
                .range([8, 20]);

            // Create quadrant backgrounds with clearer colors and labels
            const quadrants = [
                { x: 0, y: 0, width: width/2, height: height/2, label: "Monitor", color: "rgba(255, 193, 7, 0.15)", description: "Low Impact, High Likelihood" },
                { x: width/2, y: 0, width: width/2, height: height/2, label: "Critical", color: "rgba(220, 53, 69, 0.15)", description: "High Impact, High Likelihood" },
                { x: 0, y: height/2, width: width/2, height: height/2, label: "Accept", color: "rgba(40, 167, 69, 0.15)", description: "Low Impact, Low Likelihood" },
                { x: width/2, y: height/2, width: width/2, height: height/2, label: "Prepare", color: "rgba(255, 107, 53, 0.15)", description: "High Impact, Low Likelihood" }
            ];

            g.selectAll(".quadrant")
                .data(quadrants)
                .enter().append("rect")
                .attr("class", "quadrant")
                .attr("x", d => d.x)
                .attr("y", d => d.y)
                .attr("width", d => d.width)
                .attr("height", d => d.height)
                .attr("fill", d => d.color)
                .attr("stroke", "#ddd")
                .attr("stroke-width", 1)
                .style("opacity", 0.3);

            // Add quadrant labels with better positioning
            g.selectAll(".quadrant-label-main")
                .data(quadrants)
                .enter().append("text")
                .attr("class", "quadrant-label-main")
                .attr("x", d => d.x + d.width/2)
                .attr("y", d => d.y + d.height/2 - 8)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .style("font-size", "14px")
                .style("fill", "#ffffff")
                .style("font-weight", "bold")
                .style("opacity", "0.9")
                .text(d => d.label);

            // Add quadrant descriptions
            g.selectAll(".quadrant-label-desc")
                .data(quadrants)
                .enter().append("text")
                .attr("class", "quadrant-label-desc")
                .attr("x", d => d.x + d.width/2)
                .attr("y", d => d.y + d.height/2 + 8)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .style("font-size", "9px")
                .style("fill", "#ffffff")
                .style("opacity", "0.6")
                .text(d => d.description);

            // Create tooltip for risk map
            const riskTooltip = d3.select("body").append("div")
                .attr("class", "d3-risk-tooltip")
                .style("position", "absolute")
                .style("background", "rgba(0, 0, 0, 0.9)")
                .style("color", "#ffffff")
                .style("padding", "12px")
                .style("border-radius", "8px")
                .style("font-size", "12px")
                .style("pointer-events", "none")
                .style("opacity", 0)
                .style("z-index", "10000")
                .style("border", "1px solid rgba(0, 212, 255, 0.5)");

            // Create risk bubbles
            const bubbles = g.selectAll(".risk-bubble")
                .data(defaultData)
                .enter().append("g")
                .attr("class", "risk-bubble");

            bubbles.append("circle")
                .attr("cx", d => xScale(d.likelihood))
                .attr("cy", d => yScale(d.impact))
                .attr("r", 0)
                .attr("fill", d => colorScale(d.severity))
                .attr("stroke", "#fff")
                .attr("stroke-width", 2)
                .style("opacity", 0.8)
                .style("cursor", "pointer")
                .on("mouseover", function(event, d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr("stroke-width", 4)
                        .attr("stroke", "#00d4ff")
                        .style("filter", "brightness(1.2)");

                    riskTooltip.transition()
                        .duration(200)
                        .style("opacity", 1);

                    const riskScore = (d.likelihood * d.impact / 10).toFixed(1);
                    riskTooltip.html(`
                        <strong>${d.risk}</strong><br/>
                        Likelihood: ${d.likelihood}/10<br/>
                        Impact: ${d.impact}/10<br/>
                        Risk Score: ${riskScore}/10<br/>
                        Severity: <span style="color: ${colorScale(d.severity)}">${d.severity}</span>
                    `)
                        .style("left", (event.pageX + 10) + "px")
                        .style("top", (event.pageY - 10) + "px");
                })
                .on("mouseout", function(d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr("stroke-width", 2)
                        .attr("stroke", "#fff")
                        .style("filter", "brightness(1)");

                    riskTooltip.transition()
                        .duration(200)
                        .style("opacity", 0);
                })
                .transition()
                .duration(1000)
                .delay((d, i) => i * 150)
                .attr("r", d => radiusScale(Math.max(d.likelihood, d.impact)));

            // Add risk labels
            bubbles.append("text")
                .attr("x", d => xScale(d.likelihood))
                .attr("y", d => yScale(d.impact) + 4)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle")
                .style("font-size", "9px")
                .style("font-weight", "bold")
                .style("fill", "#fff")
                .text(d => d.risk.split(' ')[0])
                .style("opacity", 0)
                .transition()
                .duration(500)
                .delay((d, i) => i * 150 + 1000)
                .style("opacity", 1);

            // Add axes with proper formatting
            const xAxis = g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale).tickFormat(d => d.toFixed(0)));

            xAxis.selectAll("text")
                .style("fill", "#ffffff")
                .style("font-size", "11px");

            xAxis.selectAll("path, line")
                .style("stroke", "#ffffff")
                .style("opacity", 0.3);

            xAxis.append("text")
                .attr("x", width / 2)
                .attr("y", 40)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#ffffff")
                .style("font-weight", "bold")
                .text("Likelihood (1-10)");

            const yAxis = g.append("g")
                .call(d3.axisLeft(yScale).tickFormat(d => d.toFixed(0)));

            yAxis.selectAll("text")
                .style("fill", "#ffffff")
                .style("font-size", "11px");

            yAxis.selectAll("path, line")
                .style("stroke", "#ffffff")
                .style("opacity", 0.3);

            yAxis.append("text")
                .attr("transform", "rotate(-90)")
                .attr("y", -50)
                .attr("x", -height / 2)
                .attr("text-anchor", "middle")
                .style("font-size", "14px")
                .style("fill", "#ffffff")
                .style("font-weight", "bold")
                .text("Impact (1-10)");

            // Add grid lines
            g.append("line")
                .attr("x1", width/2)
                .attr("x2", width/2)
                .attr("y1", 0)
                .attr("y2", height)
                .attr("stroke", "#ccc")
                .attr("stroke-dasharray", "3,3");

            g.append("line")
                .attr("x1", 0)
                .attr("x2", width)
                .attr("y1", height/2)
                .attr("y2", height/2)
                .attr("stroke", "#ccc")
                .attr("stroke-dasharray", "3,3");

            // Add title
            svg.append("text")
                .attr("x", (width + margin.left + margin.right) / 2)
                .attr("y", 25)
                .attr("text-anchor", "middle")
                .style("font-size", "16px")
                .style("font-weight", "bold")
                .style("fill", "#ffffff")
                .text("Risk Impact Map");

            // Add legend
            const legend = svg.append("g")
                .attr("transform", `translate(${width + margin.left + 10}, ${margin.top})`);

            const severityLevels = ["Low", "Medium", "High"];
            const legendItems = legend.selectAll(".legend-item")
                .data(severityLevels)
                .enter().append("g")
                .attr("class", "legend-item")
                .attr("transform", (d, i) => `translate(0, ${i * 25})`);

            legendItems.append("circle")
                .attr("cx", 10)
                .attr("cy", 10)
                .attr("r", 8)
                .style("fill", d => colorScale(d));

            legendItems.append("text")
                .attr("x", 25)
                .attr("y", 15)
                .style("font-size", "12px")
                .style("fill", "#ffffff")
                .text(d => d + " Severity");
            };

            // Call the fetch and render function
            fetchAndRenderData();
        }

        function addMessage(sender, messageData, isHTML = false) {
            const messageLi = document.createElement("li");
            messageLi.classList.add("message", sender);

            const avatarSpan = document.createElement("span");
            avatarSpan.className = "bot-avatar";
            avatarSpan.innerHTML =
                '<i class="fas fa-robot chatbot-icon-small"></i>';
            if (sender === "user")
                avatarSpan.innerHTML =
                    '<i class="fas fa-user chatbot-icon-small"></i>';
            messageLi.appendChild(avatarSpan);

            const messageBubbleP = document.createElement("p");

            // Handle different message data formats
            let textForDisplay = "";
            if (typeof messageData === "string") {
                textForDisplay = messageData;
            } else if (
                typeof messageData === "object" &&
                messageData !== null
            ) {
                textForDisplay =
                    messageData.message ||
                    messageData.response ||
                    JSON.stringify(messageData);
            } else {
                textForDisplay = String(messageData || "");
            }

                if (sender === "bot") {
        // Look for stock analysis messages that might contain ratings
        const hasRatingIndicators = 
            textForDisplay.includes("Business Quality") ||
            textForDisplay.includes("Financial Strength") ||
            textForDisplay.includes("Valuation") ||
            textForDisplay.includes("Management") ||
            textForDisplay.includes("Overall Rating") ||
            textForDisplay.includes("Visual Rating") ||
            textForDisplay.includes("/10") && textForDisplay.includes("%") ||
            /\d+%/.test(textForDisplay) && /analysis|rating|evaluation|assessment/i.test(textForDisplay);
            
        if (hasRatingIndicators) {
            const ratings = extractRatingsFromText(textForDisplay);
            console.log("Found potential rating indicators, extracted:", ratings);
            
            if (Object.values(ratings).some((r) => r > 0)) {
                const chartPlaceholder = document.createElement("div");
                chartPlaceholder.className = "rating-visualization-container";
                chartPlaceholder.dataset.ratings = JSON.stringify(ratings);
                messageBubbleP.appendChild(chartPlaceholder);
                
                // Add observer with higher priority 
                if (animationObserver) {
                    animationObserver.observe(chartPlaceholder);
                    
                    // Force a check after a short delay in case the observer didn't trigger
                    setTimeout(() => {
                        if (!chartPlaceholder.classList.contains("visible")) {
                            console.log("Forcing chart render");
                            chartPlaceholder.classList.add("visible");
                            const chartNode = createEnhancedRatingCharts(ratings);
                            chartPlaceholder.innerHTML = "";
                            chartPlaceholder.appendChild(chartNode);
                            chartPlaceholder.dataset.rendered = "true";
                        }
                    }, 1000);
                } else {
                    // Fallback for no observer support
                    const chartNode = createEnhancedRatingCharts(ratings);
                    chartPlaceholder.appendChild(chartNode);
                }
            }
        }

        // Detect and render advanced D3.js visualizations
        renderAdvancedVisualizations(textForDisplay, messageBubbleP);
    }

    function renderAdvancedVisualizations(text, container) {
        // This function is now handled by the DOM-based approach below
        // to prevent duplicate chart rendering
        console.log("renderAdvancedVisualizations called - delegating to DOM-based rendering");
    }

            const textDiv = document.createElement("div");
            textDiv.className = "message-text-content";

            const hasHtml =
                (typeof messageData === "object" && messageData.is_html) ||
                (typeof textForDisplay === "string" &&
                    (/<[a-z][\s\S]*>/i.test(textForDisplay) ||
                     textForDisplay.includes('-wrapper')));
            if (hasHtml) {
                const tempDiv = document.createElement("div");
                tempDiv.innerHTML = textForDisplay;
                tempDiv.querySelectorAll("script").forEach((s) => s.remove());
                textDiv.innerHTML = tempDiv.innerHTML;

                // After setting innerHTML, check for chart wrappers in the actual DOM
                setTimeout(() => {
                    const wrappers = textDiv.querySelectorAll('[class*="-wrapper"]');
                    wrappers.forEach(wrapper => {
                        const className = wrapper.className;
                        console.log("Found wrapper in DOM:", className);

                        // Check if chart has already been rendered to prevent duplicates
                        if (wrapper.hasAttribute('data-chart-rendered')) {
                            console.log("Chart already rendered for:", className);
                            return;
                        }

                        // Mark as rendered to prevent duplicates
                        wrapper.setAttribute('data-chart-rendered', 'true');

                        // Extract ticker from current analysis state or message content
                        let currentTicker = currentAnalysisState && currentAnalysisState.ticker ?
                            currentAnalysisState.ticker.split('.')[0] : null;

                        // Fallback: try to extract ticker from the message content
                        if (!currentTicker) {
                            const messageContent = wrapper.closest('.message')?.textContent || '';
                            const tickerMatch = messageContent.match(/\b([A-Z]{1,5})\b/);
                            if (tickerMatch) {
                                currentTicker = tickerMatch[1];
                                console.log("Chart rendering - extracted ticker from message content:", currentTicker);
                            }
                        }

                        console.log("Chart rendering - currentAnalysisState:", currentAnalysisState);
                        console.log("Chart rendering - final currentTicker:", currentTicker);

                        if (className.includes('valuation-sensitivity-matrix-wrapper')) {
                            setTimeout(() => createValuationSensitivityMatrix(wrapper, null, currentTicker), 500);
                        } else if (className.includes('peer-valuation-comparison-wrapper')) {
                            setTimeout(() => createPeerComparisonChart(wrapper, null, currentTicker), 700);
                        } else if (className.includes('capital-allocation-breakdown-wrapper')) {
                            setTimeout(() => createCapitalAllocationChart(wrapper, null, currentTicker), 900);
                        } else if (className.includes('revenue-margin-history-wrapper')) {
                            setTimeout(() => createRevenueMarginChart(wrapper, null, currentTicker), 1100);
                        } else if (className.includes('risk-impact-map-wrapper')) {
                            setTimeout(() => createRiskImpactMap(wrapper, null, currentTicker), 1300);
                        }
                    });
                }, 100);
            } else {
                textDiv.textContent = textForDisplay;
            }
            messageBubbleP.appendChild(textDiv);

            messageLi.appendChild(messageBubbleP);
            chatbotMessages.appendChild(messageLi);
            scrollToBottom();
        }

        // --- Rest of your chatbot logic ---
        function toggleChatWindow() {
            if (container.classList.contains("settings-open")) {
                container.classList.remove("settings-open");
            }
            const shouldOpen = chatbotWindow.classList.contains("hidden");
            shouldOpen ? openChatWindow() : closeChatWindow();
        }

        function openChatWindow() {
            if (isOpen) return;
            isOpen = true;
            chatbotWindow.classList.remove("hidden");
            setTimeout(() => {
                scrollToBottom();
                if (!chatbotInput.disabled)
                    chatbotInput.focus({ preventScroll: true });
            }, 50);
        }

        function closeChatWindow() {
            if (!isOpen) return;
            isOpen = false;
            chatbotWindow.classList.add("hidden");
        }

        function scrollToBottom() {
            if ("scrollBehavior" in document.documentElement.style) {
                chatbotMessages.scrollTo({
                    top: chatbotMessages.scrollHeight,
                    behavior: "smooth",
                });
            } else {
                chatbotMessages.scrollTop = chatbotMessages.scrollHeight;
            }
        }

        function showTypingIndicator() {
            typingIndicator.classList.remove("hidden");
            scrollToBottom();
        }

        function hideTypingIndicator() {
            typingIndicator.classList.add("hidden");
        }

        function setInputState(disabled, placeholder = "") {
            chatbotInput.disabled = disabled;
            chatbotSend.disabled = disabled;
            chatbotInput.placeholder = disabled
                ? placeholder || "Assistant is processing..."
                : "Type your message...";
            if (disabled) chatbotInput.blur();
            else {
                // Reset currentBotAction when enabling input
                currentBotAction = null;
                if (isOpen)
                    setTimeout(
                        () => chatbotInput.focus({ preventScroll: true }),
                        50,
                    );
            }
        }

        async function handleSendMessage() {
            const messageText = chatbotInput.value.trim();
            if (messageText === "" || isBotTyping) return;

            // Validate message format
            if (typeof messageText !== "string" || messageText.length === 0) {
                console.error("Invalid message format:", messageText);
                return;
            }

            console.log("Sending message:", messageText);
            
            // Handle analysis commands - returns true if handled and we should stop
            if (handleAnalysisCommand(messageText)) {
                return;
            }
            
            // If we have an active analysis and this is a direct ticker mention without an analysis question
            if (currentAnalysisState.active && 
                currentAnalysisState.modelSelected &&
                messageText.toUpperCase().includes(currentAnalysisState.ticker) &&
                !isAnalysisQuestion(messageText, currentAnalysisState.ticker)) {
                    
                // Add a hint to help the user formulate a proper question
                console.log("Detected ticker mention without analysis question");
                setTimeout(() => {
                    const hint = document.createElement("li");
                    hint.className = "message bot hint";
                    hint.innerHTML = `<span class="bot-avatar"><i class="fas fa-lightbulb chatbot-icon-small"></i></span><p>Hint: Try asking specific questions about ${currentAnalysisState.ticker}, such as "What is your analysis of ${currentAnalysisState.ticker}?" or "What do you think about ${currentAnalysisState.ticker}'s growth prospects?"</p>`;
                    hint.style.opacity = "0.8";
                    chatbotMessages.appendChild(hint);
                    scrollToBottom();
                }, 500);
            }
            
            // If we reach here, proceed with normal message handling
            isBotTyping = true;
            addMessage("user", messageText);
            chatbotInput.value = "";
            setInputState(true);
            showTypingIndicator();

            // Ensure proper payload format
            const payload = {
                message: messageText,
            };
            console.log("Prepared payload:", payload);

            try {
                await sendChoiceToBackend("/api/chatbot", payload);
            } catch (error) {
                console.error("Error in handleSendMessage:", error);
                hideTypingIndicator();
                setInputState(false);
                isBotTyping = false;
                
                // Reset analysis state on serious errors to prevent stuck states
                if (error.message && (
                    error.message.includes("404") || 
                    error.message.includes("500") || 
                    error.message.includes("session") || 
                    error.message.includes("state")
                )) {
                    resetAnalysisState();
                    console.log("Analysis state reset due to error");
                }
            }
        }

        async function sendChoiceToBackend(url, payload) {
            showTypingIndicator();
            setInputState(true, "Please wait...");

            // Validate payload
            if (!payload || typeof payload !== "object") {
                console.error("Invalid payload:", payload);
                hideTypingIndicator();
                addMessage("bot", "😥 Sorry, invalid request format.");
                setInputState(false);
                isBotTyping = false;
                return;
            }

            try {
                console.log(`=== CHATBOT REQUEST DEBUG ===`);
                console.log(`URL: ${url}`);
                console.log(`Payload:`, payload);
                console.log(`Payload type:`, typeof payload);
                console.log(
                    `Stringified payload:`,
                    JSON.stringify(payload, null, 2),
                );
                console.log(`Current origin:`, window.location.origin);
                console.log(
                    `Full URL:`,
                    new URL(url, window.location.origin).href,
                );

                // Special debugging for API key submissions
                if (url.includes("submit_key")) {
                    console.log(`=== API KEY SUBMISSION CONTEXT ===`);
                    console.log(`Session cookies:`, document.cookie);
                    console.log(`User agent:`, navigator.userAgent);
                    console.log(`Referrer:`, document.referrer);
                }

                // Add a small delay to ensure UI updates
                await new Promise((resolve) => setTimeout(resolve, 100));

                const fetchOptions = {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        Accept: "application/json",
                        "X-Requested-With": "XMLHttpRequest",
                    },
                    body: JSON.stringify(payload),
                    credentials: "same-origin", // Include cookies/session
                };

                console.log(`Fetch options:`, fetchOptions);

                const response = await fetch(url, fetchOptions);

                console.log(`=== RESPONSE DEBUG ===`);
                console.log(
                    `Response status: ${response.status} ${response.statusText}`,
                );
                console.log(
                    `Response headers:`,
                    Object.fromEntries(response.headers.entries()),
                );
                console.log(`Response URL:`, response.url);
                console.log(`Response type:`, response.type);

                if (!response.ok) {
                    let errorMessage = `Server error: ${response.status} ${response.statusText}`;
                    let errorDetails = "";
                    let rawResponse = "";

                    try {
                        // First try to get raw response text
                        rawResponse = await response.text();
                        console.log(`Raw error response:`, rawResponse);

                        // Try to parse as JSON
                        if (rawResponse) {
                            try {
                                const errorData = JSON.parse(rawResponse);
                                if (errorData.message) {
                                    errorDetails = errorData.message;
                                } else if (errorData.error) {
                                    errorDetails = errorData.error;
                                } else {
                                    errorDetails = JSON.stringify(errorData);
                                }
                            } catch (jsonError) {
                                console.log(
                                    `Response is not JSON, using as text`,
                                );
                                errorDetails = rawResponse.substring(0, 300);
                            }
                        }
                    } catch (parseError) {
                        console.error(
                            `Error reading error response:`,
                            parseError,
                        );
                        errorDetails = "Unable to read error response";
                    }

                    if (errorDetails) {
                        errorMessage += ` - ${errorDetails}`;
                    }

                    console.error(`Final error message:`, errorMessage);
                    throw new Error(errorMessage);
                }

                // Parse successful response
                const responseText = await response.text();
                console.log(`Raw response text:`, responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                    console.log(`Parsed response data:`, data);
                } catch (jsonError) {
                    console.error(`Failed to parse JSON response:`, jsonError);
                    console.log(`Response text:`, responseText);
                    throw new Error(
                        `Invalid JSON response: ${responseText.substring(0, 100)}`,
                    );
                }

                hideTypingIndicator();

                // Handle different response formats
                if (data && typeof data === "object") {
                    // Check if response has expected structure
                    if (data.message || data.response || data.error) {
                        addMessage("bot", data);
                        currentBotAction = data.action_required || null;

                        // Synchronize frontend analysis state with backend responses
                        if (data.type === "prompt_model_choice" && data.ticker) {
                            // Backend is asking for model choice - update frontend state
                            currentAnalysisState.active = true;
                            currentAnalysisState.ticker = data.ticker;
                            currentAnalysisState.modelSelected = false;
                            currentAnalysisState.modelType = null;
                            console.log("Updated analysis state from backend response:", currentAnalysisState);
                        } else if (data.type === "analysis_response") {
                            // Analysis completed - preserve ticker from response for chart rendering
                            const responseTicker = data.ticker || currentAnalysisState.ticker;
                            resetAnalysisState();
                            // Preserve ticker for chart rendering
                            currentAnalysisState.ticker = responseTicker;
                            currentAnalysisState.analysisCompleted = true;
                            console.log("Analysis completed - preserved ticker for charts:", responseTicker);
                        }
                    } else {
                        console.warn("Response missing expected fields:", data);
                        addMessage("bot", {
                            message: "Response received but format unexpected",
                            data: data,
                        });
                        currentBotAction = null;
                    }
                } else {
                    // Fallback for unexpected response format
                    console.warn("Unexpected response format:", data);
                    addMessage("bot", { message: String(data) });
                    currentBotAction = null;
                }

                // Debug the current action state
                console.log("Current action state after response:", currentBotAction);
                
                // Only keep input disabled if there are actual buttons in the last message
                const lastMessage = chatbotMessages.lastElementChild;
                const hasButtons = lastMessage && lastMessage.querySelector('.chatbot-button:not([disabled])');
                
                if (currentBotAction !== null && hasButtons) {
                    setInputState(true, "Please respond using the buttons.");
                } else {
                    // If no buttons or no action required, enable input
                    currentBotAction = null;
                    setInputState(false);
                }
            } catch (error) {
                console.error(`=== CHATBOT ERROR ===`);
                console.error(`URL: ${url}`);
                console.error(`Error:`, error);
                console.error(`Error stack:`, error.stack);

                hideTypingIndicator();

                let userFriendlyMessage = "😥 Sorry, an error occurred.";
                const errorMsg = error.message.toLowerCase();

                if (errorMsg.includes("400")) {
                    if (url.includes("submit_key")) {
                        userFriendlyMessage =
                            "❌ API Key submission failed. This might be because:";
                        userFriendlyMessage +=
                            "\n• The session expired - please start the analysis again";
                        userFriendlyMessage +=
                            "\n• The API key format is incorrect";
                        userFriendlyMessage +=
                            "\n• There was a mismatch in the selected model";
                        userFriendlyMessage +=
                            "\n\nPlease try starting your analysis request again.";
                    } else {
                        userFriendlyMessage +=
                            " The request format seems incorrect. Please try again.";
                    }
                    console.log(
                        "DEBUG: 400 error suggests payload format or session state issue",
                    );
                } else if (errorMsg.includes("404")) {
                    userFriendlyMessage +=
                        " The chatbot endpoint was not found.";
                } else if (errorMsg.includes("500")) {
                    userFriendlyMessage +=
                        " The server encountered an internal error.";
                } else if (
                    errorMsg.includes("failed to fetch") ||
                    errorMsg.includes("network")
                ) {
                    userFriendlyMessage +=
                        " Unable to connect to the server. Please check your connection.";
                } else if (errorMsg.includes("json")) {
                    userFriendlyMessage +=
                        " The server response was not in the expected format.";
                } else if (
                    errorMsg.includes("invalid state") ||
                    errorMsg.includes("start the analysis request again")
                ) {
                    userFriendlyMessage =
                        "🔄 Session expired or invalid state. Please start your analysis request again by asking for a new stock analysis.";
                    // Reset analysis state on session errors
                    resetAnalysisState();
                } else if (errorMsg.includes("model mismatch")) {
                    userFriendlyMessage =
                        "⚠️ Model selection mismatch. Please start your analysis request again.";
                    // Reset analysis state on model mismatch
                    resetAnalysisState();
                } else if (
                    errorMsg.includes("missing api key") ||
                    errorMsg.includes("missing model")
                ) {
                    userFriendlyMessage =
                        "❌ Required information is missing. Please ensure you've selected a model and entered a valid API key.";
                } else {
                    userFriendlyMessage += ` Error details: ${error.message}`;
                }

                addMessage("bot", {
                    message: userFriendlyMessage,
                    error: true,
                });
                setInputState(false);
            } finally {
                isBotTyping = false;
            }
        }

        function extractRatingsFromText(text) {
            const ratings = {
                overall: 0,
                business: 0,
                financial: 0,
                valuation: 0,
                management: 0,
            };
            
            // Enhanced patterns to capture different formats
            const patterns = {
                // Format: "Overall Rating: 31.0/40 (77.5%)"
                overall: [
                    /Overall Rating:?\s*\d+(\.\d+)?\/\d+\s*\((\d+(\.\d+)?)%\)/i,
                    /Overall Rating:?\s*(\d+(\.\d+)?)\%/i,
                    /Overall:?\s*(\d+(\.\d+)?)\%/i,
                    /Overall:?\s*\d+(\.\d+)?\/\d+\s*\((\d+(\.\d+)?)%\)/i,
                    /Overall Investment Rating:?\s*(\d+(\.\d+)?)\%/i,
                    /Overall Investment Rating\s*(\d+(\.\d+)?)%/i
                ],
                // Format: "Business Quality/Moat: 9/10 (90%)"
                business: [
                    /Business Quality\/Moat:?\s*\d+(\.\d+)?\/\d+\s*\((\d+(\.\d+)?)%\)/i,
                    /Business Quality:?\s*(\d+(\.\d+)?)\%/i,
                    /Business:?\s*(\d+(\.\d+)?)\%/i,
                    /Moat:?\s*(\d+(\.\d+)?)\%/i
                ],
                // Format: "Financial Strength: 9/10 (90%)"
                financial: [
                    /Financial Strength:?\s*\d+(\.\d+)?\/\d+\s*\((\d+(\.\d+)?)%\)/i,
                    /Financial:?\s*(\d+(\.\d+)?)\%/i,
                    /Financial Strength:?\s*(\d+(\.\d+)?)\%/i
                ],
                // Format: "Valuation: 6/10 (60%)"
                valuation: [
                    /Valuation:?\s*\d+(\.\d+)?\/\d+\s*\((\d+(\.\d+)?)%\)/i,
                    /Valuation:?\s*(\d+(\.\d+)?)\%/i
                ],
                // Format: "Management: 7/10 (70%)"
                management: [
                    /Management:?\s*\d+(?:\.\d+)?\/\d+\s*\((\d+(\.\d+)?)%\)/i,
                    /Management:?\s*(\d+(\.\d+)?)\%/i
                ]
            };

            // Also try to match simple patterns like "Business Quality: 90%"
            const simplePattern = /([A-Za-z\s\/]+):\s*(\d+(\.\d+)?)%/gi;
            let simpleMatch;
            while ((simpleMatch = simplePattern.exec(text)) !== null) {
                const category = simpleMatch[1].trim().toLowerCase();
                const value = parseFloat(simpleMatch[2]);
                
                if (category.includes('business') || category.includes('moat')) {
                    ratings.business = Math.round(value);
                } else if (category.includes('financial') || category.includes('strength')) {
                    ratings.financial = Math.round(value);
                } else if (category.includes('valuation') || category.includes('value')) {
                    ratings.valuation = Math.round(value);
                } else if (category.includes('management') || category.includes('leadership')) {
                    ratings.management = Math.round(value);
                } else if (category.includes('overall') || category.includes('rating') || category.includes('investment rating')) {
                    ratings.overall = Math.round(value);
                }
            }

            // Try all pattern variants for each category
            for (const key in patterns) {
                for (const pattern of patterns[key]) {
                    const match = text.match(pattern);
                    if (match) {
                        // If the pattern has a capture group for the percentage, use it
                        if (match[1] && match[1].match(/\d/)) {
                            ratings[key] = Math.round(parseFloat(match[1]));
                            break;
                        } 
                        // Otherwise it might be in the second capture group
                        else if (match[2] && match[2].match(/\d/)) {
                            ratings[key] = Math.round(parseFloat(match[2]));
                            break;
                        }
                    }
                }
            }
            
            // Look for direct statements like "Business Quality/Moat: 9/10 (90%)"
            const directRatingPattern = /(\w+(?:\s+\w+)?(?:\/\w+)?)\s*:\s*\d+(?:\.\d+)?\s*\/\s*\d+\s*\(\s*(\d+(?:\.\d+)?)\s*%\s*\)/gi;
            let directMatch;
            while ((directMatch = directRatingPattern.exec(text)) !== null) {
                const category = directMatch[1].toLowerCase();
                const percentage = parseFloat(directMatch[2]);
                
                if (category.includes('business') || category.includes('moat')) {
                    ratings.business = Math.round(percentage);
                } else if (category.includes('financial') || category.includes('strength')) {
                    ratings.financial = Math.round(percentage);
                } else if (category.includes('valuation')) {
                    ratings.valuation = Math.round(percentage);
                } else if (category.includes('management')) {
                    ratings.management = Math.round(percentage);
                } else if (category.includes('overall') || category.includes('rating') || category.includes('investment')) {
                    ratings.overall = Math.round(percentage);
                }
            }
            
            // If the overall rating is still 0 but we have component ratings, calculate the overall as the average
            if (ratings.overall === 0 && (ratings.business > 0 || ratings.financial > 0 || ratings.valuation > 0 || ratings.management > 0)) {
                let count = 0;
                let sum = 0;
                
                if (ratings.business > 0) {
                    sum += ratings.business;
                    count++;
                }
                if (ratings.financial > 0) {
                    sum += ratings.financial;
                    count++;
                }
                if (ratings.valuation > 0) {
                    sum += ratings.valuation;
                    count++;
                }
                if (ratings.management > 0) {
                    sum += ratings.management;
                    count++;
                }
                
                if (count > 0) {
                    ratings.overall = Math.round(sum / count);
                    console.log(`Overall rating calculated from components: ${ratings.overall}% (avg of ${count} components)`);
                }
            }
            
            console.log("Extracted ratings:", ratings);
            return ratings;
        }

        // --- Event Delegation for Dynamic Buttons ---
        chatbotMessages.addEventListener("click", async (event) => {
            const button = event.target.closest(".chatbot-button");
            if (!button || button.disabled) return;
            event.preventDefault();

            const modelKey = button.dataset.modelKey;
            const action = button.dataset.action;
            const endpoint = button.dataset.endpoint;
            const payload = button.dataset.payload;
            const buttonGroup = button.closest(".button-group");

            console.log("Button clicked:", {
                modelKey,
                action,
                endpoint,
                payload,
                buttonText: button.textContent,
                classList: Array.from(button.classList),
            });

            if (buttonGroup)
                buttonGroup
                    .querySelectorAll(".chatbot-button")
                    .forEach((btn) => (btn.disabled = true));
            else button.disabled = true;

            addMessage("user", `Selected: ${button.textContent}`);
            
            // Always reset currentBotAction when a button is clicked
            currentBotAction = null;

            try {
                if (button.classList.contains("submit-key-button")) {
                    console.log("API key submission button clicked");

                    const elements = findApiKeyElements(button);
                    const { promptDiv, apiKeyInput, modelKey } = elements;

                    if (!promptDiv || !apiKeyInput) {
                        console.error(
                            "Could not find required API key elements",
                        );
                        addMessage("bot", {
                            message:
                                "❌ Error: Could not find API key input form. Please refresh the page and try again.",
                            error: true,
                        });
                        setInputState(false);
                        isBotTyping = false;
                        return;
                    }

                    const apiKey = apiKeyInput.value.trim();
                    console.log("=== API KEY SUBMISSION DEBUG ===");
                    console.log("Model Key:", modelKey);
                    console.log("API Key Length:", apiKey.length);
                    console.log(
                        "API Key Preview:",
                        apiKey ? apiKey.substring(0, 10) + "..." : "empty",
                    );

                    // Validate API key
                    const validation = validateApiKey(apiKey, modelKey);
                    if (!validation.valid) {
                        console.warn(
                            "API key validation failed:",
                            validation.message,
                        );
                        addMessage("bot", {
                            message: `⚠️ ${validation.message}`,
                            error: true,
                        });
                        setInputState(false);
                        isBotTyping = false;
                        return;
                    }

                    if (!modelKey) {
                        console.error("Model key is missing");
                        addMessage("bot", {
                            message:
                                "❌ Error: Missing model information. Please start the analysis again.",
                            error: true,
                        });
                        setInputState(false);
                        isBotTyping = false;
                        return;
                    }

                    // All validations passed, submit the key
                    setInputState(true, "Validating API key...");
                    showTypingIndicator();

                    const payload = {
                        api_key: apiKey,
                        model_key: modelKey,
                    };

                    console.log("Submitting validated payload:", {
                        model_key: modelKey,
                        api_key_length: apiKey.length,
                    });

                    await sendChoiceToBackend(
                        "/api/chatbot/submit_key",
                        payload,
                    );
                } else if (modelKey) {
                    console.log("Sending model choice:", modelKey);
                    // Update analysis state to track that a model has been selected
                    if (currentAnalysisState.active) {
                        currentAnalysisState.modelSelected = true;
                        // Map model_key to readable name
                        const modelNames = {
                            "openai": "OpenAI GPT",
                            "anthropic": "Anthropic Claude",
                            "google": "Google Gemini",
                            "deepseek": "DeepSeek",
                            "skip": "Standard Analysis"
                        };
                        currentAnalysisState.modelType = modelNames[modelKey] || modelKey;
                        console.log("Model selected for analysis:", currentAnalysisState);
                    }
                    await sendChoiceToBackend("/api/chatbot/choose_model", {
                        model_key: modelKey,
                    });
                } else if (action === "skip") {
                    console.log("Sending skip action");
                    // Update analysis state for skip action too
                    if (currentAnalysisState.active) {
                        currentAnalysisState.modelSelected = true;
                        currentAnalysisState.modelType = "Standard Analysis";
                        console.log("Standard analysis selected:", currentAnalysisState);
                    }
                    await sendChoiceToBackend("/api/chatbot/choose_model", {
                        action: "skip",
                        model_key: "skip",
                    });
                } else if (endpoint && payload) {
                    // Handle custom endpoint buttons with payload
                    console.log(`Sending to custom endpoint: ${endpoint}`);
                    let parsedPayload;
                    try {
                        parsedPayload = JSON.parse(payload);
                    } catch (e) {
                        console.error("Invalid payload JSON:", payload);
                        parsedPayload = { action: payload };
                    }
                    await sendChoiceToBackend(endpoint, parsedPayload);
                } else if (endpoint) {
                    // Handle custom endpoint buttons without specific payload
                    console.log(`Sending to endpoint: ${endpoint}`);
                    await sendChoiceToBackend(endpoint, {
                        action: action || "button_click",
                        button_text: button.textContent.trim(),
                    });
                } else if (action) {
                    // Handle generic action buttons
                    console.log(`Sending action: ${action}`);
                    await sendChoiceToBackend("/api/chatbot", {
                        action: action,
                        button_text: button.textContent.trim(),
                    });
                } else {
                    console.warn("Unknown button action:", {
                        modelKey,
                        action,
                        endpoint,
                        payload,
                    });
                    addMessage("bot", {
                        message:
                            "😥 Sorry, I didn't understand that selection.",
                    });
                    setInputState(false);
                    isBotTyping = false;
                }
                
                // Ensure action state is reset after any button click processing
                currentBotAction = null;
            } catch (error) {
                console.error("Error handling button click:", error);
                addMessage("bot", {
                    message:
                        "😥 Sorry, an error occurred processing your selection.",
                });
                setInputState(false);
                isBotTyping = false;
            }
        });
    });
</script>
