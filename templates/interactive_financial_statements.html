{% extends "base.html" %}

{% block title %}Interactive Financial Statements - {{ company_info.name or ticker }}{% endblock %}

{% block head_extra %}
<style>
    .statement-container {
        margin-top: 2rem;
        margin-bottom: 3rem;
    }
    
    .statement-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .statement-type-buttons {
        display: flex;
        gap: 0.5rem;
    }
    
    .period-toggle {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    
    .financial-table {
        width: 100%;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .financial-table thead th {
        background-color: #007bff;
        color: white;
        position: sticky;
        top: 0;
        z-index: 10;
    }
    
    .financial-table th, .financial-table td {
        padding: 0.75rem;
        border: 1px solid #dee2e6;
    }
    
    .financial-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .financial-table tr:hover {
        background-color: #e9ecef;
    }
    
    .item-name {
        font-weight: 600;
        text-align: left;
    }
    
    .financial-value {
        text-align: right;
    }
    
    .header-row {
        position: sticky;
        top: 0;
        background-color: #ffffff;
        z-index: 5;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
    }
    
    .highlights-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .highlight-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 1.25rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .highlight-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 0.5rem;
    }
    
    .highlight-label {
        font-size: 0.9rem;
        color: #6c757d;
        text-align: center;
    }
    
    .chart-container {
        height: 400px;
        margin-top: 2rem;
        margin-bottom: 3rem;
        position: relative;
    }
    
    .statement-section {
        padding: 2rem;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        position: relative;
    }
    
    .section-title {
        border-bottom: 2px solid #007bff;
        padding-bottom: 0.75rem;
        margin-bottom: 1.5rem;
        color: #343a40;
    }

    @media (max-width: 767.98px) {
        .statement-controls {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .highlights-container {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4 interactive-financial-template">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
        <div>
            <h1 class="mb-1">Interactive Financial Statements</h1>
            <h4 class="text-muted">{{ company_info.name or ticker }}</h4>
            {% if company_info.code and company_info.exchange %}
                <h6 class="text-muted">{{ company_info.code }}.{{ company_info.exchange }} | Currency: {{ company_info.currency or 'USD' }}</h6>
            {% else %}
                <h6 class="text-muted">{{ ticker }}</h6>
            {% endif %}
        </div>
        <div class="d-flex gap-2">
            <a href="{{ url_for('financial_reports.index') }}" class="btn btn-outline-secondary">Search Another Company</a>
            <a href="{{ url_for('financial_reports.list_reports', ticker_exchange=ticker) }}" class="btn btn-outline-primary">View Detailed Reports</a>
        </div>
    </div>

    <!-- Financial Highlights Section -->
    <div class="statement-section" id="highlights-section">
        <h2 class="section-title">Financial Highlights</h2>
        <div class="highlights-container" id="highlights-container">
            <div class="highlight-card skeleton-loading">
                <div class="highlight-value">Loading...</div>
                <div class="highlight-label">Market Cap</div>
            </div>
            <!-- More highlight cards will be added dynamically -->
        </div>
        <div id="highlights-loading" class="loading-overlay">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>

    <!-- Financial Statement Section -->
    <div class="statement-section">
        <h2 class="section-title">Financial Statements</h2>
        
        <!-- Statement Controls -->
        <div class="statement-controls">
            <div class="statement-type-buttons">
                <button type="button" class="btn btn-primary statement-type-btn active" data-statement-type="balance-sheet">Balance Sheet</button>
                <button type="button" class="btn btn-outline-primary statement-type-btn" data-statement-type="income-statement">Income Statement</button>
                <button type="button" class="btn btn-outline-primary statement-type-btn" data-statement-type="cash-flow">Cash Flow</button>
            </div>
        </div>
        
        <!-- Statement Table Container -->
        <div class="statement-container position-relative">
            <div class="table-responsive">
                <table class="financial-table" id="statement-table">
                    <thead>
                        <tr id="table-header-row">
                            <th>Item</th>
                            <!-- Date columns will be added dynamically -->
                        </tr>
                    </thead>
                    <tbody id="table-body">
                        <!-- Table rows will be added dynamically -->
                    </tbody>
                </table>
            </div>
            <div id="table-loading" class="loading-overlay">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Chart Visualization Section -->
    <div class="statement-section">
        <h2 class="section-title">Visual Comparison</h2>
        
        <div class="form-group mb-3">
            <label for="chart-item-selector">Select item to visualize:</label>
            <select class="form-select" id="chart-item-selector">
                <option value="">Select an item</option>
                <!-- Options will be populated dynamically -->
            </select>
        </div>
        
        <div class="chart-container">
            <canvas id="comparison-chart"></canvas>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the app state
        const state = {
            ticker: "{{ ticker }}",
            statementType: "balance-sheet",
            periodType: "yearly", // Always use yearly data
            chart: null,
            currentData: null
        };
        
        console.log("Interactive financial statements initialized with ticker:", state.ticker);
        
        // Initialize the page
        loadFinancialHighlights();
        loadFinancialStatement();
        
        // Event Listeners for statement type buttons
        document.querySelectorAll('.statement-type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                console.log("Statement type button clicked:", this.dataset.statementType);
                
                // Update UI
                const activeBtn = document.querySelector('.statement-type-btn.active');
                if (activeBtn) {
                    activeBtn.classList.remove('active');
                    activeBtn.classList.replace('btn-primary', 'btn-outline-primary');
                }
                
                this.classList.add('active');
                this.classList.replace('btn-outline-primary', 'btn-primary');
                
                // Update state and reload data
                state.statementType = this.dataset.statementType;
                loadFinancialStatement();
            });
        });
        
        // Item selector for chart
        document.getElementById('chart-item-selector').addEventListener('change', function() {
            updateChart(this.value);
        });
        
        // Function to load financial highlights
        function loadFinancialHighlights() {
            const highlightsContainer = document.getElementById('highlights-container');
            const loadingOverlay = document.getElementById('highlights-loading');
            
            if (!highlightsContainer || !loadingOverlay) {
                console.error("Highlights container or loading overlay not found");
                return;
            }
            
            // Show loading state
            loadingOverlay.style.display = 'flex';
            highlightsContainer.innerHTML = '';
            
            console.log("Fetching financial highlights for ticker:", state.ticker);
            fetch(`/financial-reports/api/financial-highlights/${state.ticker}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Network response was not ok: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Retrieved financial highlights:", data);
                    
                    // Hide loading state
                    loadingOverlay.style.display = 'none';
                    
                    // Populate highlights
                    highlightsContainer.innerHTML = '';
                    
                    // Summary Metrics
                    if (data.summaryMetrics) {
                        addHighlightCard(highlightsContainer, data.summaryMetrics.marketCap, 'Market Cap', true);
                        addHighlightCard(highlightsContainer, data.summaryMetrics.peRatio, 'P/E Ratio');
                        addHighlightCard(highlightsContainer, data.summaryMetrics.eps, 'EPS');
                        addHighlightCard(highlightsContainer, data.summaryMetrics.revenueTTM, 'Revenue TTM', true);
                    } else {
                        console.warn("No summary metrics found in highlights data");
                    }
                    
                    // Growth Metrics
                    if (data.growthMetrics) {
                        addHighlightCard(highlightsContainer, data.growthMetrics.revenueGrowthYOY, 'Revenue Growth YoY', false, true);
                        addHighlightCard(highlightsContainer, data.growthMetrics.epsGrowthYOY, 'EPS Growth YoY', false, true);
                    } else {
                        console.warn("No growth metrics found in highlights data");
                    }
                    
                    // Add a placeholder if no data was added
                    if (highlightsContainer.childElementCount === 0) {
                        highlightsContainer.innerHTML = `<div class="alert alert-info w-100">No financial highlights available for this company.</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error fetching highlights:', error);
                    loadingOverlay.style.display = 'none';
                    highlightsContainer.innerHTML = `<div class="alert alert-danger w-100">Error loading financial highlights: ${error.message}</div>`;
                });
        }
        
        // Helper function to add highlight card
        function addHighlightCard(container, value, label, formatLarge = false, isPercentage = false) {
            if (value === undefined || value === null) return;
            
            const card = document.createElement('div');
            card.className = 'highlight-card';
            
            const valueEl = document.createElement('div');
            valueEl.className = 'highlight-value';
            
            // Format the value
            let formattedValue;
            if (isPercentage) {
                formattedValue = `${(parseFloat(value) * 100).toFixed(2)}%`;
                if (parseFloat(value) > 0) formattedValue = '+' + formattedValue;
                valueEl.style.color = parseFloat(value) >= 0 ? '#28a745' : '#dc3545';
            } else if (formatLarge && !isNaN(parseFloat(value))) {
                formattedValue = formatLargeNumber(parseFloat(value));
            } else {
                formattedValue = isNaN(parseFloat(value)) ? value : parseFloat(value).toFixed(2);
            }
            
            valueEl.textContent = formattedValue;
            
            const labelEl = document.createElement('div');
            labelEl.className = 'highlight-label';
            labelEl.textContent = label;
            
            card.appendChild(valueEl);
            card.appendChild(labelEl);
            container.appendChild(card);
        }
        
        // Function to load financial statement data
        function loadFinancialStatement() {
            const tableHeaderRow = document.getElementById('table-header-row');
            const tableBody = document.getElementById('table-body');
            const loadingOverlay = document.getElementById('table-loading');
            const chartSelector = document.getElementById('chart-item-selector');
            
            if (!tableHeaderRow || !tableBody || !loadingOverlay || !chartSelector) {
                console.error("Table elements or chart selector not found");
                return;
            }
            
            // Show loading state
            loadingOverlay.style.display = 'flex';
            tableHeaderRow.innerHTML = '<th>Item</th>';
            tableBody.innerHTML = '';
            chartSelector.innerHTML = '<option value="">Select an item</option>';
            
            // Use the correct API endpoint that handles yearly data only
            console.log(`Fetching financial statement data: ${state.ticker}/${state.statementType}`);
            const apiUrl = `/financial-reports/api/financial-statement/${encodeURIComponent(state.ticker)}/${encodeURIComponent(state.statementType)}`;
            
            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(err => { 
                            throw new Error(err.error || `API Error: ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Retrieved statement data:", data);
                    
                    // Hide loading state
                    loadingOverlay.style.display = 'none';
                    
                    // Check if we have valid data
                    if (!data.dates || data.dates.length === 0 || Object.keys(data.items).length === 0) {
                        let message = `No data available for ${state.statementType}`;
                        
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="2" class="text-center p-3">
                                    <div class="alert alert-warning">
                                        ${message}
                                    </div>
                                </td>
                            </tr>
                        `;
                        return;
                    }
                    
                    // Store current data for charts and table
                    state.currentData = {
                        headers: data.dates.map(date => formatDate(date)),
                        rows: {}
                    };
                    
                    // Create table header
                    const headerRow = document.getElementById('table-header-row');
                    headerRow.innerHTML = '<th>Item</th>';
                    
                    data.dates.forEach(date => {
                        const th = document.createElement('th');
                        th.textContent = formatDate(date);
                        th.className = 'financial-value';
                        headerRow.appendChild(th);
                    });
                    
                    // Transform item data for table rows
                    Object.entries(data.items).forEach(([itemKey, item]) => {
                        state.currentData.rows[item.displayName] = item.values.map(value => formatLargeNumber(value));
                    });
                    
                    // Create table rows
                    tableBody.innerHTML = '';
                    chartSelector.innerHTML = '<option value="">Select an item</option>';
                    
                    // Group items for better organization
                    const itemGroups = organizeItemsByGroup(state.currentData.rows);
                    
                    // Iterate through groups to create rows
                    Object.entries(itemGroups).forEach(([groupName, items]) => {
                        // Add a group header row
                        if (groupName !== 'Other') {
                            const groupRow = document.createElement('tr');
                            groupRow.className = 'table-group-header';
                            const groupCell = document.createElement('td');
                            groupCell.colSpan = data.dates.length + 1;
                            groupCell.textContent = groupName;
                            groupCell.style.backgroundColor = '#e9ecef';
                            groupCell.style.fontWeight = 'bold';
                            groupRow.appendChild(groupCell);
                            tableBody.appendChild(groupRow);
                        }
                        
                        // Add items in this group
                        items.forEach(itemName => {
                            const values = state.currentData.rows[itemName];
                            const tr = document.createElement('tr');
                            
                            // Item name cell
                            const nameTd = document.createElement('td');
                            nameTd.textContent = itemName;
                            nameTd.className = 'item-name';
                            tr.appendChild(nameTd);
                            
                            // Add values for each date
                            values.forEach(value => {
                                const valueTd = document.createElement('td');
                                valueTd.textContent = value;
                                valueTd.className = 'financial-value';
                                tr.appendChild(valueTd);
                            });
                            
                            tableBody.appendChild(tr);
                            
                            // Add to chart selector dropdown
                            const option = document.createElement('option');
                            option.value = itemName;
                            option.textContent = itemName;
                            chartSelector.appendChild(option);
                        });
                    });
                    
                    // If chart selector has values, update the chart
                    if (chartSelector.options.length > 1) {
                        chartSelector.selectedIndex = 1; // Select first real item
                        updateChart(chartSelector.value);
                    } else {
                        const chartContainer = document.getElementById('comparison-chart').getContext('2d');
                        // Clear any existing chart
                        if (state.chart) {
                            state.chart.destroy();
                            state.chart = null;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching financial statements:', error);
                    loadingOverlay.style.display = 'none';
                    
                    // Provide more helpful error message based on the context
                    let errorMessage = `Error loading financial data: ${error.message}`;
                    
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="2" class="text-center p-3">
                                <div class="alert alert-danger">
                                    ${errorMessage}
                                </div>
                            </td>
                        </tr>
                    `;
                });
        }
        
        // Function to update chart when item is selected
        function updateChart(itemName) {
            if (!itemName || !state.currentData || !state.currentData.rows || !state.currentData.rows[itemName]) {
                console.log("Cannot update chart: missing data or invalid item name");
                return;
            }
            
            const values = state.currentData.rows[itemName];
            const headers = state.currentData.headers;
            const chartCanvas = document.getElementById('comparison-chart');
            
            if (!chartCanvas) {
                console.error("Chart canvas not found");
                return;
            }
            
            // Destroy existing chart if it exists
            if (state.chart) {
                state.chart.destroy();
            }
            
            // Process values for chart (convert from formatted strings to numbers)
            const numericValues = values.map(value => {
                // Strip formatting like 'B', 'M', 'K' and convert to number
                if (value === 'N/A' || value === '0.00') return 0;
                
                let multiplier = 1;
                let numStr = value;
                
                if (value.endsWith('B')) {
                    multiplier = 1_000_000_000;
                    numStr = value.slice(0, -1);
                } else if (value.endsWith('M')) {
                    multiplier = 1_000_000;
                    numStr = value.slice(0, -1);
                } else if (value.endsWith('K')) {
                    multiplier = 1_000;
                    numStr = value.slice(0, -1);
                }
                
                return parseFloat(numStr) * multiplier;
            });
            
            console.log("Creating chart with data:", {headers, numericValues});
            
            // Create new chart
            state.chart = new Chart(chartCanvas, {
                type: 'bar',
                data: {
                    labels: headers,
                    datasets: [{
                        label: itemName,
                        data: numericValues,
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: {
                                callback: function(value) {
                                    return formatLargeNumber(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.raw;
                                    return `${itemName}: ${formatLargeNumber(value)}`;
                                }
                            }
                        },
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: `${itemName} Comparison (${state.periodType === 'yearly' ? 'Annual' : 'Quarterly'})`,
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });
        }
        
        // Helper function to format large numbers
        function formatLargeNumber(value) {
            if (value === null || value === undefined || isNaN(value)) return 'N/A';
            
            const absValue = Math.abs(value);
            
            if (absValue >= 1_000_000_000) {
                return `${(value / 1_000_000_000).toFixed(2)}B`;
            } else if (absValue >= 1_000_000) {
                return `${(value / 1_000_000).toFixed(2)}M`;
            } else if (absValue >= 1_000) {
                return `${(value / 1_000).toFixed(2)}K`;
            } else {
                return value.toFixed(2);
            }
        }
        
        // Format financial values
        function formatFinancialValue(value, useLargeFormat = false) {
            if (value === null || value === undefined) return 'N/A';
            
            if (typeof value === 'number' || !isNaN(parseFloat(value))) {
                const numValue = parseFloat(value);
                return useLargeFormat ? formatLargeNumber(numValue) : numValue.toFixed(2);
            }
            
            return value;
        }
        
        // Format date for display
        function formatDate(dateString) {
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    // Just return the original string if date parsing failed
                    return dateString;
                }
                const options = { year: 'numeric', month: 'short', day: 'numeric' };
                return date.toLocaleDateString(undefined, options);
            } catch (e) {
                console.error("Error formatting date:", e);
                return dateString;
            }
        }
        
        // Organize items by logical groups
        function organizeItemsByGroup(items) {
            if (!items || Object.keys(items).length === 0) {
                console.error("No items provided to organizeItemsByGroup");
                return { 'Other': [] };
            }
            
            const groups = {
                'Assets': [],
                'Liabilities': [],
                'Equity': [],
                'Revenue & Income': [],
                'Expenses': [],
                'Cash Flow': [],
                'Other': []
            };
            
            // Map items to groups based on keywords in their names
            Object.keys(items).forEach(itemName => {
                const lowerKey = itemName.toLowerCase();
                
                if (lowerKey.includes('asset') || lowerKey.includes('cash') && !lowerKey.includes('flow') || 
                    lowerKey.includes('receivable') || lowerKey.includes('inventory')) {
                    groups['Assets'].push(itemName);
                }
                else if (lowerKey.includes('liab') || lowerKey.includes('debt') || 
                        lowerKey.includes('loan') || lowerKey.includes('payable')) {
                    groups['Liabilities'].push(itemName);
                }
                else if (lowerKey.includes('equity') || lowerKey.includes('stock') || 
                        lowerKey.includes('capital') || lowerKey.includes('retained')) {
                    groups['Equity'].push(itemName);
                }
                else if (lowerKey.includes('revenue') || lowerKey.includes('income') || 
                        lowerKey.includes('sale') || lowerKey.includes('profit')) {
                    groups['Revenue & Income'].push(itemName);
                }
                else if (lowerKey.includes('expense') || lowerKey.includes('cost') || 
                        lowerKey.includes('tax') || lowerKey.includes('depreciation')) {
                    groups['Expenses'].push(itemName);
                }
                else if (lowerKey.includes('flow') || lowerKey.includes('investing') || 
                        lowerKey.includes('financing') || lowerKey.includes('operating')) {
                    groups['Cash Flow'].push(itemName);
                }
                else {
                    groups['Other'].push(itemName);
                }
            });
            
            // Remove empty groups
            Object.keys(groups).forEach(key => {
                if (groups[key].length === 0) {
                    delete groups[key];
                }
            });
            
            return groups;
        }
    });
</script>
{% endblock %} 