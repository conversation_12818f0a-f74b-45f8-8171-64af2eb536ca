<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Data Result</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        /* Light Theme Styles */
        :root {
            --bg-color: #ffffff;
            --text-color: #000000;
            --nav-bg-color: #f0f4fc;
            --nav-hover-color: #d0e4fb;
            --icon-color: #000000;
            --highlight-color: #007bff;
            --video-bg-color: #f9f9f9;
            --tooltip-bg: #d0e4fb;
            --input-bg-color: #ffffff;
            --input-text-color: #000000;
            --chart-bg-color: #ffffff;
            --chart-grid-color: #ddd;
            --button-bg-color: #007bff;
            --button-text-color: #ffffff;
            --icon-color-invert: 0; /* Keep icons black */
        }

        /* Dark Theme Styles */
        [data-theme="dark"] {
            --bg-color: #1e1e2d;
            --text-color: #ffffff;
            --nav-bg-color: #2b2b3d;
            --nav-hover-color: #444466;
            --icon-color: #ffffff;
            --highlight-color: #8c4fff;
            --video-bg-color: #2b2b3d;
            --tooltip-bg: #444466;
            --input-bg-color: #2b2b3d;
            --input-text-color: #ffffff;
            --chart-bg-color: #1e1e2d;
            --chart-grid-color: #444466;
            --button-bg-color: #8c4fff;
            --button-text-color: #ffffff;
            --icon-color-invert: 1; /* Invert icons to white */
        }

        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s ease-in-out;
        }

        header {
            padding: 10px 20px;
            background-color: var(--highlight-color);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-left: 70px; /* Adjusted to not overlap with sidebar */
        }

        .theme-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .theme-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--tooltip-bg);
            transition: 0.4s;
            border-radius: 20px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 3px;
            bottom: 3px;
            background-color: var(--highlight-color);
            transition: 0.4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--highlight-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
            background-color: var(--bg-color);
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100%;
            width: 60px;
            background-color: var(--nav-bg-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 20px;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
        }

        .sidebar a {
            text-decoration: none;
            margin: 20px 0;
            color: var(--icon-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .sidebar a:hover {
            background-color: var(--nav-hover-color);
            border-radius: 50%;
        }

        .sidebar a img {
            width: 30px;
            height: 30px;
            filter: invert(var(--icon-color-invert));
        }

        .tooltip {
            position: absolute;
            top: 50%;
            left: 70px;
            transform: translateY(-50%);
            background-color: var(--tooltip-bg);
            color: var(--text-color);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            visibility: hidden;
            opacity: 0;
            transform: translateX(-10px);
            transition: opacity 0.3s, transform 0.3s;
        }

        .sidebar a:hover .tooltip {
            visibility: visible;
            opacity: 1;
            transform: translateX(0);
        }

        main {
            margin-left: 70px;
            padding: 20px;
            text-align: center;
        }

        .videos-container {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            background-color: var(--video-bg-color);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .videos-container iframe {
            width: 300px;
            height: 170px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        footer {
            margin-top: 20px;
            text-align: center;
            padding: 10px;
            background-color: var(--highlight-color);
            color: white;
        }

        body {
            overflow-y: scroll; /* Add vertical scroll */
        }
        main {
            padding-bottom: 100px; /* Add more room to scroll down */
        }

        input, textarea {
            background-color: var(--input-bg-color);
            color: var(--input-text-color);
            border: 1px solid var(--tooltip-bg);
            padding: 10px;
            border-radius: 5px;
        }

        button {
            background-color: var(--button-bg-color);
            color: var(--button-text-color);
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }

        button:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <header>
        <h1>Stock Data Result</h1>
        <label class="theme-switch">
            <input type="checkbox" onclick="toggleTheme()">
            <span class="slider"></span>
        </label>
    </header>

    <div class="sidebar">
        <a href="/">
            <img src="{{ url_for('static', filename='icons/home.svg') }}" alt="Home">
            <div class="tooltip">Home</div>
        </a>
        <a href="/portfolio">
            <img src="{{ url_for('static', filename='icons/portfolio.svg') }}" alt="Portfolio">
            <div class="tooltip">Portfolio Management</div>
        </a>
        <a href="/dcf">
            <img src="{{ url_for('static', filename='icons/calculator.svg') }}" alt="DCF Calculator">
            <div class="tooltip">DCF Calculator</div>
        </a>
        <a href="/testing">
            <img src="{{ url_for('static', filename='icons/testing.svg') }}" alt="Testing">
            <div class="tooltip">Testing</div>
        </a>
    </div>

    <main>
        <h2>{{ ticker }} Stock Prices ({{ from_date }} to {{ to_date }})</h2>
        <div id="chart" style="width: 100%; height: 500px;"></div>
        <div id="percentage-change" style="text-align: center; font-size: 1.5em; margin-top: 20px;"></div>
    </main>
    <footer>
        <p>&copy; 2025 Finance Tool</p>
    </footer>

    <script>
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);
            if(document.querySelector('.theme-switch input')) {
                document.querySelector('.theme-switch input').checked = savedTheme === 'dark';
            }
        });

        document.addEventListener("DOMContentLoaded", function () {
            const rawStockData = '{{ stock_data | tojson | safe }}';
            let stockData;

            try {
                stockData = JSON.parse(rawStockData);
                console.log("Stock Data:", stockData);
            } catch (error) {
                console.error("Error parsing stock data:", error);
                return;
            }

            if (!stockData || stockData.length === 0) {
                console.error("No data available for the chart.");
                document.getElementById('chart').innerText = "No stock data available.";
                return;
            }

            // Ensure the data is sorted in ascending order by date
            const sortedStockData = stockData.sort((a, b) => new Date(a.date) - new Date(b.date));
            const dates = sortedStockData.map(entry => entry.date);
            const closePrices = sortedStockData.map(entry => entry.close);

            console.log("Dates:", dates);
            console.log("Close Prices:", closePrices);

            // Calculate percentage change
            const startPrice = closePrices[0]; // First price (oldest)
            const endPrice = closePrices[closePrices.length - 1]; // Last price (most recent)
            const percentageChange = ((endPrice - startPrice) / startPrice) * 100;
            const formattedPercentage = `${Math.abs(percentageChange).toFixed(2)}%`; // Use Math.abs() to remove the negative sign for display
            const percentageChangeElement = document.getElementById('percentage-change');
            percentageChangeElement.textContent = `Percentage Change: ${formattedPercentage}`;
            percentageChangeElement.style.color = percentageChange >= 0 ? 'green' : 'red';

            // Define the main trace (close prices with shadow effect)
            const traceClose = {
                x: dates,
                y: closePrices,
                type: 'scatter',
                mode: 'lines',
                line: {
                    color: percentageChange >= 0 ? 'green' : 'red', // Green if positive, red if negative
                    width: 3,
                    shape: 'spline',
                },
                name: '{{ ticker }} Close Price',
                hovertemplate: '%{y:$,.2f}<extra></extra>', // Format hover effect as dollar
            };

            // Layout with enhanced styling
            const layout = {
                title: `{{ ticker }} Stock Prices ({{ from_date }} to {{ to_date }})`,
                xaxis: {
                    title: 'Date',
                    tickformat: '%Y-%m-%d',
                    showgrid: true,
                    gridcolor: 'var(--chart-grid-color)',
                },
                yaxis: {
                    title: 'Price (USD)',
                    showgrid: true,
                    gridcolor: 'var(--chart-grid-color)',
                },
                showlegend: false, // Remove icons in the right corner
                plot_bgcolor: 'var(--chart-bg-color)', // Dynamic background color
                paper_bgcolor: 'var(--chart-bg-color)', // Dynamic chart background
            };

            // Custom shadow effect via CSS
            const config = {
                displayModeBar: false, // Disable unnecessary icons
            };

            // Plotly with shadow effect (via div style)
            const chartDiv = document.getElementById('chart');
            chartDiv.style.boxShadow = '0px 4px 8px rgba(0, 0, 0, 0.2)'; // Shadow effect
            chartDiv.style.borderRadius = '8px'; // Rounded edges

            Plotly.newPlot(chartDiv, [traceClose], layout, config);
        });
    </script>
</body>
</html>
