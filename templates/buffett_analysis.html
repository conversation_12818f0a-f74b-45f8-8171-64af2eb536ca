{% extends "base.html" %}

{% block title %}{{ ticker }} - Buffett-Munger Analysis{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Analysis Header -->
    <div class="analysis-header mb-4">
        <div class="row">
            <div class="col-md-8">
                {% set scores = [] %}
                {% if analysis.moat_score is number %}{% set_global scores = scores.append(analysis.moat_score) %}{% endif %}
                {% if analysis.financial_strength_score is number %}{% set_global scores = scores.append(analysis.financial_strength_score) %}{% endif %}
                {% if analysis.valuation_score is number %}{% set_global scores = scores.append(analysis.valuation_score) %}{% endif %}
                {% if analysis.management_score is number %}{% set_global scores = scores.append(analysis.management_score) %}{% endif %}

                {% set total_score = scores|sum %}
                {% set num_scores = scores|length %}
                {% set overall_percentage = (total_score / num_scores * 10) | round if num_scores > 0 else 0 %}
                <h1>{{ overall_percentage }}%</h1>
                <h2>{{ analysis.recommendation_text }}</h2>
                <h3>Analysis of {{ ticker }} ({{ company_name }}) - A Buffett-Munger Perspective</h3>
                <p class="lead">This analysis emulates the investment philosophy of Warren Buffett and Charlie Munger, prioritizing long-term business quality, durable competitive advantages, and a substantial margin of safety.</p>
            </div>
            <div class="col-md-4">
                <!-- Rating Chart -->
                <div class="quick-metrics-box">
                    <div class="multi-category-ratings">
                        <div class="category-rating">
                            <div class="rating-chart-wrapper">
                                <svg class="rating-chart-svg" viewBox="0 0 36 36" width="80" height="80">
                                    <circle class="rating-chart-bg" cx="18" cy="18" r="15.91549430918954" fill="transparent" stroke="#e0e0e0" stroke-width="3"></circle>
                                    <circle class="rating-chart-fill" cx="18" cy="18" r="15.91549430918954" fill="transparent" 
                                        stroke="{% if analysis.moat_score >= 8 %}#28a745{% elif analysis.moat_score >= 6 %}#ffc107{% elif analysis.moat_score > 0 %}#dc3545{% else %}#6c757d{% endif %}" 
                                        stroke-width="3" 
                                        stroke-dasharray="{% if analysis.moat_score > 0 %}{{ analysis.moat_score * 10 }}{% else %}0{% endif %} 100" 
                                        stroke-dashoffset="25" 
                                        transform="rotate(-90 18 18)">
                                    </circle>
                                    <text class="rating-chart-text" x="18" y="18" text-anchor="middle" dominant-baseline="middle">
                                        <tspan class="rating-chart-value">{% if analysis.moat_score > 0 %}{{ analysis.moat_score }}{% else %}N/A{% endif %}</tspan>
                                        <tspan class="rating-chart-percent" dx="1" dy="-0.6em">{% if analysis.moat_score > 0 %}/10{% endif %}</tspan>
                                    </text>
                                </svg>
                            </div>
                            <div class="category-label">Business Quality/Moat</div>
                        </div>
                        <div class="category-rating">
                            <div class="rating-chart-wrapper">
                                <svg class="rating-chart-svg" viewBox="0 0 36 36" width="80" height="80">
                                    <circle class="rating-chart-bg" cx="18" cy="18" r="15.91549430918954" fill="transparent" stroke="#e0e0e0" stroke-width="3"></circle>
                                    <circle class="rating-chart-fill" cx="18" cy="18" r="15.91549430918954" fill="transparent" 
                                        stroke="{% if analysis.financial_strength_score >= 8 %}#28a745{% elif analysis.financial_strength_score >= 6 %}#ffc107{% else %}#dc3545{% endif %}" 
                                        stroke-width="3" 
                                        stroke-dasharray="{{ analysis.financial_strength_score * 10 }} 100" 
                                        stroke-dashoffset="25" 
                                        transform="rotate(-90 18 18)">
                                    </circle>
                                    <text class="rating-chart-text" x="18" y="18" text-anchor="middle" dominant-baseline="middle">
                                        <tspan class="rating-chart-value">{{ analysis.financial_strength_score }}</tspan>
                                        <tspan class="rating-chart-percent" dx="1" dy="-0.6em">/10</tspan>
                                    </text>
                                </svg>
                            </div>
                            <div class="category-label">Financial Strength</div>
                        </div>
                        <div class="category-rating">
                            <div class="rating-chart-wrapper">
                                <svg class="rating-chart-svg" viewBox="0 0 36 36" width="80" height="80">
                                    <circle class="rating-chart-bg" cx="18" cy="18" r="15.91549430918954" fill="transparent" stroke="#e0e0e0" stroke-width="3"></circle>
                                    <circle class="rating-chart-fill" cx="18" cy="18" r="15.91549430918954" fill="transparent" 
                                        stroke="{% if analysis.valuation_score >= 8 %}#28a745{% elif analysis.valuation_score >= 6 %}#ffc107{% else %}#dc3545{% endif %}" 
                                        stroke-width="3" 
                                        stroke-dasharray="{{ analysis.valuation_score * 10 }} 100" 
                                        stroke-dashoffset="25" 
                                        transform="rotate(-90 18 18)">
                                    </circle>
                                    <text class="rating-chart-text" x="18" y="18" text-anchor="middle" dominant-baseline="middle">
                                        <tspan class="rating-chart-value">{{ analysis.valuation_score }}</tspan>
                                        <tspan class="rating-chart-percent" dx="1" dy="-0.6em">/10</tspan>
                                    </text>
                                </svg>
                            </div>
                            <div class="category-label">Valuation</div>
                        </div>
                        <div class="category-rating">
                            <div class="rating-chart-wrapper">
                                <svg class="rating-chart-svg" viewBox="0 0 36 36" width="80" height="80">
                                    <circle class="rating-chart-bg" cx="18" cy="18" r="15.91549430918954" fill="transparent" stroke="#e0e0e0" stroke-width="3"></circle>
                                    <circle class="rating-chart-fill" cx="18" cy="18" r="15.91549430918954" fill="transparent" 
                                        stroke="{% if analysis.management_score >= 8 %}#28a745{% elif analysis.management_score >= 6 %}#ffc107{% else %}#dc3545{% endif %}" 
                                        stroke-width="3" 
                                        stroke-dasharray="{{ analysis.management_score * 10 }} 100" 
                                        stroke-dashoffset="25" 
                                        transform="rotate(-90 18 18)">
                                    </circle>
                                    <text class="rating-chart-text" x="18" y="18" text-anchor="middle" dominant-baseline="middle">
                                        <tspan class="rating-chart-value">{{ analysis.management_score }}</tspan>
                                        <tspan class="rating-chart-percent" dx="1" dy="-0.6em">/10</tspan>
                                    </text>
                                </svg>
                            </div>
                            <div class="category-label">Management</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Reference Metrics Box -->
    <div class="card mb-4 quick-metrics-box">
        <div class="card-header">
            <h4>Quick Reference Metrics</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <h5>Profitability</h5>
                    <table class="table table-sm">
                        <tr>
                            <td>ROE</td>
                            <td>{{ analysis.metrics.roe }}%</td>
                        </tr>
                        <tr>
                            <td>ROIC</td>
                            <td>{{ analysis.metrics.roic }}%</td>
                        </tr>
                        <tr>
                            <td>ROCE</td>
                            <td>{{ analysis.metrics.roce }}%</td>
                        </tr>
                        <tr>
                            <td>Operating Margin</td>
                            <td>{{ analysis.metrics.operating_margin }}%</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-3 mb-3">
                    <h5>Valuation</h5>
                    <table class="table table-sm">
                        <tr>
                            <td>P/E</td>
                            <td>{{ analysis.metrics.pe_ratio }}</td>
                        </tr>
                        <tr>
                            <td>P/S</td>
                            <td>{{ analysis.metrics.ps_ratio }}</td>
                        </tr>
                        <tr>
                            <td>P/B</td>
                            <td>{{ analysis.metrics.pb_ratio }}</td>
                        </tr>
                        <tr>
                            <td>P/FCF</td>
                            <td>{{ analysis.metrics.pfcf_ratio }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-3 mb-3">
                    <h5>Financial Health</h5>
                    <table class="table table-sm">
                        <tr>
                            <td>Current Ratio</td>
                            <td>{{ analysis.metrics.current_ratio }}</td>
                        </tr>
                        <tr>
                            <td>Quick Ratio</td>
                            <td>{{ analysis.metrics.quick_ratio }}</td>
                        </tr>
                        <tr>
                            <td>Debt/Equity</td>
                            <td>{{ analysis.metrics.debt_to_equity }}</td>
                        </tr>
                        <tr>
                            <td>Interest Coverage</td>
                            <td>{{ analysis.metrics.interest_coverage }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-3 mb-3">
                    <h5>Cash Flow</h5>
                    <table class="table table-sm">
                        <tr>
                            <td>FCF (billions)</td>
                            <td>${{ analysis.metrics.fcf_billions }}</td>
                        </tr>
                        <tr>
                            <td>FCF Yield</td>
                            <td>{{ analysis.metrics.fcf_yield }}%</td>
                        </tr>
                        <tr>
                            <td>CapEx/FCF</td>
                            <td>{{ analysis.metrics.capex_to_fcf }}%</td>
                        </tr>
                        <tr>
                            <td>CapEx/Revenue</td>
                            <td>{{ analysis.metrics.capex_to_revenue }}%</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- DCF Sensitivity Analysis -->
    <div class="card mb-4">
        <div class="card-header">
            <h4>DCF Sensitivity Analysis</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Growth Rate / WACC</th>
                            {% for wacc in analysis.dcf_sensitivity.wacc_rates %}
                            <th>{{ wacc }}%</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for growth_rate, values in analysis.dcf_sensitivity.values.items() %}
                        <tr>
                            <th>{{ growth_rate }}%</th>
                            {% for value in values %}
                            <td class="{% if value > current_price * 1.1 %}bg-success text-white{% elif value < current_price * 0.9 %}bg-danger text-white{% elif value >= current_price * 0.9 and value <= current_price * 1.1 %}bg-warning{% endif %}">
                                ${{ value }}
                            </td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="mt-3">
                <p><strong>Current Price:</strong> ${{ current_price }}</p>
                <p><strong>Base Case Fair Value:</strong> ${{ analysis.dcf_base_case.fair_value }} (Growth: {{ analysis.dcf_base_case.growth_rate }}%, WACC: {{ analysis.dcf_base_case.wacc }}%)</p>
                <p><strong>Margin of Safety Value:</strong> ${{ analysis.dcf_base_case.mos_value }} (MoS: {{ analysis.dcf_base_case.mos_percentage }}%)</p>
            </div>
        </div>
    </div>
    
    <!-- Main Analysis Content -->
    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="analysis-tabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="business-tab" data-toggle="tab" href="#business" role="tab">1. Understand the Business</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="moat-tab" data-toggle="tab" href="#moat" role="tab">2. Economic Moat</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="management-tab" data-toggle="tab" href="#management" role="tab">3. Management</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="financial-tab" data-toggle="tab" href="#financial" role="tab">4. Financial Strength</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="valuation-tab" data-toggle="tab" href="#valuation" role="tab">5. Valuation</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="risks-tab" data-toggle="tab" href="#risks" role="tab">6. Risks</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="sentiment-tab" data-toggle="tab" href="#sentiment" role="tab">7. Sentiment</a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="analysis-tabs-content">
                <!-- Business Understanding Tab -->
                <div class="tab-pane fade show active" id="business" role="tabpanel">
                    {{ analysis.sections.business|safe }}
                </div>
                
                <!-- Economic Moat Tab -->
                <div class="tab-pane fade" id="moat" role="tabpanel">
                    {{ analysis.sections.moat|safe }}
                </div>
                
                <!-- Management Tab -->
                <div class="tab-pane fade" id="management" role="tabpanel">
                    {{ analysis.sections.management|safe }}
                </div>
                
                <!-- Financial Strength Tab -->
                <div class="tab-pane fade" id="financial" role="tabpanel">
                    {{ analysis.sections.financial|safe }}
                </div>
                
                <!-- Valuation Tab -->
                <div class="tab-pane fade" id="valuation" role="tabpanel">
                    {{ analysis.sections.valuation|safe }}
                </div>
                
                <!-- Risks Tab -->
                <div class="tab-pane fade" id="risks" role="tabpanel">
                    {{ analysis.sections.risks|safe }}
                </div>
                
                <!-- Sentiment Tab -->
                <div class="tab-pane fade" id="sentiment" role="tabpanel">
                    {{ analysis.sections.sentiment|safe }}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Final Recommendation -->
    <div class="card mb-4">
        <div class="card-header">
            <h4 id="final-recommendation-title">Final Recommendation</h4>
        </div>
        <div class="card-body">
            <div id="final-recommendation-ratings-summary" style="margin-bottom: 20px;">
                <!-- Clean rating summary will be injected here by JavaScript -->
            </div>
            <div id="original-recommendation-content">
                 {{ analysis.sections.recommendation|safe }}
            </div>
        </div>
    </div>
    
    <!-- Disclaimer -->
    <div class="alert alert-secondary">
        <p class="mb-0"><small>Disclaimer: This analysis is based on the Buffett-Munger investing philosophy and provided data, but does not constitute financial advice. Always conduct your own thorough research and consult with a qualified financial advisor before making any investment decisions.</small></p>
    </div>
</div>

<!-- Add this for the tabs to work properly -->
<script>
    $(document).ready(function() {
        $('#analysis-tabs a').on('click', function (e) {
            e.preventDefault();
            $(this).tab('show');
        });
    });
</script>

<style>
    .quick-metrics-box {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-radius: 8px;
        border: 1px solid rgba(0,0,0,0.125);
    }
    
    .analysis-header h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 0;
    }
    
    .analysis-header h2 {
        font-size: 1.8rem;
        margin-bottom: 1rem;
        color: #555;
    }
    
    .analysis-header h3 {
        font-size: 1.5rem;
    }
    
    .rating-chart-svg {
        width: 80px;
        height: 80px;
    }
    
    .rating-chart-text {
        font-family: Arial, sans-serif;
        font-weight: bold;
    }
    
    .rating-chart-value {
        font-size: 12px;
    }
    
    .rating-chart-percent {
        font-size: 8px;
    }
    
    .category-label {
        font-size: 0.85rem;
        font-weight: 600;
        text-align: center;
        margin-top: 5px;
    }
    
    .multi-category-ratings {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
        margin: 15px 0;
    }
    
    .category-rating {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 90px;
    }
    
    .overall-rating-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .overall-rating {
        width: 100px;
        height: 100px;
    }
    
    /* Risk assessment matrix styles */
    .risk-matrix {
        border-collapse: collapse;
        width: 100%;
    }
    
    .risk-matrix th, .risk-matrix td {
        border: 1px solid #dee2e6;
        padding: 8px;
        text-align: center;
    }
    
    .risk-matrix th {
        background-color: #f8f9fa;
    }
    
    .risk-low {
        background-color: #d4edda;
        color: #155724;
    }
    
    .risk-medium {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .risk-high {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .overall-label {
        font-size: 1rem;
        font-weight: 600;
        text-align: center;
        margin-top: 5px;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ensure rating_charts.js is loaded and DOM is ready
    if (typeof generateHorizontalBarCharts !== 'function') {
        console.error('generateHorizontalBarCharts function not found. Ensure rating_charts.js is loaded before this script.');
        return;
    }

    const analysisJsonString = '{{ ({ "moat_score": analysis.moat_score, "financial_strength_score": analysis.financial_strength_score, "valuation_score": analysis.valuation_score, "management_score": analysis.management_score }) | tojson | safe }}';
    const analysisData = JSON.parse(analysisJsonString);

    const rawMoat = (typeof analysisData.moat_score === 'number') ? analysisData.moat_score : 0;
    const rawFinancial = (typeof analysisData.financial_strength_score === 'number') ? analysisData.financial_strength_score : 0;
    const rawValuation = (typeof analysisData.valuation_score === 'number') ? analysisData.valuation_score : 0;
    const rawManagement = (typeof analysisData.management_score === 'number') ? analysisData.management_score : 0;

    const moatScorePercent = rawMoat * 10;
    const financialStrengthScorePercent = rawFinancial * 10;
    const valuationScorePercent = rawValuation * 10;
    const managementScorePercent = rawManagement * 10;
    
    const overallScoreForChart = Math.round(((rawMoat + rawFinancial + rawValuation + rawManagement) / 4) * 10);

    const chartsContainer = document.getElementById('final-recommendation-ratings-summary');
    if (chartsContainer) {
        const horizontalBars = generateHorizontalBarCharts(
            moatScorePercent,          // Business Quality/Moat (expects 0-100)
            financialStrengthScorePercent,
            valuationScorePercent,
            managementScorePercent,
            overallScoreForChart // Overall score (expects 0-100)
        );
        chartsContainer.innerHTML = ''; // Clear placeholder content if any
        chartsContainer.appendChild(horizontalBars);
        console.log('Clean rating summary generated in Final Recommendation section.');
    } else {
        console.error('Placeholder for clean rating summary (final-recommendation-ratings-summary) not found.');
    }

    // Find and hide the malformed block within the original recommendation content
    const originalContentContainer = document.getElementById('original-recommendation-content');
    if (originalContentContainer) {
        const searchText1 = "Visual Rating Component:";
        // The '+' might be part of the string or could be rendered differently, e.g. with spaces
        const searchText2Part = "generateValuationRangeHTML"; 
        const searchText3 = "Overall: 43%";
        const searchText3Alt = "Overall: 0.43"; // Alternative format for percentage

        const elementsToScan = Array.from(originalContentContainer.getElementsByTagName('p'))
                                .concat(Array.from(originalContentContainer.getElementsByTagName('pre')));
        
        for (let el of elementsToScan) {
            const elText = el.textContent || el.innerText || "";
            
            const hasSearchText1 = elText.includes(searchText1);
            const hasSearchText2 = elText.includes(searchText2Part);
            const hasSearchText3 = elText.includes(searchText3) || elText.includes(searchText3Alt);

            if (hasSearchText1 && hasSearchText2 && hasSearchText3) {
                el.style.display = 'none';
                console.log("Hid malformed content block inside 'original-recommendation-content':", elText.substring(0, 150) + "...");
                // Assuming there's only one such block, but removing break to catch all instances if multiple exist
            }
        }
    } else {
        console.log("Original recommendation content container (original-recommendation-content) not found.");
    }
});
</script>

{% endblock %}
