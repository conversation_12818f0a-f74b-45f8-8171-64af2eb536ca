{% extends "base.html" %}

{% block title %}Chatbot Button Test{% endblock %}

{% block head_extra %}
<style>
    .test-container {
        max-width: 800px;
        margin: 40px auto;
        padding: 20px;
    }

    .test-section {
        margin-bottom: 40px;
        padding: 20px;
        background: var(--card-bg-color, white);
        border-radius: 12px;
        box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.08));
        border: 1px solid var(--border-color, #e0e0e0);
    }

    .test-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: var(--text-color);
    }

    .test-description {
        margin-bottom: 20px;
        color: var(--text-color);
        opacity: 0.8;
    }

    /* Enhanced Chatbot <PERSON><PERSON> Styles */
    .chatbot-button {
        display: inline-flex !important;
        align-items: center;
        justify-content: center;
        gap: 5px;
        padding: 12px 18px;
        font-size: 0.9rem;
        font-weight: 600;
        color: #fff !important;
        background: var(--highlight-color, #007bff) !important;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        margin: 4px;
        min-width: 120px;
        white-space: nowrap;
        position: relative;
        z-index: 1;
    }

    .chatbot-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        background: var(--highlight-darker, #0056b3) !important;
    }

    .chatbot-button:disabled {
        background: #6c757d !important;
        cursor: not-allowed;
        opacity: 0.6;
        box-shadow: none;
        transform: none;
        color: #fff !important;
    }

    .button-group {
        display: flex !important;
        flex-wrap: wrap !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 15px 0 !important;
        padding: 15px !important;
        width: 100% !important;
        gap: 8px !important;
        background: rgba(var(--highlight-color-rgb, 0, 123, 255), 0.02) !important;
        border-radius: 12px !important;
        border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.1) !important;
    }

    .llm-model-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .llm-model-button:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
        transform: translateY(-2px) scale(1.02);
    }

    .skip-button {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
        color: #fff !important;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .skip-button:hover {
        background: linear-gradient(135deg, #5a6268 0%, #495057 100%) !important;
        transform: translateY(-2px) scale(1.02);
    }

    .submit-key-button {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    }

    .submit-key-button:hover {
        background: linear-gradient(135deg, #218838 0%, #1aa085 100%) !important;
    }

    /* Message simulation styles */
    .message-simulation {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        margin: 20px 0;
    }

    .bot-avatar {
        background: var(--highlight-color, #007bff);
        color: white;
        padding: 8px;
        border-radius: 50%;
        min-width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .message-bubble {
        background: var(--card-bg-color, white);
        padding: 20px;
        border-radius: 16px;
        max-width: 80%;
        border: 1px solid var(--border-color, #e0e0e0);
        box-shadow: var(--shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.05));
    }

    .result-display {
        margin-top: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid var(--highlight-color, #007bff);
    }

    /* Dark theme adjustments */
    [data-theme="dark"] .test-section {
        background: var(--chatbot-bg, #1e1e2d);
        border-color: var(--chatbot-border-color, #444466);
    }

    [data-theme="dark"] .message-bubble {
        background: var(--chatbot-input-bg, #2b2b3d);
        border-color: var(--chatbot-border-color, #444466);
    }

    [data-theme="dark"] .result-display {
        background: var(--chatbot-input-bg, #2b2b3d);
        color: var(--chatbot-text, #e0e0e0);
    }
</style>
{% endblock %}

{% block content %}
<div class="test-container">
    <h1 style="text-align: center; margin-bottom: 40px; color: var(--text-color);">Chatbot Button Test Page</h1>

    <!-- Test Section 1: Basic Button Group -->
    <div class="test-section">
        <h2 class="test-title">1. LLM Model Selection Buttons</h2>
        <p class="test-description">Test the buttons that appear when the chatbot asks which AI model to use:</p>

        <p>Okay, which AI model should I use for the summary of <strong>GOOG.US</strong>?</p>
        <div class='button-group'>
            <button class="chatbot-button llm-model-button" data-model-key="openai" onclick="testButtonClick('OpenAI GPT')">OpenAI GPT</button>
            <button class="chatbot-button llm-model-button" data-model-key="anthropic" onclick="testButtonClick('Anthropic Claude')">Anthropic Claude</button>
            <button class="chatbot-button llm-model-button" data-model-key="google" onclick="testButtonClick('Google Gemini')">Google Gemini</button>
            <button class="chatbot-button llm-model-button" data-model-key="deepseek" onclick="testButtonClick('DeepSeek')">DeepSeek</button>
            <button class="chatbot-button skip-button" data-action="skip" onclick="testButtonClick('Standard Analysis')">Use Standard Analysis Instead</button>
        </div>
    </div>

    <!-- Test Section 2: API Key Input -->
    <div class="test-section">
        <h2 class="test-title">2. API Key Input Form</h2>
        <p class="test-description">Test the API key input form that appears after selecting a model:</p>

        <p>To analyze <strong>GOOG.US</strong> using <strong>OpenAI GPT</strong>, please provide your API key below.</p>
        <div class='api-key-prompt' data-model-key='openai'>
            <label for='api-key-input-openai'>Enter OpenAI GPT API Key:</label>
            <input type='password' class='api-key-input' id='api-key-input-openai' name='api_key' placeholder='Your OpenAI GPT API Key' required style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid var(--border-color); border-radius: 6px;">
            <div class='button-row button-group'>
                <button type='button' class='chatbot-button submit-key-button' data-model-key='openai' onclick="testButtonClick('Submit API Key')">Submit Key</button>
            </div>
        </div>
    </div>

    <!-- Test Section 3: Message Simulation -->
    <div class="test-section">
        <h2 class="test-title">3. Message Context Simulation</h2>
        <p class="test-description">Test how buttons appear within a chatbot message bubble:</p>

        <div class="message-simulation">
            <span class="bot-avatar">
                <i class="fas fa-robot"></i>
            </span>
            <div class="message-bubble">
                <p>I can help you analyze this stock. Which analysis method would you prefer?</p>
                <div class='button-group'>
                    <button class="chatbot-button llm-model-button" onclick="testButtonClick('AI Analysis')">AI-Powered Analysis</button>
                    <button class="chatbot-button" onclick="testButtonClick('Quick Analysis')">Quick Analysis</button>
                    <button class="chatbot-button skip-button" onclick="testButtonClick('Skip')">Skip Analysis</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Section 4: Button States -->
    <div class="test-section">
        <h2 class="test-title">4. Button States Test</h2>
        <p class="test-description">Test different button states (normal, hover, disabled):</p>

        <div class='button-group'>
            <button class="chatbot-button llm-model-button" onclick="testButtonClick('Normal Button')">Normal Button</button>
            <button class="chatbot-button llm-model-button" onclick="testButtonClick('Hover Me')" onmouseover="this.style.transform='translateY(-2px) scale(1.02)'" onmouseout="this.style.transform=''">Hover Me</button>
            <button class="chatbot-button llm-model-button" disabled>Disabled Button</button>
            <button class="chatbot-button submit-key-button" onclick="testButtonClick('Submit Style')">Submit Style</button>
            <button class="chatbot-button skip-button" onclick="testButtonClick('Skip Style')">Skip Style</button>
        </div>
    </div>

    <!-- Test Section 5: Responsive Test -->
    <div class="test-section">
        <h2 class="test-title">5. Responsive Layout Test</h2>
        <p class="test-description">Test how buttons behave on different screen sizes (resize your browser window):</p>

        <div class='button-group'>
            <button class="chatbot-button llm-model-button" onclick="testButtonClick('Long Button Name for Testing')">Long Button Name for Testing</button>
            <button class="chatbot-button llm-model-button" onclick="testButtonClick('Short')">Short</button>
            <button class="chatbot-button llm-model-button" onclick="testButtonClick('Medium Length')">Medium Length</button>
            <button class="chatbot-button llm-model-button" onclick="testButtonClick('Another Long Button Name')">Another Long Button Name</button>
            <button class="chatbot-button skip-button" onclick="testButtonClick('Skip This Test')">Skip This Test</button>
        </div>
    </div>

    <!-- Test Section 6: Theme Toggle -->
    <div class="test-section">
        <h2 class="test-title">6. Theme Compatibility Test</h2>
        <p class="test-description">Test button appearance in different themes:</p>

        <div style="text-align: center; margin-bottom: 20px;">
            <button onclick="toggleTheme()" style="padding: 10px 20px; background: var(--highlight-color); color: white; border: none; border-radius: 6px; cursor: pointer;">
                <i class="fas fa-adjust"></i> Toggle Dark/Light Theme
            </button>
        </div>

        <div class='button-group'>
            <button class="chatbot-button llm-model-button" onclick="testButtonClick('Theme Test 1')">Theme Test 1</button>
            <button class="chatbot-button llm-model-button" onclick="testButtonClick('Theme Test 2')">Theme Test 2</button>
            <button class="chatbot-button skip-button" onclick="testButtonClick('Theme Skip')">Theme Skip</button>
        </div>
    </div>

    <!-- Result Display -->
    <div id="test-results" class="result-display" style="display: none;">
        <h3>Test Results:</h3>
        <p id="result-text">No button clicked yet.</p>
        <button onclick="clearResults()" style="margin-top: 10px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">Clear Results</button>
    </div>
</div>

<script>
let clickCount = 0;

function testButtonClick(buttonName) {
    clickCount++;
    const resultsDiv = document.getElementById('test-results');
    const resultText = document.getElementById('result-text');

    resultsDiv.style.display = 'block';
    resultText.innerHTML = `
        <strong>Button Clicked:</strong> ${buttonName}<br>
        <strong>Click Count:</strong> ${clickCount}<br>
        <strong>Time:</strong> ${new Date().toLocaleTimeString()}
    `;

    // Scroll to results
    resultsDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

    console.log(`Button clicked: ${buttonName} (Click #${clickCount})`);
}

function clearResults() {
    document.getElementById('test-results').style.display = 'none';
    clickCount = 0;
}

// Test event delegation (simulating how the real chatbot works)
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('chatbot-button')) {
        console.log('Event delegation working for button:', event.target.textContent);

        // Test data attributes
        const modelKey = event.target.dataset.modelKey;
        const action = event.target.dataset.action;

        if (modelKey) {
            console.log('Model key detected:', modelKey);
        }
        if (action) {
            console.log('Action detected:', action);
        }
    }
});

// Add visual feedback for testing
document.addEventListener('DOMContentLoaded', function() {
    // Add ripple effect to buttons
    document.querySelectorAll('.chatbot-button').forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => ripple.remove(), 600);
        });
    });

    // Add CSS animation for ripple effect
    if (!document.getElementById('ripple-styles')) {
        const style = document.createElement('style');
        style.id = 'ripple-styles';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
});

console.log('Button test page loaded successfully!');
</script>
{% endblock %}
