<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testing</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        /* Base Styles (Light Theme Default) */
        :root {
            --bg-color: #ffffff;
            --text-color: #000000;
            --nav-bg-color: #f0f4fc;
            --nav-hover-color: #d0e4fb;
            --icon-color: #000000;
            --highlight-color: #007bff;
            --video-bg-color: #f9f9f9;
            --tooltip-bg: #d0e4fb;
            --input-bg-color: #ffffff;
            --input-text-color: #000000;
            --button-bg-color: #007bff;
            --button-text-color: #ffffff;
            --icon-color-invert: 0; /* Keep icons black */
        }

        /* Dark Theme Styles */
        [data-theme="dark"] {
            --bg-color: #1e1e2d;
            --text-color: #ffffff;
            --nav-bg-color: #2b2b3d;
            --nav-hover-color: #444466;
            --icon-color: #ffffff;
            --highlight-color: #8c4fff;
            --video-bg-color: #2b2b3d;
            --tooltip-bg: #444466;
            --input-bg-color: #2b2b3d;
            --input-text-color: #ffffff;
            --button-bg-color: #8c4fff;
            --button-text-color: #ffffff;
            --icon-color-invert: 1; /* Invert icons to white */
        }

        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s ease-in-out;
        }

        input, textarea {
            background-color: var(--input-bg-color);
            color: var(--input-text-color);
            border: 1px solid var(--tooltip-bg);
            padding: 10px;
            border-radius: 5px;
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 10px;
            transition: background-color 0.3s, color 0.3s;
        }

        form {
            background-color: var(--bg-color); /* Ensure the form itself adapts */
            color: var(--text-color);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
            transition: background-color 0.3s, color 0.3s;
        }

        [data-theme="dark"] form {
            background-color: var(--video-bg-color); /* Explicit override for form in dark mode */
        }

        button {
            background-color: var(--button-bg-color);
            color: var(--button-text-color);
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s, color 0.3s;
        }

        [data-theme="dark"] button {
            background-color: var(--button-bg-color); /* Ensure proper dark mode color */
            color: var(--button-text-color);
        }

        button:hover {
            opacity: 0.9;
        }

        header {
            padding: 10px 20px;
            background-color: var(--highlight-color);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-left: 70px; /* Adjusted to not overlap with sidebar */
        }

        .theme-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .theme-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--tooltip-bg);
            transition: 0.4s;
            border-radius: 20px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 3px;
            bottom: 3px;
            background-color: var(--highlight-color);
            transition: 0.4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--highlight-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
            background-color: var(--bg-color);
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100%;
            width: 60px;
            background-color: var(--nav-bg-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 20px;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
        }

        .sidebar a {
            text-decoration: none;
            margin: 20px 0;
            color: var(--icon-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .sidebar a:hover {
            background-color: var(--nav-hover-color);
            border-radius: 50%;
        }

        .sidebar a img {
            width: 30px;
            height: 30px;
            filter: invert(var(--icon-color-invert));
        }

        .tooltip {
            position: absolute;
            top: 50%;
            left: 70px;
            transform: translateY(-50%);
            background-color: var(--tooltip-bg);
            color: var(--text-color);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            visibility: hidden;
            opacity: 0;
            transform: translateX(-10px);
            transition: opacity 0.3s, transform 0.3s;
        }

        .sidebar a:hover .tooltip {
            visibility: visible;
            opacity: 1;
            transform: translateX(0);
        }

        main {
            margin-left: 70px;
            padding: 20px;
            text-align: center;
        }

        .videos-container {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            background-color: var(--video-bg-color);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .videos-container iframe {
            width: 300px;
            height: 170px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        footer {
            margin-top: 20px;
            text-align: center;
            padding: 10px;
            background-color: var(--highlight-color);
            color: white;
        }

        #loading {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5em;
            color: #333;
        }
    </style>
</head>
<body>
    <header>
        <h1>Testing</h1>
        <label class="theme-switch">
            <input type="checkbox" onclick="toggleTheme()">
            <span class="slider"></span>
        </label>
    </header>

    <div class="sidebar">
        <a href="/">
            <img src="{{ url_for('static', filename='icons/home.svg') }}" alt="Home">
            <div class="tooltip">Home</div>
        </a>
        <a href="/portfolio">
            <img src="{{ url_for('static', filename='icons/portfolio.svg') }}" alt="Portfolio">
            <div class="tooltip">Portfolio Management</div>
        </a>
        <a href="/dcf">
            <img src="{{ url_for('static', filename='icons/calculator.svg') }}" alt="DCF Calculator">
            <div class="tooltip">DCF Calculator</div>
        </a>
        <a href="/testing">
            <img src="{{ url_for('static', filename='icons/testing.svg') }}" alt="Testing">
            <div class="tooltip">Testing</div>
        </a>
    </div>

    <main>
        <h2>Testing</h2>
        <form id="stock-form">
            <label for="ticker">Stock Ticker:</label>
            <input type="text" id="ticker" name="ticker" required>
            <label for="from_date">From Date:</label>
            <input type="date" id="from_date" name="from_date" required>
            <label for="to_date">To Date:</label>
            <input type="date" id="to_date" name="to_date" required>
            <button type="submit">Fetch Data</button>
        </form>
        <div id="loading">Loading...</div>
        <div id="chart" style="width: 100%; height: 500px;"></div>
    </main>

    <footer>
        <p>&copy; 2025 Finance Tool</p>
    </footer>

    <script>
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Reapply styles immediately to avoid caching issues
            document.querySelectorAll('button').forEach((btn) => {
                btn.style.backgroundColor = getComputedStyle(body).getPropertyValue('--button-bg-color');
                btn.style.color = getComputedStyle(body).getPropertyValue('--button-text-color');
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);
            document.querySelector('.theme-switch input').checked = savedTheme === 'dark';
        });

        document.addEventListener("DOMContentLoaded", function () {
            document.getElementById("stock-form").addEventListener("submit", function (event) {
                event.preventDefault();
                const ticker = document.getElementById("ticker").value;
                const fromDate = document.getElementById("from_date").value;
                const toDate = document.getElementById("to_date").value;

                console.log("Fetching data for:", ticker, fromDate, toDate);

                document.getElementById("loading").style.display = "block";
                document.getElementById("chart").style.display = "none";

                fetch(`/fetch-live-stock-data?ticker=${ticker}&from_date=${fromDate}&to_date=${toDate}`)
                    .then(response => {
                        console.log("Response status:", response.status);
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log("Fetched Data:", data);
                        document.getElementById("loading").style.display = "none";
                        document.getElementById("chart").style.display = "block";

                        if (data.error) {
                            alert(data.error);
                        } else {
                            const dataString = encodeURIComponent(JSON.stringify(data));
                            window.location.href = `/result?ticker=${ticker}&from_date=${fromDate}&to_date=${toDate}&data=${dataString}`;
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching stock data:', error);
                        document.getElementById("loading").style.display = "none";
                        document.getElementById("chart").style.display = "block";
                        alert('Failed to fetch stock data. Please try again.');
                    });
            });
        });
    </script>
</body>
</html>
