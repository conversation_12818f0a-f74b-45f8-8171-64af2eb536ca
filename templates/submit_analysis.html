{% extends "base.html" %}

{% block title %}Submit Analysis{% endblock %}

{% block header_title %}Submit Analysis{% endblock %}

{% block head_extra %}
<style>
    .analysis-form {
        max-width: 800px;
        margin: 0 auto;
        padding: 40px;
    }
    
    .form-card {
        background: var(--card-bg-color);
        border-radius: 16px;
        padding: 40px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
    }
    
    .form-header {
        text-align: center;
        margin-bottom: 40px;
    }
    
    .form-header h1 {
        color: var(--highlight-color);
        margin-bottom: 10px;
    }
    
    .form-header p {
        color: var(--text-muted-color);
        font-size: 1.1rem;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: var(--text-color);
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        background: var(--input-bg-color);
        color: var(--text-color);
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--highlight-color);
        box-shadow: 0 0 0 3px rgba(var(--highlight-color-rgb), 0.1);
    }
    
    .form-group textarea {
        resize: vertical;
        min-height: 120px;
    }
    
    .submit-btn {
        background: var(--highlight-gradient);
        color: white;
        border: none;
        padding: 16px 32px;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.3s ease;
        width: 100%;
    }
    
    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(var(--highlight-color-rgb), 0.3);
    }
    
    .analysis-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .analysis-option {
        background: var(--nav-hover-color);
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .analysis-option:hover {
        border-color: var(--highlight-color);
        background: rgba(var(--highlight-color-rgb), 0.1);
    }
    
    .analysis-option.selected {
        border-color: var(--highlight-color);
        background: rgba(var(--highlight-color-rgb), 0.15);
    }
    
    .analysis-option i {
        font-size: 2rem;
        color: var(--highlight-color);
        margin-bottom: 10px;
    }
    
    .analysis-option h3 {
        margin: 0 0 8px 0;
        color: var(--text-color);
    }
    
    .analysis-option p {
        margin: 0;
        color: var(--text-muted-color);
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="analysis-form">
    <div class="form-card">
        <div class="form-header">
            <h1><i class="fas fa-chart-line"></i> Submit Analysis Request</h1>
            <p>Request a comprehensive financial analysis for any stock or company</p>
        </div>
        
        <form method="POST" action="{{ url_for('submit_analysis_form') }}">
            <div class="form-group">
                <label for="ticker">
                    <i class="fas fa-search"></i> Stock Ticker Symbol
                </label>
                <input 
                    type="text" 
                    id="ticker" 
                    name="ticker" 
                    placeholder="e.g., AAPL, MSFT, GOOGL" 
                    required
                    style="text-transform: uppercase;"
                >
            </div>
            
            <div class="form-group">
                <label>Analysis Type</label>
                <div class="analysis-options">
                    <div class="analysis-option selected" data-type="comprehensive">
                        <i class="fas fa-chart-bar"></i>
                        <h3>Comprehensive</h3>
                        <p>Full financial analysis with DCF, ratios, and peer comparison</p>
                    </div>
                    <div class="analysis-option" data-type="dcf">
                        <i class="fas fa-calculator"></i>
                        <h3>DCF Focus</h3>
                        <p>Detailed discounted cash flow valuation analysis</p>
                    </div>
                    <div class="analysis-option" data-type="buffett">
                        <i class="fas fa-user-tie"></i>
                        <h3>Buffett Style</h3>
                        <p>Value investing analysis in Warren Buffett's style</p>
                    </div>
                </div>
                <input type="hidden" id="analysis_type" name="analysis_type" value="comprehensive">
            </div>
            
            <div class="form-group">
                <label for="timeframe">
                    <i class="fas fa-clock"></i> Analysis Timeframe
                </label>
                <select id="timeframe" name="timeframe">
                    <option value="current">Current Analysis</option>
                    <option value="1y">1 Year Historical</option>
                    <option value="3y">3 Year Historical</option>
                    <option value="5y">5 Year Historical</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="focus_areas">
                    <i class="fas fa-bullseye"></i> Specific Focus Areas (Optional)
                </label>
                <textarea 
                    id="focus_areas" 
                    name="focus_areas" 
                    placeholder="e.g., Growth prospects, dividend sustainability, competitive position, ESG factors..."
                ></textarea>
            </div>
            
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i> Email for Results (Optional)
                </label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    placeholder="<EMAIL>"
                >
            </div>
            
            <button type="submit" class="submit-btn">
                <i class="fas fa-paper-plane"></i> Submit Analysis Request
            </button>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts_extra %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle analysis type selection
    const analysisOptions = document.querySelectorAll('.analysis-option');
    const analysisTypeInput = document.getElementById('analysis_type');
    
    analysisOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            analysisOptions.forEach(opt => opt.classList.remove('selected'));
            
            // Add selected class to clicked option
            this.classList.add('selected');
            
            // Update hidden input value
            analysisTypeInput.value = this.dataset.type;
        });
    });
    
    // Handle form submission
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const ticker = document.getElementById('ticker').value.trim().toUpperCase();
        const analysisType = document.getElementById('analysis_type').value;
        
        if (!ticker) {
            alert('Please enter a stock ticker symbol');
            return;
        }
        
        // Redirect based on analysis type
        if (analysisType === 'dcf') {
            window.location.href = `/dcf?ticker=${ticker}`;
        } else if (analysisType === 'buffett') {
            window.location.href = `/buffett-analysis/${ticker}`;
        } else {
            // For comprehensive analysis, go to portfolio to add the stock
            window.location.href = `/portfolio?add=${ticker}`;
        }
    });
    
    // Auto-uppercase ticker input
    const tickerInput = document.getElementById('ticker');
    tickerInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });
});
</script>
{% endblock %}