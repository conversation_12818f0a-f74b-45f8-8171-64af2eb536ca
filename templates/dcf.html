{% extends "base.html" %}

{% block title %}Advanced DCF Calculator{% endblock %}

{% block header_title %}Advanced DCF Calculator{% endblock %}

{% block head_extra %}
    {# Enhanced DCF Calculator Styles - Refined and Cohesive #}
    <style>
        /* === DCF Page Variables === */
        :root {
            --dcf-accent: var(--highlight-color);
            --dcf-secondary: var(--highlight-secondary);
            --dcf-gradient: var(--highlight-gradient);
            --dcf-card-bg: var(--card-bg-color);
            --dcf-border: var(--border-color);
            --dcf-shadow: var(--shadow-lg);
            --dcf-text: var(--text-color);
            --dcf-muted: var(--text-muted-color);
            --dcf-hover-bg: var(--nav-hover-color);
            --dcf-input-bg: var(--input-bg-color);
            --dcf-positive: var(--positive-color);
            --dcf-negative: var(--negative-color);
            --dcf-accent-rgb: var(--highlight-color-rgb);
            --dcf-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        [data-theme="dark"] {
            --dcf-glow: rgba(140, 79, 255, 0.15);
            --dcf-hover-glow: rgba(140, 79, 255, 0.25);
        }

        /* === DCF Page Layout === */
        .dcf-page {
            background: var(--bg-color);
            position: relative;
            min-height: calc(100vh - var(--header-height));
        }

        /* Subtle ambient glow for dark mode */
        [data-theme="dark"] .dcf-page::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 30% 70%, var(--dcf-glow) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            opacity: 0.4;
        }

        /* === Page Container === */
        .page-container {
            padding: 40px;
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        .dcf-content-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            width: 100%;
            max-width: 1200px;
        }

        /* === Enhanced Cards === */
        .calculator-section, .history-section {
            opacity: 0;
            transform: translateY(20px);
            animation: smoothSlideIn 0.8s ease-out forwards;
        }

        .calculator-section {
            animation-delay: 0.1s;
        }

        .history-section {
            animation-delay: 0.3s;
        }

        @keyframes smoothSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card, .form-card {
            background: var(--dcf-card-bg);
            border: 1px solid var(--dcf-border);
            border-radius: 16px;
            padding: 32px;
            box-shadow: var(--dcf-shadow);
            transition: var(--dcf-transition);
            position: relative;
            overflow: hidden;
        }

        .card:hover, .form-card:hover {
            transform: translateY(-4px);
            box-shadow:
                var(--dcf-shadow),
                0 20px 40px rgba(var(--dcf-accent-rgb), 0.1);
        }

        [data-theme="dark"] .card:hover,
        [data-theme="dark"] .form-card:hover {
            box-shadow:
                var(--dcf-shadow),
                0 20px 40px var(--dcf-hover-glow);
        }

        /* === Enhanced Typography === */
        .card h3, .form-card h3 {
            font-family: var(--font-family);
            font-weight: 600;
            font-size: 1.4rem;
            color: var(--dcf-text);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card h3 i, .form-card h3 i {
            color: var(--dcf-accent);
            font-size: 1.2rem;
        }

        .dcf-subtitle {
            font-family: var(--font-family);
            font-size: 0.95rem;
            color: var(--dcf-muted);
            margin-bottom: 32px;
            font-weight: 400;
            line-height: 1.5;
        }

        /* === Enhanced Form Styling === */
        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-family: var(--font-family);
            font-weight: 500;
            font-size: 0.95rem;
            color: var(--dcf-text);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group label i {
            color: var(--dcf-accent);
            font-size: 0.9rem;
        }

        .dcf-form input[type="text"],
        .dcf-form input[type="number"] {
            background: var(--dcf-input-bg);
            border: 2px solid var(--dcf-border);
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 1rem;
            color: var(--dcf-text);
            transition: var(--dcf-transition);
            width: 100%;
            box-sizing: border-box;
            font-family: var(--font-family);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
        }

        .dcf-form input[type="text"]:focus,
        .dcf-form input[type="number"]:focus {
            border-color: var(--dcf-accent);
            box-shadow: 0 0 0 3px rgba(var(--dcf-accent-rgb), 0.12);
            transform: translateY(-1px);
            outline: none;
        }

        .dcf-form input[type="text"]:hover,
        .dcf-form input[type="number"]:hover {
            border-color: var(--dcf-accent);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
        }

        .dcf-form input[type="text"]::placeholder,
        .dcf-form input[type="number"]::placeholder {
            color: var(--dcf-muted);
            font-style: italic;
            opacity: 0.8;
        }

        /* === Enhanced Submit Button === */
        .dcf-form button[type="submit"] {
            background: var(--dcf-gradient);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 18px 32px;
            font-size: 1.1rem;
            font-weight: 600;
            font-family: var(--font-family);
            cursor: pointer;
            transition: var(--dcf-transition);
            width: 100%;
            margin-top: 24px;
            box-shadow: 0 4px 12px rgba(var(--dcf-accent-rgb), 0.25);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
        }

        .dcf-form button[type="submit"]:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(var(--dcf-accent-rgb), 0.35);
        }

        .dcf-form button[type="submit"]:active {
            transform: translateY(0);
        }

        /* === Enhanced Search Container === */
        .dcf-search-container {
            position: relative;
            margin-bottom: 32px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .dcf-search-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            background: var(--dcf-input-bg);
            border: 2px solid var(--dcf-border);
            border-radius: 12px;
            font-size: 0.95rem;
            font-family: var(--font-family);
            color: var(--dcf-text);
            transition: var(--dcf-transition);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
            z-index: 1;
        }

        .dcf-search-input:focus {
            outline: none;
            border-color: var(--dcf-accent);
            box-shadow: 0 0 0 3px rgba(var(--dcf-accent-rgb), 0.12);
            transform: translateY(-1px);
        }

        .dcf-search-input::placeholder {
            color: var(--dcf-muted);
            font-style: italic;
            opacity: 0.8;
        }

        .search-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--dcf-muted);
            font-size: 1rem;
            pointer-events: none;
            transition: var(--dcf-transition);
            z-index: 2;
        }

        .dcf-search-input:focus + .search-icon {
            color: var(--dcf-accent);
        }

        /* === Enhanced Suggestions Dropdown === */
        .dcf-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--dcf-card-bg);
            border: 1px solid var(--dcf-border);
            border-radius: 12px;
            box-shadow: var(--dcf-shadow);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
            color: var(--dcf-text);
        }

        .dcf-suggestions.visible {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        /* Dark mode specific styling for suggestions dropdown */
        [data-theme="dark"] .dcf-suggestions {
            background: var(--card-bg-color);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }

        .quantum-suggestion-item {
            padding: 16px;
            border-bottom: 1px solid var(--dcf-border);
            cursor: pointer;
            transition: var(--dcf-transition);
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            overflow: hidden;
            color: var(--dcf-text);
        }

        /* Dark mode styling for suggestion items */
        [data-theme="dark"] .quantum-suggestion-item {
            border-bottom: 1px solid var(--border-color);
            color: var(--text-color);
        }

        [data-theme="dark"] .quantum-suggestion-item:hover {
            background: var(--nav-hover-color);
            color: var(--text-color);
        }
        }

        .quantum-suggestion-item:last-child {
            border-bottom: none;
        }

        .quantum-suggestion-item:hover {
            background: var(--dcf-hover-bg);
            transform: translateX(4px);
        }

        .quantum-suggestion-item.selected {
            background: var(--dcf-accent);
            color: white;
            animation: suggestionFadeOut 0.5s ease-out forwards;
        }

        @keyframes suggestionFadeOut {
            0% { opacity: 1; transform: translateX(0); }
            100% { opacity: 0; transform: translateX(20px); }
        }

        .suggestion-logo {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            object-fit: cover;
        }

        .suggestion-info {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .suggestion-name {
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--dcf-text);
        }

        .suggestion-symbol {
            font-size: 0.8rem;
            color: var(--dcf-muted);
            font-weight: 500;
        }

        .suggestion-date {
            font-size: 0.8rem;
            color: var(--dcf-muted);
        }

        /* === Enhanced Analytics Dashboard === */
        .analytics-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 28px;
            margin-bottom: 36px;
            padding: 0 4px;
        }

        .analytics-card {
            background: linear-gradient(135deg, var(--dcf-hover-bg) 0%, rgba(var(--dcf-accent-rgb), 0.05) 100%);
            border: 1px solid var(--dcf-border);
            border-radius: 20px;
            padding: 28px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
            color: var(--dcf-text);
        }

        /* Dark mode styling for analytics cards */
        [data-theme="dark"] .analytics-card {
            background: var(--analysis-gradient);
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }

        [data-theme="dark"] .analytics-card:hover {
            background: linear-gradient(135deg, var(--nav-hover-color) 0%, var(--card-bg-color) 100%);
            border-color: var(--highlight-color);
        }

        .analytics-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                var(--dcf-accent) 0%,
                var(--neural-secondary) 50%,
                var(--dcf-accent) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .analytics-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 16px 40px rgba(var(--dcf-accent-rgb), 0.25);
            border-color: var(--dcf-accent);
        }

        .analytics-card:hover::before {
            opacity: 1;
        }

        .card-header {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 24px;
        }

        .metric-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--dcf-accent), var(--neural-secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.4rem;
            color: white;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(var(--dcf-accent-rgb), 0.3);
        }

        /* Dark mode styling for metric icons */
        [data-theme="dark"] .metric-icon {
            background: linear-gradient(135deg, var(--highlight-color), var(--highlight-secondary));
            box-shadow: 0 4px 12px rgba(140, 79, 255, 0.3);
        }

        .metric-info h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dcf-text);
            margin: 0 0 4px 0;
            line-height: 1.3;
        }

        /* Dark mode styling for metric info */
        [data-theme="dark"] .metric-info h4 {
            color: var(--text-color);
        }

        .metric-description {
            font-size: 0.85rem;
            color: var(--dcf-muted);
            margin: 0;
            line-height: 1.4;
        }

        /* Dark mode styling for metric description */
        [data-theme="dark"] .metric-description {
            color: var(--text-muted-color);
        }
        }

        .metric-visualization {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }

        .progress-ring {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .ring-chart {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .ring-background {
            fill: none;
            stroke: var(--dcf-border);
            stroke-width: 8;
            stroke-linecap: round;
            opacity: 0.3;
        }

        .ring-progress {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
            stroke-dasharray: 283;
            stroke-dashoffset: 283;
            transition: stroke-dashoffset 2.5s cubic-bezier(0.4, 0, 0.2, 1);
            filter: drop-shadow(0 0 8px rgba(var(--dcf-accent-rgb), 0.4));
        }

        .ring-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .metric-value {
            display: block;
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--dcf-text);
            line-height: 1;
        }

        .metric-unit {
            display: block;
            font-size: 0.75rem;
            color: var(--dcf-muted);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 2px;
        }

        .metric-insights {
            text-align: center;
            width: 100%;
        }

        .insight-text {
            font-size: 0.9rem;
            color: var(--dcf-accent);
            font-weight: 500;
            padding: 8px 16px;
            background: rgba(var(--dcf-accent-rgb), 0.1);
            border-radius: 20px;
            border: 1px solid rgba(var(--dcf-accent-rgb), 0.2);
            display: inline-block;
            transition: all 0.3s ease;
        }

        .analytics-card:hover .insight-text {
            background: rgba(var(--dcf-accent-rgb), 0.15);
            border-color: rgba(var(--dcf-accent-rgb), 0.3);
            transform: scale(1.05);
        }

        /* === Enhanced Stat Animations === */
        .stat-item.animate-in {
            animation: statSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        @keyframes statSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* === Enhanced Micro-Interactions === */
        .analytics-card .metric-icon {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .analytics-card:hover .metric-icon {
            animation: iconFloat 2s ease-in-out infinite;
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0) scale(1); }
            50% { transform: translateY(-4px) scale(1.05); }
        }

        /* === Smooth Form Interactions === */
        .quantum-input:focus {
            animation: inputFocusGlow 0.3s ease-out;
        }

        @keyframes inputFocusGlow {
            0% { box-shadow: 0 0 0 0 rgba(var(--dcf-accent-rgb), 0.4); }
            100% { box-shadow: 0 0 0 4px rgba(var(--dcf-accent-rgb), 0.1); }
        }

        /* === Enhanced Button Interactions === */
        .dcf-submit-btn {
            position: relative;
            overflow: hidden;
        }

        .dcf-submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .dcf-submit-btn:hover::before {
            left: 100%;
        }

        /* === Smooth Page Transitions === */
        .page-container {
            animation: pageSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes pageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* === Enhanced Search Experience === */
        .dcf-search-container:focus-within .search-icon {
            animation: searchPulse 1s ease-in-out infinite;
        }

        @keyframes searchPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* === Advanced Hover Effects === */
        .stat-item:hover .stat-icon {
            animation: iconBounce 0.6s ease-out;
        }

        @keyframes iconBounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-8px); }
            80% { transform: translateY(-4px); }
        }

        /* === Sophisticated Loading States === */
        .dcf-form.loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .dcf-form.loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                transparent,
                rgba(var(--dcf-accent-rgb), 0.1),
                transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* === Enhanced Focus Ring === */
        .dcf-form input:focus,
        .dcf-search-input:focus {
            position: relative;
        }

        .dcf-form input:focus::after,
        .dcf-search-input:focus::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border: 2px solid var(--dcf-accent);
            border-radius: 16px;
            opacity: 0.3;
            animation: focusRing 0.3s ease-out;
        }

        @keyframes focusRing {
            from {
                transform: scale(0.95);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 0.3;
            }
        }

        /* === Micro-interactions === */
        .dcf-form button[type="submit"]:hover {
            animation: buttonPulse 0.3s ease-out;
        }

        @keyframes buttonPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        /* === Smooth Page Transitions === */
        .page-container {
            animation: pageSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes pageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* === Empty State === */
        .dcf-empty-state {
            text-align: center;
            padding: 48px 24px;
            color: var(--dcf-muted);
        }

        .empty-icon {
            font-size: 2.5rem;
            color: var(--dcf-accent);
            margin-bottom: 16px;
            opacity: 0.7;
        }

        .empty-message {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--dcf-text);
        }

        .empty-subtitle {
            font-size: 0.9rem;
            color: var(--dcf-muted);
            line-height: 1.5;
        }

        /* === Focus Effects === */
        .dcf-form input:focus {
            animation: focusPulse 0.4s ease-out;
        }

        @keyframes focusPulse {
            0% { box-shadow: 0 0 0 0 rgba(var(--dcf-accent-rgb), 0.4); }
            70% { box-shadow: 0 0 0 6px rgba(var(--dcf-accent-rgb), 0); }
            100% { box-shadow: 0 0 0 0 rgba(var(--dcf-accent-rgb), 0); }
        }

        /* === Loading States === */
        .dcf-form button[type="submit"]:disabled {
            opacity: 0.8;
            cursor: not-allowed;
            transform: none;
        }

        .dcf-form button[type="submit"]:disabled:hover {
            transform: none;
            box-shadow: 0 4px 12px rgba(var(--dcf-accent-rgb), 0.25);
        }







        /* Advanced Quantum Input Styling */
        .quantum-input-group {
            position: relative;
            margin-bottom: 40px;
        }

        .quantum-input-group label {
            display: block;
            margin-bottom: 15px;
            font-family: var(--font-primary);
            font-weight: 700;
            font-size: 1rem;
            color: var(--neural-primary);
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
            position: relative;
        }

        .quantum-input {
            width: 100%;
            padding: 25px 30px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 20px;
            font-size: 1.2rem;
            font-family: var(--font-secondary);
            font-weight: 600;
            color: var(--text-color);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            backdrop-filter: blur(15px);
            box-shadow:
                inset 0 4px 15px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(0, 212, 255, 0.1);
        }

        .input-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            background: linear-gradient(45deg,
                rgba(0, 212, 255, 0.1),
                rgba(139, 92, 246, 0.1));
            opacity: 0;
            transition: opacity 0.5s ease;
            pointer-events: none;
            z-index: -1;
        }

        .quantum-input:focus {
            outline: none;
            border-color: var(--neural-primary);
            background: rgba(0, 212, 255, 0.08);
            box-shadow:
                inset 0 4px 15px rgba(0, 0, 0, 0.3),
                0 0 40px rgba(0, 212, 255, 0.4),
                0 0 80px rgba(0, 212, 255, 0.2);
            transform: translateY(-3px) scale(1.02);
        }

        .quantum-input:focus + .input-glow {
            opacity: 1;
            animation: glowPulse 2s ease-in-out infinite;
        }

        @keyframes glowPulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.05); opacity: 0.6; }
        }

        .quantum-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-style: italic;
            font-weight: 400;
        }

        /* Revolutionary Submit Button */
        .quantum-submit-btn {
            width: 100%;
            padding: 25px;
            background: linear-gradient(45deg,
                var(--neural-primary),
                var(--energy-purple),
                var(--neural-secondary));
            background-size: 300% 300%;
            border: none;
            border-radius: 20px;
            font-family: var(--font-primary);
            font-weight: 900;
            font-size: 1.3rem;
            color: white;
            text-transform: uppercase;
            letter-spacing: 3px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            margin-top: 40px;
            box-shadow:
                0 15px 35px rgba(0, 212, 255, 0.4),
                inset 0 2px 4px rgba(255, 255, 255, 0.2);
            animation: gradientShift 4s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .btn-text {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .btn-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.3) 1px,
                transparent 1px);
            background-size: 20px 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
            animation: particleFloat 6s linear infinite;
        }

        @keyframes particleFloat {
            0% { transform: translateY(100%); }
            100% { transform: translateY(-100%); }
        }

        .quantum-submit-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow:
                0 25px 50px rgba(0, 212, 255, 0.6),
                0 0 100px rgba(139, 92, 246, 0.4),
                inset 0 2px 4px rgba(255, 255, 255, 0.3);
        }

        .quantum-submit-btn:hover .btn-particles {
            opacity: 1;
        }

        .quantum-submit-btn:active {
            transform: translateY(-2px) scale(1.02);
        }
        .search-history .suggestion {
            transition: background-color 0.2s ease, transform 0.2s ease, padding-left 0.2s ease;
        }
        .search-history .suggestion:hover {
            background-color: var(--nav-hover-color);
            transform: translateX(5px);
            padding-left: 20px;
        }
        #suggestions {
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 0.3s ease, transform 0.3s ease;
            pointer-events: none;
        }
        #suggestions:not(.hidden) {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }
        /* Enhanced DCF Layout */
        .page-container {
            padding: 30px;
            min-height: calc(100vh - var(--header-height));
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        .dcf-content-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            max-width: 1200px;
            width: 100%;
        }

        .calculator-section, .history-section {
            opacity: 0;
            transform: translateY(30px);
            animation: smoothFadeIn 0.8s ease-out forwards;
        }

        .calculator-section {
            animation-delay: 0.2s;
        }

        .history-section {
            animation-delay: 0.4s;
        }

        @keyframes smoothFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Cards */
        .card, .form-card {
            background: var(--dcf-card-bg);
            border: 1px solid var(--dcf-border);
            border-radius: 20px;
            padding: 35px;
            box-shadow: var(--dcf-shadow);
            position: relative;
            transition: all 0.3s ease;
        }

        /* Subtle top accent for dark mode */
        [data-theme="dark"] .card::before,
        [data-theme="dark"] .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--dcf-gradient);
            border-radius: 20px 20px 0 0;
        }

        .card:hover, .form-card:hover {
            transform: translateY(-5px);
            box-shadow:
                var(--dcf-shadow),
                0 15px 30px var(--shadow-hover-color);
        }

        [data-theme="dark"] .card:hover,
        [data-theme="dark"] .form-card:hover {
            box-shadow:
                var(--dcf-shadow),
                0 15px 30px var(--dcf-hover-glow);
        }

        /* Quantum Search Container */
        .quantum-search-container {
            position: relative;
            margin-bottom: 40px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
        }

        .search-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--dcf-accent);
            font-size: 1.1rem;
            z-index: 2;
            pointer-events: none;
        }

        .dcf-search-input {
            width: 100%;
            padding: 18px 20px 18px 55px; /* Left padding accounts for search icon */
            background: var(--dcf-input-bg);
            border: 2px solid var(--dcf-border);
            border-radius: 15px;
            color: var(--dcf-text);
            font-size: 1rem;
            font-family: var(--font-family);
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            min-height: 54px; /* Ensure consistent height */
            box-sizing: border-box;
        }

        .dcf-search-input:focus {
            border-color: var(--dcf-accent);
            box-shadow: 0 0 0 3px rgba(var(--dcf-accent-rgb), 0.15);
            outline: none;
            transform: translateY(-2px);
        }

        .dcf-search-input::placeholder {
            color: var(--dcf-muted);
            font-style: italic;
        }

        .search-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            background: linear-gradient(45deg,
                rgba(0, 212, 255, 0.1),
                rgba(139, 92, 246, 0.1));
            opacity: 0;
            transition: opacity 0.5s ease;
            pointer-events: none;
            z-index: -1;
        }

        .quantum-search-input:focus + .search-glow {
            opacity: 1;
            animation: searchGlow 2s ease-in-out infinite;
        }

        @keyframes searchGlow {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.02); opacity: 0.6; }
        }




            font-family: var(--font-family);
        }

        .stat-label {
            font-size: 0.85rem;
            color: var(--dcf-muted);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Enhanced Empty State */
        .dcf-empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--dcf-muted);
        }

        .empty-icon {
            font-size: 3rem;
            color: var(--dcf-accent);
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .empty-message {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dcf-text);
        }

        .empty-subtitle {
            font-size: 0.9rem;
            color: var(--dcf-muted);
        }

        /* Quantum Suggestions Dropdown */
        .quantum-suggestions {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-top: none;
            border-radius: 0 0 20px 20px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1050;
            box-shadow: var(--glass-shadow);
            opacity: 0;
            transform: translateY(-15px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
        }

        .quantum-suggestions.visible {
            display: block;
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        /* Quantum Suggestion Items */
        .quantum-suggestion-item {
            display: flex;
            align-items: center;
            padding: 20px 25px;
            cursor: pointer;
            border-bottom: 1px solid rgba(0, 212, 255, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            gap: 20px;
            position: relative;
            overflow: hidden;
        }

        .quantum-suggestion-item:last-child {
            border-bottom: none;
        }

        .quantum-suggestion-item:hover {
            background: rgba(0, 212, 255, 0.08);
            transform: translateX(10px);
            border-left: 3px solid var(--neural-primary);
        }

        .suggestion-glow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(0, 212, 255, 0.1) 50%,
                transparent 100%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .quantum-suggestion-item:hover .suggestion-glow {
            transform: translateX(100%);
        }

        .suggestion-logo {
            width: 40px;
            height: 40px;
            object-fit: contain;
            border-radius: 10px;
            flex-shrink: 0;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            padding: 5px;
            transition: all 0.3s ease;
        }

        .quantum-suggestion-item:hover .suggestion-logo {
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
        }

        .suggestion-info {
            flex-grow: 1;
            min-width: 0;
        }

        .suggestion-name {
            display: block;
            font-family: var(--font-secondary);
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--text-color);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 5px;
        }

        .suggestion-symbol {
            font-family: var(--font-primary);
            font-weight: 600;
            color: var(--neural-primary);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .suggestion-date {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
            margin-left: auto;
            flex-shrink: 0;
            white-space: nowrap;
            padding-left: 15px;
            font-family: var(--font-secondary);
        }

        .suggestion-neural-indicator {
            color: var(--energy-purple);
            font-size: 1.2rem;
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .quantum-suggestion-item:hover .suggestion-neural-indicator {
            opacity: 1;
            transform: scale(1.2);
            color: var(--neural-primary);
        }

        .suggestion-state {
            padding: 25px;
            text-align: center;
            color: var(--neural-primary);
            font-family: var(--font-secondary);
            font-style: italic;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .suggestion-state i {
            animation: brainPulse 2s ease-in-out infinite;
        }

        /* Dark mode styling for suggestion state */
        [data-theme="dark"] .suggestion-state {
            color: var(--text-muted-color);
        }

        [data-theme="dark"] .suggestion-state.error {
            color: var(--negative-color);
        }

        @keyframes brainPulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.2); opacity: 1; }
        }

        /* No History Styling */
        .no-history-container {
            text-align: center;
            padding: 60px 20px;
            margin: 40px 0;
        }

        .no-history-icon {
            font-size: 4rem;
            color: var(--neural-primary);
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .no-history-icon i {
            animation: robotFloat 3s ease-in-out infinite;
        }

        @keyframes robotFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .no-history-message {
            font-family: var(--font-primary);
            font-size: 1.5rem;
            color: var(--neural-primary);
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .no-history-subtitle {
            font-family: var(--font-secondary);
            color: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
            line-height: 1.5;
            max-width: 400px;
            margin: 0 auto;
        }

        /* Enhanced Results Section */
        .results-section {
            margin-top: 30px;
        }

        .results-card {
            background: var(--dcf-card-bg);
            border: 1px solid var(--dcf-border);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .results-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--dcf-border);
        }

        .results-header h4 {
            margin: 0;
            color: var(--dcf-text);
            font-family: var(--font-family);
            font-weight: 600;
        }

        .results-header i {
            margin-right: 10px;
            color: var(--dcf-accent);
        }

        /* --- Input Fill Animation --- */
        @keyframes inputHighlight {
            0% { background-color: rgba(var(--highlight-color-rgb), 0); box-shadow: 0 0 0 0 rgba(var(--highlight-color-rgb), 0.3); }
            50% { background-color: rgba(var(--highlight-color-rgb), 0.15); box-shadow: 0 0 0 4px rgba(var(--highlight-color-rgb), 0.1); }
            100% { background-color: rgba(var(--highlight-color-rgb), 0); box-shadow: 0 0 0 0 rgba(var(--highlight-color-rgb), 0.0); }
        }

        .input-filled-animation {
            animation: inputHighlight 0.7s ease-out forwards;
        }

        /* Minor adjustments for label/input alignment */
        .form-card .form-group {
            margin-bottom: 18px; /* Consistent spacing */
        }
        .form-card label {
            display: block;
            margin-bottom: 6px; /* Space between label and input */
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            color: var(--text-secondary-color);
        }
        /* Responsive Quantum Design */
        @media (max-width: 1200px) {
            .dcf-content-wrapper {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .page-container {
                padding: 30px 20px 30px 100px;
            }
        }

        @media (max-width: 768px) {
            .page-container {
                padding: 20px 15px 20px 80px;
            }

            .card, .form-card {
                padding: 25px;
                border-radius: 20px;
            }

            .analytics-dashboard {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            header h1 {
                font-size: 1.4rem;
                letter-spacing: 1px;
            }

            .quantum-input {
                padding: 20px 25px;
                font-size: 1rem;
            }

            .quantum-submit-btn {
                padding: 20px;
                font-size: 1.1rem;
                letter-spacing: 2px;
            }
        }

        /* Quantum Loading Animation */
        .quantum-loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 10, 15, 0.9);
            backdrop-filter: blur(10px);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loading-spinner {
            width: 80px;
            height: 80px;
            border: 3px solid rgba(0, 212, 255, 0.3);
            border-top: 3px solid var(--neural-primary);
            border-radius: 50%;
            animation: quantumSpin 1s linear infinite;
        }

        @keyframes quantumSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Quantum Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, var(--neural-primary), var(--energy-purple));
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, var(--energy-purple), var(--neural-secondary));
        }

        /* Enhanced Focus Effects */
        .dcf-form input:focus,
        .dcf-search-input:focus {
            animation: dcfFocus 0.4s ease-out;
        }

        @keyframes dcfFocus {
            0% { box-shadow: 0 0 0 0 rgba(var(--dcf-accent-rgb), 0.5); }
            70% { box-shadow: 0 0 0 8px rgba(var(--dcf-accent-rgb), 0); }
            100% { box-shadow: 0 0 0 0 rgba(var(--dcf-accent-rgb), 0); }
        }

        /* Quantum Success Animation */
        .quantum-success {
            animation: quantumSuccess 0.8s ease-out;
        }

        @keyframes quantumSuccess {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); box-shadow: 0 0 50px rgba(16, 185, 129, 0.6); }
            100% { transform: scale(1); }
        }

        /* Additional Quantum Effects */
        .quantum-calculator::before {
            animation-delay: 0.5s;
        }

        .quantum-history::before {
            animation-delay: 1s;
        }

        /* === Responsive Design === */
        @media (max-width: 1024px) {
            .dcf-content-wrapper {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .page-container {
                padding: 30px;
            }
        }

        @media (max-width: 768px) {
            .page-container {
                padding: 20px;
            }

            .dcf-content-wrapper {
                gap: 30px;
            }

            .card, .form-card {
                padding: 24px;
            }

            .analytics-dashboard {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .analytics-card {
                padding: 20px;
            }

            .progress-ring {
                width: 100px;
                height: 100px;
            }

            .metric-value {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .page-container {
                padding: 16px;
            }

            .card, .form-card {
                padding: 20px;
            }

            .card h3, .form-card h3 {
                font-size: 1.2rem;
            }

            .dcf-form input[type="text"],
            .dcf-form input[type="number"] {
                font-size: 16px; /* Prevent zoom on iOS */
                padding: 14px 16px;
            }

            .dcf-form button[type="submit"] {
                padding: 16px 24px;
                font-size: 1rem;
            }
        }
    </style>
{% endblock %}

{# Body tag and theme logic are inherited from base.html #}
{% block content %}
    {# Header and Sidebar are inherited from base.html #}
    <div class="page-container dcf-page">
        <div class="dcf-content-wrapper">
            <section class="calculator-section">
                <div class="card form-card dcf-calculator">
                    <h3><i class="fas fa-calculator"></i> Advanced DCF Calculator</h3>
                    <p class="dcf-subtitle">Professional-grade discounted cash flow analysis</p>

                    <form method="POST" action="{{ url_for('dcf_page') }}" class="dcf-form">
                        <div class="form-group">
                            <label for="ticker"><i class="fas fa-chart-line"></i> Ticker Symbol</label>
                            <input type="text" id="ticker" name="ticker" required
                                   placeholder="e.g., AAPL, MSFT, TSLA"
                                   value="{{ ticker or '' }}">
                        </div>

                        <div class="form-group">
                            <label for="years"><i class="fas fa-calendar-alt"></i> Projection Years</label>
                            <input type="number" id="years" name="years" required min="1" max="30" step="1"
                                   placeholder="Number of years (1-30)"
                                   value="{{ years or '' }}">
                        </div>

                        <div class="form-group">
                            <label for="growth_rate"><i class="fas fa-trending-up"></i> Growth Rate (%)</label>
                            <input type="number" id="growth_rate" name="growth_rate" required step="0.1"
                                   placeholder="Expected annual growth rate"
                                   value="{{ growth_rate or '' }}">
                        </div>

                        <div class="form-group">
                            <label for="discount_rate"><i class="fas fa-percentage"></i> Discount Rate (%)</label>
                            <input type="number" id="discount_rate" name="discount_rate" required step="0.1"
                                   placeholder="WACC or required return"
                                   value="{{ discount_rate or '' }}">
                        </div>

                        <div class="form-group">
                            <label for="margin_of_safety"><i class="fas fa-shield-alt"></i> Margin of Safety (%)</label>
                            <input type="number" id="margin_of_safety" name="margin_of_safety" required min="0" max="99" step="1"
                                   placeholder="Conservative buffer percentage"
                                   value="{{ margin_of_safety or '' }}">
                        </div>

                        <button type="submit" class="dcf-submit-btn">
                            <i class="fas fa-cogs"></i>
                            Calculate Fair Value
                        </button>
                    </form>
                </div>
            </section>
            <section class="history-section">
                <div class="card dcf-history">
                    <h3><i class="fas fa-history"></i> Calculation History</h3>
                    <p class="dcf-subtitle">Previous DCF analyses and results</p>

                    <div class="dcf-search-container">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="search" id="dcf-history-search"
                                   placeholder="Search by ticker or company..."
                                   autocomplete="off"
                                   class="dcf-search-input">
                        </div>

                        <div id="dcf-suggestions-dropdown" class="dcf-suggestions">
                            <div class="suggestion-state">
                                <i class="fas fa-search"></i>
                                Start typing to search...
                            </div>
                        </div>
                    </div>

                    {% if not session.get('dcf_history') %}
                    <div class="dcf-empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <p class="empty-message">No calculations yet</p>
                        <p class="empty-subtitle">Start your first DCF analysis to build your calculation history</p>
                    </div>
                    {% endif %}

                    <!-- Enhanced Analytics Dashboard -->
                    <div class="analytics-dashboard">
                        <!-- SVG Gradient Definitions -->
                        <svg width="0" height="0" style="position: absolute;">
                            <defs>
                                <linearGradient id="analysisGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="diversityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#E91E63;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="conservatismGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#3F51B5;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                        </svg>

                        <!-- Analysis Activity Meter -->
                        <div class="analytics-card" data-metric="activity">
                            <div class="card-header">
                                <div class="metric-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="metric-info">
                                    <h4>Analysis Activity</h4>
                                    <p class="metric-description">Your DCF calculation frequency</p>
                                </div>
                            </div>
                            <div class="metric-visualization">
                                <div class="progress-ring">
                                    <svg class="ring-chart" viewBox="0 0 120 120">
                                        <circle class="ring-background" cx="60" cy="60" r="45"></circle>
                                        <circle class="ring-progress" cx="60" cy="60" r="45"
                                                stroke="url(#analysisGradient)"
                                                data-progress="{{ ((session.get('dcf_history', [])|length / 20) * 100)|round(0) if session.get('dcf_history', [])|length <= 20 else 100 }}"></circle>
                                    </svg>
                                    <div class="ring-content">
                                        <span class="metric-value">{{ session.get('dcf_history', [])|length }}</span>
                                        <span class="metric-unit">analyses</span>
                                    </div>
                                </div>
                                <div class="metric-insights">
                                    {% set analysis_count = session.get('dcf_history', [])|length %}
                                    {% if analysis_count == 0 %}
                                        <span class="insight-text">Start your investment analysis journey</span>
                                    {% elif analysis_count < 5 %}
                                        <span class="insight-text">Building your analysis foundation</span>
                                    {% elif analysis_count < 15 %}
                                        <span class="insight-text">Developing analytical expertise</span>
                                    {% else %}
                                        <span class="insight-text">Advanced investor mindset</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Portfolio Diversity Meter -->
                        <div class="analytics-card" data-metric="diversity">
                            <div class="card-header">
                                <div class="metric-icon">
                                    <i class="fas fa-layer-group"></i>
                                </div>
                                <div class="metric-info">
                                    <h4>Portfolio Diversity</h4>
                                    <p class="metric-description">Unique companies analyzed</p>
                                </div>
                            </div>
                            <div class="metric-visualization">
                                <div class="progress-ring">
                                    <svg class="ring-chart" viewBox="0 0 120 120">
                                        <circle class="ring-background" cx="60" cy="60" r="45"></circle>
                                        <circle class="ring-progress" cx="60" cy="60" r="45"
                                                stroke="url(#diversityGradient)"
                                                data-progress="{{ ((session.get('dcf_history', [])|map(attribute='ticker')|unique|list|length / 10) * 100)|round(0) if session.get('dcf_history', [])|map(attribute='ticker')|unique|list|length <= 10 else 100 }}"></circle>
                                    </svg>
                                    <div class="ring-content">
                                        <span class="metric-value">{{ session.get('dcf_history', [])|map(attribute='ticker')|unique|list|length }}</span>
                                        <span class="metric-unit">companies</span>
                                    </div>
                                </div>
                                <div class="metric-insights">
                                    {% set company_count = session.get('dcf_history', [])|map(attribute='ticker')|unique|list|length %}
                                    {% if company_count == 0 %}
                                        <span class="insight-text">No companies analyzed yet</span>
                                    {% elif company_count < 3 %}
                                        <span class="insight-text">Consider diversifying research</span>
                                    {% elif company_count < 8 %}
                                        <span class="insight-text">Good research diversification</span>
                                    {% else %}
                                        <span class="insight-text">Excellent research breadth</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Investment Conservatism Meter -->
                        <div class="analytics-card" data-metric="conservatism">
                            <div class="card-header">
                                <div class="metric-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="metric-info">
                                    <h4>Investment Conservatism</h4>
                                    <p class="metric-description">Average margin of safety preference</p>
                                </div>
                            </div>
                            <div class="metric-visualization">
                                <div class="progress-ring">
                                    <svg class="ring-chart" viewBox="0 0 120 120">
                                        <circle class="ring-background" cx="60" cy="60" r="45"></circle>
                                        <circle class="ring-progress" cx="60" cy="60" r="45"
                                                stroke="url(#conservatismGradient)"
                                                data-progress="{{ (session.get('dcf_history', [])|map(attribute='inputs')|map(attribute='margin_of_safety')|sum / session.get('dcf_history', [])|length)|round(0) if session.get('dcf_history') and session.get('dcf_history')|length > 0 else 0 }}"></circle>
                                    </svg>
                                    <div class="ring-content">
                                        <span class="metric-value">{{ (session.get('dcf_history', [])|map(attribute='inputs')|map(attribute='margin_of_safety')|sum / session.get('dcf_history', [])|length)|round(0) if session.get('dcf_history') and session.get('dcf_history')|length > 0 else 0 }}</span>
                                        <span class="metric-unit">% safety</span>
                                    </div>
                                </div>
                                <div class="metric-insights">
                                    {% set avg_safety = (session.get('dcf_history', [])|map(attribute='inputs')|map(attribute='margin_of_safety')|sum / session.get('dcf_history', [])|length)|round(0) if session.get('dcf_history') and session.get('dcf_history')|length > 0 else 0 %}
                                    {% if avg_safety == 0 %}
                                        <span class="insight-text">No safety data available</span>
                                    {% elif avg_safety < 15 %}
                                        <span class="insight-text">Aggressive investment style</span>
                                    {% elif avg_safety < 25 %}
                                        <span class="insight-text">Balanced risk approach</span>
                                    {% else %}
                                        <span class="insight-text">Conservative investor profile</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
    {# Footer is inherited from base.html #}
{% endblock %}

{% block scripts_extra %}
    {# Enhanced DCF Scripts #}
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // === Enhanced Form Interactions ===
            const dcfInputs = document.querySelectorAll('.dcf-form input[type="text"], .dcf-form input[type="number"], .dcf-search-input');

            // Enhanced focus effects
            dcfInputs.forEach(input => {
                input.addEventListener('focus', () => {
                    input.style.transform = 'translateY(-1px)';
                });

                input.addEventListener('blur', () => {
                    input.style.transform = '';
                });
            });

            // === Enhanced Form Submission ===
            const dcfForm = document.querySelector('.dcf-form');
            if (dcfForm) {
                dcfForm.addEventListener('submit', (e) => {
                    const submitBtn = dcfForm.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.innerHTML = `
                            <i class="fas fa-cog fa-spin"></i>
                            Calculating...
                        `;
                        submitBtn.disabled = true;
                    }

                    // Smooth loading overlay
                    const overlay = document.createElement('div');
                    overlay.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(135deg, var(--dcf-accent) 0%, var(--dcf-secondary) 100%);
                        z-index: 9999;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        opacity: 0;
                        transition: opacity 0.5s ease;
                        backdrop-filter: blur(10px);
                    `;

                    // Add enhanced loading content with multiple elements
                    const loadingContent = document.createElement('div');
                    loadingContent.style.cssText = `
                        text-align: center;
                        color: white;
                        transform: translateY(30px);
                        transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.3s;
                    `;

                    // Create animated progress ring
                    const progressRing = document.createElement('div');
                    progressRing.style.cssText = `
                        position: relative;
                        width: 80px;
                        height: 80px;
                        margin: 0 auto 30px;
                    `;
                    progressRing.innerHTML = `
                        <svg width="80" height="80" style="transform: rotate(-90deg);">
                            <circle cx="40" cy="40" r="35" stroke="rgba(255,255,255,0.2)" stroke-width="4" fill="none"/>
                            <circle cx="40" cy="40" r="35" stroke="white" stroke-width="4" fill="none"
                                    stroke-dasharray="220" stroke-dashoffset="220"
                                    style="transition: stroke-dashoffset 2s cubic-bezier(0.4, 0, 0.2, 1);">
                                <animate attributeName="stroke-dashoffset" values="220;0;220" dur="3s" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 1.2rem;">
                            <i class="fas fa-calculator" style="animation: pulse 2s ease-in-out infinite;"></i>
                        </div>
                    `;

                    // Create animated text elements
                    const titleElement = document.createElement('h3');
                    titleElement.style.cssText = `
                        margin: 0 0 15px;
                        font-size: 1.8rem;
                        font-weight: 700;
                        opacity: 0;
                        transform: translateY(20px);
                        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.5s;
                        background: linear-gradient(45deg, #fff, #f0f0f0);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    `;
                    titleElement.textContent = 'Analyzing Investment';

                    const subtitleElement = document.createElement('p');
                    subtitleElement.style.cssText = `
                        margin: 0 0 20px;
                        opacity: 0;
                        font-size: 1.1rem;
                        transform: translateY(20px);
                        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.7s;
                        color: rgba(255, 255, 255, 0.9);
                    `;
                    subtitleElement.textContent = 'Calculating fair value and generating insights...';

                    // Create progress steps
                    const stepsContainer = document.createElement('div');
                    stepsContainer.style.cssText = `
                        display: flex;
                        gap: 20px;
                        margin-top: 20px;
                        opacity: 0;
                        transform: translateY(20px);
                        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.9s;
                    `;

                    const steps = [
                        { icon: 'fas fa-download', text: 'Fetching Data' },
                        { icon: 'fas fa-cogs', text: 'Processing' },
                        { icon: 'fas fa-chart-line', text: 'Analyzing' }
                    ];

                    steps.forEach((step, index) => {
                        const stepElement = document.createElement('div');
                        stepElement.style.cssText = `
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 8px;
                            opacity: 0.5;
                            transition: opacity 0.3s ease;
                        `;
                        stepElement.innerHTML = `
                            <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(255,255,255,0.2); display: flex; align-items: center; justify-content: center;">
                                <i class="${step.icon}" style="font-size: 1rem;"></i>
                            </div>
                            <span style="font-size: 0.85rem; white-space: nowrap;">${step.text}</span>
                        `;
                        stepsContainer.appendChild(stepElement);

                        // Animate steps sequentially
                        setTimeout(() => {
                            stepElement.style.opacity = '1';
                            stepElement.style.transform = 'scale(1.1)';
                            setTimeout(() => {
                                stepElement.style.transform = 'scale(1)';
                            }, 200);
                        }, 1000 + (index * 400));
                    });

                    loadingContent.appendChild(progressRing);
                    loadingContent.appendChild(titleElement);
                    loadingContent.appendChild(subtitleElement);
                    loadingContent.appendChild(stepsContainer);

                    transitionOverlay.appendChild(loadingContent);
                    document.body.appendChild(transitionOverlay);

                    // Enhanced animation sequence
                    setTimeout(() => {
                        transitionOverlay.style.opacity = '1';
                        transitionOverlay.style.transform = 'scale(1)';
                    }, 50);

                    setTimeout(() => {
                        loadingContent.style.transform = 'translateY(0)';
                        titleElement.style.opacity = '1';
                        titleElement.style.transform = 'translateY(0)';
                    }, 300);

                    setTimeout(() => {
                        subtitleElement.style.opacity = '1';
                        subtitleElement.style.transform = 'translateY(0)';
                    }, 500);

                    setTimeout(() => {
                        stepsContainer.style.opacity = '1';
                        stepsContainer.style.transform = 'translateY(0)';
                    }, 700);
                });
            }

            // --- NEW Smart Search Functionality ---
            const searchInput = document.getElementById('dcf-history-search');
            const suggestionsDropdown = document.getElementById('dcf-suggestions-dropdown');
            const tickerInput = document.getElementById('ticker'); // Main form ticker input
            const yearsInput = document.getElementById('years');
            const growthInput = document.getElementById('growth_rate');
            const discountInput = document.getElementById('discount_rate');
            const mosInput = document.getElementById('margin_of_safety');
            const formInputs = [tickerInput, yearsInput, growthInput, discountInput, mosInput]; // Array for animation

            // Debounce function (if not already defined globally)
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => { clearTimeout(timeout); func(...args); };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            const fetchSuggestions = async (query) => {
                if (!suggestionsDropdown) return;
                suggestionsDropdown.innerHTML = '<div class="suggestion-state">Searching...</div>';
                suggestionsDropdown.classList.add('visible');

                try {
                    // Use the correct API endpoint defined in app.py
                    const response = await fetch(`/api/dcf-history-search?query=${encodeURIComponent(query)}`);
                    if (!response.ok) {
                        throw new Error(`Network error: ${response.statusText}`);
                    }
                    const results = await response.json();
                    displaySuggestions(results);
                } catch (error) {
                    console.error("Error fetching search suggestions:", error);
                    suggestionsDropdown.innerHTML = `<div class="suggestion-state error">Error: ${error.message}</div>`;
                     suggestionsDropdown.classList.add('visible'); // Keep visible to show error
                }
            };

            const displaySuggestions = (results) => {
                if (!suggestionsDropdown) return;
                suggestionsDropdown.innerHTML = ''; // Clear previous

                if (!results || results.length === 0) {
                    suggestionsDropdown.innerHTML = `
                        <div class="suggestion-state">
                            <i class="fas fa-search"></i>
                            No neural patterns found
                        </div>`;
                     suggestionsDropdown.classList.add('visible'); // Keep visible
                    return;
                }

                results.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'quantum-suggestion-item';
                    // Store inputs safely as JSON string in data attribute
                    div.dataset.inputs = JSON.stringify(item.inputs || {});
                    div.dataset.ticker = item.ticker || ''; // Store full ticker if needed

                    // Use the fields returned by the API endpoint with quantum styling
                    div.innerHTML = `
                        <div class="suggestion-glow"></div>
                        <img src="${item.logo_url}" alt="${item.symbol || ''} Logo" class="suggestion-logo" onerror="this.style.display='none'; this.onerror=null; this.src='{{ url_for('static', filename='icons/default_logo.svg') }}';">
                        <div class="suggestion-info">
                            <span class="suggestion-name">${item.name || 'Unknown'}</span>
                            <span class="suggestion-symbol">${item.symbol || 'N/A'}</span>
                        </div>
                        <span class="suggestion-date">${item.date || 'N/A'}</span>
                        <div class="suggestion-neural-indicator">
                            <i class="fas fa-brain"></i>
                        </div>
                    `;
                    div.addEventListener('click', handleSuggestionClick);
                    suggestionsDropdown.appendChild(div);
                });
                 suggestionsDropdown.classList.add('visible'); // Ensure visible
            };

            const handleSuggestionClick = (event) => {
                 const suggestionItem = event.currentTarget; // The clicked div
                 if (!suggestionItem || !suggestionItem.dataset.inputs) return;

                 // Add selected class and fade out animation
                 suggestionItem.classList.add('selected');

                 // Hide dropdown after animation
                 setTimeout(() => {
                     if (suggestionsDropdown) {
                         suggestionsDropdown.classList.remove('visible');
                     }
                 }, 500);

                 try {
                    const inputs = JSON.parse(suggestionItem.dataset.inputs);
                    // Fill form using the retrieved inputs object
                    fillFormInputs(inputs);

                    // Optional: Set search bar value to selected ticker symbol
                    // searchInput.value = inputs.ticker || '';

                 } catch (e) {
                     console.error("Error parsing stored inputs:", e);
                     alert("Error loading saved data for this entry.");
                 }

                 // Hide dropdown after selection
                 if (suggestionsDropdown) suggestionsDropdown.classList.remove('visible');
                 if (searchInput) searchInput.value = ''; // Clear search input after selection
            };

            const fillFormInputs = (inputs) => {
                 // Fill each form field, providing a default empty string if the key is missing
                 // Use the keys from the 'inputs' object saved in the session
                 if (tickerInput) tickerInput.value = inputs.ticker || ''; // Use the original ticker input
                 if (yearsInput) yearsInput.value = inputs.years || '';
                 if (growthInput) growthInput.value = inputs.growth_rate || '';
                 if (discountInput) discountInput.value = inputs.discount_rate || '';
                 if (mosInput) mosInput.value = inputs.margin_of_safety || '';

                 // Trigger animation on all relevant inputs
                 formInputs.forEach(input => {
                     if (input) {
                          // Remove first to allow re-triggering if clicked quickly
                          input.classList.remove('input-filled-animation');
                          // Force reflow/repaint before adding class again
                          void input.offsetWidth;
                          input.classList.add('input-filled-animation');
                          // Remove class after animation completes
                          setTimeout(() => {
                             input.classList.remove('input-filled-animation');
                          }, 700); // Match CSS animation duration
                     }
                 });
            };

            // Debounced search handler
            const debouncedSearch = debounce(fetchSuggestions, 300); // 300ms delay

            // Attach listener to search input
            if (searchInput) {
                searchInput.addEventListener('input', (e) => {
                    const query = e.target.value.trim();
                    if (query.length > 0) {
                        debouncedSearch(query);
                    } else {
                        if (suggestionsDropdown) suggestionsDropdown.classList.remove('visible');
                    }
                });

                // Hide dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (suggestionsDropdown && !searchInput.contains(e.target) && !suggestionsDropdown.contains(e.target)) {
                        suggestionsDropdown.classList.remove('visible');
                    }
                });

                 // Hide dropdown on Escape key
                 searchInput.addEventListener('keydown', (e) => {
                      if (e.key === "Escape" && suggestionsDropdown) {
                         suggestionsDropdown.classList.remove('visible');
                      }
                 });
            } else {
                console.warn("DCF History Search input (#dcf-history-search) not found.");
            }

            // === Enhanced Card Interactions ===
            const cards = document.querySelectorAll('.card, .form-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-4px)';
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = '';
                });
            });

            // === Enhanced Analytics Dashboard Animations ===
            const animateAnalyticsDashboard = () => {
                const analyticsCards = document.querySelectorAll('.analytics-card');

                analyticsCards.forEach((card, index) => {
                    // Animate cards with stagger
                    setTimeout(() => {
                        card.classList.add('animate-in');
                    }, index * 300);

                    // Animate ring progress charts
                    const progressRing = card.querySelector('.ring-progress');
                    if (progressRing) {
                        const progress = parseInt(progressRing.getAttribute('data-progress')) || 0;
                        const circumference = 2 * Math.PI * 45; // radius = 45
                        const offset = circumference - (progress / 100) * circumference;

                        // Start with full offset (empty circle)
                        progressRing.style.strokeDashoffset = circumference;

                        // Animate to target offset with delay
                        setTimeout(() => {
                            progressRing.style.strokeDashoffset = offset;
                        }, 800 + (index * 300));
                    }

                    // Animate metric value count-up
                    const metricValue = card.querySelector('.metric-value');

                    if (metricValue) {
                        const targetValue = parseInt(metricValue.textContent) || 0;
                        const isPercentage = metricValue.textContent.includes('%');

                        let currentValue = 0;
                        const increment = Math.max(1, targetValue / 40); // 40 frames for smooth animation

                        const countUp = () => {
                            currentValue += increment;
                            if (currentValue >= targetValue) {
                                currentValue = targetValue;
                                clearInterval(countInterval);
                            }

                            const displayValue = Math.floor(currentValue);
                            metricValue.textContent = isPercentage ? `${displayValue}` : displayValue;
                        };

                        const countInterval = setInterval(countUp, 60);
                        setTimeout(() => {
                            countInterval;
                        }, 1200 + (index * 300));
                    }

                    // Add hover effect enhancement
                    card.addEventListener('mouseenter', () => {
                        const icon = card.querySelector('.metric-icon');
                        if (icon) {
                            icon.style.transform = 'scale(1.1) rotate(5deg)';
                        }
                    });

                    card.addEventListener('mouseleave', () => {
                        const icon = card.querySelector('.metric-icon');
                        if (icon) {
                            icon.style.transform = 'scale(1) rotate(0deg)';
                        }
                    });
                });
            };

            // === Enhanced Page Load Animations ===
            const animatePageElements = () => {
                // Animate analytics dashboard
                setTimeout(animateAnalyticsDashboard, 800);

                // Add subtle parallax effect to cards
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const cardObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }
                    });
                }, observerOptions);

                // Observe all cards for scroll animations
                document.querySelectorAll('.card, .form-card').forEach(card => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    cardObserver.observe(card);
                });
            };

            // === Enhanced Form Experience ===
            const enhanceFormExperience = () => {
                // Add smooth focus transitions
                const inputs = document.querySelectorAll('.quantum-input');
                inputs.forEach(input => {
                    input.addEventListener('focus', () => {
                        input.parentElement.style.transform = 'scale(1.02)';
                    });

                    input.addEventListener('blur', () => {
                        input.parentElement.style.transform = 'scale(1)';
                    });
                });

                // Enhanced submit button interaction
                const submitBtn = document.querySelector('.dcf-submit-btn');
                if (submitBtn) {
                    submitBtn.addEventListener('click', (e) => {
                        // Add ripple effect
                        const ripple = document.createElement('span');
                        ripple.style.cssText = `
                            position: absolute;
                            border-radius: 50%;
                            background: rgba(255,255,255,0.6);
                            transform: scale(0);
                            animation: ripple 0.6s linear;
                            pointer-events: none;
                        `;

                        const rect = submitBtn.getBoundingClientRect();
                        const size = Math.max(rect.width, rect.height);
                        ripple.style.width = ripple.style.height = size + 'px';
                        ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                        ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

                        submitBtn.appendChild(ripple);
                        setTimeout(() => ripple.remove(), 600);
                    });
                }
            };

            // === Smooth Scroll Enhancements ===
            const enhanceScrollExperience = () => {
                // Add smooth scroll behavior for internal links
                document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                    anchor.addEventListener('click', function (e) {
                        e.preventDefault();
                        const target = document.querySelector(this.getAttribute('href'));
                        if (target) {
                            target.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    });
                });

                // Add subtle fade-in effect for elements coming into view
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }
                    });
                }, observerOptions);

                // Observe cards for fade-in effect (without moving them)
                document.querySelectorAll('.card, .analytics-card').forEach(card => {
                    card.style.opacity = '0.95';
                    card.style.transform = 'translateY(10px)';
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    observer.observe(card);
                });
            };

            // === Initialize All Enhancements ===
            animatePageElements();
            enhanceFormExperience();
            enhanceScrollExperience();

        }); // End DOMContentLoaded

        // === Add Ripple Animation CSS ===
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
{% endblock %}
