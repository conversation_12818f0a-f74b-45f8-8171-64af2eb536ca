{% extends "base.html" %}
{% block title %}Financial Reports{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Search and Filter Section -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="companySearch" class="form-control" placeholder="Search for a company...">
                                <button class="btn btn-primary" type="button" onclick="searchCompanies()">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <select id="reportType" class="form-select" onchange="filterReports()">
                                <option value="all">All Reports</option>
                                <option value="10k">10-K Reports</option>
                                <option value="10q">10-Q Reports</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Companies</h5>
                </div>
                <div class="card-body">
                    <div id="companiesList" class="list-group">
                        <!-- Companies will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Financial Reports</h5>
                </div>
                <div class="card-body">
                    <div id="reportsList">
                        <!-- Reports will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Content Modal -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Report Content</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="reportContent">
                    <!-- Report content will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.list-group-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.list-group-item:hover {
    background-color: var(--bs-primary-bg-subtle);
}

.report-card {
    cursor: pointer;
    transition: transform 0.2s;
}

.report-card:hover {
    transform: translateY(-2px);
}

.chart-container {
    height: 300px;
    margin-bottom: 1rem;
}

.section-card {
    margin-bottom: 1rem;
}

.section-card .card-header {
    background-color: var(--bs-primary-bg-subtle);
}

.table th {
    font-weight: 600;
}

.table td {
    font-family: var(--bs-font-monospace);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentCompany = null;
let currentReports = [];

function searchCompanies() {
    const query = document.getElementById('companySearch').value.trim();
    if (!query) return;

    fetch(`/api/search_companies?query=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            const companiesList = document.getElementById('companiesList');
            companiesList.innerHTML = '';

            data.companies.forEach(company => {
                const item = document.createElement('button');
                item.className = 'list-group-item list-group-item-action';
                item.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">${company.name}</h6>
                            <small class="text-muted">${company.symbol} - ${company.exchange}</small>
                        </div>
                    </div>
                `;
                item.onclick = () => loadCompanyReports(company);
                companiesList.appendChild(item);
            });
        })
        .catch(error => {
            console.error('Error searching companies:', error);
            alert('Failed to search companies. Please try again.');
        });
}

function loadCompanyReports(company) {
    currentCompany = company;
    const reportType = document.getElementById('reportType').value;

    fetch(`/api/company_reports?symbol=${encodeURIComponent(company.symbol)}&type=${reportType}`)
        .then(response => response.json())
        .then(data => {
            currentReports = data.reports;
            displayReports();
        })
        .catch(error => {
            console.error('Error loading reports:', error);
            alert('Failed to load reports. Please try again.');
        });
}

function displayReports() {
    const reportsList = document.getElementById('reportsList');
    reportsList.innerHTML = '';

    if (currentReports.length === 0) {
        reportsList.innerHTML = '<p class="text-muted">No reports found for this company.</p>';
        return;
    }

    currentReports.forEach(report => {
        const card = document.createElement('div');
        card.className = 'card report-card mb-3';
        card.innerHTML = `
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">${report.type} - ${report.statement_type.replace('_', ' ')}</h6>
                        <small class="text-muted">Period: ${report.period}</small>
                    </div>
                    <button class="btn btn-primary btn-sm" onclick="viewReport('${report.id}')">
                        View Report
                    </button>
                </div>
            </div>
        `;
        reportsList.appendChild(card);
    });
}

function filterReports() {
    if (currentCompany) {
        loadCompanyReports(currentCompany);
    }
}

function viewReport(reportId) {
    if (!currentCompany) return;

    fetch(`/api/report_content?symbol=${encodeURIComponent(currentCompany.symbol)}&report_id=${reportId}`)
        .then(response => response.json())
        .then(data => {
            const reportContent = document.getElementById('reportContent');
            reportContent.innerHTML = ''; // Clear previous content

            if (data.error) {
                reportContent.innerHTML = `<p class="text-danger">Error loading report: ${data.error}</p>`;
                return;
            }
            if (!data.sections || data.sections.length === 0) {
                reportContent.innerHTML = '<p>No content available for this report.</p>';
                return;
            }

            data.sections.forEach((section, sectionIndex) => {
                let chartsHTML = '';
                if (section.charts && section.charts.length > 0) {
                    section.charts.forEach((chartDef, chartIndex) => {
                        const chartId = `d3chart-${sectionIndex}-${chartIndex}`;
                        chartsHTML += `<div id="${chartId}" class="d3-chart-container mb-3" style="width: 100%; height: 350px;"></div>`;
                    });
                }

                const sectionCard = document.createElement('div');
                sectionCard.className = 'card section-card';
                sectionCard.innerHTML = `
                    <div class="card-header">
                        <h6 class="mb-0">${section.title}</h6>
                    </div>
                    <div class="card-body">
                        ${section.content}
                        ${chartsHTML}
                    </div>
                `;
                reportContent.appendChild(sectionCard);

                // After appending, render D3 charts if any
                if (section.charts && section.charts.length > 0) {
                    section.charts.forEach((chartDef, chartIndex) => {
                        const chartId = `d3chart-${sectionIndex}-${chartIndex}`;
                        renderD3Chart(chartDef, chartId);
                    });
                }
            });

            const modal = new bootstrap.Modal(document.getElementById('reportModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error loading report content:', error);
            const reportContent = document.getElementById('reportContent');
            reportContent.innerHTML = '<p class="text-danger">Failed to load report content. Please check console for details.</p>';
            const modal = new bootstrap.Modal(document.getElementById('reportModal'));
            modal.show();
        });
}

function renderD3Chart(chartDefinition, targetDivId) {
    if (!chartDefinition || !chartDefinition.data || chartDefinition.data.length === 0) {
        console.warn("Invalid chart definition for", targetDivId, chartDefinition);
        return;
    }
    const chartType = chartDefinition.data[0].type;
    const chartTitle = chartDefinition.layout ? chartDefinition.layout.title : "Chart";

    // Clear any previous chart in the target div
    d3.select("#" + targetDivId).selectAll("*").remove();


    if (chartType === 'pie') {
        renderD3PieChart(chartDefinition.data[0], targetDivId, chartTitle);
    } else if (chartType === 'bar') {
        // Placeholder for bar chart rendering
        renderD3BarChart(chartDefinition.data[0], targetDivId, chartTitle);
        console.log("Bar chart rendering to be implemented for:", targetDivId);
    } else if (chartType === 'line') {
        // Placeholder for line chart rendering
        renderD3LineChart(chartDefinition.data[0], targetDivId, chartTitle);
        console.log("Line chart rendering to be implemented for:", targetDivId);
    } else {
        console.warn("Unsupported D3 chart type:", chartType, "for div:", targetDivId);
        d3.select("#" + targetDivId).html(`<p class="text-muted text-center">Unsupported chart type: ${chartType}</p>`);
    }
}

function renderD3PieChart(chartData, targetDivId, title) {
    const data = chartData.labels.map((label, i) => ({ label: label, value: chartData.values[i] }));
    const container = d3.select("#" + targetDivId);

    const width = parseInt(container.style("width"));
    const height = parseInt(container.style("height"));
    const radius = Math.min(width, height) / 2.5; // Adjusted radius for better fit

    const svg = container.append("svg")
        .attr("width", width)
        .attr("height", height)
        .append("g")
        .attr("transform", `translate(${width / 2},${height / 2})`);

    // Add title
    svg.append("text")
        .attr("x", 0)
        .attr("y", -height / 2 + 20) // Position title at the top
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "bold")
        .style("fill", "var(--text-color)") // Use theme color
        .text(title);

    const color = d3.scaleOrdinal(d3.schemeCategory10);

    const pie = d3.pie()
        .value(d => d.value)
        .sort(null); // Keep original order

    const arc = d3.arc()
        .innerRadius(0) // For a pie chart, not a donut
        .outerRadius(radius);

    const g = svg.selectAll(".arc")
        .data(pie(data))
        .enter().append("g")
        .attr("class", "arc");

    g.append("path")
        .attr("d", arc)
        .style("fill", d => color(d.data.label))
        .attr("stroke", "var(--card-bg-color)") // Use theme color for border
        .style("stroke-width", "2px")
        .on("mouseover", function(event, d) {
            d3.select(this).transition().duration(200).attr("opacity", 0.7);
            tooltip.transition().duration(200).style("opacity", .9);
            tooltip.html(`<strong>${d.data.label}</strong><br/>Value: ${d.data.value.toLocaleString()}<br/>(${(d.data.value / d3.sum(data, item => item.value) * 100).toFixed(1)}%)`)
                .style("left", (event.pageX + 15) + "px")
                .style("top", (event.pageY - 28) + "px");
        })
        .on("mouseout", function(d) {
            d3.select(this).transition().duration(200).attr("opacity", 1);
            tooltip.transition().duration(500).style("opacity", 0);
        });

    // Tooltip
    const tooltip = d3.select("body").append("div")
        .attr("class", "d3-tooltip card") // Apply card styling
        .style("opacity", 0)
        .style("position", "absolute")
        .style("padding", "10px")
        .style("background-color", "var(--card-bg-color)")
        .style("border", "1px solid var(--border-color)")
        .style("border-radius", "8px")
        .style("box-shadow", "var(--shadow-lg)")
        .style("pointer-events", "none") // To prevent tooltip from interfering with mouse events
        .style("color", "var(--text-color)");


    // Optional: Add a simple legend
    const legendRectSize = 18;
    const legendSpacing = 4;
    const legend = svg.selectAll('.legend')
        .data(data)
        .enter()
        .append('g')
        .attr('class', 'legend')
        .attr('transform', (d, i) => {
            const legendHeight = data.length * (legendRectSize + legendSpacing);
            const vert = i * (legendRectSize + legendSpacing) - (legendHeight / 2);
            // Position legend to the right of the pie. Adjust horizontal as needed.
            return `translate(${radius + 20}, ${vert})`;
        });

    legend.append('rect')
        .attr('width', legendRectSize)
        .attr('height', legendRectSize)
        .style('fill', d => color(d.label))
        .style('stroke', d => color(d.label));

    legend.append('text')
        .attr('x', legendRectSize + legendSpacing)
        .attr('y', legendRectSize - legendSpacing)
        .style("fill", "var(--text-color)") // Use theme color
        .text(d => d.label.length > 15 ? d.label.substring(0,12) + "..." : d.label) // Truncate long labels
         .append("title") // Full label on hover for truncated ones
         .text(d => d.label);
}

// Placeholder for D3 Bar Chart
function renderD3BarChart(chartData, targetDivId, title) {
    d3.select("#" + targetDivId).html(`<p class="text-muted text-center m-5">D3 Bar Chart for "${title}" to be implemented.</p>`);
}

// Placeholder for D3 Line Chart
function renderD3LineChart(chartData, targetDivId, title) {
     d3.select("#" + targetDivId).html(`<p class="text-muted text-center m-5">D3 Line Chart for "${title}" to be implemented.</p>`);
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('companySearch');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchCompanies();
        }
    });
});
</script>
{% endblock %} 