{% extends "base.html" %} {% block title %}Enhanced D3.js Rating Charts Demo{%
endblock %} {% block head_extra %}
<style>
    .demo-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    .demo-section {
        margin-bottom: 60px;
        padding: 40px;
        background: var(--card-bg-color, white);
        border-radius: 16px;
        box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.08));
        border: 1px solid var(--border-color, #e0e0e0);
    }

    .demo-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .demo-subtitle {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 30px;
        color: var(--text-color);
        text-align: center;
    }

    .demo-description {
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--text-color);
        text-align: center;
        margin-bottom: 40px;
        opacity: 0.8;
    }

    .charts-showcase {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        margin: 40px 0;
    }

    .chart-demo-item {
        background: var(--card-bg-color, white);
        padding: 30px;
        border-radius: 16px;
        text-align: center;
        border: 2px solid var(--border-color, #e0e0e0);
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        opacity: 0;
        transform: translateY(50px) scale(0.95);
    }

    .chart-demo-item.visible {
        opacity: 1;
        transform: translateY(0) scale(1);
    }

    .chart-demo-item:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border-color: var(--highlight-color, #007bff);
    }

    .chart-demo-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: var(--text-color);
    }

    .chart-demo-description {
        font-size: 0.95rem;
        color: var(--text-color);
        opacity: 0.7;
        margin-bottom: 20px;
    }

    .interactive-section {
        background: linear-gradient(
            135deg,
            rgba(var(--highlight-color-rgb, 0, 123, 255), 0.05),
            rgba(var(--highlight-color-rgb, 0, 123, 255), 0.02)
        );
        border: 2px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.1);
    }

    .theme-toggle-demo {
        text-align: center;
        margin: 30px 0;
    }

    .theme-toggle-demo button {
        background: var(--highlight-color, #007bff);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .theme-toggle-demo button:hover {
        background: var(--highlight-darker, #0056b3);
        transform: translateY(-2px);
    }

    .features-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 40px 0;
    }

    .feature-item {
        display: flex;
        align-items: center;
        padding: 20px;
        background: rgba(var(--positive-color-rgb, 0, 128, 0), 0.05);
        border-radius: 12px;
        border: 1px solid rgba(var(--positive-color-rgb, 0, 128, 0), 0.1);
    }

    .feature-icon {
        font-size: 1.5rem;
        margin-right: 15px;
        color: var(--positive-color, #28a745);
    }

    .feature-text {
        font-weight: 500;
        color: var(--text-color);
    }

    .scroll-indicator {
        text-align: center;
        margin: 40px 0;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    /* Dark theme adjustments */
    [data-theme="dark"] .demo-section {
        background: var(--chatbot-bg, #1e1e2d);
        border-color: var(--chatbot-border-color, #444466);
    }

    [data-theme="dark"] .chart-demo-item {
        background: var(--chatbot-input-bg, #2b2b3d);
        border-color: var(--chatbot-border-color, #444466);
    }

    [data-theme="dark"] .interactive-section {
        background: linear-gradient(
            135deg,
            rgba(121, 40, 202, 0.1),
            rgba(255, 0, 128, 0.05)
        );
        border-color: rgba(121, 40, 202, 0.2);
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .demo-container {
            padding: 20px 10px;
        }

        .demo-section {
            padding: 20px;
            margin-bottom: 30px;
        }

        .demo-title {
            font-size: 2rem;
        }

        .charts-showcase {
            grid-template-columns: 1fr;
            gap: 20px;
        }
    }
</style>
{% endblock %} {% block content %}
<div class="demo-container">
    <!-- Header Section -->
    <div class="demo-section">
        <h1 class="demo-title">Enhanced D3.js Rating Charts</h1>
        <p class="demo-description">
            Experience the next generation of interactive rating visualizations
            with smooth scroll-triggered animations, responsive design, and
            enhanced visual appeal. These charts provide an engaging way to
            display financial analysis ratings and investment scores.
        </p>

        <div class="features-list">
            <div class="feature-item">
                <i class="fas fa-magic feature-icon"></i>
                <span class="feature-text">Scroll-triggered animations</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-palette feature-icon"></i>
                <span class="feature-text">Dynamic color coding</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-mobile-alt feature-icon"></i>
                <span class="feature-text">Fully responsive design</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-mouse-pointer feature-icon"></i>
                <span class="feature-text">Interactive hover effects</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-chart-pie feature-icon"></i>
                <span class="feature-text">Smooth arc animations</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-moon feature-icon"></i>
                <span class="feature-text">Dark/Light theme support</span>
            </div>
        </div>

        <div class="theme-toggle-demo">
            <button onclick="toggleTheme()">
                <i class="fas fa-adjust"></i> Toggle Theme
            </button>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="scroll-indicator">
        <i
            class="fas fa-chevron-down"
            style="font-size: 2rem; color: var(--highlight-color)"
        ></i>
        <p style="margin-top: 10px; color: var(--text-color); opacity: 0.7">
            Scroll down to see the animations
        </p>
    </div>

    <!-- Individual Chart Demos -->
    <div class="demo-section">
        <h2 class="demo-subtitle">Individual Rating Charts</h2>
        <div class="charts-showcase">
            <div class="chart-demo-item" data-delay="0">
                <div class="chart-demo-title">Excellent Performance</div>
                <div class="chart-demo-description">
                    High-performing investment with strong fundamentals
                </div>
                <div class="chart-container" id="chart-excellent"></div>
            </div>

            <div class="chart-demo-item" data-delay="200">
                <div class="chart-demo-title">Good Performance</div>
                <div class="chart-demo-description">
                    Solid investment opportunity with decent returns
                </div>
                <div class="chart-container" id="chart-good"></div>
            </div>

            <div class="chart-demo-item" data-delay="400">
                <div class="chart-demo-title">Fair Performance</div>
                <div class="chart-demo-description">
                    Average investment with moderate risk
                </div>
                <div class="chart-container" id="chart-fair"></div>
            </div>

            <div class="chart-demo-item" data-delay="600">
                <div class="chart-demo-title">Poor Performance</div>
                <div class="chart-demo-description">
                    Below-average investment requiring attention
                </div>
                <div class="chart-container" id="chart-poor"></div>
            </div>
        </div>
    </div>

    <!-- Comprehensive Analysis Section -->
    <div class="demo-section interactive-section">
        <h2 class="demo-subtitle">Comprehensive Stock Analysis</h2>
        <p class="demo-description">
            This section demonstrates how multiple rating categories work
            together to provide a complete investment picture.
        </p>
        <div id="comprehensive-analysis">
            <!-- Enhanced charts will be dynamically generated here -->
        </div>
    </div>

    <!-- Message-Style Charts -->
    <div class="demo-section">
        <h2 class="demo-subtitle">Chatbot Message Integration</h2>
        <p class="demo-description">
            See how these charts integrate seamlessly into chatbot conversations
            with staggered animations.
        </p>

        <div
            class="message bot"
            style="
                margin: 20px 0;
                display: flex;
                align-items: flex-start;
                gap: 12px;
            "
        >
            <span
                class="bot-avatar"
                style="
                    background: var(--highlight-color);
                    color: white;
                    padding: 8px;
                    border-radius: 50%;
                    min-width: 36px;
                    height: 36px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                "
            >
                <i class="fas fa-robot" style="font-size: 16px"></i>
            </span>
            <div
                style="
                    background: var(--card-bg-color);
                    padding: 20px;
                    border-radius: 16px;
                    max-width: 80%;
                    border: 1px solid var(--border-color);
                "
            >
                <div class="message-rating-charts" id="message-charts-demo">
                    <!-- Message charts will be generated here -->
                </div>
                <p style="margin: 0; color: var(--text-color)">
                    <strong>Analysis Complete:</strong> Based on our
                    comprehensive evaluation, this stock shows strong business
                    fundamentals with good financial health. The valuation
                    appears reasonable given current market conditions, and
                    management has demonstrated effective leadership.
                    <em>Overall recommendation: BUY</em>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Create intersection observer for scroll animations
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        const delay =
                            parseInt(entry.target.dataset.delay) || index * 150;
                        setTimeout(() => {
                            entry.target.classList.add("visible");

                            // Trigger enhanced chart animations
                            if (entry.target.id === "comprehensive-analysis") {
                                const ratingsData = {
                                    overall: 78,
                                    business: 85,
                                    financial: 72,
                                    valuation: 65,
                                    management: 80,
                                };
                                const enhancedCharts =
                                    createEnhancedRatingCharts(ratingsData);
                                entry.target.appendChild(enhancedCharts);
                            }
                        }, delay);

                        observer.unobserve(entry.target);
                    }
                });
            },
            {
                threshold: 0.2,
                rootMargin: "0px 0px -50px 0px",
            },
        );

        // Observe all chart demo items
        document.querySelectorAll(".chart-demo-item").forEach((item) => {
            observer.observe(item);
        });

        // Observe comprehensive analysis container
        document
            .querySelectorAll("#comprehensive-analysis")
            .forEach((container) => {
                observer.observe(container);
            });

        // Create individual demo charts
        setTimeout(
            () => createDemoChart("chart-excellent", 92, "#28a745"),
            1000,
        );
        setTimeout(() => createDemoChart("chart-good", 78, "#ffc107"), 1200);
        setTimeout(() => createDemoChart("chart-fair", 58, "#fd7e14"), 1400);
        setTimeout(() => createDemoChart("chart-poor", 32, "#dc3545"), 1600);

        // Create message-style charts
        setTimeout(() => createMessageCharts(), 2000);
    });

    function createDemoChart(containerId, score, color) {
        const container = d3.select(`#${containerId}`);
        const size = 120;
        const strokeWidth = 8;
        const radius = size / 2 - strokeWidth / 2;
        const circumference = 2 * Math.PI * radius;

        const svg = container
            .append("svg")
            .attr("width", size)
            .attr("height", size)
            .style("filter", "drop-shadow(0 4px 12px rgba(0,0,0,0.1))");

        const g = svg
            .append("g")
            .attr("transform", `translate(${size / 2}, ${size / 2})`);

        // Background circle
        g.append("circle")
            .attr("r", radius)
            .attr("fill", "none")
            .attr("stroke", "#f0f0f0")
            .attr("stroke-width", strokeWidth);

        // Animated foreground circle
        const foreground = g
            .append("circle")
            .attr("r", radius)
            .attr("fill", "none")
            .attr("stroke", color)
            .attr("stroke-width", strokeWidth)
            .attr("stroke-dasharray", circumference)
            .attr("stroke-dashoffset", circumference)
            .attr("stroke-linecap", "round")
            .style("filter", `drop-shadow(0 0 6px ${color}60)`);

        // Animate the arc
        foreground
            .transition()
            .duration(1800)
            .ease(d3.easeElasticOut.amplitude(1).period(0.6))
            .attr(
                "stroke-dashoffset",
                circumference - (score / 100) * circumference,
            );

        // Center text
        const text = g
            .append("text")
            .attr("text-anchor", "middle")
            .attr("dominant-baseline", "middle")
            .attr("font-size", "18px")
            .attr("font-weight", "bold")
            .attr("fill", color)
            .style("filter", `drop-shadow(0 0 3px ${color}40)`)
            .text("0%");

        // Animate counter
        text.transition()
            .duration(1500)
            .tween("text", function () {
                const i = d3.interpolate(0, score);
                return function (t) {
                    d3.select(this).text(Math.round(i(t)) + "%");
                };
            });
    }

    // Function to create enhanced D3.js rating charts with animations
    function createEnhancedRatingCharts(ratingsData) {
        console.log("Creating enhanced charts for demo:", ratingsData);

        // Create container for all rating charts
        const container = document.createElement("div");
        container.className = "enhanced-rating-charts-container";
        container.style.cssText = `
        margin: 20px 0;
        padding: 20px;
        background: rgba(var(--highlight-color-rgb, 0, 123, 255), 0.02);
        border-radius: 16px;
        border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.1);
    `;

        // Overall rating section
        const overallSection = document.createElement("div");
        overallSection.className = "overall-rating-section";
        overallSection.style.cssText = `
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 30px;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, rgba(var(--highlight-color-rgb, 0, 123, 255), 0.05), rgba(var(--highlight-color-rgb, 0, 123, 255), 0.02));
        border-radius: 12px;
        border: 1px solid rgba(var(--highlight-color-rgb, 0, 123, 255), 0.1);
    `;

        // Overall chart wrapper
        const overallChartWrapper = document.createElement("div");
        overallChartWrapper.className = "overall-chart-wrapper";

        // Overall details
        const overallDetails = document.createElement("div");
        overallDetails.className = "overall-rating-details";
        overallDetails.style.textAlign = "center";

        const categoryLabel = document.createElement("div");
        categoryLabel.className = "rating-category";
        categoryLabel.textContent = "Overall Rating";
        categoryLabel.style.cssText = `
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: var(--text-color);
    `;

        const scoreContainer = document.createElement("div");
        scoreContainer.className = "rating-score-text";
        scoreContainer.style.cssText = `
        font-size: 3rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 5px;
        color: ${getRatingColor(ratingsData.overall)};
    `;

        const scoreText = document.createElement("span");
        scoreText.textContent = "0";
        const percentSign = document.createElement("span");
        percentSign.className = "percent-sign";
        percentSign.textContent = "%";
        percentSign.style.cssText = `
        font-size: 1.8rem;
        opacity: 0.8;
        margin-left: 2px;
    `;

        scoreContainer.appendChild(scoreText);
        scoreContainer.appendChild(percentSign);
        overallDetails.appendChild(categoryLabel);
        overallDetails.appendChild(scoreContainer);

        overallSection.appendChild(overallChartWrapper);
        overallSection.appendChild(overallDetails);

        // Component ratings grid
        const componentGrid = document.createElement("div");
        componentGrid.className = "component-ratings-grid";
        componentGrid.style.cssText = `
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 20px;
    `;

        const componentData = [
            {
                name: "Business Quality / Moat",
                score: ratingsData.business || 0,
                color: "#17a2b8",
            },
            {
                name: "Financial Strength",
                score: ratingsData.financial || 0,
                color: "#28a745",
            },
            {
                name: "Valuation",
                score: ratingsData.valuation || 0,
                color: "#fd7e14",
            },
            {
                name: "Management",
                score: ratingsData.management || 0,
                color: "#6f42c1",
            },
        ];

        // Helper function to get rating color
        function getRatingColor(score) {
            if (score >= 80) return "#28a745";
            if (score >= 60) return "#ffc107";
            if (score >= 40) return "#fd7e14";
            return "#dc3545";
        }

        // Create overall chart with D3.js
        setTimeout(() => {
            createAnimatedDonutChart(
                overallChartWrapper,
                120,
                10,
                ratingsData.overall,
                getRatingColor(ratingsData.overall),
                0,
            );

            // Animate score counter
            animateCounter(scoreText, 0, ratingsData.overall, 1500, 300);
        }, 100);

        // Create component charts
        componentData.forEach((comp, index) => {
            const item = document.createElement("div");
            item.className = "component-rating-item";
            item.style.cssText = `
            background: var(--card-bg-color, white);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid var(--border-color, #e0e0e0);
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            opacity: 0;
            transform: scale(0.8) translateY(20px);
        `;

            const componentName = document.createElement("div");
            componentName.className = "component-name";
            componentName.textContent = comp.name;
            componentName.style.cssText = `
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-color);
        `;

            const chartWrapper = document.createElement("div");
            chartWrapper.className = "component-chart-wrapper";
            chartWrapper.style.cssText = `
            display: flex;
            justify-content: center;
            margin-bottom: 10px;
        `;

            item.appendChild(componentName);
            item.appendChild(chartWrapper);
            componentGrid.appendChild(item);

            // Animate item entrance
            setTimeout(
                () => {
                    item.style.transition =
                        "all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)";
                    item.style.opacity = "1";
                    item.style.transform = "scale(1) translateY(0px)";
                },
                600 + index * 150,
            );

            // Create component chart
            setTimeout(
                () => {
                    createAnimatedDonutChart(
                        chartWrapper,
                        80,
                        8,
                        comp.score,
                        comp.color,
                        index * 200,
                    );
                },
                800 + index * 200,
            );

            // Add hover effects
            item.addEventListener("mouseenter", function () {
                this.style.transform = "scale(1.05) translateY(-5px)";
                this.style.boxShadow = "0 12px 24px rgba(0, 0, 0, 0.15)";
                this.style.borderColor = "var(--highlight-color, #007bff)";
            });

            item.addEventListener("mouseleave", function () {
                this.style.transform = "scale(1) translateY(0px)";
                this.style.boxShadow = "";
                this.style.borderColor = "var(--border-color, #e0e0e0)";
            });
        });

        container.appendChild(overallSection);
        container.appendChild(componentGrid);

        return container;
    }

    // Function to create animated donut chart with D3.js
    function createAnimatedDonutChart(
        container,
        size,
        strokeWidth,
        score,
        color,
        delay = 0,
    ) {
        const radius = size / 2 - strokeWidth / 2;
        const circumference = 2 * Math.PI * radius;

        const svg = d3
            .select(container)
            .append("svg")
            .attr("width", size)
            .attr("height", size)
            .style("filter", "drop-shadow(0 4px 12px rgba(0,0,0,0.1))");

        const g = svg
            .append("g")
            .attr("transform", `translate(${size / 2}, ${size / 2})`);

        // Background circle
        g.append("circle")
            .attr("r", radius)
            .attr("fill", "none")
            .attr("stroke", "#e0e0e0")
            .attr("stroke-width", strokeWidth)
            .style("filter", "drop-shadow(0 0 2px rgba(0,0,0,0.1))");

        // Animated foreground circle
        const foreground = g
            .append("circle")
            .attr("r", radius)
            .attr("fill", "none")
            .attr("stroke", color)
            .attr("stroke-width", strokeWidth)
            .attr("stroke-dasharray", circumference)
            .attr("stroke-dashoffset", circumference)
            .attr("stroke-linecap", "round")
            .style("filter", `drop-shadow(0 0 4px ${color}40)`);

        // Animate the arc
        foreground
            .transition()
            .delay(delay)
            .duration(1500)
            .ease(d3.easeElasticOut.amplitude(1).period(0.8))
            .attr(
                "stroke-dashoffset",
                circumference - (score / 100) * circumference,
            );

        // Center text
        const text = g
            .append("text")
            .attr("text-anchor", "middle")
            .attr("dominant-baseline", "middle")
            .attr("font-weight", "bold")
            .attr("font-size", size > 80 ? "14px" : "10px")
            .attr("fill", color)
            .text("0%");

        // Animate counter
        text.transition()
            .delay(delay + 200)
            .duration(1200)
            .tween("text", function () {
                const i = d3.interpolate(0, score);
                return function (t) {
                    d3.select(this).text(Math.round(i(t)) + "%");
                };
            });

        // Add pulsing effect for high scores
        if (score >= 75) {
            foreground
                .transition()
                .delay(delay + 1500)
                .duration(800)
                .ease(d3.easeSinInOut)
                .attr("stroke-width", strokeWidth + 1)
                .transition()
                .duration(800)
                .ease(d3.easeSinInOut)
                .attr("stroke-width", strokeWidth);
        }
    }

    // Function to animate counter
    function animateCounter(element, start, end, duration, delay = 0) {
        setTimeout(() => {
            const startTime = performance.now();
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current =
                    start + (end - start) * d3.easeQuadOut(progress);
                element.textContent = Math.round(current);

                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            requestAnimationFrame(update);
        }, delay);
    }

    function createMessageCharts() {
        const container = d3.select("#message-charts-demo");

        const charts = [
            {
                id: "msg-overall",
                label: "Overall Rating",
                score: 78,
                color: "#ffc107",
            },
            {
                id: "msg-business",
                label: "Business Quality/Moat",
                score: 85,
                color: "#17a2b8",
            },
            {
                id: "msg-financial",
                label: "Financial Strength",
                score: 72,
                color: "#28a745",
            },
            {
                id: "msg-valuation",
                label: "Valuation",
                score: 65,
                color: "#fd7e14",
            },
            {
                id: "msg-management",
                label: "Management",
                score: 80,
                color: "#6f42c1",
            },
        ];

        charts.forEach((chart, index) => {
            const chartContainer = container
                .append("div")
                .attr("class", "rating-chart-container-message")
                .style("margin", "10px")
                .style("opacity", 0)
                .style("transform", "translateY(30px) scale(0.9)");

            chartContainer
                .append("div")
                .attr("class", "chart-label")
                .text(chart.label);

            const chartDiv = chartContainer
                .append("div")
                .attr("id", chart.id)
                .attr("class", "component-chart");

            // Staggered animation
            setTimeout(() => {
                chartContainer
                    .transition()
                    .duration(600)
                    .ease(d3.easeBackOut.overshoot(1.2))
                    .style("opacity", 1)
                    .style("transform", "translateY(0px) scale(1)");

                createDemoChart(chart.id, chart.score, chart.color);
            }, index * 150);
        });
    }
</script>
{% endblock %}
