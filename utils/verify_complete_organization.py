#!/usr/bin/env python3
"""
Complete Organization Verification Script

Verifies that the comprehensive file reorganization was successful.
Tests both Python and non-Python file organization.
"""

import os
import sys
from typing import Dict, List, Tuple

def check_folder_structure() -> Dict[str, bool]:
    """Check that all expected folders exist."""
    print("🗂️  Checking Folder Structure")
    print("=" * 50)
    
    expected_folders = [
        # Main folders
        'docs', 'docs/summaries', 'tests', 'debug', 'demos', 'utils', 'archive',
        'templates', 'static', 'static/css', 'data', 'data/test_data', 'config',
        
        # Test subfolders
        'tests/currency', 'tests/portfolio_import', 'tests/ocr', 'tests/web_integration',
        
        # Debug subfolders
        'debug/currency', 'debug/gemini', 'debug/ocr', 'debug/general',
        
        # Data subfolders
        'data/test_data/images', 'data/test_data/html_tests'
    ]
    
    results = {}
    for folder in expected_folders:
        exists = os.path.exists(folder) and os.path.isdir(folder)
        status = "✅" if exists else "❌"
        print(f"  {status} {folder}")
        results[folder] = exists
    
    return results

def check_root_directory_cleanliness() -> Tuple[bool, List[str]]:
    """Check that root directory only contains essential files."""
    print("\n🧹 Checking Root Directory Cleanliness")
    print("=" * 50)
    
    allowed_files = {
        'app.py', 'portfolio_import.py', 'financial_reports.py', 
        'requirements.txt', '.env', '.gitignore'
    }
    
    allowed_folders = {
        'docs', 'tests', 'debug', 'demos', 'utils', 'archive',
        'templates', 'static', 'data', 'config', 'node_modules',
        '.git', '.venv', '.vscode', '.crush', '.cursor'
    }
    
    root_items = os.listdir('.')
    unexpected_items = []
    
    for item in root_items:
        if os.path.isfile(item) and item not in allowed_files:
            if not item.startswith('.'):  # Allow hidden files
                unexpected_items.append(f"FILE: {item}")
        elif os.path.isdir(item) and item not in allowed_folders:
            unexpected_items.append(f"FOLDER: {item}")
    
    if not unexpected_items:
        print("  ✅ Root directory is clean!")
        print(f"  📁 {len([f for f in root_items if os.path.isfile(f) and f in allowed_files])} essential files")
        print(f"  📂 {len([f for f in root_items if os.path.isdir(f) and f in allowed_folders])} organized folders")
    else:
        print("  ❌ Unexpected items in root directory:")
        for item in unexpected_items:
            print(f"    - {item}")
    
    return len(unexpected_items) == 0, unexpected_items

def check_file_organization() -> Dict[str, int]:
    """Check file counts in organized folders."""
    print("\n📊 Checking File Organization")
    print("=" * 50)
    
    file_counts = {}
    
    # Check documentation
    docs_files = len([f for f in os.listdir('docs') if f.endswith('.md')])
    summaries_files = len([f for f in os.listdir('docs/summaries') if f.endswith('.md')]) if os.path.exists('docs/summaries') else 0
    file_counts['docs'] = docs_files
    file_counts['docs/summaries'] = summaries_files
    print(f"  📚 Documentation: {docs_files} main docs, {summaries_files} summaries")
    
    # Check templates
    template_files = len([f for f in os.listdir('templates') if f.endswith('.html')]) if os.path.exists('templates') else 0
    file_counts['templates'] = template_files
    print(f"  🌐 Templates: {template_files} HTML templates")
    
    # Check static assets
    css_files = len([f for f in os.listdir('static/css') if f.endswith('.css')]) if os.path.exists('static/css') else 0
    file_counts['static/css'] = css_files
    print(f"  🎨 Static CSS: {css_files} stylesheets")
    
    # Check test data
    test_data_files = 0
    if os.path.exists('data/test_data'):
        for root, dirs, files in os.walk('data/test_data'):
            test_data_files += len(files)
    file_counts['data/test_data'] = test_data_files
    print(f"  📊 Test Data: {test_data_files} test files")
    
    # Check configuration
    config_files = len([f for f in os.listdir('config') if not f.startswith('.')]) if os.path.exists('config') else 0
    file_counts['config'] = config_files
    print(f"  ⚙️  Configuration: {config_files} config files")
    
    return file_counts

def check_python_organization() -> Dict[str, int]:
    """Check Python file organization."""
    print("\n🐍 Checking Python File Organization")
    print("=" * 50)
    
    python_counts = {}
    
    # Root Python files
    root_py = len([f for f in os.listdir('.') if f.endswith('.py')])
    python_counts['root'] = root_py
    print(f"  🚀 Root Python files: {root_py}")
    
    # Test files
    test_py = 0
    if os.path.exists('tests'):
        for root, dirs, files in os.walk('tests'):
            test_py += len([f for f in files if f.endswith('.py')])
    python_counts['tests'] = test_py
    print(f"  🧪 Test Python files: {test_py}")
    
    # Debug files
    debug_py = 0
    if os.path.exists('debug'):
        for root, dirs, files in os.walk('debug'):
            debug_py += len([f for f in files if f.endswith('.py')])
    python_counts['debug'] = debug_py
    print(f"  🔧 Debug Python files: {debug_py}")
    
    # Utility files
    util_py = len([f for f in os.listdir('utils') if f.endswith('.py')]) if os.path.exists('utils') else 0
    python_counts['utils'] = util_py
    print(f"  🛠️  Utility Python files: {util_py}")
    
    return python_counts

def check_cleanup_success() -> Dict[str, bool]:
    """Check that cleanup was successful."""
    print("\n🧹 Checking Cleanup Success")
    print("=" * 50)
    
    cleanup_checks = {}
    
    # Check for cache files
    cache_found = False
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            cache_found = True
            break
    cleanup_checks['no_cache'] = not cache_found
    print(f"  {'✅' if not cache_found else '❌'} Python cache files cleaned")
    
    # Check for session files
    session_found = any(os.path.exists(d) for d in ['flask_session', 'financeapp/flask_session'])
    cleanup_checks['no_sessions'] = not session_found
    print(f"  {'✅' if not session_found else '❌'} Flask session files cleaned")
    
    # Check for system files
    ds_store_found = os.path.exists('.DS_Store')
    cleanup_checks['no_ds_store'] = not ds_store_found
    print(f"  {'✅' if not ds_store_found else '❌'} System files (.DS_Store) cleaned")
    
    return cleanup_checks

def main():
    """Run complete organization verification."""
    print("🚀 COMPLETE ORGANIZATION VERIFICATION")
    print("=" * 70)
    print("Verifying comprehensive file reorganization...")
    print()
    
    # Run all checks
    folder_results = check_folder_structure()
    root_clean, unexpected = check_root_directory_cleanliness()
    file_counts = check_file_organization()
    python_counts = check_python_organization()
    cleanup_results = check_cleanup_success()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 COMPLETE ORGANIZATION SUMMARY")
    print("=" * 70)
    
    # Folder structure
    folders_ok = sum(folder_results.values())
    total_folders = len(folder_results)
    print(f"Folder Structure: {folders_ok}/{total_folders} folders exist")
    
    # Root cleanliness
    print(f"Root Directory: {'✅ CLEAN' if root_clean else '❌ NEEDS CLEANUP'}")
    if not root_clean:
        print(f"  Unexpected items: {len(unexpected)}")
    
    # File organization
    total_organized = sum(file_counts.values())
    print(f"File Organization: {total_organized} files organized")
    print(f"  • {file_counts.get('docs', 0)} + {file_counts.get('docs/summaries', 0)} documentation files")
    print(f"  • {file_counts.get('templates', 0)} HTML templates")
    print(f"  • {file_counts.get('static/css', 0)} CSS files")
    print(f"  • {file_counts.get('data/test_data', 0)} test data files")
    print(f"  • {file_counts.get('config', 0)} configuration files")
    
    # Python organization
    total_python = sum(python_counts.values())
    print(f"Python Organization: {total_python} Python files organized")
    print(f"  • {python_counts.get('root', 0)} root files (should be ~3)")
    print(f"  • {python_counts.get('tests', 0)} test files")
    print(f"  • {python_counts.get('debug', 0)} debug files")
    print(f"  • {python_counts.get('utils', 0)} utility files")
    
    # Cleanup success
    cleanup_ok = sum(cleanup_results.values())
    total_cleanup = len(cleanup_results)
    print(f"Cleanup Success: {cleanup_ok}/{total_cleanup} cleanup tasks completed")
    
    # Overall assessment
    overall_success = (
        folders_ok == total_folders and
        root_clean and
        cleanup_ok == total_cleanup and
        python_counts.get('root', 0) <= 5  # Should have very few root Python files
    )
    
    print(f"\n🎯 OVERALL RESULT:")
    if overall_success:
        print("🎉 COMPLETE ORGANIZATION SUCCESS!")
        print("   ✅ All folders created correctly")
        print("   ✅ Root directory is clean")
        print("   ✅ Files organized systematically")
        print("   ✅ Python files properly structured")
        print("   ✅ Cleanup completed successfully")
        print("\n🚀 Your project is now MAXIMALLY ORGANIZED!")
    else:
        print("⚠️  Some organization issues detected:")
        if folders_ok < total_folders:
            print(f"   • {total_folders - folders_ok} folders missing")
        if not root_clean:
            print(f"   • Root directory needs cleanup")
        if cleanup_ok < total_cleanup:
            print(f"   • {total_cleanup - cleanup_ok} cleanup tasks incomplete")
        print("\n🔧 Review the output above for specific issues.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
