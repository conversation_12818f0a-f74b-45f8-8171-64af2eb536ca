#!/usr/bin/env python3
"""
Identify duplicate and obsolete test files for cleanup.
"""

import os
import re
from collections import defaultdict
from typing import Dict, List, Set

def analyze_test_files() -> Dict[str, List[str]]:
    """Analyze test files and group by likely functionality."""
    
    categories = defaultdict(list)
    
    # Walk through test directories
    for root, dirs, files in os.walk('tests'):
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                
                # Categorize by filename patterns
                if 'currency' in file:
                    categories['currency'].append(filepath)
                elif 'user' in file:
                    categories['user_scenarios'].append(filepath)
                elif 'final' in file:
                    categories['final_tests'].append(filepath)
                elif 'complete' in file:
                    categories['complete_tests'].append(filepath)
                elif 'comprehensive' in file:
                    categories['comprehensive_tests'].append(filepath)
                elif 'enhanced' in file:
                    categories['enhanced_tests'].append(filepath)
                elif 'improved' in file:
                    categories['improved_tests'].append(filepath)
                elif 'fix' in file:
                    categories['fix_tests'].append(filepath)
                elif 'ocr' in file:
                    categories['ocr_tests'].append(filepath)
                elif 'gemini' in file:
                    categories['gemini_tests'].append(filepath)
                elif 'web' in file:
                    categories['web_tests'].append(filepath)
                elif 'delete' in file:
                    categories['delete_tests'].append(filepath)
                elif 'simple' in file:
                    categories['simple_tests'].append(filepath)
                else:
                    categories['other'].append(filepath)
    
    return dict(categories)

def identify_likely_duplicates() -> List[str]:
    """Identify files that are likely duplicates or obsolete."""
    
    likely_duplicates = []
    
    # Files with "old", "backup", "temp" in name
    for root, dirs, files in os.walk('tests'):
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                if any(word in file.lower() for word in ['old', 'backup', 'temp', 'deprecated']):
                    likely_duplicates.append(filepath)
    
    # Files that are very similar in name
    similar_patterns = [
        ('test_user_scenario.py', 'test_user_scenario_fixed.py'),
        ('test_currency_bug.py', 'test_currency_bug_fix.py'),
        ('test_portfolio_import.py', 'test_portfolio_import_fix.py'),
    ]
    
    for pattern1, pattern2 in similar_patterns:
        files1 = []
        files2 = []
        
        for root, dirs, files in os.walk('tests'):
            for file in files:
                if pattern1 in file:
                    files1.append(os.path.join(root, file))
                elif pattern2 in file:
                    files2.append(os.path.join(root, file))
        
        # If we have both versions, the non-"fixed" version might be obsolete
        if files1 and files2:
            likely_duplicates.extend(files1)
    
    return likely_duplicates

def get_file_size(filepath: str) -> int:
    """Get file size in bytes."""
    try:
        return os.path.getsize(filepath)
    except:
        return 0

def analyze_file_content_similarity(files: List[str]) -> List[str]:
    """Analyze files for content similarity (basic check)."""
    
    small_files = []  # Files under 1KB might be stubs or minimal tests
    
    for filepath in files:
        size = get_file_size(filepath)
        if size < 1024:  # Less than 1KB
            small_files.append(filepath)
    
    return small_files

def main():
    """Main analysis function."""
    print("🔍 ANALYZING TEST FILES FOR CLEANUP")
    print("=" * 60)
    
    # Analyze categories
    categories = analyze_test_files()
    
    print("📊 FILE CATEGORIES:")
    for category, files in categories.items():
        print(f"  {category}: {len(files)} files")
        if len(files) > 5:  # Show details for large categories
            print(f"    (Many files - potential for consolidation)")
    
    print("\n🔍 POTENTIAL DUPLICATES:")
    duplicates = identify_likely_duplicates()
    for dup in duplicates:
        size = get_file_size(dup)
        print(f"  {dup} ({size} bytes)")
    
    print("\n📏 SMALL FILES (potential stubs):")
    all_test_files = []
    for root, dirs, files in os.walk('tests'):
        for file in files:
            if file.endswith('.py'):
                all_test_files.append(os.path.join(root, file))
    
    small_files = analyze_file_content_similarity(all_test_files)
    for small_file in small_files[:10]:  # Show first 10
        size = get_file_size(small_file)
        print(f"  {small_file} ({size} bytes)")
    
    if len(small_files) > 10:
        print(f"  ... and {len(small_files) - 10} more small files")
    
    print("\n💡 CLEANUP RECOMMENDATIONS:")
    print("=" * 60)
    
    # User scenario files
    user_files = categories.get('user_scenarios', [])
    if len(user_files) > 3:
        print(f"📁 User Scenario Tests ({len(user_files)} files):")
        print("   Consider consolidating into 1-2 comprehensive user tests")
        for f in user_files[:5]:
            print(f"   - {f}")
        if len(user_files) > 5:
            print(f"   ... and {len(user_files) - 5} more")
    
    # Final/Complete/Comprehensive tests
    final_files = categories.get('final_tests', []) + categories.get('complete_tests', []) + categories.get('comprehensive_tests', [])
    if len(final_files) > 2:
        print(f"\n📁 Final/Complete Tests ({len(final_files)} files):")
        print("   Multiple 'final' tests suggest iterative development")
        print("   Keep the most recent and comprehensive one")
        for f in final_files:
            print(f"   - {f}")
    
    # Currency tests
    currency_files = categories.get('currency_tests', [])
    if len(currency_files) > 5:
        print(f"\n📁 Currency Tests ({len(currency_files)} files):")
        print("   Many currency tests - consider grouping by currency type")
        print("   (DKK, USD, EUR, JPY, etc.)")
    
    # Fix tests
    fix_files = categories.get('fix_tests', [])
    if len(fix_files) > 3:
        print(f"\n📁 Fix Tests ({len(fix_files)} files):")
        print("   If issues are resolved, these tests might be obsolete")
        print("   Consider archiving or removing old fix tests")
    
    print(f"\n📈 SUMMARY:")
    print(f"   Total test files: {len(all_test_files)}")
    print(f"   Potential duplicates: {len(duplicates)}")
    print(f"   Small files (< 1KB): {len(small_files)}")
    print(f"   Categories with many files: {len([c for c, f in categories.items() if len(f) > 5])}")
    
    print(f"\n🎯 NEXT STEPS:")
    print("   1. Review files marked as potential duplicates")
    print("   2. Consolidate user scenario tests")
    print("   3. Keep only the most recent 'final' test")
    print("   4. Archive or remove resolved fix tests")
    print("   5. Group currency tests by currency type")

if __name__ == "__main__":
    main()
