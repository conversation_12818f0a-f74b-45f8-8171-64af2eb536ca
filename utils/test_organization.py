#!/usr/bin/env python3
"""
Test script to verify the reorganized project structure works correctly.
"""

import sys
import os
import importlib.util

def test_main_imports():
    """Test that main application files can be imported."""
    print("🧪 Testing Main Application Imports")
    print("=" * 50)
    
    try:
        import app
        print("✅ app.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import app.py: {e}")
        return False
    
    try:
        import portfolio_import
        print("✅ portfolio_import.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import portfolio_import.py: {e}")
        return False
    
    try:
        import financial_reports
        print("✅ financial_reports.py imported successfully")
    except Exception as e:
        print(f"❌ Failed to import financial_reports.py: {e}")
        return False
    
    return True

def test_organized_structure():
    """Test that the organized folder structure is correct."""
    print("\n🗂️  Testing Organized Structure")
    print("=" * 50)
    
    expected_folders = [
        'tests',
        'tests/currency',
        'tests/portfolio_import', 
        'tests/ocr',
        'tests/web_integration',
        'debug',
        'debug/currency',
        'debug/gemini',
        'debug/ocr',
        'debug/general',
        'demos',
        'utils',
        'archive'
    ]
    
    all_exist = True
    for folder in expected_folders:
        if os.path.exists(folder):
            print(f"✅ {folder}")
        else:
            print(f"❌ {folder} - Missing")
            all_exist = False
    
    return all_exist

def test_consolidated_tools():
    """Test that consolidated tools work."""
    print("\n🛠️  Testing Consolidated Tools")
    print("=" * 50)
    
    # Test debug suite
    debug_suite_path = "debug/portfolio_debug_suite.py"
    if os.path.exists(debug_suite_path):
        print(f"✅ {debug_suite_path} exists")
        
        # Try to import it
        try:
            spec = importlib.util.spec_from_file_location("debug_suite", debug_suite_path)
            debug_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(debug_module)
            print("✅ Debug suite can be imported")
        except Exception as e:
            print(f"❌ Debug suite import failed: {e}")
            return False
    else:
        print(f"❌ {debug_suite_path} missing")
        return False
    
    # Test test suite
    test_suite_path = "tests/portfolio_test_suite.py"
    if os.path.exists(test_suite_path):
        print(f"✅ {test_suite_path} exists")
        
        # Try to import it
        try:
            spec = importlib.util.spec_from_file_location("test_suite", test_suite_path)
            test_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(test_module)
            print("✅ Test suite can be imported")
        except Exception as e:
            print(f"❌ Test suite import failed: {e}")
            return False
    else:
        print(f"❌ {test_suite_path} missing")
        return False
    
    return True

def count_files():
    """Count files in each category."""
    print("\n📊 File Count Summary")
    print("=" * 50)
    
    categories = {
        'Root Python files': len([f for f in os.listdir('.') if f.endswith('.py')]),
        'Test files': len([f for root, dirs, files in os.walk('tests') for f in files if f.endswith('.py')]),
        'Debug files': len([f for root, dirs, files in os.walk('debug') for f in files if f.endswith('.py')]),
        'Demo files': len([f for root, dirs, files in os.walk('demos') for f in files if f.endswith('.py')]),
        'Util files': len([f for root, dirs, files in os.walk('utils') for f in files if f.endswith('.py')]),
        'Archive files': len([f for root, dirs, files in os.walk('archive') for f in files if f.endswith('.py')])
    }
    
    total_organized = 0
    for category, count in categories.items():
        print(f"  {category}: {count}")
        if category != 'Root Python files':
            total_organized += count
    
    print(f"\n  Total organized files: {total_organized}")
    print(f"  Root directory cleanup: {131 - categories['Root Python files']} files moved")
    
    return categories

def test_key_functionality():
    """Test that key portfolio import functionality still works."""
    print("\n⚙️  Testing Key Functionality")
    print("=" * 50)
    
    try:
        from portfolio_import import PortfolioImportService
        
        # Create service instance
        service = PortfolioImportService("test_key", "test_key")
        print("✅ PortfolioImportService can be instantiated")
        
        # Test basic currency detection
        test_text = "Market Value (DKK) 12,345.67"
        detected_currency = service.ai_extractor._detect_primary_currency(test_text)
        print(f"✅ Currency detection works: '{test_text}' → {detected_currency}")
        
        return True
        
    except Exception as e:
        print(f"❌ Key functionality test failed: {e}")
        return False

def main():
    """Run all organization tests."""
    print("🚀 TESTING REORGANIZED PROJECT STRUCTURE")
    print("=" * 70)
    print("Verifying that the file reorganization was successful")
    print()
    
    # Run tests
    tests = [
        ("Main Imports", test_main_imports),
        ("Folder Structure", test_organized_structure),
        ("Consolidated Tools", test_consolidated_tools),
        ("Key Functionality", test_key_functionality)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Count files
    file_counts = count_files()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 REORGANIZATION TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\nFile organization:")
    print(f"  • Moved {131 - file_counts['Root Python files']} files from root directory")
    print(f"  • Organized into {file_counts['Test files']} tests, {file_counts['Debug files']} debug scripts")
    print(f"  • Created consolidated tools and documentation")
    
    if all(results.values()):
        print(f"\n🎉 SUCCESS! Project reorganization completed successfully!")
        print(f"   • Clean root directory with only 3 main Python files")
        print(f"   • Well-organized folder structure")
        print(f"   • Working consolidated tools")
        print(f"   • All imports and functionality preserved")
    else:
        print(f"\n⚠️  Some issues detected. Check the test output above.")
    
    return all(results.values())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
