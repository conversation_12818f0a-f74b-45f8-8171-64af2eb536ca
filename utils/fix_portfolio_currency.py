#!/usr/bin/env python3
"""
Script to fix the portfolio currency issue by clearing corrupted data
and providing instructions for re-import.
"""

import sys
import os

# Add the current directory to the path so we can import from app.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_portfolio_currency():
    """Fix the portfolio currency issue."""
    print("🔧 PORTFOLIO CURRENCY FIX")
    print("=" * 50)
    
    try:
        from app import app, reset_portfolio
        
        with app.test_request_context():
            from flask import session
            
            # Clear the corrupted portfolio data
            print("1. Clearing corrupted portfolio data...")
            session['portfolio_data'] = []
            session['cash_position'] = 0.0
            session.modified = True
            print("   ✅ Portfolio cleared")
            
            # Set the correct portfolio currency
            print("2. Setting portfolio currency to DKK...")
            session['portfolio_currency'] = 'DKK'
            session.modified = True
            print("   ✅ Portfolio currency set to DKK")
            
            print("\n🎯 NEXT STEPS:")
            print("1. Go to your portfolio page")
            print("2. Use the 'Import Portfolio' feature")
            print("3. Upload your portfolio image/spreadsheet again")
            print("4. The amounts will now be preserved in DKK correctly")
            
            print("\n✅ Portfolio reset complete!")
            print("Your next import will preserve DKK amounts correctly.")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_portfolio_currency()