# Portfolio Import Project - File Organization

This document describes the reorganized file structure for the portfolio import project.

## 📁 Project Structure

### Root Directory (Main Application)
- `app.py` - Main Flask application (1.1MB - large monolithic file)
- `portfolio_import.py` - Core portfolio import service (355KB - comprehensive service)
- `financial_reports.py` - Financial reports blueprint

### 🧪 Tests Directory (`tests/`)
**101 test files organized by category:**

#### `tests/currency/` (25 files)
Currency-related tests including:
- DKK, CHF, JPY, Danish currency tests
- Currency detection and conversion tests
- Mixed currency scenario tests

#### `tests/portfolio_import/` (70+ files)
Main portfolio import functionality tests:
- AI extraction tests
- Image processing tests
- User scenario tests
- Comprehensive integration tests

#### `tests/ocr/` (3 files)
OCR processing tests:
- EasyOCR, Tesseract, OCR Space tests
- OCR fallback mechanism tests

#### `tests/web_integration/` (3 files)
Web interface integration tests:
- Frontend integration tests
- API endpoint tests

### 🔧 Debug Directory (`debug/`)
**15 debug scripts organized by category:**

#### `debug/currency/` (7 files)
Currency debugging tools:
- Currency detection debugging
- Currency pattern analysis
- Session currency debugging

#### `debug/gemini/` (1 file)
Gemini AI debugging:
- AI extraction debugging

#### `debug/ocr/` (1 file)
OCR debugging tools:
- OCR processing debugging

#### `debug/general/` (6 files)
General debugging tools:
- Portfolio price pattern debugging
- Ticker extraction debugging
- Spreadsheet processing debugging

#### `debug/portfolio_debug_suite.py` (NEW)
**Consolidated debug suite** combining the most useful debugging functionality.

### 🎯 Demos Directory (`demos/`)
**2 demo files:**
- `demo_fixed_portfolio_import.py` - Fixed import demonstration
- `demo_your_portfolio.py` - Portfolio demo

### 🛠️ Utils Directory (`utils/`)
**9 utility files:**
- Currency bug detection tools
- Quick test utilities
- Verification scripts
- Fix utilities

### 📦 Archive Directory (`archive/`)
**1 obsolete file:**
- `app_clean.py` - Empty file (archived)

## 🚀 Quick Start

### Run Main Test Suite
```bash
python tests/portfolio_test_suite.py
```

### Run Debug Suite
```bash
python debug/portfolio_debug_suite.py
```

### Run Specific Category Tests
```bash
# Currency tests
python -m pytest tests/currency/ -v

# Portfolio import tests  
python -m pytest tests/portfolio_import/ -v

# OCR tests
python -m pytest tests/ocr/ -v
```

## 📊 File Statistics

- **Before**: 131 Python files scattered in root directory
- **After**: 3 main files in root + 128 organized files in categorized folders
- **Reduction**: 97% fewer files in root directory
- **Organization**: Files grouped by functionality and purpose

## 🎯 Key Improvements

1. **Clean Root Directory**: Only essential application files remain
2. **Logical Organization**: Files grouped by functionality (tests, debug, demos, utils)
3. **Easy Navigation**: Clear folder structure with descriptive names
4. **Consolidated Tools**: New unified test and debug suites
5. **Better Maintainability**: Related files are grouped together

## 🔍 Finding Files

### Looking for a specific test?
- **Currency issues**: Check `tests/currency/`
- **Import problems**: Check `tests/portfolio_import/`
- **OCR issues**: Check `tests/ocr/`
- **Web problems**: Check `tests/web_integration/`

### Need to debug something?
- **General debugging**: Use `debug/portfolio_debug_suite.py`
- **Specific issues**: Check appropriate `debug/` subfolder

### Want to see a demo?
- Check `demos/` folder for working examples

### Need utility scripts?
- Check `utils/` folder for helper scripts

## 🧹 Cleanup Recommendations

### Files that can likely be deleted:
Many of the test files appear to be duplicates or variations testing the same functionality. Consider reviewing and removing:

1. **Duplicate currency tests** - Many test the same DKK/currency detection
2. **Redundant user scenario tests** - Multiple files testing similar user workflows  
3. **Old debug scripts** - Now replaced by consolidated debug suite
4. **Obsolete fix scripts** - Issues that have been resolved

### Consolidation opportunities:
1. **Merge similar test files** - Combine tests with similar purposes
2. **Create test categories** - Group related tests into test classes
3. **Standardize naming** - Use consistent naming conventions

## 📝 Next Steps

1. **Review test files** - Identify and remove duplicates
2. **Update imports** - Fix any broken import statements
3. **Create test runners** - Add more consolidated test suites
4. **Documentation** - Add docstrings to key files
5. **CI/CD Integration** - Set up automated testing with the new structure
