# CRUSH.md - Developer Guidelines

## Project Structure
- `app/` - Main Python application code
- `static/` - Static assets (JS, CSS, images)
- `templates/` - HTML templates
- `tests/` - Test files (various test_*.py files)

## Build/Lint/Test Commands
- Install dependencies: `pip install -r requirements.txt`
- Run all tests: `python -m pytest`
- Run single test: `python -m pytest test_file.py::test_function_name -v`
- Run app: `python app.py` or `waitress-serve --port=8000 app:app`

## Code Style Guidelines
- Python: PEP 8 compliant
- Imports: Standard library, third-party, local (separated by blank lines)
- Naming: snake_case for functions/variables, PascalCase for classes
- Types: Use type hints for function parameters and return values
- Error handling: Prefer specific exceptions over generic except clauses
- Comments: Explain "why" not "what"

## Additional Notes
- Uses Flask framework
- Heavy use of AI/LLM integrations (OpenAI, Anthropic, Google Gemini)
- Financial data processing with yfinance, pandas
- Interactive visualizations with Plotly
- No specific Cursor or Copilot rules beyond standard Python practices