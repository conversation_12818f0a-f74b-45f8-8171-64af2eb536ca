<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drag & Drop Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .upload-area {
            border: 2px dashed #666;
            border-radius: 12px;
            padding: 48px 24px;
            margin: 24px 0;
            text-align: center;
            cursor: pointer;
            background: #2a2a2a;
            transition: all 0.3s ease;
            user-select: none;
        }
        
        .upload-area:hover,
        .upload-area.dragover {
            border-color: #f59e0b;
            background: rgba(245, 158, 11, 0.05);
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);
        }

        .upload-area.dragover {
            border-style: solid;
            border-width: 3px;
            background: rgba(245, 158, 11, 0.1);
            animation: dragPulse 1s ease-in-out infinite alternate;
        }

        @keyframes dragPulse {
            0% { transform: scale(1.02); }
            100% { transform: scale(1.05); }
        }
        
        .file-input {
            display: none;
        }
        
        .upload-button {
            background: #f59e0b;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 16px;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            background: #2a2a2a;
            border-radius: 8px;
            min-height: 100px;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            background: #333;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <h1>🧪 Drag & Drop Test</h1>
    <p>This tests the basic drag and drop functionality. Try dragging an image file onto the area below.</p>
    
    <div class="upload-area" id="uploadArea">
        <div style="font-size: 2rem; margin-bottom: 10px;">📁</div>
        <div>Drop your image here or click to browse</div>
        <div style="font-size: 0.9rem; color: #999; margin-top: 8px;">Supports JPEG, PNG, GIF</div>
        <input type="file" id="fileInput" class="file-input" accept="image/*">
        <button type="button" class="upload-button" id="uploadButton">
            Choose Image
        </button>
    </div>
    
    <div class="status" id="status">
        <strong>Event Log:</strong>
        <div id="logContainer"></div>
    </div>

    <script>
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.style.borderLeft = type === 'error' ? '3px solid #ef4444' : 
                                       type === 'success' ? '3px solid #10b981' : '3px solid #3b82f6';
            logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        // Initialize when DOM loads
        document.addEventListener('DOMContentLoaded', () => {
            addLog('🚀 Initializing drag & drop test...');
            
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            const uploadButton = document.getElementById('uploadButton');
            
            if (!fileInput || !uploadArea || !uploadButton) {
                addLog('❌ Missing required elements', 'error');
                return;
            }
            
            addLog('✅ All elements found, setting up event listeners...');
            
            // File input change handler
            fileInput.addEventListener('change', (e) => {
                const files = e.target.files;
                addLog(`📁 File input changed: ${files.length} files selected`);
                if (files.length > 0) {
                    const file = files[0];
                    addLog(`📄 File: ${file.name} (${file.type}, ${file.size} bytes)`, 'success');
                }
            });
            
            // Click handlers
            uploadArea.addEventListener('click', (e) => {
                addLog('🖱️ Upload area clicked');
                e.preventDefault();
                e.stopPropagation();
                
                // Visual feedback
                uploadArea.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    uploadArea.style.transform = '';
                }, 150);
                
                fileInput.click();
                addLog('🔄 File dialog triggered');
            });
            
            uploadButton.addEventListener('click', (e) => {
                addLog('🔘 Upload button clicked');
                e.preventDefault();
                e.stopPropagation();
                fileInput.click();
            });
            
            // Drag and drop handlers
            uploadArea.addEventListener('dragover', (e) => {
                addLog('🔄 Drag over');
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragenter', (e) => {
                addLog('🔄 Drag enter');
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', (e) => {
                addLog('🔄 Drag leave');
                e.preventDefault();
                e.stopPropagation();
                if (!uploadArea.contains(e.relatedTarget)) {
                    uploadArea.classList.remove('dragover');
                    addLog('🔄 Removed dragover class');
                }
            });
            
            uploadArea.addEventListener('drop', (e) => {
                addLog('📂 DROP EVENT TRIGGERED!');
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                addLog(`📁 Files dropped: ${files.length}`);
                
                if (files.length > 0) {
                    const file = files[0];
                    addLog(`📄 File details: ${file.name} (${file.type}, ${file.size} bytes)`);
                    
                    if (file.type.startsWith('image/')) {
                        addLog('✅ Valid image file!', 'success');
                    } else {
                        addLog('❌ Invalid file type', 'error');
                    }
                } else {
                    addLog('❌ No files in drop', 'error');
                }
            });
            
            // Global drag prevention
            document.addEventListener('dragover', (e) => {
                e.preventDefault();
            });
            
            document.addEventListener('drop', (e) => {
                e.preventDefault();
            });
            
            addLog('✅ All event listeners attached');
            addLog('🎯 Ready for testing! Try dragging an image file here.');
        });
    </script>
</body>
</html>