<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Bug Detector</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        textarea {
            width: 100%;
            height: 150px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        select, button {
            padding: 10px 15px;
            margin: 10px 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .bug-detected {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            font-weight: bold;
        }
        .currency-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #004085;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Currency Bug Detector</h1>
        <p>This tool tests whether the portfolio import system preserves the original currency detected from source data or incorrectly converts it to the user's portfolio currency preference.</p>
        
        <div class="test-section">
            <h3>Test Portfolio Data</h3>
            <textarea id="testText" placeholder="Enter portfolio text with currency symbols...">Portfolio Overview:

Novo Nordisk: 1.195,00 kr invested, buy price: 850,00 kr
Vestas: 2.500,00 kr invested, buy price: 125,50 kr  
Carlsberg: 1.800,00 kr invested, buy price: 900,00 kr</textarea>
            
            <div>
                <label for="userCurrency">User's Portfolio Currency Preference:</label>
                <select id="userCurrency">
                    <option value="USD">USD (US Dollar)</option>
                    <option value="DKK">DKK (Danish Krone)</option>
                    <option value="EUR">EUR (Euro)</option>
                    <option value="GBP">GBP (British Pound)</option>
                </select>
                
                <button onclick="testCurrencyDetection()">🧪 Test Currency Detection</button>
                <button onclick="clearResults()">🗑️ Clear Results</button>
            </div>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        async function testCurrencyDetection() {
            const testText = document.getElementById('testText').value;
            const userCurrency = document.getElementById('userCurrency').value;
            const resultsDiv = document.getElementById('results');
            
            if (!testText.trim()) {
                showResult('error', 'Please enter some test text');
                return;
            }
            
            showResult('info', 'Testing currency detection...');
            
            try {
                const response = await fetch('/api/import/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: testText,
                        user_currency: userCurrency
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    analyzeResult(result, userCurrency, testText);
                } else {
                    showResult('error', `Test failed: ${result.error}`);
                }
                
            } catch (error) {
                showResult('error', `Network error: ${error.message}`);
            }
        }
        
        function analyzeResult(result, userCurrency, testText) {
            const resultsDiv = document.getElementById('results');
            
            let output = `🎯 Test Results:\n`;
            output += `User Portfolio Currency: ${userCurrency}\n`;
            output += `Detected Currency: ${result.detected_currency || 'Not detected'}\n\n`;
            
            const portfolioData = result.ai_result?.portfolio_data || [];
            output += `📊 Portfolio Entries Found: ${portfolioData.length}\n\n`;
            
            let bugDetected = false;
            
            portfolioData.forEach((entry, index) => {
                output += `${index + 1}. ${entry.ticker || 'Unknown'}:\n`;
                output += `   💰 Amount Invested: ${entry.amount_invested || 'N/A'} ${entry.amount_invested_currency || 'N/A'}\n`;
                output += `   💵 Buy Price: ${entry.buy_price || 'N/A'} ${entry.buy_price_currency || 'N/A'}\n`;
                
                // Check for the bug
                if (entry.amount_invested_currency === 'USD' && testText.includes('kr')) {
                    output += `   🚨 BUG DETECTED: Currency shows USD but source has 'kr'!\n`;
                    bugDetected = true;
                } else if (entry.amount_invested_currency === 'DKK' || entry.amount_invested_currency === 'kr') {
                    output += `   ✅ Currency correctly preserved from source\n`;
                } else {
                    output += `   ⚠️  Unexpected currency: ${entry.amount_invested_currency}\n`;
                }
                output += `\n`;
            });
            
            output += `\n🎯 Analysis:\n`;
            if (bugDetected) {
                output += `❌ BUG CONFIRMED: The system is converting currencies to user preference instead of preserving original detected currency.\n`;
                showResult('bug-detected', output);
            } else if (portfolioData.length > 0) {
                output += `✅ WORKING CORRECTLY: The system is preserving the original detected currency from source data.\n`;
                showResult('success', output);
            } else {
                output += `⚠️  NO DATA: No portfolio entries were extracted. Check the AI extraction logic.\n`;
                showResult('error', output);
            }
            
            // Show raw result for debugging
            output += `\n🔍 Raw AI Result:\n${JSON.stringify(result.ai_result, null, 2)}`;
            showResult(bugDetected ? 'bug-detected' : 'success', output);
        }
        
        function showResult(type, message) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
