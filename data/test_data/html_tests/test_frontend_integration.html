<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Selection Modal Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        
        /* Include the modal styles from the main template */
        .modal {
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            display: none;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border: none;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            padding: 24px 32px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close {
            color: #666;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #333;
        }

        .modal-body {
            padding: 32px;
        }

        .currency-question {
            margin-bottom: 24px;
        }

        .currency-question p {
            color: #333;
            font-size: 1.1rem;
            line-height: 1.6;
            margin: 0;
        }

        .currency-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .currency-option {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #f8f9fa;
        }

        .currency-option:hover {
            border-color: #007bff;
            background: #fff;
            transform: translateY(-2px);
        }

        .currency-option.selected {
            border-color: #007bff;
            background: linear-gradient(135deg, rgba(0,123,255,0.1), #fff);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .currency-flag {
            font-size: 2rem;
            margin-right: 16px;
        }

        .currency-info {
            flex: 1;
        }

        .currency-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .currency-code {
            color: #666;
            font-size: 0.9rem;
        }

        .detected-info {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .detected-info p {
            margin: 8px 0;
            color: #666;
            font-size: 0.9rem;
        }

        .detected-info strong {
            color: #333;
        }

        .modal-footer {
            padding: 24px 32px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .modal-footer .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .modal-footer .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Currency Selection Modal Test</h1>
        <p>This page tests the currency selection modal functionality that will be shown when the AI detects non-USD currencies in portfolio imports.</p>
        
        <div class="test-buttons">
            <button class="test-button" onclick="testDKKCurrency()">Test DKK Currency Detection</button>
            <button class="test-button" onclick="testMixedCurrencies()">Test Mixed Currencies</button>
            <button class="test-button" onclick="testEURCurrency()">Test EUR Currency</button>
        </div>
        
        <div id="testResults" class="test-result" style="display: none;">
            <h3>Test Results:</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <!-- Currency Selection Modal -->
    <div id="currencySelectionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🌍 Currency Selection Required</h3>
                <span class="close" onclick="closeCurrencyModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="currencyQuestion" class="currency-question">
                    <p>I detected multiple currencies or was uncertain about the currency in your portfolio. Please select your preferred display currency:</p>
                </div>
                
                <div class="currency-options">
                    <div class="currency-grid">
                        <div class="currency-option" data-currency="USD" onclick="selectCurrency('USD')">
                            <div class="currency-flag">🇺🇸</div>
                            <div class="currency-info">
                                <div class="currency-name">US Dollar</div>
                                <div class="currency-code">USD ($)</div>
                            </div>
                        </div>
                        <div class="currency-option" data-currency="EUR" onclick="selectCurrency('EUR')">
                            <div class="currency-flag">🇪🇺</div>
                            <div class="currency-info">
                                <div class="currency-name">Euro</div>
                                <div class="currency-code">EUR (€)</div>
                            </div>
                        </div>
                        <div class="currency-option" data-currency="DKK" onclick="selectCurrency('DKK')">
                            <div class="currency-flag">🇩🇰</div>
                            <div class="currency-info">
                                <div class="currency-name">Danish Krone</div>
                                <div class="currency-code">DKK (kr)</div>
                            </div>
                        </div>
                        <div class="currency-option" data-currency="GBP" onclick="selectCurrency('GBP')">
                            <div class="currency-flag">🇬🇧</div>
                            <div class="currency-info">
                                <div class="currency-name">British Pound</div>
                                <div class="currency-code">GBP (£)</div>
                            </div>
                        </div>
                        <div class="currency-option" data-currency="SEK" onclick="selectCurrency('SEK')">
                            <div class="currency-flag">🇸🇪</div>
                            <div class="currency-info">
                                <div class="currency-name">Swedish Krona</div>
                                <div class="currency-code">SEK (kr)</div>
                            </div>
                        </div>
                        <div class="currency-option" data-currency="NOK" onclick="selectCurrency('NOK')">
                            <div class="currency-flag">🇳🇴</div>
                            <div class="currency-info">
                                <div class="currency-name">Norwegian Krone</div>
                                <div class="currency-code">NOK (kr)</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="detected-info">
                    <p><strong>Detected currencies:</strong> <span id="detectedCurrencies">DKK</span></p>
                    <p><strong>AI Analysis:</strong> <span id="currencyAnalysis">Currency detected from portfolio data patterns</span></p>
                </div>
            </div>
            <div class="modal-footer">
                <button id="confirmCurrencyButton" class="btn btn-primary" disabled onclick="confirmCurrencySelection()">
                    ✓ Confirm Selection
                </button>
                <button class="btn btn-secondary" onclick="closeCurrencyModal()">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <script>
        let selectedCurrency = null;

        function testDKKCurrency() {
            showTestResult('DKK Currency Test', 'Testing Danish Krone detection...');
            
            // Update modal content for DKK test
            document.getElementById('currencyQuestion').innerHTML = 
                '<p>I detected <strong>Danish Krone (DKK)</strong> in your portfolio. Please confirm this is correct or select your preferred display currency:</p>';
            document.getElementById('detectedCurrencies').textContent = 'DKK';
            document.getElementById('currencyAnalysis').textContent = 'DKK: 4 fields (100%) - detected from "kr" symbols and Danish text patterns';
            
            // Pre-select DKK
            selectCurrency('DKK');
            
            // Show modal
            document.getElementById('currencySelectionModal').style.display = 'block';
        }

        function testMixedCurrencies() {
            showTestResult('Mixed Currencies Test', 'Testing mixed USD/DKK detection...');
            
            // Update modal content for mixed currencies test
            document.getElementById('currencyQuestion').innerHTML = 
                '<p>I detected <strong>mixed currencies</strong> in your portfolio: buy prices in USD but current values in DKK. Please select your preferred display currency:</p>';
            document.getElementById('detectedCurrencies').textContent = 'USD, DKK';
            document.getElementById('currencyAnalysis').textContent = 'USD: 3 fields (43%), DKK: 4 fields (57%) - mixed currency portfolio detected';
            
            // Pre-select DKK as it's more common
            selectCurrency('DKK');
            
            // Show modal
            document.getElementById('currencySelectionModal').style.display = 'block';
        }

        function testEURCurrency() {
            showTestResult('EUR Currency Test', 'Testing Euro detection...');
            
            // Update modal content for EUR test
            document.getElementById('currencyQuestion').innerHTML = 
                '<p>I detected <strong>Euro (EUR)</strong> in your portfolio. Please confirm this is correct or select your preferred display currency:</p>';
            document.getElementById('detectedCurrencies').textContent = 'EUR';
            document.getElementById('currencyAnalysis').textContent = 'EUR: 5 fields (100%) - detected from "€" symbols and European formatting';
            
            // Pre-select EUR
            selectCurrency('EUR');
            
            // Show modal
            document.getElementById('currencySelectionModal').style.display = 'block';
        }

        function selectCurrency(currency) {
            // Remove previous selections
            document.querySelectorAll('.currency-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Select the new option
            const selectedOption = document.querySelector(`[data-currency="${currency}"]`);
            if (selectedOption) {
                selectedOption.classList.add('selected');
                selectedCurrency = currency;
                
                // Enable confirm button
                document.getElementById('confirmCurrencyButton').disabled = false;
                
                console.log('Selected currency:', currency);
            }
        }

        function confirmCurrencySelection() {
            if (!selectedCurrency) {
                alert('Please select a currency');
                return;
            }

            // Close modal
            closeCurrencyModal();
            
            // Show confirmation
            showTestResult('Currency Confirmed', `You selected: ${getCurrencyDisplayName(selectedCurrency)}`);
            
            console.log('Currency selection confirmed:', selectedCurrency);
        }

        function closeCurrencyModal() {
            document.getElementById('currencySelectionModal').style.display = 'none';
        }

        function getCurrencyDisplayName(currency) {
            const names = {
                'USD': '🇺🇸 US Dollar ($)',
                'EUR': '🇪🇺 Euro (€)',
                'DKK': '🇩🇰 Danish Krone (kr)',
                'GBP': '🇬🇧 British Pound (£)',
                'SEK': '🇸🇪 Swedish Krona (kr)',
                'NOK': '🇳🇴 Norwegian Krone (kr)'
            };
            return names[currency] || currency;
        }

        function showTestResult(title, message) {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('resultContent');
            
            contentDiv.innerHTML = `<strong>${title}:</strong> ${message}`;
            resultsDiv.style.display = 'block';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('currencySelectionModal');
            if (event.target === modal) {
                closeCurrencyModal();
            }
        }
    </script>
</body>
</html>