<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.danger:hover {
            background-color: #c82333;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        select {
            padding: 5px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 Currency Debug Tool</h1>
    <p>Use this tool to debug currency detection issues with your portfolio images.</p>

    <!-- Clear Portfolio Section -->
    <div class="container">
        <h2>1. Clear Portfolio Data</h2>
        <p>Clear all existing portfolio data to start fresh.</p>
        <button class="button danger" onclick="clearPortfolio()">Clear Portfolio</button>
        <div id="clearResult"></div>
    </div>

    <!-- Debug Image Text Section -->
    <div class="container">
        <h2>2. Debug Image Text Extraction</h2>
        <p>Upload your portfolio image to see exactly what text is being extracted.</p>
        <input type="file" id="debugImageFile" accept="image/*">
        <button class="button" onclick="debugImageText()">Debug Image Text</button>
        <div id="debugResult"></div>
    </div>

    <!-- Force Currency Section -->
    <div class="container">
        <h2>3. Force Currency Import</h2>
        <p>If OCR is not reading ¥ symbols correctly, you can force a specific currency.</p>
        <input type="file" id="forceImageFile" accept="image/*">
        <select id="forceCurrency">
            <option value="JPY">Japanese Yen (JPY)</option>
            <option value="USD">US Dollar (USD)</option>
            <option value="EUR">Euro (EUR)</option>
            <option value="GBP">British Pound (GBP)</option>
            <option value="DKK">Danish Krone (DKK)</option>
        </select>
        <button class="button" onclick="forceImport()">Force Import with Currency</button>
        <div id="forceResult"></div>
    </div>

    <script>
        async function clearPortfolio() {
            try {
                const response = await fetch('/api/portfolio/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const result = await response.json();
                const resultDiv = document.getElementById('clearResult');
                
                if (result.success) {
                    resultDiv.innerHTML = '<div class="result success">✅ Portfolio cleared successfully!</div>';
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Error: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('clearResult').innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }

        async function debugImageText() {
            const fileInput = document.getElementById('debugImageFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select an image file first');
                return;
            }

            const formData = new FormData();
            formData.append('image', file);

            try {
                const response = await fetch('/api/debug/image-text', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                const resultDiv = document.getElementById('debugResult');
                
                if (result.success) {
                    const analysis = result.currency_analysis;
                    const output = `✅ Text Extraction Results:

📝 Extracted Text (first 200 chars):
${result.first_200_chars}

📊 Currency Analysis:
- ¥ symbols found: ${analysis.yen_symbols}
- $ symbols found: ${analysis.dollar_symbols}  
- "USD" text found: ${analysis.usd_text}
- Contains ¥: ${result.contains_yen}
- Contains JPY: ${result.contains_jpy}

📏 Total text length: ${result.text_length} characters

🔍 Full extracted text:
${result.extracted_text}`;
                    
                    resultDiv.innerHTML = `<div class="result success">${output}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Error: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('debugResult').innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }

        async function forceImport() {
            const fileInput = document.getElementById('forceImageFile');
            const file = fileInput.files[0];
            const currency = document.getElementById('forceCurrency').value;
            
            if (!file) {
                alert('Please select an image file first');
                return;
            }

            // First, extract the portfolio data
            const formData = new FormData();
            formData.append('image', file);

            try {
                // Step 1: Extract portfolio data from image
                const extractResponse = await fetch('/api/import/image', {
                    method: 'POST',
                    body: formData
                });
                
                const extractResult = await extractResponse.json();
                
                if (!extractResult.success) {
                    document.getElementById('forceResult').innerHTML = `<div class="result error">❌ Extraction failed: ${extractResult.errors?.join(', ')}</div>`;
                    return;
                }

                // Step 2: Force the currency
                const forceResponse = await fetch('/api/import/force-currency', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        force_currency: currency,
                        portfolio_data: extractResult
                    })
                });
                
                const forceResult = await forceResponse.json();
                const resultDiv = document.getElementById('forceResult');
                
                if (forceResult.success) {
                    resultDiv.innerHTML = `<div class="result success">✅ Successfully imported portfolio with ${currency} currency!
                    
📊 Imported ${forceResult.total_entries} entries
💱 All entries set to: ${currency}

Go to your portfolio page to see the results.</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Force import failed: ${forceResult.error}</div>`;
                }
            } catch (error) {
                document.getElementById('forceResult').innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
