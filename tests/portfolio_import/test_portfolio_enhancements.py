#!/usr/bin/env python3
"""
Test script to validate portfolio enhancements implementation.
This script tests the backend functionality and API endpoints.
"""

import sys
import os
import requests
import json
from datetime import datetime

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_portfolio_performance_api():
    """Test the portfolio performance API endpoint."""
    print("🧪 Testing Portfolio Performance API...")
    
    try:
        # Test different timeframes
        timeframes = ['1M', '3M', '6M', '1Y', 'ALL']
        
        for timeframe in timeframes:
            print(f"  Testing timeframe: {timeframe}")
            
            # This would normally make an HTTP request to the running app
            # For now, we'll test the service function directly
            from services.portfolio_service import calculate_portfolio_performance
            
            result = calculate_portfolio_performance(timeframe)
            
            # Validate response structure
            assert 'success' in result, f"Missing 'success' field for {timeframe}"
            assert 'performance_data' in result, f"Missing 'performance_data' field for {timeframe}"
            assert 'total_return' in result, f"Missing 'total_return' field for {timeframe}"
            assert 'value_weighted_return' in result, f"Missing 'value_weighted_return' field for {timeframe}"
            assert 'timeframe' in result, f"Missing 'timeframe' field for {timeframe}"
            
            # Validate data types
            assert isinstance(result['performance_data'], list), f"performance_data should be list for {timeframe}"
            assert isinstance(result['total_return'], (int, float)), f"total_return should be numeric for {timeframe}"
            assert isinstance(result['value_weighted_return'], (int, float)), f"value_weighted_return should be numeric for {timeframe}"
            assert result['timeframe'] == timeframe, f"timeframe mismatch for {timeframe}"
            
            print(f"    ✅ {timeframe}: {len(result['performance_data'])} data points, {result['total_return']:.2f}% return")
        
        print("✅ Portfolio Performance API tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Portfolio Performance API test failed: {e}")
        return False


def test_portfolio_service_functions():
    """Test the portfolio service functions."""
    print("🧪 Testing Portfolio Service Functions...")
    
    try:
        from services.portfolio_service import (
            calculate_portfolio_performance,
            generate_synthetic_performance_data
        )
        
        # Test synthetic data generation
        print("  Testing synthetic performance data generation...")
        
        mock_summary = {
            'total_portfolio_value': 15000,
            'total_percent_gain': 12.5
        }
        
        synthetic_data = generate_synthetic_performance_data(mock_summary, '1Y')
        
        assert isinstance(synthetic_data, list), "Synthetic data should be a list"
        assert len(synthetic_data) > 0, "Synthetic data should not be empty"
        
        # Validate data structure
        for item in synthetic_data[:3]:  # Check first 3 items
            assert 'date' in item, "Each data point should have a date"
            assert 'portfolio_value' in item, "Each data point should have portfolio_value"
            assert 'return_percentage' in item, "Each data point should have return_percentage"
            
            # Validate date format
            datetime.strptime(item['date'], '%Y-%m-%d')
        
        print(f"    ✅ Generated {len(synthetic_data)} synthetic data points")
        
        # Test performance calculation with mock data
        print("  Testing performance calculation...")
        
        perf_result = calculate_portfolio_performance('6M')
        assert perf_result['success'] or 'error' in perf_result, "Should return success or error"
        
        print("✅ Portfolio Service Functions tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Portfolio Service Functions test failed: {e}")
        return False


def validate_html_template():
    """Validate that the HTML template has been properly updated."""
    print("🧪 Validating HTML Template Changes...")
    
    try:
        with open('templates/portfolio.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Check that Comprehensive Financial Analysis section was removed
        assert 'Comprehensive Financial Analysis' not in html_content, "Comprehensive Financial Analysis section should be removed"
        assert 'Profitability Ratios' not in html_content, "Profitability Ratios should be removed"
        assert 'Liquidity Ratios' not in html_content, "Liquidity Ratios should be removed"
        
        print("    ✅ Comprehensive Financial Analysis section removed")
        
        # Check that circular chart elements exist
        assert 'metric-dial' in html_content, "Metric dial elements should exist"
        assert 'health-dial' in html_content, "Health dial should exist"
        assert 'diversification-dial' in html_content, "Diversification dial should exist"
        
        print("    ✅ Circular chart elements present")
        
        # Check that performance API endpoint is used
        assert '/api/portfolio-performance' in html_content, "New performance API endpoint should be used"
        assert 'timeframe=' in html_content, "Timeframe parameter should be used"
        
        print("    ✅ Performance API integration updated")
        
        # Check for improved error messages
        assert 'No data points for this period' not in html_content, "Old error message should be replaced"
        assert 'Loading...' in html_content, "Better loading states should be present"
        
        print("    ✅ Error messages improved")
        
        print("✅ HTML Template validation passed!")
        return True
        
    except Exception as e:
        print(f"❌ HTML Template validation failed: {e}")
        return False


def validate_circular_chart_implementation():
    """Validate the circular chart CSS and JavaScript implementation."""
    print("🧪 Validating Circular Chart Implementation...")
    
    try:
        with open('templates/portfolio.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Check for CSS custom property usage
        assert '--dial-percentage' in html_content, "CSS custom property for dial percentage should exist"
        assert 'conic-gradient' in html_content, "Conic gradient CSS should be present"
        
        print("    ✅ CSS implementation present")
        
        # Check for JavaScript functions
        assert 'renderMetricDials' in html_content, "renderMetricDials function should exist"
        assert 'setProperty' in html_content, "CSS property setting should be present"
        
        print("    ✅ JavaScript implementation present")
        
        # Check for test data initialization
        assert 'healthScore: 84' in html_content, "Test health score should be set"
        assert 'diversificationScore: 40' in html_content, "Test diversification score should be set"
        
        print("    ✅ Test data initialization present")
        
        print("✅ Circular Chart Implementation validation passed!")
        return True
        
    except Exception as e:
        print(f"❌ Circular Chart Implementation validation failed: {e}")
        return False


def run_all_tests():
    """Run all validation tests."""
    print("🚀 Starting Portfolio Enhancements Validation Tests\n")
    
    tests = [
        ("HTML Template Changes", validate_html_template),
        ("Circular Chart Implementation", validate_circular_chart_implementation),
        ("Portfolio Service Functions", test_portfolio_service_functions),
        ("Portfolio Performance API", test_portfolio_performance_api),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All portfolio enhancements validated successfully!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)