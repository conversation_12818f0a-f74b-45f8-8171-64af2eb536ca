#!/usr/bin/env python3
"""
Test script to verify the portfolio import fixes work with real OCR data from user logs.

This script tests the exact OCR text that was failing in the user's logs.
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to the path so we can import portfolio_import
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService

def test_real_ocr_data():
    """Test with the actual OCR data that was failing."""
    
    print("🧪 TESTING REAL OCR DATA FROM USER LOGS")
    print("=" * 60)
    
    # This is the exact OCR text from the user's logs that was failing
    real_ocr_text = """Alphabet A\t\r\nVæerdi\tAfkast\tI dag\tSeneste\t\r\n15.848 kr\t+10,77%\t+0,38%\t192,06 USD\t\r\na\tAmazon.com\t\r\nVæerdi\tAfkast\t1 dag\tSeneste\t\r\n18.858 kr\t+17,21%\t+0,36%\t228,29 USD\t\r\nASML\tASML Holding\t\r\nVardi\tAfkast\tI dag\tSeneste\t\r\n31.962 kr\t-4,27%\t+1,78%\t718,07 USD\t\r\nUber\tUber Technologies\t\r\nVæerdi\tAfkast\tI dag\tSeneste\t\r\n5.886 kr\t+11,14%\t+0,56%\t92,30 USD\t\r\n"""
    
    print(f"📝 OCR Text Length: {len(real_ocr_text)} characters")
    print(f"📝 OCR Text Preview:")
    print(f"   {repr(real_ocr_text[:100])}...")
    
    # Clean version for better readability
    clean_text = real_ocr_text.replace('\t', ' ').replace('\r\n', '\n').strip()
    print(f"\n📝 Cleaned OCR Text:")
    print(f"   {clean_text}")
    
    try:
        # Initialize the service (same as web interface)
        google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
        eodhd_api_key = "test_key"
        
        service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
        
        # Extract portfolio data (this is the actual method called by web interface)
        print(f"\n🔧 Testing PortfolioImportService.extract_portfolio_from_text()...")
        result = service.extract_portfolio_from_text(real_ocr_text)
        
        print(f"\n✅ Service extraction completed!")
        print(f"   Success: {result.success}")
        print(f"   Portfolio entries: {len(result.portfolio_entries)}")
        print(f"   Errors: {len(result.errors)}")
        print(f"   Warnings: {len(result.warnings)}")
        
        if result.errors:
            print(f"   ❌ Error details:")
            for error in result.errors:
                print(f"      - {error}")
        
        if result.warnings:
            print(f"   ⚠️  Warning details:")
            for warning in result.warnings[:5]:  # Show first 5
                print(f"      - {warning}")
        
        # Test the format_for_api method (this formats the result for the web interface)
        print(f"\n🔧 Testing format_for_api()...")
        formatted_result = service.format_for_api(result)
        
        print(f"✅ API formatting completed!")
        print(f"   Success: {formatted_result.get('success', False)}")
        print(f"   Portfolio entries: {len(formatted_result.get('portfolio', []))}")
        
        # Show the actual portfolio entries that would be sent to the web interface
        print(f"\n📊 PORTFOLIO ENTRIES FOR WEB INTERFACE:")
        for i, entry in enumerate(formatted_result.get('portfolio', []), 1):
            print(f"   --- Entry {i}: {entry.get('ticker', 'Unknown')} ---")
            print(f"     Company: {entry.get('company_name', 'N/A')}")
            print(f"     Shares: {entry.get('shares', 'N/A')}")
            print(f"     Buy Price: {entry.get('buy_price', 'N/A')} {entry.get('buy_price_currency', entry.get('currency', 'N/A'))}")
            print(f"     Current Price: {entry.get('current_price', 'N/A')} USD")
            print(f"     Current Value: {entry.get('current_value', 'N/A')} {entry.get('current_value_currency', entry.get('currency', 'N/A'))}")
            print(f"     Amount Invested: {entry.get('amount_invested', 'N/A')} {entry.get('buy_price_currency', entry.get('currency', 'N/A'))}")
            print(f"     Gain/Loss %: {entry.get('gain_loss_percent', 'N/A')}%")
            print(f"     Purchase Date: {entry.get('purchase_date', 'N/A')}")
            
            # Validate the data makes sense
            shares = entry.get('shares', 0)
            current_price = entry.get('current_price', 0)
            current_value = entry.get('current_value', 0)
            
            if shares and current_price and current_value:
                calculated_value_usd = shares * current_price
                print(f"     ✓ Math check: {shares:.2f} shares × ${current_price} = ${calculated_value_usd:.2f} USD")
                
                # Convert to DKK for comparison (approximate rate)
                calculated_value_dkk = calculated_value_usd * 6.9  # USD to DKK
                print(f"     ✓ DKK equivalent: ${calculated_value_usd:.2f} USD ≈ {calculated_value_dkk:.0f} DKK (actual: {current_value} DKK)")
        
        return formatted_result
        
    except Exception as e:
        print(f"❌ Service test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_expected_data_structure():
    """Test what the expected data structure should look like."""
    
    print(f"\n🧪 EXPECTED DATA STRUCTURE")
    print("=" * 60)
    
    print("Based on the OCR text, we should extract:")
    print("1. GOOGL (Alphabet A): Current value 15,848 kr, Current price 192.06 USD, Gain +10.77%")
    print("2. AMZN (Amazon.com): Current value 18,858 kr, Current price 228.29 USD, Gain +17.21%")
    print("3. ASML (ASML Holding): Current value 31,962 kr, Current price 718.07 USD, Gain -4.27%")
    print("4. UBER (Uber Technologies): Current value 5,886 kr, Current price 92.30 USD, Gain +11.14%")
    
    print("\nCalculated shares (using DKK to USD conversion ~6.9):")
    stocks = [
        ("GOOGL", 15848, 192.06, 10.77),
        ("AMZN", 18858, 228.29, 17.21),
        ("ASML", 31962, 718.07, -4.27),
        ("UBER", 5886, 92.30, 11.14)
    ]
    
    for ticker, value_dkk, price_usd, gain_pct in stocks:
        value_usd = value_dkk * 0.145  # DKK to USD conversion
        shares = value_usd / price_usd
        print(f"   {ticker}: {value_dkk} DKK → {value_usd:.2f} USD ÷ ${price_usd} = {shares:.2f} shares")

if __name__ == "__main__":
    print("🚀 REAL OCR DATA TEST SUITE")
    print("Testing the exact OCR data that was failing in user logs")
    print("=" * 70)
    
    try:
        # Test expected data structure first
        test_expected_data_structure()
        
        # Test the actual extraction
        result = test_real_ocr_data()
        
        print("\n\n✅ REAL OCR DATA TEST COMPLETED")
        print("=" * 70)
        
        # Summary
        if result and result.get('success'):
            print("🎉 Real OCR data extraction: SUCCESS!")
            portfolio_count = len(result.get('portfolio', []))
            print(f"📊 Portfolio entries extracted: {portfolio_count}")
            
            if portfolio_count >= 4:
                print("✅ All expected stocks found!")
                print("✅ Users should no longer see 'No valid portfolio entries found'")
                print("✅ The currency mixing issue should be resolved")
            elif portfolio_count > 0:
                print(f"⚠️  Only {portfolio_count} stocks found (expected 4)")
                print("✅ But at least some entries were extracted successfully")
            else:
                print("❌ No portfolio entries found - extraction still failing")
        else:
            print("❌ Real OCR data extraction: FAILED")
            print("❌ Users will still see 'No valid portfolio entries found'")
            
    except Exception as e:
        print(f"❌ TEST SUITE FAILED: {e}")
        import traceback
        traceback.print_exc()
