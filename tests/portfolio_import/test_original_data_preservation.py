#!/usr/bin/env python3
"""
Test to verify that the system preserves original data exactly as <PERSON> extracts it.
This addresses the user's concern that the system is showing wrong values.
"""

def test_data_preservation():
    """Test that original data is preserved without unwanted conversions."""
    print("🧪 TESTING ORIGINAL DATA PRESERVATION")
    print("=" * 60)
    
    # Simulate what should happen with DKK portfolio
    original_gemini_data = {
        'portfolio': [
            {
                'ticker': 'AAPL',
                'shares': 75.0,
                'amount_invested': 10162.5,  # Original DKK amount
                'buy_price': 135.5,          # Original DKK price
                'currency': 'DKK',
                'buy_price_currency': 'DKK',
                'current_value': 10162.5,
                'current_value_currency': 'DKK'
            },
            {
                'ticker': 'MSFT',
                'shares': 40.0,
                'amount_invested': 9032.0,   # Original DKK amount
                'buy_price': 225.8,          # Original DKK price
                'currency': 'DKK',
                'buy_price_currency': 'DKK',
                'current_value': 9032.0,
                'current_value_currency': 'DKK'
            }
        ],
        'currency': 'DKK',
        'detected_currency': 'DKK',
        'success': True
    }
    
    print("📊 ORIGINAL GEMINI DATA:")
    for entry in original_gemini_data['portfolio']:
        ticker = entry['ticker']
        shares = entry['shares']
        amount = entry['amount_invested']
        price = entry['buy_price']
        currency = entry['currency']
        
        print(f"  {ticker}: {shares} shares × {price} {currency} = {amount} {currency}")
        
        # Verify calculation
        calc = shares * price
        if abs(calc - amount) < 0.01:
            print(f"    ✅ CALCULATION CORRECT")
        else:
            print(f"    ❌ CALCULATION ERROR: {calc:.2f} ≠ {amount}")
    
    print("\n🎯 WHAT SHOULD HAPPEN:")
    print("✅ System preserves these exact values")
    print("✅ No automatic USD conversion")
    print("✅ No currency modal unless Gemini is confused")
    print("✅ Display shows: '10.162,5 kr' not '$1,473.46'")
    print("✅ User sees exactly what's in their original image")
    
    print("\n❌ WHAT SHOULD NOT HAPPEN:")
    print("❌ Converting DKK amounts to USD automatically")
    print("❌ Showing different numbers than in the image")
    print("❌ Forcing currency selection when Gemini is confident")
    print("❌ Changing shares calculation based on currency conversion")
    
    print("\n🔧 THE FIX:")
    print("1. Trust Gemini AI's extraction completely")
    print("2. Only show currency modal when Gemini is genuinely confused")
    print("3. Preserve original amounts in their detected currency")
    print("4. Format display according to currency conventions")
    print("5. Let user choose display currency only if needed")

def test_currency_formatting():
    """Test proper currency formatting for different currencies."""
    print("\n\n🎨 TESTING CURRENCY FORMATTING")
    print("=" * 60)
    
    test_amounts = [
        {'amount': 10162.5, 'currency': 'DKK', 'expected': '10.162,5 kr'},
        {'amount': 9032.0, 'currency': 'DKK', 'expected': '9.032 kr'},
        {'amount': 1500.75, 'currency': 'USD', 'expected': '$1,500.75'},
        {'amount': 2500.50, 'currency': 'EUR', 'expected': '€2,500.50'},
        {'amount': 1200.25, 'currency': 'GBP', 'expected': '£1,200.25'},
    ]
    
    for test in test_amounts:
        amount = test['amount']
        currency = test['currency']
        expected = test['expected']
        
        # Simulate proper formatting
        if currency == 'DKK':
            # Danish format: amount with . as thousands separator, , as decimal, kr after
            formatted = f"{amount:,.1f} kr".replace(',', 'X').replace('.', ',').replace('X', '.')
        elif currency == 'USD':
            formatted = f"${amount:,.2f}"
        elif currency == 'EUR':
            formatted = f"€{amount:,.2f}"
        elif currency == 'GBP':
            formatted = f"£{amount:,.2f}"
        else:
            formatted = f"{amount:,.2f} {currency}"
        
        print(f"  {amount} {currency} → {formatted}")
        if formatted == expected:
            print(f"    ✅ CORRECT FORMATTING")
        else:
            print(f"    ❌ WRONG: Expected {expected}")

def main():
    """Run all tests."""
    test_data_preservation()
    test_currency_formatting()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY")
    print("=" * 60)
    print("The system should now:")
    print("✅ Preserve original data exactly as Gemini extracts it")
    print("✅ Only show currency modal when Gemini is confused")
    print("✅ Format currencies according to local conventions")
    print("✅ Show the same values as in the user's original image")
    print("\nThe user should see their DKK amounts correctly!")

if __name__ == '__main__':
    main()
