#!/usr/bin/env python3
"""
Test the portfolio service fix to ensure DKK amounts are preserved correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_portfolio_service_currency_preservation():
    """Test that portfolio service preserves DKK amounts correctly."""
    
    print("🧪 Testing Portfolio Service Currency Fix")
    print("=" * 50)
    
    try:
        # Import Flask app and create test context
        from app import app
        
        with app.test_request_context():
            from flask import session
            from app.services.portfolio_service import calculate_portfolio_data
            
            # Create test portfolio data with DKK amounts (simulating import result)
            test_portfolio = [
                {
                    'ticker': 'GOOGL',
                    'amount_invested': 15000,  # 15,000 DKK
                    'amount_invested_currency': 'DKK',
                    'buy_price': 830,  # 830 DKK
                    'buy_price_currency': 'DKK',
                    'current_value': 15500,  # 15,500 DKK
                    'current_price': 835,  # 835 DKK
                    'shares': 18.07,
                    'currency': 'DKK'
                },
                {
                    'ticker': 'AMZN',
                    'amount_invested': 12000,  # 12,000 DKK
                    'amount_invested_currency': 'DKK',
                    'buy_price': 710,  # 710 DKK
                    'buy_price_currency': 'DKK',
                    'current_value': 12300,  # 12,300 DKK
                    'current_price': 715,  # 715 DKK
                    'shares': 16.90,
                    'currency': 'DKK'
                }
            ]
            
            print("📊 Input Portfolio Data:")
            for stock in test_portfolio:
                print(f"  {stock['ticker']}: {stock['amount_invested']} {stock['amount_invested_currency']}")
            print()
            
            # Set portfolio data in session
            session['portfolio_data'] = test_portfolio
            session['cash_position'] = 0.0
            
            # Call the portfolio service function
            result = calculate_portfolio_data()
            
            print("📊 After Portfolio Service Processing:")
            portfolio_after = session.get('portfolio_data', [])
            
            success = True
            
            for i, stock in enumerate(portfolio_after):
                original = test_portfolio[i]
                ticker = stock['ticker']
                
                print(f"{ticker}:")
                print(f"  Original Amount: {original['amount_invested']} {original['amount_invested_currency']}")
                print(f"  After Processing: {stock['amount_invested']} {stock.get('amount_invested_currency', stock.get('currency', 'N/A'))}")
                
                # Check if amounts are preserved (should be exactly the same)
                if abs(stock['amount_invested'] - original['amount_invested']) < 0.01:
                    print(f"  ✅ Amount preserved correctly")
                else:
                    print(f"  ❌ Amount changed! Expected: {original['amount_invested']}, Got: {stock['amount_invested']}")
                    success = False
                
                # Check if currency is preserved
                stock_currency = stock.get('amount_invested_currency', stock.get('currency', 'N/A'))
                if stock_currency == 'DKK':
                    print(f"  ✅ Currency preserved correctly (DKK)")
                else:
                    print(f"  ❌ Currency changed! Expected: DKK, Got: {stock_currency}")
                    success = False
                
                # Check if amounts are reasonable (should be in thousands, not millions)
                if 10000 <= stock['amount_invested'] <= 20000:
                    print(f"  ✅ Amount scale is reasonable")
                else:
                    print(f"  ❌ Amount scale is wrong: {stock['amount_invested']}")
                    success = False
                
                print()
            
            print("🎯 Summary:")
            if success:
                print("✅ ALL TESTS PASSED!")
                print("✅ DKK amounts preserved correctly")
                print("✅ No unwanted currency conversion")
                print("✅ Portfolio service working correctly")
            else:
                print("❌ SOME TESTS FAILED!")
                print("❌ Portfolio service is still converting amounts")
            
            # Also test the totals calculation
            if result:
                print(f"\n📈 Portfolio Totals (converted to USD for summary only):")
                print(f"  Total Invested: ${result.get('total_invested', 0):,.2f} USD")
                print(f"  Total Current Value: ${result.get('total_current_value', 0):,.2f} USD")
                print("  (These totals should be reasonable USD amounts for summary)")
            
            return success
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_portfolio_service_currency_preservation()
