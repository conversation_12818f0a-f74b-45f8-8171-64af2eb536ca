#!/usr/bin/env python3
"""
Test that the system no longer returns fake/default data
"""

from portfolio_import import process_image_upload

def test_no_fake_data():
    """Test that image processing no longer returns fake data"""
    print("🔍 Testing No More Fake Data")
    print("=" * 50)
    
    # Test with various image sizes to trigger different scenarios
    test_cases = [
        {'name': 'Small image', 'size': 100},
        {'name': 'Medium image', 'size': 1000},
        {'name': 'Large image', 'size': 5000},
        {'name': 'Very large image', 'size': 10000},
    ]
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    
    # Known fake data patterns that should NOT appear
    fake_data_patterns = {
        'amounts': [2554.80, 2137.50, 425.80, 142.50, 285.75],
        'tickers': ['FAKE', 'MOCK', 'TEST'],
        'prices': [425.80, 142.50, 285.75, 100.00, 200.00]
    }
    
    all_clean = True
    
    for test_case in test_cases:
        print(f"\nTesting {test_case['name']} ({test_case['size']} bytes):")
        
        # Create test image data
        image_data = b'PORTFOLIO_IMAGE_' + b'x' * test_case['size']
        
        result = process_image_upload(image_data, google_vision_api_key)
        
        print(f"  Success: {result['success']}")
        print(f"  Entries: {len(result.get('portfolio', []))}")
        print(f"  Errors: {len(result.get('errors', []))}")
        
        # Check for fake data
        portfolio = result.get('portfolio', [])
        has_fake_data = False
        
        if portfolio:
            print(f"  Portfolio entries found:")
            for entry in portfolio:
                ticker = entry.get('ticker', 'UNKNOWN')
                amount = entry.get('amount_invested', 0)
                price = entry.get('buy_price', 0)
                
                print(f"    {ticker}: ${amount} @ ${price}")
                
                # Check against known fake patterns
                if (amount in fake_data_patterns['amounts'] or 
                    price in fake_data_patterns['prices'] or
                    ticker in fake_data_patterns['tickers']):
                    has_fake_data = True
                    print(f"      ❌ FAKE DATA DETECTED!")
                
                # Check for the specific user's default data
                if (ticker == 'GOOGL' and amount == 12216.61 and price == 161.00) or \
                   (ticker == 'ASML' and amount == 9279.65 and price == 668.50) or \
                   (ticker == 'UBER' and amount == 5851.40 and price == 74.59) or \
                   (ticker == 'AMZN' and amount == 11790.22 and price == 186.92):
                    has_fake_data = True
                    print(f"      ❌ USER'S DEFAULT DATA DETECTED!")
        
        if has_fake_data:
            print(f"  ❌ Contains fake/default data!")
            all_clean = False
        else:
            print(f"  ✅ No fake data detected")
    
    return all_clean

def test_realistic_image_scenario():
    """Test with a realistic image scenario"""
    print(f"\n🔍 Testing Realistic Image Scenario")
    print("=" * 50)
    
    # Simulate a real portfolio image with different data
    realistic_image_text = """
Portfolio Holdings:

Apple Inc (AAPL): 5 shares at 180.00 USD each, total value 900.00 USD
Microsoft Corp (MSFT): 3 shares at 350.00 USD each, total value 1050.00 USD
Tesla Inc (TSLA): 2 shares at 250.00 USD each, total value 500.00 USD
"""
    
    # Create image data that includes this text
    image_data = b'REALISTIC_PORTFOLIO_' + realistic_image_text.encode() + b'x' * 2000
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_image_upload(image_data, google_vision_api_key)
    
    print(f"Result:")
    print(f"Success: {result['success']}")
    print(f"Entries: {len(result.get('portfolio', []))}")
    print(f"Errors: {len(result.get('errors', []))}")
    
    portfolio = result.get('portfolio', [])
    
    # Check if we get the user's default data instead of the realistic data
    has_default_data = False
    has_realistic_data = False
    
    if portfolio:
        print(f"\nPortfolio entries:")
        for entry in portfolio:
            ticker = entry.get('ticker', 'UNKNOWN')
            amount = entry.get('amount_invested', 0)
            price = entry.get('buy_price', 0)
            
            print(f"  {ticker}: ${amount} @ ${price}")
            
            # Check for user's default data
            if (ticker == 'GOOGL' and amount == 12216.61) or \
               (ticker == 'ASML' and amount == 9279.65) or \
               (ticker == 'UBER' and amount == 5851.40) or \
               (ticker == 'AMZN' and amount == 11790.22):
                has_default_data = True
            
            # Check for realistic data
            if (ticker == 'AAPL' and amount == 900.00) or \
               (ticker == 'MSFT' and amount == 1050.00) or \
               (ticker == 'TSLA' and amount == 500.00):
                has_realistic_data = True
    
    if has_default_data:
        print(f"\n❌ PROBLEM: Still returning user's default data!")
        return False
    elif has_realistic_data:
        print(f"\n✅ SUCCESS: Processing realistic data correctly!")
        return True
    elif not portfolio:
        print(f"\n✅ SUCCESS: No fake data returned (system failed gracefully)")
        return True
    else:
        print(f"\n⚠️  UNKNOWN: Unexpected data returned")
        return False

def main():
    print("🚀 TESTING NO MORE FAKE DATA")
    print("=" * 60)
    print("Verifying that the system no longer returns fake/default data")
    print("when it can't properly read the image")
    print()
    
    test1 = test_no_fake_data()
    test2 = test_realistic_image_scenario()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    
    if test1 and test2:
        print("🎉 SUCCESS! No more fake data!")
        print()
        print("✅ System no longer returns hardcoded default data")
        print("✅ No fake amounts like $2,554.80 or $425.80")
        print("✅ No user's default GOOGL/ASML/UBER/AMZN data")
        print("✅ System fails gracefully when OCR fails")
        print()
        print("🎯 The user will now get:")
        print("   • Real data when OCR works correctly")
        print("   • Clear error messages when OCR fails")
        print("   • No more confusing fake/default data")
        
    else:
        print("❌ Issues remain!")
        if not test1:
            print("   - Still detecting fake data patterns")
        if not test2:
            print("   - Still returning default data instead of real data")

if __name__ == "__main__":
    main()
