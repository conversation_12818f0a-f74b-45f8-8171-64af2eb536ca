#!/usr/bin/env python3
"""
Test script to verify that the portfolio import share calculation fix works correctly.
This tests the scenario where the user has USD as their portfolio currency.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_share_calculation():
    """Test that share calculations use the user's portfolio currency correctly."""
    
    try:
        from portfolio_import import AIPortfolioExtractor
        
        # Initialize the AI extractor
        extractor = AIPortfolioExtractor("test_key", "test_eodhd_key")
        
        # Set user's portfolio currency to USD (as shown in the user's screenshot)
        extractor.user_portfolio_currency = "USD"
        
        # Create test portfolio data that simulates the user's scenario
        test_portfolio_data = [
            {
                'ticker': 'GOOGL',
                'amount_invested': 10000,  # $10,000 USD
                'amount_invested_currency': 'USD',
                'buy_price': 120.50,  # $120.50 USD per share
                'buy_price_currency': 'USD',
                'current_price': 121.25,  # $121.25 USD per share
                'current_price_currency': 'USD',
                'shares': 0  # Will be calculated
            },
            {
                'ticker': 'AMZN',
                'amount_invested': 8500,  # $8,500 USD
                'amount_invested_currency': 'USD',
                'buy_price': 102.75,  # $102.75 USD per share
                'buy_price_currency': 'USD',
                'current_price': 103.50,  # $103.50 USD per share
                'current_price_currency': 'USD',
                'shares': 0  # Will be calculated
            }
        ]
        
        print("🧪 Testing Portfolio Import Share Calculation Fix")
        print("=" * 60)
        print(f"User's Portfolio Currency: {extractor.user_portfolio_currency}")
        print()
        
        # Process the portfolio data
        processed_entries = extractor._process_gemini_response(test_portfolio_data)
        
        print("📊 Results:")
        print("-" * 40)
        
        expected_shares = {
            'GOOGL': 10000 / 120.50,  # Should be ~82.99 shares
            'AMZN': 8500 / 102.75,    # Should be ~82.72 shares  
        }
        
        for entry in processed_entries:
            ticker = entry['ticker']
            shares = entry['shares']
            expected = expected_shares.get(ticker, 0)
            
            print(f"{ticker}:")
            print(f"  Calculated Shares: {shares:.6f}")
            print(f"  Expected Shares:   {expected:.6f}")
            print(f"  Difference:        {abs(shares - expected):.6f}")
            print(f"  Currency:          {entry.get('currency', 'N/A')}")
            print(f"  Amount Invested:   {entry['amount_invested']} {entry.get('amount_invested_currency', 'N/A')}")
            print(f"  Buy Price:         {entry['buy_price']} {entry.get('buy_price_currency', 'N/A')}")
            
            # Check if the calculation is correct (within 0.001 tolerance)
            if abs(shares - expected) < 0.001:
                print(f"  ✅ CORRECT")
            else:
                print(f"  ❌ INCORRECT")
            print()
        
        print("🎯 Summary:")
        print(f"Total entries processed: {len(processed_entries)}")
        
        # Check if all currencies are USD
        all_usd = all(
            entry.get('currency') == 'USD' and
            entry.get('amount_invested_currency') == 'USD' and
            entry.get('buy_price_currency') == 'USD'
            for entry in processed_entries
        )
        
        if all_usd:
            print("✅ All entries use USD currency (user's preference)")
        else:
            print("❌ Some entries don't use USD currency")
            
        # Check if share calculations are reasonable
        reasonable_shares = all(
            1 <= entry['shares'] <= 1000 for entry in processed_entries
        )
        
        if reasonable_shares:
            print("✅ All share calculations are reasonable")
        else:
            print("❌ Some share calculations seem unreasonable")
        
        return processed_entries
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    test_share_calculation()
