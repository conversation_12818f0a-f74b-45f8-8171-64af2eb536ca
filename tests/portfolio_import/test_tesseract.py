#!/usr/bin/env python3
"""
Test Tesseract OCR directly
"""

import pytesseract
from PIL import Image, ImageDraw, ImageFont
import io

def create_test_portfolio_image():
    """Create a test image with portfolio data"""
    
    # Create a white image
    width, height = 800, 400
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # Try to use a default font
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # Draw portfolio data similar to the user's image
    portfolio_text = [
        "Ticker    Shares    Avg. Cost    Market Value (DKK)    % Chg.",
        "GOOGL     10        161          DKK 12,216.61         18.1%",
        "ASML      2         668.5        DKK 9,279.65          8.1%", 
        "UBER      10        74.59        DKK 5,851.40          22.1%",
        "AMZN      8         186.92       DKK 11,790.22         22.7%"
    ]
    
    y_position = 50
    for line in portfolio_text:
        draw.text((50, y_position), line, fill='black', font=font)
        y_position += 30
    
    return image

def test_tesseract_ocr():
    """Test Tesseract OCR with portfolio image"""
    
    try:
        print("Creating test portfolio image...")
        image = create_test_portfolio_image()
        
        # Save for debugging
        image.save('test_portfolio_image.png')
        print("Saved test image as test_portfolio_image.png")
        
        print("Testing Tesseract OCR...")
        
        # Try different PSM modes
        configs = [
            '--oem 3 --psm 6',  # Uniform block of text
            '--oem 3 --psm 4',  # Single column of text
            '--oem 3 --psm 3',  # Fully automatic page segmentation
            '--oem 3 --psm 11', # Sparse text
            '--oem 3 --psm 12', # Sparse text with OSD
        ]
        
        for i, config in enumerate(configs):
            try:
                print(f"\nTrying config {i+1}: {config}")
                text = pytesseract.image_to_string(image, config=config)
                print(f"Extracted text ({len(text)} chars):")
                print(f"'{text}'")
                
                if text.strip() and len(text.strip()) > 10:
                    print(f"✅ Success with config: {config}")
                    return text
                    
            except Exception as e:
                print(f"❌ Config {config} failed: {e}")
                continue
        
        print("❌ All Tesseract configs failed")
        return None
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_tesseract_ocr()
    if result:
        print(f"\n✅ Final result: {result}")
    else:
        print("\n❌ No text extracted")
