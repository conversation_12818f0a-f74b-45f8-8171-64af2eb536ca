#!/usr/bin/env python3
"""
Test the improved AI extraction for portfolio data
"""

from portfolio_import import PortfolioImportService

def test_user_portfolio_format():
    """Test with the exact format from the user's image"""
    print("🔍 Testing User's Portfolio Format")
    print("=" * 50)
    
    # Simulate the text that would be extracted from the user's image
    portfolio_text = """
    Portfolio Holdings

    Alphabet Inc. (GOOGL)
    Shares: 10
    Avg. Cost Basis: 161
    Market Value: DKK 12,216.61

    ASML Holding N.V. (ASML)
    Shares: 2
    Avg. Cost Basis: 668.5
    Market Value: DKK 9,279.65

    Uber Technologies, Inc. (UBER)
    Shares: 10
    Avg. Cost Basis: 74.59
    Market Value: DKK 5,851.40

    Amazon.com Inc. (AMZN)
    Shares: 8
    Avg. Cost Basis: 186.92
    Market Value: DKK 11,790.22
    """
    
    # Test the AI extraction
    service = PortfolioImportService("test_key")
    result = service.extract_portfolio_from_text(portfolio_text)
    
    print(f"Extraction Result:")
    print(f"Success: {result.success}")
    print(f"Entries found: {len(result.portfolio_entries)}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    
    if result.portfolio_entries:
        print(f"\nExtracted portfolio data:")
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: {entry.shares} shares @ ${entry.buy_price:.2f} = ${entry.amount_invested:.2f}")
        
        # Validate the extraction
        expected_data = {
            'GOOGL': {'shares': 10, 'price': 161.0, 'value': 12216.61},
            'ASML': {'shares': 2, 'price': 668.5, 'value': 9279.65},
            'UBER': {'shares': 10, 'price': 74.59, 'value': 5851.40},
            'AMZN': {'shares': 8, 'price': 186.92, 'value': 11790.22}
        }
        
        correct_extractions = 0
        for entry in result.portfolio_entries:
            if entry.ticker in expected_data:
                expected = expected_data[entry.ticker]
                
                # Check if the extraction is approximately correct
                shares_ok = abs(entry.shares - expected['shares']) < 0.1 if entry.shares else False
                price_ok = abs(entry.buy_price - expected['price']) < 1.0
                
                if shares_ok and price_ok:
                    correct_extractions += 1
                    print(f"    ✅ {entry.ticker}: Correctly extracted")
                else:
                    print(f"    ❌ {entry.ticker}: Incorrect - got {entry.shares} shares @ ${entry.buy_price}")
                    print(f"       Expected: {expected['shares']} shares @ ${expected['price']}")
        
        success_rate = correct_extractions / len(expected_data) * 100
        print(f"\nExtraction accuracy: {success_rate:.1f}% ({correct_extractions}/{len(expected_data)})")
        
        # Check for DKK being incorrectly identified as ticker
        dkk_found = any(entry.ticker == 'DKK' for entry in result.portfolio_entries)
        if dkk_found:
            print("❌ PROBLEM: DKK incorrectly identified as ticker!")
            return False
        else:
            print("✅ SUCCESS: DKK not incorrectly identified as ticker")
        
        return success_rate >= 75  # 75% accuracy threshold
    else:
        print("❌ No portfolio entries extracted!")
        return False

def test_table_format():
    """Test with table-like format"""
    print(f"\n🔍 Testing Table Format")
    print("=" * 50)
    
    # Simulate table format that might come from OCR
    table_text = """
    Ticker    Shares    Avg. Cost    Market Value (DKK)    % Chg.
    GOOGL     10        161          12,216.61             18.1%
    ASML      2         668.5        9,279.65              8.1%
    UBER      10        74.59        5,851.40              22.1%
    AMZN      8         186.92       11,790.22             22.7%
    """
    
    service = PortfolioImportService("test_key")
    result = service.extract_portfolio_from_text(table_text)
    
    print(f"Table extraction result:")
    print(f"Success: {result.success}")
    print(f"Entries: {len(result.portfolio_entries)}")
    
    if result.portfolio_entries:
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: {entry.shares} shares @ ${entry.buy_price:.2f}")
        
        # Check for correct tickers
        expected_tickers = {'GOOGL', 'ASML', 'UBER', 'AMZN'}
        found_tickers = {entry.ticker for entry in result.portfolio_entries}
        
        if expected_tickers.issubset(found_tickers):
            print("✅ All expected tickers found")
            return True
        else:
            missing = expected_tickers - found_tickers
            extra = found_tickers - expected_tickers
            print(f"❌ Missing tickers: {missing}")
            print(f"❌ Extra tickers: {extra}")
            return False
    else:
        print("❌ No entries extracted from table format")
        return False

def test_problematic_cases():
    """Test cases that were causing issues"""
    print(f"\n🔍 Testing Problematic Cases")
    print("=" * 50)
    
    # Test case that was returning DKK as ticker
    problematic_text = """
    Market Value (DKK)
    DKK 12,216.61
    DKK 9,279.65
    DKK 5,851.40
    DKK 11,790.22
    """
    
    service = PortfolioImportService("test_key")
    result = service.extract_portfolio_from_text(problematic_text)
    
    print(f"Problematic text result:")
    print(f"Success: {result.success}")
    print(f"Entries: {len(result.portfolio_entries)}")
    
    if result.portfolio_entries:
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: ${entry.amount_invested} @ ${entry.buy_price}")
    
    # Check if DKK is incorrectly identified as ticker
    dkk_entries = [entry for entry in result.portfolio_entries if entry.ticker == 'DKK']
    
    if dkk_entries:
        print("❌ PROBLEM: DKK still being identified as ticker!")
        return False
    else:
        print("✅ SUCCESS: DKK not identified as ticker")
        return True

def main():
    print("🚀 TESTING IMPROVED AI EXTRACTION")
    print("=" * 60)
    print("Testing the enhanced AI extraction that should correctly")
    print("parse the user's portfolio data without DKK confusion.")
    print()
    
    test1 = test_user_portfolio_format()
    test2 = test_table_format()
    test3 = test_problematic_cases()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    
    if all([test1, test2, test3]):
        print("🎉 SUCCESS! AI extraction is working correctly!")
        print()
        print("✅ User's portfolio format parsed correctly")
        print("✅ Correct tickers extracted (GOOGL, ASML, UBER, AMZN)")
        print("✅ Correct shares and prices extracted")
        print("✅ DKK not incorrectly identified as ticker")
        print("✅ Table formats handled properly")
        print()
        print("🎯 The user should now see their actual portfolio data:")
        print("   - GOOGL: 10 shares @ 161 DKK")
        print("   - ASML: 2 shares @ 668.5 DKK")
        print("   - UBER: 10 shares @ 74.59 DKK")
        print("   - AMZN: 8 shares @ 186.92 DKK")
        print("   Instead of: DKK $12,216.61 @ $12,216.61")
        
    else:
        print("❌ Some issues remain!")
        if not test1:
            print("   - User's portfolio format not parsed correctly")
        if not test2:
            print("   - Table format issues")
        if not test3:
            print("   - DKK still being identified as ticker")

if __name__ == "__main__":
    main()
