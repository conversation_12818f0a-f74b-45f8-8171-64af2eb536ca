#!/usr/bin/env python3
"""
Test the portfolio import fixes to ensure real data processing
"""

import pandas as pd
from portfolio_import import PortfolioImportService

def test_simple_csv_processing():
    """Test processing of simple CSV with A,B,C,D columns"""
    print("Testing simple CSV processing...")
    
    # Create test data
    data = {
        'A': ['AAPL', 'MSFT', 'GOOGL'],
        'B': [1500.00, 2000.00, 1200.00],
        'C': [150.50, 250.75, 120.25],
        'D': ['2023-04-15', '2023-03-20', '2023-05-10']
    }
    df = pd.DataFrame(data)
    
    # Process with import service
    service = PortfolioImportService("test_key")
    result = service.extract_portfolio_from_spreadsheet(df)
    
    print(f"Success: {result.success}")
    print(f"Entries found: {len(result.portfolio_entries)}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    
    if result.success:
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: ${entry.amount_invested} @ ${entry.buy_price} on {entry.purchase_date}")
    
    return result.success and len(result.portfolio_entries) > 0

def test_named_columns_processing():
    """Test processing of CSV with proper column names"""
    print("\nTesting named columns processing...")
    
    # Create test data
    data = {
        'Stock Symbol': ['AAPL', 'MSFT', 'GOOGL'],
        'Total Investment': [1500.00, 2000.00, 1200.00],
        'Avg Cost Basis': [150.50, 250.75, 120.25],
        'Acquisition Date': ['2023-04-15', '2023-03-20', '2023-05-10']
    }
    df = pd.DataFrame(data)
    
    # Process with import service
    service = PortfolioImportService("test_key")
    result = service.extract_portfolio_from_spreadsheet(df)
    
    print(f"Success: {result.success}")
    print(f"Entries found: {len(result.portfolio_entries)}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    
    if result.success:
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: ${entry.amount_invested} @ ${entry.buy_price} on {entry.purchase_date}")
    
    return result.success and len(result.portfolio_entries) > 0

def test_api_format():
    """Test the API format output"""
    print("\nTesting API format output...")
    
    # Create test data
    data = {
        'A': ['AAPL', 'MSFT'],
        'B': [1500.00, 2000.00],
        'C': [150.50, 250.75],
        'D': ['2023-04-15', '2023-03-20']
    }
    df = pd.DataFrame(data)
    
    # Process with import service
    service = PortfolioImportService("test_key")
    result = service.extract_portfolio_from_spreadsheet(df)
    
    # Format for API
    api_result = service.format_for_api(result)
    
    print(f"API Result: {api_result}")
    
    # Check that we have the expected structure
    expected_keys = ['success', 'portfolio', 'cash_position', 'errors', 'warnings']
    has_all_keys = all(key in api_result for key in expected_keys)
    
    if has_all_keys and api_result['success']:
        print("✅ API format test passed")
        return True
    else:
        print("❌ API format test failed")
        return False

if __name__ == "__main__":
    print("Testing Portfolio Import Fixes")
    print("=" * 50)
    
    test1 = test_simple_csv_processing()
    test2 = test_named_columns_processing()
    test3 = test_api_format()
    
    print("\n" + "=" * 50)
    if all([test1, test2, test3]):
        print("✅ All tests passed! Portfolio import should work correctly.")
    else:
        print("❌ Some tests failed. Check the implementation.")
