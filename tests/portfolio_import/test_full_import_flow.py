#!/usr/bin/env python3
"""
Test the complete portfolio import flow to identify where currency selection breaks.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import process_image_upload
import json

def test_complete_import_flow():
    """Test the complete import flow that the frontend would use."""
    print("🔄 TESTING COMPLETE PORTFOLIO IMPORT FLOW")
    print("=" * 60)
    
    # Simulate DKK portfolio image data (this would be actual image bytes in real usage)
    dkk_portfolio_text = """
    Min Aktieportefølje
    
    Ticker    Markedsværdi    GAK        Antal
    AAPL      2.462,85 DKK    161,61 kr  13 stk
    MSFT      3.250,75 DKK    325,08 kr  10 stk
    NOVO      1.890,50 DKK    189,05 kr  10 stk
    """
    dkk_portfolio_image_data = dkk_portfolio_text.encode('utf-8')
    
    print("📤 Step 1: Simulating image upload to /api/import/image")
    print(f"   Image data size: {len(dkk_portfolio_image_data)} bytes")
    
    # This is what the /api/import/image endpoint would call
    api_response = process_image_upload(
        image_data=dkk_portfolio_image_data,
        google_vision_api_key="demo_key",
        eodhd_api_key="demo_key"
    )
    
    print("\n📥 Step 2: API Response Analysis")
    print(f"   Success: {api_response.get('success', False)}")
    print(f"   Portfolio entries: {len(api_response.get('portfolio', []))}")
    print(f"   Detected currency: {api_response.get('detected_currency', 'None')}")
    
    # Check currency_info structure
    currency_info = api_response.get('currency_info', {})
    print(f"\n💱 Step 3: Currency Info Analysis")
    print(f"   Detected currencies: {currency_info.get('detected_currencies', [])}")
    print(f"   Primary currency: {currency_info.get('primary_currency', 'None')}")
    print(f"   Requires user selection: {currency_info.get('requires_user_selection', False)}")
    print(f"   Has mixed currencies: {currency_info.get('has_mixed_currencies', False)}")
    
    gemini_question = currency_info.get('gemini_question')
    if gemini_question:
        print(f"   Gemini question: {gemini_question}")
    else:
        print(f"   Gemini question: None")
    
    # Check what the frontend should do
    should_show_modal = currency_info.get('requires_user_selection', False)
    print(f"\n🎯 Step 4: Frontend Behavior Prediction")
    if should_show_modal:
        print("   ✅ Frontend SHOULD show currency selection modal")
        print("   ✅ User will be prompted to select currency")
        print("   ✅ Modal will display Gemini AI question if available")
    else:
        print("   ❌ Frontend will NOT show currency selection modal")
        print("   ❌ Will proceed directly to results display")
        print("   ❌ May default to USD instead of detected currency")
    
    # Simulate what happens if user selects DKK
    if should_show_modal:
        print(f"\n🔄 Step 5: Simulating User Currency Selection")
        print("   User selects: DKK")
        
        # This would be sent to /api/import/confirm
        confirm_data = {
            **api_response,
            'selected_currency': 'DKK',
            'user_selected_currency': True
        }
        
        print("   Data sent to /api/import/confirm:")
        print(f"     - Portfolio entries: {len(confirm_data.get('portfolio', []))}")
        print(f"     - Selected currency: {confirm_data.get('selected_currency')}")
        print(f"     - User selected: {confirm_data.get('user_selected_currency')}")
    
    # Show the complete API response structure for debugging
    print(f"\n🔍 Step 6: Complete API Response Structure")
    print("=" * 40)
    print(json.dumps(api_response, indent=2, default=str))
    
    return api_response

def test_mixed_currency_flow():
    """Test mixed currency flow."""
    print("\n\n🌍 TESTING MIXED CURRENCY IMPORT FLOW")
    print("=" * 60)
    
    # Simulate mixed currency portfolio
    mixed_portfolio_text = """
    International Portfolio
    
    AAPL    $2,500.00 USD    $125.00    20 shares
    ASML    €1,800.00 EUR    €180.00    10 shares  
    NOVO    1,200.00 DKK     150.00 kr  8 shares
    """
    mixed_portfolio_image_data = mixed_portfolio_text.encode('utf-8')
    
    api_response = process_image_upload(
        image_data=mixed_portfolio_image_data,
        google_vision_api_key="demo_key",
        eodhd_api_key="demo_key"
    )
    
    currency_info = api_response.get('currency_info', {})
    should_show_modal = currency_info.get('requires_user_selection', False)
    
    print(f"📊 Mixed Currency Results:")
    print(f"   Detected currencies: {currency_info.get('detected_currencies', [])}")
    print(f"   Should show modal: {should_show_modal}")
    print(f"   Gemini question: {currency_info.get('gemini_question', 'None')}")
    
    return api_response

def main():
    """Run all tests."""
    print("🚨 COMPLETE PORTFOLIO IMPORT FLOW TEST")
    print("=" * 70)
    print("This test simulates the exact flow that happens in the web interface")
    print("=" * 70)
    
    try:
        # Test 1: DKK portfolio flow
        dkk_result = test_complete_import_flow()
        
        # Test 2: Mixed currency flow
        mixed_result = test_mixed_currency_flow()
        
        print("\n" + "=" * 70)
        print("🎯 FLOW TEST SUMMARY")
        print("=" * 70)
        
        # Analyze results
        dkk_should_show = dkk_result.get('currency_info', {}).get('requires_user_selection', False)
        mixed_should_show = mixed_result.get('currency_info', {}).get('requires_user_selection', False)
        
        print(f"DKK Portfolio Modal: {'✅ SHOULD SHOW' if dkk_should_show else '❌ NOT SHOWING'}")
        print(f"Mixed Currency Modal: {'✅ SHOULD SHOW' if mixed_should_show else '❌ NOT SHOWING'}")
        
        if dkk_should_show and mixed_should_show:
            print("\n🎉 BACKEND IS WORKING CORRECTLY!")
            print("✅ Currency selection requirements are being set properly")
            print("✅ Gemini questions are being generated")
            print("✅ API responses include all necessary data")
            print("\n🔧 ISSUE IS LIKELY IN FRONTEND:")
            print("   - Check if JavaScript is receiving the API response correctly")
            print("   - Verify that displayResults() function is being called")
            print("   - Ensure showCurrencySelectionModal() is working")
            print("   - Check browser console for JavaScript errors")
        else:
            print("\n❌ BACKEND ISSUE IDENTIFIED:")
            if not dkk_should_show:
                print("   - DKK currency selection not being triggered")
            if not mixed_should_show:
                print("   - Mixed currency selection not being triggered")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()