#!/usr/bin/env python3
"""
Test script for the enhanced portfolio import system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService, PortfolioEntry

def test_multilingual_extraction():
    """Test the enhanced multilingual portfolio extraction."""
    
    # Initialize the service
    service = PortfolioImportService("test_api_key")
    
    # Test Danish portfolio data (like the user's example)
    danish_text = """
    Mine beholdninger
    
    Antal: 13 stk
    GAK: 161,61 USD
    
    Markedsværdi: 2.462,85 USD
    Ureal.afkast: +9,9%
    
    Vigtige datoer
    Dage til regnskab: 1
    Næste regnskab: 23. jul.
    
    Om virksomheden
    Alphabet is a holding company that wholly owns
    internet giant Google. The California-based
    company derives slightly less ... Læs mere
    
    NasdaqGS:GOOGL 10 161 DKK 12,216.61 18.1%
    """
    
    print("Testing Danish portfolio extraction...")
    result = service.extract_portfolio_from_text(danish_text)
    
    print(f"Success: {result.success}")
    print(f"Errors: {result.errors}")
    print(f"Warnings: {result.warnings}")
    print(f"Portfolio entries: {len(result.portfolio_entries)}")
    
    for entry in result.portfolio_entries:
        print(f"  Ticker: {entry.ticker}")
        print(f"  Shares: {entry.shares}")
        print(f"  Buy Price: ${entry.buy_price:.2f}")
        print(f"  Amount Invested: ${entry.amount_invested:.2f}")
        print(f"  Current Value: ${entry.current_value:.2f}")
        print(f"  Currency: {entry.currency}")
        print()

def test_current_value_scenario():
    """Test scenario where only current value is available."""
    
    print("Testing current value only scenario...")
    
    # Create a portfolio entry with only current value
    entry = PortfolioEntry(
        ticker="AAPL",
        current_value=5000.0,  # $5000 current value
        buy_price=150.0,       # Bought at $150
        current_price=200.0    # Current price $200
    )
    
    print(f"After calculation:")
    print(f"  Ticker: {entry.ticker}")
    print(f"  Shares: {entry.shares}")
    print(f"  Buy Price: ${entry.buy_price:.2f}")
    print(f"  Amount Invested: ${entry.amount_invested:.2f}")
    print(f"  Current Value: ${entry.current_value:.2f}")
    print(f"  Current Price: ${entry.current_price:.2f}")

def test_currency_conversion():
    """Test currency conversion functionality."""
    
    print("Testing currency conversion...")
    
    service = PortfolioImportService("test_api_key")
    
    # Test DKK to USD conversion
    dkk_amount = 1000.0  # 1000 DKK
    usd_amount = service.ai_extractor._convert_to_usd(dkk_amount, 'DKK')
    print(f"1000 DKK = ${usd_amount:.2f} USD")
    
    # Test EUR to USD conversion
    eur_amount = 1000.0  # 1000 EUR
    usd_amount = service.ai_extractor._convert_to_usd(eur_amount, 'EUR')
    print(f"1000 EUR = ${usd_amount:.2f} USD")

def test_language_detection():
    """Test language detection functionality."""
    
    print("Testing language detection...")
    
    service = PortfolioImportService("test_api_key")
    
    # Test Danish text
    danish_text = "Mine beholdninger aktier værdi pris antal stk"
    detected_lang = service.ai_extractor._detect_language(danish_text)
    print(f"Danish text detected as: {detected_lang}")
    
    # Test English text
    english_text = "My portfolio stocks value price shares quantity"
    detected_lang = service.ai_extractor._detect_language(english_text)
    print(f"English text detected as: {detected_lang}")
    
    # Test German text
    german_text = "Mein Portfolio Aktien Wert Preis Anzahl Stück"
    detected_lang = service.ai_extractor._detect_language(german_text)
    print(f"German text detected as: {detected_lang}")

if __name__ == "__main__":
    print("Enhanced Portfolio Import System Test")
    print("=" * 50)
    
    test_multilingual_extraction()
    print("\n" + "=" * 50)
    
    test_current_value_scenario()
    print("\n" + "=" * 50)
    
    test_currency_conversion()
    print("\n" + "=" * 50)
    
    test_language_detection()
    print("\n" + "=" * 50)
    
    print("Test completed!")
