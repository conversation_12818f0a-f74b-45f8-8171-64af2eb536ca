#!/usr/bin/env python3
"""
Test script to verify delete functionality with detailed logging.
"""

import requests
import time

def test_delete_with_session():
    """Test delete functionality with proper session handling."""
    
    base_url = "http://127.0.0.1:9878"
    
    print("🧪 Testing Delete Functionality with Session Tracking")
    print("=" * 60)
    
    # Use requests.Session to maintain cookies/session
    session = requests.Session()
    
    try:
        # Step 1: Import a test portfolio
        print("\n1. Importing Test Portfolio")
        
        test_portfolio = {
            'success': True,
            'portfolio': [
                {
                    'ticker': 'AAPL',
                    'amount_invested': 1000.0,
                    'buy_price': 150.0,
                    'shares': 6.67,
                    'currency': 'USD',
                    'amount_invested_currency': 'USD',
                    'buy_price_currency': 'USD',
                    'purchase_date': '2024-01-15'
                },
                {
                    'ticker': 'GOOGL',
                    'amount_invested': 2000.0,
                    'buy_price': 100.0,
                    'shares': 20.0,
                    'currency': 'USD',
                    'amount_invested_currency': 'USD',
                    'buy_price_currency': 'USD',
                    'purchase_date': '2024-02-10'
                }
            ],
            'cash_position': 500.0,
            'detected_currency': 'USD',
            'currency': 'USD',
            'selected_currency': 'USD'
        }
        
        import_response = session.post(
            f"{base_url}/api/import/confirm",
            json=test_portfolio,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if import_response.status_code == 200:
            print("   ✅ Portfolio imported successfully")
            
            # Step 2: Get portfolio page to establish session
            print("\n2. Loading Portfolio Page")
            
            portfolio_response = session.get(f"{base_url}/portfolio", timeout=30)
            
            if portfolio_response.status_code == 200:
                print("   ✅ Portfolio page loaded")
                
                # Check if both stocks are present
                portfolio_html = portfolio_response.text
                aapl_present = 'AAPL' in portfolio_html
                googl_present = 'GOOGL' in portfolio_html
                
                print(f"   AAPL present: {'✅' if aapl_present else '❌'}")
                print(f"   GOOGL present: {'✅' if googl_present else '❌'}")
                
                if aapl_present and googl_present:
                    # Step 3: Delete GOOGL
                    print("\n3. Deleting GOOGL Stock")
                    
                    delete_response = session.post(
                        f"{base_url}/delete_stock/GOOGL",
                        timeout=30,
                        allow_redirects=True  # Follow the redirect
                    )
                    
                    print(f"   Delete response status: {delete_response.status_code}")
                    
                    if delete_response.status_code == 200:
                        print("   ✅ Delete request completed")
                        
                        # Step 4: Check portfolio after deletion
                        print("\n4. Checking Portfolio After Deletion")
                        
                        # Wait a moment for session to be saved
                        time.sleep(2)
                        
                        portfolio_after_response = session.get(f"{base_url}/portfolio", timeout=30)
                        
                        if portfolio_after_response.status_code == 200:
                            portfolio_after_html = portfolio_after_response.text
                            
                            aapl_after = 'AAPL' in portfolio_after_html
                            googl_after = 'GOOGL' in portfolio_after_html
                            
                            print(f"   AAPL present: {'✅' if aapl_after else '❌'}")
                            print(f"   GOOGL present: {'❌ (deleted)' if not googl_after else '⚠️  STILL THERE!'}")
                            
                            if aapl_after and not googl_after:
                                print("   ✅ SUCCESS: Delete worked correctly!")
                            elif googl_after:
                                print("   ❌ FAILURE: GOOGL is still present after deletion")
                                print("   📋 This confirms the delete bug exists")
                            else:
                                print("   ⚠️  UNEXPECTED: Both stocks missing")
                                
                        else:
                            print(f"   ❌ Portfolio page failed after deletion: {portfolio_after_response.status_code}")
                            
                    else:
                        print(f"   ❌ Delete request failed: {delete_response.status_code}")
                        
                else:
                    print("   ❌ Test stocks not found in portfolio")
                    
            else:
                print(f"   ❌ Portfolio page failed: {portfolio_response.status_code}")
                
        else:
            print(f"   ❌ Portfolio import failed: {import_response.status_code}")
            print(f"   Response: {import_response.text}")
            
    except Exception as e:
        print(f"   ❌ Error during test: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Test completed!")
    print("\nCheck the Flask app logs for detailed DELETE DEBUG messages")
    print("This will help identify where the delete process is failing")

if __name__ == '__main__':
    test_delete_with_session()
