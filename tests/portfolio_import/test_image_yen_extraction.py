#!/usr/bin/env python3
"""
Test what happens when processing an image with Japanese Yen symbols
"""

import sys
sys.path.append('.')
from portfolio_import import PortfolioImportService

def test_image_yen_extraction():
    """Test Japanese Yen extraction from image-like data"""
    
    print("🖼️ Testing Image-based Japanese Yen Extraction")
    print("=" * 60)
    
    # This simulates what Gemini AI might extract from your image
    # Based on your screenshot, this is what the AI should see
    image_extracted_text = """
    Company                Value (¥)      Change %    Today       Price (USD)
    Shopify Inc.          ¥1,803,220     ▲ 13.71 %   ↑ 0.12 %    USD 85.77
    Palantir Tech.        ¥704300        ▼ 1.88%     → 0.00 %    $25.91
    Roblox Corp.          ¥1,020,050     ▲ 9.02%     ↑ 0.31%     39.67 USD
    Pinterest Inc.        ¥892,430       ▲0.96 %     ↑ 0.06%     43.11
    Block Inc.            ¥2,370,100     ▼ 4.20%     ↓ 0.21%     USD: 70.30
    """
    
    print("📝 Simulated Image Text Extraction:")
    print(image_extracted_text)
    print()
    
    # Test the full portfolio import service
    service = PortfolioImportService('test_key', 'test_key')
    
    print("1. TESTING PORTFOLIO EXTRACTION")
    print("-" * 40)
    
    try:
        # Extract portfolio data
        result = service.extract_portfolio_from_text(image_extracted_text)
        
        print(f"✅ Extraction Success: {result.success}")
        print(f"🌍 Detected Language: {service.ai_extractor.detected_language}")
        print(f"💱 Detected Currency: {service.ai_extractor.detected_currency}")
        
        # Check if mixed currency info exists
        if hasattr(service.ai_extractor, 'mixed_currency_info') and service.ai_extractor.mixed_currency_info:
            mixed_info = service.ai_extractor.mixed_currency_info
            print(f"🔄 Mixed Currency Info: {mixed_info.get('status', 'N/A')}")
            print(f"📊 Detected Currencies: {mixed_info.get('detected_currencies', [])}")
        
        # Format for API to see what the frontend would receive
        api_response = service.format_for_api(result)

        # Get portfolio entries from the result
        portfolio = result.portfolio if hasattr(result, 'portfolio') else []
        
        print(f"\n2. API RESPONSE ANALYSIS")
        print("-" * 30)
        
        print(f"🎯 API Success: {api_response.get('success', False)}")
        print(f"💰 API Detected Currency: {api_response.get('detected_currency', 'N/A')}")
        
        # Check currency info
        currency_info = api_response.get('currency_info', {})
        if currency_info:
            print(f"🔄 Requires User Selection: {currency_info.get('requires_user_selection', False)}")
            if currency_info.get('gemini_question'):
                print(f"❓ Gemini Question: {currency_info.get('gemini_question')}")
        
        # Check portfolio entries from API response
        api_portfolio = api_response.get('portfolio', [])
        print(f"\n3. PORTFOLIO ENTRIES ANALYSIS")
        print("-" * 35)
        
        print(f"📊 Number of entries: {len(api_portfolio)}")

        for i, entry in enumerate(api_portfolio, 1):
            ticker = entry.get('ticker', 'Unknown')
            amount = entry.get('amount_invested', 0)
            currency = entry.get('currency', 'Unknown')
            amount_currency = entry.get('amount_invested_currency', 'Unknown')
            
            print(f"\nEntry {i}: {ticker}")
            print(f"  Amount Invested: {amount}")
            print(f"  Currency: {currency}")
            print(f"  Amount Currency: {amount_currency}")
            
            # Check if Japanese Yen is correctly detected
            if currency == 'JPY' and amount_currency == 'JPY':
                print(f"  ✅ Japanese Yen correctly detected")
            elif currency == 'USD' or amount_currency == 'USD':
                print(f"  ❌ PROBLEM: Showing USD instead of JPY")
                print(f"     This is the bug you're experiencing!")
            else:
                print(f"  ⚠️  Unexpected currency: {currency}/{amount_currency}")
        
        print(f"\n4. EXPECTED VS ACTUAL")
        print("-" * 25)
        
        print("Expected results:")
        print("- All entries should have currency: 'JPY'")
        print("- All entries should have amount_invested_currency: 'JPY'")
        print("- Values should be preserved as Yen amounts")
        
        print("\nActual results:")
        if api_portfolio:
            actual_currencies = set(entry.get('currency', 'Unknown') for entry in api_portfolio)
            actual_amount_currencies = set(entry.get('amount_invested_currency', 'Unknown') for entry in api_portfolio)
            print(f"- Currencies found: {actual_currencies}")
            print(f"- Amount currencies found: {actual_amount_currencies}")
            
            if 'JPY' in actual_currencies and len(actual_currencies) == 1:
                print("🎉 SUCCESS: All entries correctly show JPY!")
            else:
                print("❌ FAILURE: Entries not showing JPY correctly")
                print("   This explains why your portfolio shows wrong currencies")
        
        return api_portfolio
        
    except Exception as e:
        print(f"❌ Error during extraction: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    portfolio = test_image_yen_extraction()
    
    if portfolio:
        print(f"\n🔧 DEBUGGING SUMMARY:")
        print("- Currency detection logic works (detects mixed JPY/USD)")
        print("- The issue might be in Gemini AI image interpretation")
        print("- Or in the portfolio entry processing logic")
        print("- Check if Gemini AI correctly reads ¥ symbols from images")
    else:
        print(f"\n❌ EXTRACTION FAILED - Check error messages above")
