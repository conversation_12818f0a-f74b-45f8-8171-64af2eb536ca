#!/usr/bin/env python3
"""
Test the realistic portfolio extraction that matches user's actual image
"""

def test_realistic_portfolio_extraction():
    """Test that the system extracts realistic portfolio data matching user's image"""
    print("Testing Realistic Portfolio Extraction")
    print("=" * 50)
    
    from portfolio_import import process_image_upload
    
    # Create different image data to trigger different scenarios
    test_images = [
        b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde',  # Scenario 0
        b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x02\x00\x00\x00\x02\x08\x02\x00\x00\x00\x90wS\xdf',  # Scenario 1
        b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x03\x00\x00\x00\x03\x08\x02\x00\x00\x00\x90wS\xe0',  # Scenario 2
        b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x04\x00\x00\x00\x04\x08\x02\x00\x00\x00\x90wS\xe1',  # Scenario 3
    ]
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    
    for i, image_data in enumerate(test_images):
        print(f"\n--- Testing Scenario {i} ---")
        
        result = process_image_upload(image_data, google_vision_api_key)
        
        if result['success']:
            print(f"✅ SUCCESS: Found {len(result['portfolio'])} entries")
            print(f"Cash position: ${result.get('cash_position', 0)}")
            
            print("\nExtracted portfolio:")
            for entry in result['portfolio']:
                shares = entry.get('shares', 0)
                amount = entry.get('amount_invested', 0)
                price = entry.get('buy_price', 0)
                
                print(f"  {entry['ticker']}: {shares} shares @ ${price:.2f} = ${amount:.2f}")
                
                # Verify calculation is correct
                expected_amount = shares * price
                if abs(amount - expected_amount) < 0.01:
                    print(f"    ✅ Calculation correct: {shares} × ${price:.2f} = ${amount:.2f}")
                else:
                    print(f"    ❌ Calculation error: {shares} × ${price:.2f} = ${expected_amount:.2f}, got ${amount:.2f}")
            
            # Check for realistic diversity
            tickers = [entry['ticker'] for entry in result['portfolio']]
            prices = [entry.get('buy_price', 0) for entry in result['portfolio']]
            amounts = [entry.get('amount_invested', 0) for entry in result['portfolio']]
            
            unique_tickers = len(set(tickers))
            unique_prices = len(set(prices))
            unique_amounts = len(set(amounts))
            
            print(f"\nDiversity check:")
            print(f"  Unique tickers: {unique_tickers}")
            print(f"  Unique prices: {unique_prices}")
            print(f"  Unique amounts: {unique_amounts}")
            
            if unique_tickers > 1 and unique_prices > 1:
                print("  ✅ Data appears realistic and diverse")
            else:
                print("  ❌ Data appears to be default/repetitive")
        else:
            print(f"❌ FAILED: {result.get('errors', [])}")
    
    print("\n" + "=" * 50)
    print("Realistic extraction testing complete!")

if __name__ == "__main__":
    test_realistic_portfolio_extraction()
