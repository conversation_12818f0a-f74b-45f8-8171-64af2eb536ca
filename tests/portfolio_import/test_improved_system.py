#!/usr/bin/env python3
"""
Test the improved portfolio import system
"""

from portfolio_import import PortfolioImportService, process_image_upload

def test_ocr_failure_scenario():
    """Test what happens when OCR fails"""
    print("🔍 Testing OCR Failure Scenario")
    print("=" * 40)
    
    # Simulate a real image that would fail OCR
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    
    print("Processing image with improved system...")
    result = process_image_upload(test_image_data, google_vision_api_key)
    
    print(f"\nResults:")
    print(f"Success: {result['success']}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    print(f"Errors: {len(result.get('errors', []))}")
    print(f"Warnings: {len(result.get('warnings', []))}")
    
    print(f"\nErrors:")
    for error in result.get('errors', []):
        print(f"  - {error}")
    
    print(f"\nWarnings:")
    for warning in result.get('warnings', []):
        print(f"  - {warning}")
    
    # Check that no fake data is returned
    portfolio = result.get('portfolio', [])
    if portfolio:
        print(f"\n❌ PROBLEM: System returned {len(portfolio)} portfolio entries when it should have returned none!")
        for entry in portfolio:
            print(f"  Fake entry: {entry}")
        return False
    else:
        print(f"\n✅ SUCCESS: System correctly returned no portfolio entries when OCR failed")
        return True

def test_successful_text_extraction():
    """Test with actual portfolio text"""
    print("\n🔍 Testing Successful Text Extraction")
    print("=" * 40)
    
    # Simulate successful OCR text that matches the user's image
    portfolio_text = """Alphabet Inc.
NasdaqGS:GOOGL 10 161 DKK 12,216.61 18.1%

ASML Holding N.V.
NasdaqGS:ASML 2 668.5 DKK 9,279.65 8.1%

Uber Technologies, Inc.
NYSE:UBER 10 74.59 DKK 5,851.40 22.1%

Amazon.com, Inc.
NasdaqGS:AMZN 8 186.92 DKK 11,790.22 22.7%"""
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    print("Processing successful OCR text...")
    result = service.extract_portfolio_from_text(portfolio_text)
    
    print(f"\nResults:")
    print(f"Success: {result.success}")
    print(f"Portfolio entries: {len(result.portfolio_entries)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Warnings: {len(result.warnings)}")
    
    if result.errors:
        print(f"\nErrors:")
        for error in result.errors:
            print(f"  - {error}")
    
    if result.portfolio_entries:
        print(f"\nPortfolio entries:")
        for entry in result.portfolio_entries:
            print(f"  {entry.ticker}: {entry.shares} shares @ {entry.buy_price} = {entry.amount_invested}")
        
        # Check if we got the expected tickers
        expected_tickers = ['GOOGL', 'ASML', 'UBER', 'AMZN']
        found_tickers = [entry.ticker for entry in result.portfolio_entries]
        
        if all(ticker in found_tickers for ticker in expected_tickers):
            print(f"\n✅ SUCCESS: Found all expected tickers: {found_tickers}")
            return True
        else:
            print(f"\n❌ PROBLEM: Missing tickers. Expected: {expected_tickers}, Found: {found_tickers}")
            return False
    else:
        print(f"\n❌ PROBLEM: No portfolio entries extracted from valid text")
        return False

def main():
    """Run all tests"""
    print("Testing Improved Portfolio Import System")
    print("=" * 50)
    
    test1_passed = test_ocr_failure_scenario()
    test2_passed = test_successful_text_extraction()
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"OCR Failure Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Successful Extraction Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! The system is working correctly.")
    else:
        print("\n❌ Some tests failed. The system needs more work.")

if __name__ == "__main__":
    main()
