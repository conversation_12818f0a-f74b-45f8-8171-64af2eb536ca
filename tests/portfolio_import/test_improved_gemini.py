#!/usr/bin/env python3
"""
Test the improved Gemini AI portfolio import system
"""

import os
import sys

# Set the Google API key for testing
os.environ['GOOGLE_API_KEY'] = 'AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o'

from portfolio_import import AIPortfolioExtractor

def test_improved_gemini_extraction():
    """Test the improved Gemini extraction with the user's specific format"""
    print("Testing Improved Gemini AI Portfolio Extraction")
    print("=" * 50)
    
    # Test data that matches the user's description
    test_text = """
    Currency Settings

    Portfolio Currency: USD

    Add New Entry:

    Add Stock
    Ticker	Amount Invested	Buy Price	Shares	Purchase Date	Actions
    AAPL    $2,100.93       $161.61     13      2023-06-15      
    TSLA    $1,850.00       $185.00     10      2023-05-20      
    MSFT    $3,250.75       $325.08     10      2023-04-10      
    
    Import to Portfolio

    Cancel
    """
    
    # Initialize the extractor
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    # Test the extraction
    result = extractor.extract_portfolio_data_with_ai(test_text)
    
    print(f"Extraction Result:")
    print(f"Success: {result['success']}")
    print(f"Method: {result.get('extraction_method', 'Unknown')}")
    print(f"Detected Language: {result.get('detected_language', 'Unknown')}")
    print(f"Detected Currency: {result.get('detected_currency', 'Unknown')}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Warnings: {result.get('warnings', [])}")
    
    if result['success']:
        portfolio = result.get('portfolio', [])
        print(f"\nExtracted {len(portfolio)} portfolio entries:")
        
        for i, entry in enumerate(portfolio, 1):
            print(f"\n{i}. {entry.get('ticker', 'Unknown')}")
            print(f"   Shares: {entry.get('shares', 0)}")
            print(f"   Buy Price: ${entry.get('buy_price', 0):.2f}")
            print(f"   Amount Invested: ${entry.get('amount_invested', 0):.2f}")
            print(f"   Current Value: ${entry.get('current_value', 0):.2f}")
            print(f"   Purchase Date: {entry.get('purchase_date', 'Unknown')}")
            print(f"   Currency: {entry.get('currency', 'USD')}")
        
        cash_position = result.get('cash_position', 0)
        if cash_position > 0:
            print(f"\nCash Position: ${cash_position:.2f}")
    else:
        print("Extraction failed!")

def test_danish_format():
    """Test with Danish portfolio format"""
    print("\n" + "=" * 50)
    print("Testing Danish Portfolio Format")
    print("=" * 50)
    
    # Danish format test
    danish_text = """
    Portefølje Oversigt
    
    AAPL - Apple Inc
    13 stk
    GAK: 161,61 USD
    Markedsværdi: 2.462,85 USD
    
    TSLA - Tesla Inc  
    10 stk
    GAK: 185,00 USD
    Markedsværdi: 1.890,50 USD
    
    Total værdi: 4.353,35 USD
    """
    
    # Initialize the extractor
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    # Test the extraction
    result = extractor.extract_portfolio_data_with_ai(danish_text)
    
    print(f"Extraction Result:")
    print(f"Success: {result['success']}")
    print(f"Method: {result.get('extraction_method', 'Unknown')}")
    print(f"Detected Language: {result.get('detected_language', 'Unknown')}")
    print(f"Detected Currency: {result.get('detected_currency', 'Unknown')}")
    
    if result['success']:
        portfolio = result.get('portfolio', [])
        print(f"\nExtracted {len(portfolio)} portfolio entries:")
        
        for i, entry in enumerate(portfolio, 1):
            print(f"\n{i}. {entry.get('ticker', 'Unknown')}")
            print(f"   Shares: {entry.get('shares', 0)}")
            print(f"   Buy Price: ${entry.get('buy_price', 0):.2f}")
            print(f"   Amount Invested: ${entry.get('amount_invested', 0):.2f}")
            print(f"   Current Value: ${entry.get('current_value', 0):.2f}")

def test_financial_value_parsing():
    """Test the improved financial value parsing"""
    print("\n" + "=" * 50)
    print("Testing Financial Value Parsing")
    print("=" * 50)
    
    # Initialize the extractor
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    test_values = [
        "2.462,85",      # European format
        "1,234.56",      # US format
        "$2,100.93",     # US with currency
        "161,61 USD",    # European with currency
        "13 stk",        # Shares
        "185.00",        # Simple decimal
        "1.890,50 USD",  # European with currency
    ]
    
    print("Testing value parsing:")
    for value in test_values:
        parsed = extractor._parse_financial_value(value)
        print(f"'{value}' → {parsed}")

if __name__ == "__main__":
    test_improved_gemini_extraction()
    test_danish_format()
    test_financial_value_parsing()
