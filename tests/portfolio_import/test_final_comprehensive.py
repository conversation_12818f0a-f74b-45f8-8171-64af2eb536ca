#!/usr/bin/env python3
"""
Final comprehensive test to verify all portfolio import issues are resolved
"""

import io
import csv
from portfolio_import import process_spreadsheet_upload, process_image_upload

def test_original_user_issues():
    """Test the original issues reported by the user"""
    print("🔍 Testing Original User Issues")
    print("=" * 50)
    
    print("1. Testing DKK ticker issue (from image)...")
    
    # Simulate the user's image OCR text
    user_image_text = """
Ticker Shares Avg. Cost Basis Market Value (DKK) % Chg.

Alphabet Inc.
NasdaqGS:GOOGL 10 161 DKK 12,216.61 18.1%

ASML Holding N.V.
NasdaqGS:ASML 2 668.5 DKK 9,279.65 8.1%

Uber Technologies, Inc.
NYSE:UBER 10 74.59 DKK 5,851.40 22.1%

Amazon.com, Inc.
NasdaqGS:AMZN 8 186.92 DKK 11,790.22 22.7%
"""
    
    image_data = b'PORTFOLIO_IMAGE_DATA' + user_image_text.encode() + b'x' * 1000
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    
    result = process_image_upload(image_data, google_vision_api_key)
    
    portfolio = result.get('portfolio', [])
    found_tickers = [entry.get('ticker') for entry in portfolio]
    
    # Check for DKK issue
    if 'DKK' in found_tickers:
        print("❌ FAILED: Still finding DKK as ticker")
        return False
    
    # Check for correct tickers
    expected_tickers = ['GOOGL', 'ASML', 'UBER', 'AMZN']
    missing_tickers = [t for t in expected_tickers if t not in found_tickers]
    
    if missing_tickers:
        print(f"❌ FAILED: Missing tickers {missing_tickers}")
        return False
    
    print("✅ SUCCESS: DKK issue resolved, correct tickers found")
    
    print("\n2. Testing single letter 'a' issue...")
    
    # Test the 'a' case that was failing
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerow(['A', 'B', 'C', 'D'])
    writer.writerow(['a', '1500.00', '150.50', '2023-04-15'])
    
    csv_content = output.getvalue().encode('utf-8')
    result = process_spreadsheet_upload(csv_content, 'test_a.csv', google_vision_api_key)
    
    if not result['success']:
        print(f"❌ FAILED: {result.get('errors', [])}")
        return False
    
    portfolio = result.get('portfolio', [])
    if not portfolio or portfolio[0].get('ticker') != 'A':
        print("❌ FAILED: 'a' not converted to 'A'")
        return False
    
    print("✅ SUCCESS: Single letter 'a' issue resolved")
    
    return True

def test_company_name_support():
    """Test company name recognition"""
    print(f"\n🔍 Testing Company Name Support")
    print("=" * 50)
    
    # Create test CSV with company names
    output = io.StringIO()
    writer = csv.writer(output)
    
    writer.writerow(['Company', 'Amount', 'Price', 'Date'])
    writer.writerow(['Apple Inc.', '1500.00', '150.50', '2023-04-15'])
    writer.writerow(['Microsoft Corporation', '2000.00', '250.75', '2023-03-20'])
    writer.writerow(['Alphabet Inc.', '1200.00', '120.25', '2023-05-10'])
    writer.writerow(['ASML Holding N.V.', '800.00', '400.00', '2023-06-01'])
    writer.writerow(['Tesla', '600.00', '200.00', '2023-07-01'])
    
    csv_content = output.getvalue().encode('utf-8')
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    
    result = process_spreadsheet_upload(csv_content, 'test_companies.csv', google_vision_api_key)
    
    if not result['success']:
        print(f"❌ FAILED: {result.get('errors', [])}")
        return False
    
    portfolio = result.get('portfolio', [])
    expected_tickers = ['AAPL', 'MSFT', 'GOOGL', 'ASML', 'TSLA']
    found_tickers = [entry.get('ticker') for entry in portfolio]
    
    missing_tickers = [t for t in expected_tickers if t not in found_tickers]
    if missing_tickers:
        print(f"❌ FAILED: Missing tickers {missing_tickers}")
        print(f"Found: {found_tickers}")
        return False
    
    print("✅ SUCCESS: Company names correctly converted to tickers")
    return True

def test_error_handling():
    """Test error handling for invalid data"""
    print(f"\n🔍 Testing Error Handling")
    print("=" * 50)
    
    # Test with unknown company
    output = io.StringIO()
    writer = csv.writer(output)
    
    writer.writerow(['Company', 'Amount', 'Price', 'Date'])
    writer.writerow(['Unknown Company XYZ', '1000.00', '100.00', '2023-04-15'])
    
    csv_content = output.getvalue().encode('utf-8')
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    
    result = process_spreadsheet_upload(csv_content, 'test_unknown.csv', google_vision_api_key)
    
    errors = result.get('errors', [])
    has_helpful_error = any('Unknown company name' in error for error in errors)
    
    if not has_helpful_error:
        print("❌ FAILED: No helpful error message for unknown company")
        return False
    
    print("✅ SUCCESS: Helpful error messages for unknown companies")
    return True

def test_mixed_formats():
    """Test mixed ticker and company name formats"""
    print(f"\n🔍 Testing Mixed Formats")
    print("=" * 50)
    
    # Create test CSV with mixed formats
    output = io.StringIO()
    writer = csv.writer(output)
    
    writer.writerow(['Symbol', 'Amount', 'Price', 'Date'])
    writer.writerow(['AAPL', '1500.00', '150.50', '2023-04-15'])  # Already a ticker
    writer.writerow(['Microsoft Corp', '2000.00', '250.75', '2023-03-20'])  # Company name
    writer.writerow(['GOOGL', '1200.00', '120.25', '2023-05-10'])  # Already a ticker
    writer.writerow(['Tesla Inc.', '800.00', '400.00', '2023-06-01'])  # Company name
    
    csv_content = output.getvalue().encode('utf-8')
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    
    result = process_spreadsheet_upload(csv_content, 'test_mixed.csv', google_vision_api_key)
    
    if not result['success']:
        print(f"❌ FAILED: {result.get('errors', [])}")
        return False
    
    portfolio = result.get('portfolio', [])
    expected_tickers = ['AAPL', 'MSFT', 'GOOGL', 'TSLA']
    found_tickers = [entry.get('ticker') for entry in portfolio]
    
    if set(found_tickers) != set(expected_tickers):
        print(f"❌ FAILED: Expected {expected_tickers}, got {found_tickers}")
        return False
    
    print("✅ SUCCESS: Mixed ticker and company name formats handled correctly")
    return True

def main():
    print("🚀 FINAL COMPREHENSIVE PORTFOLIO IMPORT TEST")
    print("=" * 70)
    print("Testing all reported issues and new company name functionality")
    print()
    
    test1 = test_original_user_issues()
    test2 = test_company_name_support()
    test3 = test_error_handling()
    test4 = test_mixed_formats()
    
    print("\n" + "=" * 70)
    print("📋 FINAL RESULTS:")
    
    if test1 and test2 and test3 and test4:
        print("🎉 COMPLETE SUCCESS! All issues resolved!")
        print()
        print("✅ Original DKK ticker issue FIXED")
        print("✅ Single letter 'a' issue FIXED")
        print("✅ Company name recognition WORKING")
        print("✅ Error handling IMPROVED")
        print("✅ Mixed formats SUPPORTED")
        print()
        print("🎯 Users can now:")
        print("   • Upload portfolio images without DKK ticker confusion")
        print("   • Use company names like 'Apple Inc.' -> AAPL")
        print("   • Use single letter tickers like 'a' -> A")
        print("   • Mix ticker symbols and company names")
        print("   • Get helpful error messages for unknown companies")
        print()
        print("🚀 Portfolio import is now FULLY FUNCTIONAL and USER-FRIENDLY!")
        
    else:
        print("❌ Some issues remain:")
        if not test1:
            print("   - Original user issues not fully resolved")
        if not test2:
            print("   - Company name support issues")
        if not test3:
            print("   - Error handling issues")
        if not test4:
            print("   - Mixed format issues")

if __name__ == "__main__":
    main()
