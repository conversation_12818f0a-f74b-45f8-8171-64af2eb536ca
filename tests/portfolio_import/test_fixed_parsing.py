#!/usr/bin/env python3
"""
Test the fixed parsing logic with the actual OCR text
"""

from portfolio_import import PortfolioImportService

def test_fixed_parsing():
    """Test with the actual OCR text that was extracted"""
    print("🔍 Testing Fixed Parsing Logic")
    print("=" * 50)
    
    # This is the actual OCR text that was extracted from the user's image
    actual_ocr_text = """Alphabet Inc.
. . .
10      161
NasdaqGS:GOOGL
ASML Holding N.V.
:::     2       668.5
NasdaqGS:ASML
Uber Technologies, Inc.
:::     10      74.59
NYSE:UBER
Amazon.com, Inc.
a       X       8       186.92
NasdaqGS:AMZN"""
    
    print("OCR Text:")
    print(actual_ocr_text)
    print("\n" + "=" * 50)
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    print("Processing with improved parsing logic...")
    result = service.extract_portfolio_from_text(actual_ocr_text)
    
    print(f"\nResults:")
    print(f"Success: {result.success}")
    print(f"Portfolio entries: {len(result.portfolio_entries)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Warnings: {len(result.warnings)}")
    
    if result.errors:
        print(f"\nErrors:")
        for error in result.errors:
            print(f"  - {error}")
    
    if result.portfolio_entries:
        print(f"\nExtracted Portfolio:")
        print(f"{'Ticker':<8} {'Shares':<8} {'Price':<10} {'Amount':<12}")
        print("-" * 40)
        
        total_amount = 0
        for entry in result.portfolio_entries:
            print(f"{entry.ticker:<8} {entry.shares:<8.1f} ${entry.buy_price:<9.2f} ${entry.amount_invested:<11.2f}")
            total_amount += entry.amount_invested
        
        print("-" * 40)
        print(f"{'Total':<27} ${total_amount:<11.2f}")
        
        # Check against expected values
        expected_data = {
            'GOOGL': {'shares': 10, 'price': 161},
            'ASML': {'shares': 2, 'price': 668.5},
            'UBER': {'shares': 10, 'price': 74.59},
            'AMZN': {'shares': 8, 'price': 186.92}
        }
        
        print(f"\n" + "=" * 50)
        print("VALIDATION:")
        
        all_correct = True
        for entry in result.portfolio_entries:
            ticker = entry.ticker
            if ticker in expected_data:
                expected_shares = expected_data[ticker]['shares']
                expected_price = expected_data[ticker]['price']
                
                shares_correct = abs(entry.shares - expected_shares) < 0.1
                price_correct = abs(entry.buy_price - expected_price) < 0.1
                
                if shares_correct and price_correct:
                    print(f"✅ {ticker}: Correct ({entry.shares} shares @ ${entry.buy_price})")
                else:
                    print(f"❌ {ticker}: Wrong - Expected {expected_shares} shares @ ${expected_price}, Got {entry.shares} shares @ ${entry.buy_price}")
                    all_correct = False
            else:
                print(f"❓ {ticker}: Unexpected ticker")
                all_correct = False
        
        # Check for missing tickers
        found_tickers = [entry.ticker for entry in result.portfolio_entries]
        for expected_ticker in expected_data:
            if expected_ticker not in found_tickers:
                print(f"❌ Missing ticker: {expected_ticker}")
                all_correct = False
        
        if all_correct:
            print(f"\n🎉 SUCCESS: All data extracted correctly!")
        else:
            print(f"\n❌ ISSUES: Some data was extracted incorrectly")
            
        return all_correct
    else:
        print(f"\n❌ PROBLEM: No portfolio entries extracted")
        return False

def test_clean_text():
    """Test with clean, well-formatted text"""
    print("\n🔍 Testing with Clean Text")
    print("=" * 50)
    
    clean_text = """Alphabet Inc.
NasdaqGS:GOOGL 10 161

ASML Holding N.V.
NasdaqGS:ASML 2 668.5

Uber Technologies, Inc.
NYSE:UBER 10 74.59

Amazon.com, Inc.
NasdaqGS:AMZN 8 186.92"""
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    service = PortfolioImportService(google_vision_api_key)
    
    print("Processing clean text...")
    result = service.extract_portfolio_from_text(clean_text)
    
    if result.success and result.portfolio_entries:
        print(f"✅ Clean text extracted {len(result.portfolio_entries)} entries correctly")
        for entry in result.portfolio_entries:
            print(f"   {entry.ticker}: {entry.shares} shares @ ${entry.buy_price}")
        return True
    else:
        print(f"❌ Clean text extraction failed")
        return False

def main():
    """Run the parsing tests"""
    print("Testing Fixed Portfolio Parsing Logic")
    print("=" * 60)
    
    test1_passed = test_fixed_parsing()
    test2_passed = test_clean_text()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"OCR Text Parsing: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Clean Text Parsing: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 Parsing logic is working correctly!")
    else:
        print("\n❌ Parsing logic still needs improvement")

if __name__ == "__main__":
    main()
