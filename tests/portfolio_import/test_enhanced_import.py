#!/usr/bin/env python3
"""
Test the enhanced portfolio import system with currency support and better ticker extraction
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService, AIPortfolioExtractor, convert_company_name_to_ticker

def test_alphabet_ticker_extraction():
    """Test that 'Alphabet A' correctly maps to GOOGL, not A"""
    print("=== Testing Alphabet Ticker Extraction ===")
    
    # Test the company name to ticker conversion
    test_cases = [
        ("Alphabet A", "GOOGL"),
        ("Alphabet", "GOOGL"),
        ("alphabet a", "GOOGL"),
        ("ALPHABET A", "GOOGL"),
        ("Alphabet Inc", "GOOGL"),
        ("Google", "GOOGL"),
        ("Amazon.com", "AMZN"),
        ("ASML Holding", "ASML"),
        ("Uber Technologies", "UBER"),
    ]
    
    for company_name, expected_ticker in test_cases:
        result = convert_company_name_to_ticker(company_name)
        status = "✅ PASS" if result == expected_ticker else "❌ FAIL"
        print(f"  {company_name} -> {result} (expected: {expected_ticker}) {status}")

def test_currency_conversion():
    """Test currency conversion functionality"""
    print("\n=== Testing Currency Conversion ===")

    extractor = AIPortfolioExtractor("test_key")

    # Test DKK to USD conversion (your image showed DKK values)
    dkk_amount = 15848  # From your image: "15.848 kr"
    usd_amount = extractor._convert_to_usd(dkk_amount, 'DKK')
    print(f"  DKK {dkk_amount:,.2f} -> USD ${usd_amount:,.2f}")

    # Test other currencies
    test_conversions = [
        (1000, 'EUR'),
        (1000, 'GBP'),
        (100000, 'JPY'),
        (1000, 'DKK'),
    ]

    for amount, from_curr in test_conversions:
        converted = extractor._convert_to_usd(amount, from_curr)
        print(f"  {from_curr} {amount:,.2f} -> USD ${converted:,.2f}")

def test_portfolio_extraction():
    """Test portfolio extraction with the user's actual data"""
    print("\n=== Testing Portfolio Extraction ===")
    
    # Simulate the text from your image
    test_text = """
    Alphabet A
    Værdi: 15.848 kr
    Afkast: +10,77%
    I dag: +0,38%
    Seneste: 192,06 USD
    
    Amazon.com
    Værdi: 18.858 kr
    Afkast: +17,21%
    I dag: +0,36%
    Seneste: 228,29 USD
    
    ASML Holding
    Værdi: 31.962 kr
    Afkast: -4,27%
    I dag: +1,78%
    Seneste: 718,07 USD
    
    Uber Technologies
    Værdi: 5.886 kr
    Afkast: +11,14%
    I dag: +0,56%
    Seneste: 92,30 USD
    """
    
    service = PortfolioImportService("test_key")
    service.detected_currency = 'DKK'  # Set detected currency
    
    result = service.extract_portfolio_from_text(test_text)
    
    print(f"  Success: {result.success}")
    print(f"  Entries found: {len(result.portfolio_entries)}")
    print(f"  Errors: {result.errors}")
    print(f"  Warnings: {result.warnings}")
    
    for entry in result.portfolio_entries:
        print(f"    {entry.ticker}: ${entry.amount_invested:.2f} invested, {entry.shares:.2f} shares @ ${entry.buy_price:.2f}")

def test_editable_portfolio_data():
    """Test that portfolio data structure supports editing"""
    print("\n=== Testing Editable Portfolio Data ===")
    
    # Sample portfolio entry
    portfolio_entry = {
        'ticker': 'GOOGL',
        'amount_invested': 14313.20,
        'buy_price': 173.46,
        'shares': 82.52,
        'purchase_date': '2025-01-22',
        'currency': 'USD'
    }
    
    print("  Original entry:")
    for key, value in portfolio_entry.items():
        print(f"    {key}: {value}")
    
    # Test editing
    portfolio_entry['ticker'] = 'AAPL'
    portfolio_entry['amount_invested'] = 15000.00
    portfolio_entry['buy_price'] = 180.00
    
    print("  After editing:")
    for key, value in portfolio_entry.items():
        print(f"    {key}: {value}")
    
    print("  ✅ Portfolio data is editable")

if __name__ == "__main__":
    print("🚀 Testing Enhanced Portfolio Import System\n")
    
    test_alphabet_ticker_extraction()
    test_currency_conversion()
    test_portfolio_extraction()
    test_editable_portfolio_data()
    
    print("\n✅ All tests completed!")
    print("\n📋 Summary of Enhancements:")
    print("  ✅ Fixed 'Alphabet A' -> 'GOOGL' mapping (not 'A')")
    print("  ✅ Added comprehensive currency support (30+ currencies)")
    print("  ✅ Added editable portfolio import results")
    print("  ✅ Added currency selection in portfolio import")
    print("  ✅ Added currency display options in main portfolio")
    print("  ✅ Added 'Add New Entry' and 'Remove Entry' functionality")
    print("  ✅ Enhanced company name to ticker mapping")
