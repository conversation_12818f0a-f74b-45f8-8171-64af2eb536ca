#!/usr/bin/env python3
"""
Test the universal multilingual false positive filtering
"""

from portfolio_import import PortfolioImportService

def test_multilingual_false_positives():
    """Test with various languages that should NOT extract false tickers"""
    print("🌍 Testing Universal Multilingual False Positive Filtering")
    print("=" * 70)
    
    # Test cases for different languages
    test_cases = [
        {
            "language": "Danish",
            "text": """
            AAPL
            Mine beholdninger
            Antal: 10 stk
            GAK: 150,00 USD
            Markedsværdi: 1.500,00 USD
            """,
            "expected_ticker": "AAPL",
            "false_positives": ["GAK", "STK", "ANTAL"]
        },
        {
            "language": "French", 
            "text": """
            MSFT
            Mes participations
            Quantité: 5 pcs
            Prix: 300,00 EUR
            Valeur: 1.500,00 EUR
            """,
            "expected_ticker": "MSFT",
            "false_positives": ["PCS", "PRIX", "EUR"]
        },
        {
            "language": "German",
            "text": """
            TSLA
            Meine Bestände
            Anzahl: 8 Stk
            Preis: 200,00 EUR
            Wert: 1.600,00 EUR
            """,
            "expected_ticker": "TSLA", 
            "false_positives": ["STK", "PREIS", "WERT", "EUR"]
        },
        {
            "language": "Spanish",
            "text": """
            GOOGL
            Mis participaciones
            Cantidad: 12 uds
            Precio: 140,00 USD
            Valor: 1.680,00 USD
            """,
            "expected_ticker": "GOOGL",
            "false_positives": ["UDS", "PRECIO", "VALOR", "USD"]
        },
        {
            "language": "Italian",
            "text": """
            AMZN
            Le mie partecipazioni
            Quantità: 3 pz
            Prezzo: 150,00 EUR
            Valore: 450,00 EUR
            """,
            "expected_ticker": "AMZN",
            "false_positives": ["PREZZO", "VALORE", "EUR"]
        }
    ]
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = "673b0b8b8b8b8b.12345678"
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
    
    all_tests_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing {test_case['language']} Language")
        print("-" * 50)
        print(f"Text: {test_case['text'].strip()}")
        
        # Process the text
        result = service.extract_portfolio_from_text(test_case['text'])
        
        # Check results
        found_tickers = [entry.ticker for entry in result.portfolio_entries]
        print(f"Found tickers: {found_tickers}")
        
        # Check if expected ticker was found
        expected_found = test_case['expected_ticker'] in found_tickers
        
        # Check for false positives
        false_positives_found = [fp for fp in test_case['false_positives'] if fp in found_tickers]
        
        # Results
        if expected_found and not false_positives_found:
            print(f"✅ {test_case['language']}: PASSED")
            print(f"   ✅ Found expected ticker: {test_case['expected_ticker']}")
            print(f"   ✅ No false positives detected")
        else:
            print(f"❌ {test_case['language']}: FAILED")
            if not expected_found:
                print(f"   ❌ Missing expected ticker: {test_case['expected_ticker']}")
            if false_positives_found:
                print(f"   ❌ False positives found: {false_positives_found}")
            all_tests_passed = False
    
    print("\n" + "=" * 70)
    print("FINAL MULTILINGUAL TEST RESULTS:")
    print("=" * 70)
    
    if all_tests_passed:
        print("🎉 SUCCESS: Universal multilingual filtering works!")
        print("✅ All languages correctly filter false positives")
        print("✅ Real tickers extracted correctly across all languages")
        print("\n🌍 UNIVERSAL LANGUAGE SUPPORT CONFIRMED!")
    else:
        print("❌ SOME TESTS FAILED:")
        print("❌ Multilingual false positive filtering needs improvement")
    
    return all_tests_passed

def test_edge_cases():
    """Test edge cases and tricky scenarios"""
    print("\n" + "=" * 70)
    print("🔍 Testing Edge Cases")
    print("=" * 70)
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = "673b0b8b8b8b8b.12345678"
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
    
    edge_cases = [
        {
            "name": "Mixed Languages",
            "text": "AAPL shares: 10 stk, prix: 150 USD, valor: 1500",
            "expected": ["AAPL"],
            "forbidden": ["STK", "PRIX", "VALOR", "USD"]
        },
        {
            "name": "Currency Codes",
            "text": "MSFT 5 shares USD 300 EUR 250 GBP 200",
            "expected": ["MSFT"], 
            "forbidden": ["USD", "EUR", "GBP"]
        },
        {
            "name": "Short Words",
            "text": "GOOGL stock AT 140 TO buy OR sell",
            "expected": ["GOOGL"],
            "forbidden": ["AT", "TO", "OR"]  # These should be rejected as too short/common
        }
    ]
    
    all_passed = True
    
    for case in edge_cases:
        print(f"\nTesting: {case['name']}")
        print(f"Text: {case['text']}")
        
        result = service.extract_portfolio_from_text(case['text'])
        found_tickers = [entry.ticker for entry in result.portfolio_entries]
        
        print(f"Found: {found_tickers}")
        
        # Check expected tickers
        expected_found = all(ticker in found_tickers for ticker in case['expected'])
        
        # Check forbidden tickers
        forbidden_found = [ticker for ticker in case['forbidden'] if ticker in found_tickers]
        
        if expected_found and not forbidden_found:
            print(f"✅ PASSED")
        else:
            print(f"❌ FAILED")
            if not expected_found:
                print(f"   Missing expected: {case['expected']}")
            if forbidden_found:
                print(f"   Found forbidden: {forbidden_found}")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("Testing Universal Multilingual False Positive Filtering")
    print("=" * 70)
    
    # Test 1: Multiple languages
    test1_success = test_multilingual_false_positives()
    
    # Test 2: Edge cases
    test2_success = test_edge_cases()
    
    print("\n" + "=" * 70)
    print("COMPREHENSIVE TEST RESULTS:")
    print("=" * 70)
    
    if test1_success and test2_success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Universal multilingual filtering works perfectly")
        print("✅ Edge cases handled correctly")
        print("✅ False positives eliminated across all languages")
        print("\n🌍 READY FOR GLOBAL USE!")
        print("\n🎯 USER'S ISSUE IS COMPLETELY RESOLVED!")
    else:
        print("❌ SOME TESTS FAILED:")
        if not test1_success:
            print("❌ Multilingual filtering needs work")
        if not test2_success:
            print("❌ Edge case handling needs work")
