#!/usr/bin/env python3
"""
Test the new Gemini AI-powered portfolio import system
"""

import os
import sys
from portfolio_import import AIPortfolioExtractor

def test_gemini_portfolio_extraction():
    """Test Gemini AI portfolio extraction with sample data"""
    print("🚀 TESTING GEMINI AI PORTFOLIO EXTRACTION")
    print("=" * 60)
    
    # Sample portfolio text (similar to what OCR might extract)
    sample_text = """
    Apple Inc.
    NASDAQ:AAPL
    10 shares
    $180.50 avg cost
    Current value: $1,950.00
    
    Microsoft Corporation
    NASDAQ:MSFT
    5 shares
    Buy price: $320.00
    Market value: $1,750.00
    
    Tesla Inc
    NASDAQ:TSLA
    3 shares
    Average cost: $245.00
    Current: $735.00
    
    Cash position: $2,500.00
    """
    
    print("Sample text to extract:")
    print("-" * 40)
    print(sample_text)
    print("-" * 40)
    
    # Test with Google Vision API key
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    
    # Create extractor
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    # Test extraction
    try:
        result = extractor.extract_portfolio_data_with_ai(sample_text)
        
        print(f"\n📊 EXTRACTION RESULTS:")
        print(f"Success: {result['success']}")
        print(f"Method: {result.get('extraction_method', 'unknown')}")
        print(f"Detected Language: {result.get('detected_language', 'unknown')}")
        print(f"Detected Currency: {result.get('detected_currency', 'unknown')}")
        
        if result['success']:
            portfolio = result.get('portfolio', [])
            print(f"\n💼 PORTFOLIO ENTRIES ({len(portfolio)} found):")
            
            for i, entry in enumerate(portfolio, 1):
                print(f"\n{i}. {entry.get('ticker', 'Unknown')}")
                print(f"   Amount Invested: ${entry.get('amount_invested', 0):.2f}")
                print(f"   Buy Price: ${entry.get('buy_price', 0):.2f}")
                print(f"   Shares: {entry.get('shares', 0)}")
                print(f"   Current Value: ${entry.get('current_value', 0):.2f}")
                print(f"   Purchase Date: {entry.get('purchase_date', 'Unknown')}")
            
            cash_position = result.get('cash_position', 0)
            print(f"\n💰 Cash Position: ${cash_position:.2f}")
            
            # Calculate totals
            total_invested = sum(entry.get('amount_invested', 0) for entry in portfolio)
            total_current = sum(entry.get('current_value', 0) for entry in portfolio)
            
            print(f"\n📈 PORTFOLIO SUMMARY:")
            print(f"Total Invested: ${total_invested:.2f}")
            print(f"Total Current Value: ${total_current:.2f}")
            print(f"Total Gain/Loss: ${total_current - total_invested:.2f}")
            print(f"Total Return: {((total_current - total_invested) / total_invested * 100):.2f}%" if total_invested > 0 else "N/A")
            
        else:
            print(f"\n❌ EXTRACTION FAILED:")
            for error in result.get('errors', []):
                print(f"   - {error}")
        
        if result.get('warnings'):
            print(f"\n⚠️  WARNINGS:")
            for warning in result.get('warnings', []):
                print(f"   - {warning}")
                
        return result['success']
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gemini_with_danish_portfolio():
    """Test with Danish portfolio format"""
    print("\n\n🇩🇰 TESTING DANISH PORTFOLIO FORMAT")
    print("=" * 60)
    
    danish_text = """
    Alphabet Inc.
    NasdaqGS:GOOGL
    10 stk
    GAK 161.61 USD
    Markedsværdi 2.462,85 USD
    
    ASML Holding N.V.
    NasdaqGS:ASML
    2 stk
    GAK 668.50 USD
    Markedsværdi 1.337,00 USD
    
    Kontanter: DKK 5.000,00
    """
    
    print("Danish text to extract:")
    print("-" * 40)
    print(danish_text)
    print("-" * 40)
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    try:
        result = extractor.extract_portfolio_data_with_ai(danish_text)
        
        print(f"\n📊 DANISH EXTRACTION RESULTS:")
        print(f"Success: {result['success']}")
        print(f"Method: {result.get('extraction_method', 'unknown')}")
        
        if result['success']:
            portfolio = result.get('portfolio', [])
            print(f"\n💼 PORTFOLIO ENTRIES ({len(portfolio)} found):")
            
            for i, entry in enumerate(portfolio, 1):
                print(f"\n{i}. {entry.get('ticker', 'Unknown')}")
                print(f"   Amount Invested: ${entry.get('amount_invested', 0):.2f}")
                print(f"   Buy Price: ${entry.get('buy_price', 0):.2f}")
                print(f"   Shares: {entry.get('shares', 0)}")
        
        return result['success']
        
    except Exception as e:
        print(f"\n❌ DANISH TEST FAILED: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing Gemini AI Portfolio Import System")
    print("This test requires a valid Google API key with Gemini access")
    print()
    
    # Check if we have the required API key
    api_key = os.environ.get('GOOGLE_API_KEY') or os.environ.get('GEMINI_API_KEY')
    if not api_key:
        print("⚠️  No GOOGLE_API_KEY or GEMINI_API_KEY found in environment")
        print("   The test will try to use the hardcoded key, but Gemini extraction may fail")
        print()
    
    # Run tests
    test1_success = test_gemini_portfolio_extraction()
    test2_success = test_gemini_with_danish_portfolio()
    
    print("\n" + "=" * 60)
    print("📋 FINAL TEST RESULTS:")
    print(f"✅ Basic Portfolio Test: {'PASSED' if test1_success else 'FAILED'}")
    print(f"✅ Danish Portfolio Test: {'PASSED' if test2_success else 'FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 ALL TESTS PASSED! Gemini AI portfolio extraction is working!")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
        print("   Note: Gemini AI extraction requires a valid API key and may fall back to regex-based extraction.")

if __name__ == "__main__":
    main()
