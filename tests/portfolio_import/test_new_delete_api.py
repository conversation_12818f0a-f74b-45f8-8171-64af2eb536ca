#!/usr/bin/env python3
"""
Test script to verify the new delete API functionality.
"""

import requests
import time

def test_new_delete_api():
    """Test the new delete API functionality."""
    
    base_url = "http://127.0.0.1:9878"
    
    print("🧪 Testing New Delete API Functionality")
    print("=" * 50)
    
    # Use requests.Session to maintain cookies/session
    session = requests.Session()
    
    try:
        # Step 1: Import test portfolio
        print("\n1. Importing Test Portfolio")
        
        test_portfolio = {
            'success': True,
            'portfolio': [
                {
                    'ticker': 'AAPL',
                    'amount_invested': 1000.0,
                    'buy_price': 150.0,
                    'shares': 6.67,
                    'currency': 'USD',
                    'amount_invested_currency': 'USD',
                    'buy_price_currency': 'USD',
                    'purchase_date': '2024-01-15'
                },
                {
                    'ticker': 'GOOGL',
                    'amount_invested': 2000.0,
                    'buy_price': 100.0,
                    'shares': 20.0,
                    'currency': 'USD',
                    'amount_invested_currency': 'USD',
                    'buy_price_currency': 'USD',
                    'purchase_date': '2024-02-10'
                },
                {
                    'ticker': 'MSFT',
                    'amount_invested': 1500.0,
                    'buy_price': 300.0,
                    'shares': 5.0,
                    'currency': 'USD',
                    'amount_invested_currency': 'USD',
                    'buy_price_currency': 'USD',
                    'purchase_date': '2024-03-05'
                }
            ],
            'cash_position': 500.0,
            'detected_currency': 'USD',
            'currency': 'USD',
            'selected_currency': 'USD'
        }
        
        import_response = session.post(
            f"{base_url}/api/import/confirm",
            json=test_portfolio,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if import_response.status_code == 200:
            print("   ✅ Test portfolio imported successfully")
            
            # Step 2: Verify portfolio before deletion
            print("\n2. Verifying Portfolio Before Deletion")
            
            portfolio_response = session.get(f"{base_url}/portfolio", timeout=30)
            
            if portfolio_response.status_code == 200:
                portfolio_html = portfolio_response.text
                
                stocks_before = {
                    'AAPL': 'AAPL' in portfolio_html,
                    'GOOGL': 'GOOGL' in portfolio_html,
                    'MSFT': 'MSFT' in portfolio_html
                }
                
                print(f"   AAPL present: {'✅' if stocks_before['AAPL'] else '❌'}")
                print(f"   GOOGL present: {'✅' if stocks_before['GOOGL'] else '❌'}")
                print(f"   MSFT present: {'✅' if stocks_before['MSFT'] else '❌'}")
                
                if all(stocks_before.values()):
                    print("   ✅ All test stocks are present")
                    
                    # Step 3: Test new delete API
                    print("\n3. Testing New Delete API (Deleting GOOGL)")
                    
                    delete_response = session.post(
                        f"{base_url}/api/portfolio/delete/GOOGL",
                        headers={'Content-Type': 'application/json'},
                        timeout=30
                    )
                    
                    print(f"   Delete API response status: {delete_response.status_code}")
                    
                    if delete_response.status_code == 200:
                        delete_result = delete_response.json()
                        print(f"   Delete API response: {delete_result}")
                        
                        if delete_result.get('success'):
                            print("   ✅ Delete API returned success")
                            
                            # Step 4: Verify deletion worked
                            print("\n4. Verifying Deletion Worked")
                            
                            time.sleep(1)  # Give a moment for changes to take effect
                            
                            portfolio_after_response = session.get(f"{base_url}/portfolio", timeout=30)
                            
                            if portfolio_after_response.status_code == 200:
                                portfolio_after_html = portfolio_after_response.text
                                
                                stocks_after = {
                                    'AAPL': 'AAPL' in portfolio_after_html,
                                    'GOOGL': 'GOOGL' in portfolio_after_html,
                                    'MSFT': 'MSFT' in portfolio_after_html
                                }
                                
                                print(f"   AAPL present: {'✅' if stocks_after['AAPL'] else '❌'}")
                                print(f"   GOOGL present: {'❌ (deleted)' if not stocks_after['GOOGL'] else '⚠️  STILL THERE!'}")
                                print(f"   MSFT present: {'✅' if stocks_after['MSFT'] else '❌'}")
                                
                                if stocks_after['AAPL'] and not stocks_after['GOOGL'] and stocks_after['MSFT']:
                                    print("   🎉 SUCCESS: New delete API works perfectly!")
                                    print("   ✅ GOOGL was deleted, other stocks remain")
                                elif stocks_after['GOOGL']:
                                    print("   ❌ FAILURE: GOOGL is still present after deletion")
                                else:
                                    print("   ⚠️  UNEXPECTED: Some other changes occurred")
                                    
                            else:
                                print(f"   ❌ Portfolio page failed after deletion: {portfolio_after_response.status_code}")
                                
                        else:
                            print(f"   ❌ Delete API returned failure: {delete_result.get('error')}")
                            
                    else:
                        print(f"   ❌ Delete API request failed: {delete_response.status_code}")
                        try:
                            error_result = delete_response.json()
                            print(f"   Error response: {error_result}")
                        except:
                            print(f"   Error response: {delete_response.text}")
                            
                else:
                    print("   ❌ Not all test stocks are present")
                    
            else:
                print(f"   ❌ Portfolio page failed: {portfolio_response.status_code}")
                
        else:
            print(f"   ❌ Portfolio import failed: {import_response.status_code}")
            print(f"   Response: {import_response.text}")
            
    except Exception as e:
        print(f"   ❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎯 New Delete API Test Completed!")
    print("\nTo test manually:")
    print("1. Go to http://127.0.0.1:9878/portfolio")
    print("2. Click the trash icon next to any stock")
    print("3. Confirm the deletion")
    print("4. Watch for the success notification")
    print("5. Page should reload automatically")
    print("6. Verify the stock is gone")

if __name__ == '__main__':
    test_new_delete_api()
