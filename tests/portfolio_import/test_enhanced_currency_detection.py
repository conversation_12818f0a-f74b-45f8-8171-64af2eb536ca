#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Enhanced Currency Detection and Selection

This script tests the new enhanced currency detection features including:
1. Smart currency selection (prioritizing non-USD/EUR currencies)
2. Gemini AI currency uncertainty detection
3. Mixed currency handling
4. Beautiful UI popup generation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService
import json

def test_mixed_currency_scenario():
    """Test scenario with mixed currencies (USD buy prices, DKK current values)."""
    print("🧪 Testing Mixed Currency Scenario (USD buy prices, DKK current values)")
    print("=" * 70)
    
    mixed_currency_text = """
    Portfolio Holdings:
    
    GOOGL - Alphabet Inc
    Shares: 83.26 stk
    GAK: 161.61 USD
    Current Price: 192.06 USD
    Markedsværdi: 15,848 kr
    Afkast: 23.5%
    
    AAPL - Apple Inc
    Shares: 44.64 stk
    GAK: 189.45 USD
    Current Price: 225.30 USD
    Markedsværdi: 10,058 kr
    Afkast: 18.9%
    
    TSLA - Tesla Inc
    Shares: 13.0 stk
    GAK: 161.61 USD
    Current Price: 189.25 USD
    Markedsværdi: 2,462.85 kr
    Afkast: 17.1%
    """
    
    try:
        service = PortfolioImportService("test_key", "test_key")
        result = service.extract_portfolio_from_text(mixed_currency_text)
        api_response = service.format_for_api(result)
        
        print(f"✅ Extraction completed!")
        print(f"   Success: {api_response.get('success', False)}")
        print(f"   Portfolio entries: {len(api_response.get('portfolio', []))}")
        
        currency_info = api_response.get('currency_info', {})
        print(f"\n💱 Currency Detection Results:")
        print(f"   Detected currencies: {currency_info.get('detected_currencies', [])}")
        print(f"   Primary currency: {currency_info.get('primary_currency', 'Unknown')}")
        print(f"   Has mixed currencies: {currency_info.get('has_mixed_currencies', False)}")
        print(f"   Requires user selection: {currency_info.get('requires_user_selection', False)}")
        print(f"   Selection reason: {currency_info.get('currency_selection_reason', 'N/A')}")
        
        # Check for Gemini AI question
        gemini_question = currency_info.get('gemini_question')
        if gemini_question:
            print(f"\n🤖 Gemini AI Question:")
            print(f"   \"{gemini_question}\"")
        
        # Check for mixed currency entries
        mixed_entries = currency_info.get('mixed_currency_entries', [])
        if mixed_entries:
            print(f"\n🔄 Mixed Currency Entries:")
            for entry in mixed_entries:
                print(f"   {entry['ticker']}: buy in {entry['buy_currency']}, current in {entry['current_currency']}")
        
        # Check for currency uncertainties
        uncertainties = currency_info.get('currency_uncertainties', [])
        if uncertainties:
            print(f"\n❓ Currency Uncertainties:")
            for uncertainty in uncertainties:
                print(f"   {uncertainty['ticker']}: {uncertainty['uncertainty']}")
        
        return api_response
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_single_currency_scenario():
    """Test scenario with single currency (should not require selection)."""
    print("\n🧪 Testing Single Currency Scenario (USD only)")
    print("=" * 70)
    
    single_currency_text = """
    Portfolio Holdings:
    
    AAPL - Apple Inc
    Shares: 100
    Buy Price: $150.00
    Current Price: $175.50
    Current Value: $17,550.00
    
    MSFT - Microsoft Corp
    Shares: 50
    Buy Price: $300.00
    Current Price: $350.00
    Current Value: $17,500.00
    """
    
    try:
        service = PortfolioImportService("test_key", "test_key")
        result = service.extract_portfolio_from_text(single_currency_text)
        api_response = service.format_for_api(result)
        
        currency_info = api_response.get('currency_info', {})
        print(f"💱 Single Currency Test Results:")
        print(f"   Detected currencies: {currency_info.get('detected_currencies', [])}")
        print(f"   Requires user selection: {currency_info.get('requires_user_selection', False)}")
        
        if not currency_info.get('requires_user_selection', True):
            print(f"✅ Single currency scenario correctly bypasses selection modal")
        else:
            print(f"❌ Single currency scenario incorrectly triggers selection modal")
            
        return api_response
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return None

def test_currency_priority_logic():
    """Test the smart currency priority logic (non-USD/EUR currencies should be prioritized)."""
    print("\n🧪 Testing Currency Priority Logic (DKK should be prioritized over USD)")
    print("=" * 70)
    
    priority_test_text = """
    Portfolio Holdings:
    
    NOVO - Novo Nordisk
    Shares: 100 stk
    Buy Price: 850.50 kr
    Current Price: 920.75 kr
    Current Value: 92,075 kr
    
    AAPL - Apple Inc  
    Shares: 10 stk
    Buy Price: 150.00 USD
    Current Price: 175.50 USD
    Current Value: 1,755.00 USD
    """
    
    try:
        service = PortfolioImportService("test_key", "test_key")
        result = service.extract_portfolio_from_text(priority_test_text)
        api_response = service.format_for_api(result)
        
        currency_info = api_response.get('currency_info', {})
        primary_currency = currency_info.get('primary_currency')
        
        print(f"💱 Priority Test Results:")
        print(f"   Detected currencies: {currency_info.get('detected_currencies', [])}")
        print(f"   Primary currency: {primary_currency}")
        
        if primary_currency == 'DKK':
            print(f"✅ Smart priority logic working: DKK correctly prioritized over USD")
        else:
            print(f"❌ Smart priority logic failed: Expected DKK, got {primary_currency}")
            
        return api_response
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return None

def main():
    """Run all currency detection tests."""
    print("🚀 Enhanced Currency Detection Test Suite")
    print("=" * 70)
    
    # Test 1: Mixed currency scenario
    mixed_result = test_mixed_currency_scenario()
    
    # Test 2: Single currency scenario  
    single_result = test_single_currency_scenario()
    
    # Test 3: Currency priority logic
    priority_result = test_currency_priority_logic()
    
    print("\n📊 Test Summary:")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 3
    
    if mixed_result and mixed_result.get('success'):
        print("✅ Mixed currency detection: PASSED")
        tests_passed += 1
    else:
        print("❌ Mixed currency detection: FAILED")
    
    if single_result and not single_result.get('currency_info', {}).get('requires_user_selection', True):
        print("✅ Single currency bypass: PASSED")
        tests_passed += 1
    else:
        print("❌ Single currency bypass: FAILED")
    
    if priority_result and priority_result.get('currency_info', {}).get('primary_currency') == 'DKK':
        print("✅ Currency priority logic: PASSED")
        tests_passed += 1
    else:
        print("❌ Currency priority logic: FAILED")
    
    print(f"\n🎯 Overall Result: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Enhanced currency detection is working perfectly.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")

if __name__ == "__main__":
    main()
