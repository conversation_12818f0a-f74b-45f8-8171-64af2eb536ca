#!/usr/bin/env python3
"""
Test script to verify CHF currency fixes are working correctly.
"""

import requests
import json
import time

def test_chf_import():
    """Test CHF currency import and display."""
    
    base_url = "http://127.0.0.1:9878"
    
    print("🧪 Testing CHF Currency Import Fixes")
    print("=" * 50)
    
    # Test 1: Simulate CHF portfolio import
    print("\n1. Testing CHF Portfolio Import")
    
    chf_portfolio_data = {
        'success': True,
        'portfolio': [
            {
                'ticker': 'NESN.SW',
                'amount_invested': 5000.0,
                'buy_price': 100.0,
                'shares': 50.0,
                'currency': 'CHF',
                'amount_invested_currency': 'CHF',
                'buy_price_currency': 'CHF',
                'purchase_date': '2024-01-15'
            },
            {
                'ticker': 'NOVN.SW',
                'amount_invested': 3000.0,
                'buy_price': 75.0,
                'shares': 40.0,
                'currency': 'CHF',
                'amount_invested_currency': 'CHF',
                'buy_price_currency': 'CHF',
                'purchase_date': '2024-02-10'
            }
        ],
        'cash_position': 1000.0,
        'detected_currency': 'CHF',
        'currency': 'CHF',
        'selected_currency': 'CHF'
    }
    
    try:
        # Send import confirmation request
        response = requests.post(
            f"{base_url}/api/import/confirm",
            json=chf_portfolio_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Import successful: {result.get('message')}")
            print(f"   Portfolio currency: {result.get('portfolio_currency')}")
            
            # Test 2: Check portfolio page rendering
            print("\n2. Testing Portfolio Page Rendering")
            
            portfolio_response = requests.get(f"{base_url}/portfolio", timeout=30)
            
            if portfolio_response.status_code == 200:
                portfolio_html = portfolio_response.text
                
                # Check for CHF currency indicators
                chf_indicators = [
                    'CHF' in portfolio_html,
                    'Swiss Franc' in portfolio_html,
                    'portfolioCurrencySymbol = \'CHF \'' in portfolio_html,
                    'portfolioCurrencyFromServer = \'CHF\'' in portfolio_html
                ]
                
                print(f"   CHF in HTML: {'✅' if chf_indicators[0] else '❌'}")
                print(f"   Swiss Franc in HTML: {'✅' if chf_indicators[1] else '❌'}")
                print(f"   CHF symbol in JS: {'✅' if chf_indicators[2] else '❌'}")
                print(f"   CHF currency in JS: {'✅' if chf_indicators[3] else '❌'}")
                
                if all(chf_indicators):
                    print("   ✅ Portfolio page correctly shows CHF currency")
                else:
                    print("   ❌ Portfolio page missing CHF indicators")
                    
            else:
                print(f"   ❌ Portfolio page request failed: {portfolio_response.status_code}")
                
        else:
            print(f"   ❌ Import failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error during test: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Test completed!")
    print("\nTo verify manually:")
    print("1. Go to http://127.0.0.1:9878/portfolio")
    print("2. Check that portfolio values show 'CHF' instead of '$'")
    print("3. Check that Currency Settings shows 'Swiss Franc (CHF)'")
    print("4. Check that Display Currency dropdown shows 'CHF'")

if __name__ == '__main__':
    test_chf_import()
