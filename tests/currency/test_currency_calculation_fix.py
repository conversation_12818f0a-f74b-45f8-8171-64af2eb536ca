#!/usr/bin/env python3
"""
Currency Calculation Fix Test
This test validates that the critical currency conversion fixes are working correctly.
Specifically addresses the issue where JPY amounts were being treated as USD,
resulting in incorrect share calculations.
"""
import sys
import os
import unittest
from unittest.mock import patch, MagicMock

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from app import CurrencyConverter
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)

class TestCurrencyCalculationFix(unittest.TestCase):
    
    def setUp(self):
        """Set up test environment."""
        print("🧪 Setting up currency calculation tests...")
    
    def test_currency_converter_jpy_to_usd(self):
        """Test that CurrencyConverter correctly converts JPY to USD."""
        print("\n🔍 Testing CurrencyConverter JPY→USD conversion")
        
        # Test the exact scenario from user's screenshot
        jpy_amount = 1585806.00  # Amount invested from SHOP
        expected_usd = jpy_amount * 0.0067  # JPY rate to USD
        
        converted_amount = CurrencyConverter.convert_amount(jpy_amount, 'JPY', 'USD')
        
        print(f"   Input: ¥{jpy_amount:,.2f} JPY")
        print(f"   Expected: ${expected_usd:,.2f} USD")
        print(f"   Converted: ${converted_amount:,.2f} USD")
        
        self.assertAlmostEqual(converted_amount, expected_usd, places=2,
                              msg=f"JPY to USD conversion failed: expected {expected_usd}, got {converted_amount}")
        print("   ✅ SUCCESS: JPY→USD conversion working correctly")
    
    def test_share_calculation_scenario(self):
        """Test the complete share calculation scenario from the screenshot."""
        print("\n📊 Testing complete share calculation scenario")
        
        # Data from user's screenshot - SHOP stock
        amount_invested_jpy = 1585806.00  # Original JPY amount
        buy_price_usd = 75.43            # Buy price in USD
        expected_shares = 140.86         # Expected shares from screenshot
        
        # Step 1: Convert JPY to USD
        amount_invested_usd = CurrencyConverter.convert_amount(amount_invested_jpy, 'JPY', 'USD')
        
        # Step 2: Calculate shares
        calculated_shares = amount_invested_usd / buy_price_usd
        
        print(f"   📋 Scenario: SHOP stock from screenshot")
        print(f"   💰 Amount Invested: ¥{amount_invested_jpy:,.2f} JPY")
        print(f"   💵 Converted to USD: ${amount_invested_usd:,.2f}")
        print(f"   💲 Buy Price: ${buy_price_usd} USD")
        print(f"   📈 Calculated Shares: {calculated_shares:.4f}")
        print(f"   🎯 Expected Shares: {expected_shares}")
        
        # Allow some tolerance for rounding differences
        self.assertAlmostEqual(calculated_shares, expected_shares, places=2,
                              msg=f"Share calculation failed: expected {expected_shares}, got {calculated_shares}")
        print("   ✅ SUCCESS: Share calculation matches expected result")
    
    def test_old_vs_new_calculation(self):
        """Test that shows the difference between old (broken) and new (fixed) calculation."""
        print("\n⚖️  Testing old vs new calculation methods")
        
        amount_invested_jpy = 1585806.00
        buy_price_usd = 75.43
        
        # OLD (BROKEN) METHOD: Treat JPY as USD
        old_wrong_shares = amount_invested_jpy / buy_price_usd
        
        # NEW (FIXED) METHOD: Convert currencies first
        amount_invested_usd = CurrencyConverter.convert_amount(amount_invested_jpy, 'JPY', 'USD')
        new_correct_shares = amount_invested_usd / buy_price_usd
        
        print(f"   💸 Amount: ¥{amount_invested_jpy:,.2f} JPY")
        print(f"   💲 Buy Price: ${buy_price_usd} USD")
        print(f"   🚫 OLD (BROKEN): {old_wrong_shares:,.2f} shares (treats JPY as USD)")
        print(f"   ✅ NEW (FIXED): {new_correct_shares:.2f} shares (converts JPY→USD)")
        print(f"   📊 Difference: {abs(old_wrong_shares - new_correct_shares):,.2f} shares")
        
        # The old method should produce a massively wrong result
        self.assertGreater(old_wrong_shares, 20000, "Old method should produce inflated share count")
        self.assertLess(new_correct_shares, 200, "New method should produce reasonable share count")
        self.assertGreater(abs(old_wrong_shares - new_correct_shares), 20000, 
                          "Difference should be massive, proving the fix is significant")
        print("   ✅ SUCCESS: Fix produces dramatically different (correct) results")
    
    def test_multiple_currencies(self):
        """Test conversion accuracy for multiple currencies used in portfolios."""
        print("\n🌐 Testing multiple currency conversions")
        
        test_cases = [
            {'amount': 100000, 'from': 'JPY', 'to': 'USD', 'expected_rate': 0.0067},
            {'amount': 1000, 'from': 'USD', 'to': 'USD', 'expected_rate': 1.0},
            {'amount': 1000, 'from': 'EUR', 'to': 'USD', 'expected_rate': 1.08},
            {'amount': 1000, 'from': 'GBP', 'to': 'USD', 'expected_rate': 1.27},
        ]
        
        for case in test_cases:
            expected = case['amount'] * case['expected_rate']
            actual = CurrencyConverter.convert_amount(case['amount'], case['from'], case['to'])
            
            print(f"   💱 {case['amount']} {case['from']} → ${actual:.2f} USD (expected: ${expected:.2f})")
            
            self.assertAlmostEqual(actual, expected, places=2,
                                  msg=f"Conversion failed for {case['from']}→{case['to']}")
        
        print("   ✅ SUCCESS: All currency conversions working correctly")
    
    def test_currency_formatting(self):
        """Test currency formatting for display purposes."""
        print("\n🎨 Testing currency formatting")
        
        test_cases = [
            {'amount': 1585806, 'currency': 'JPY', 'expected': '¥1,585,806'},
            {'amount': 10622.34, 'currency': 'USD', 'expected': '$10,622.34'},
            {'amount': 8765.43, 'currency': 'EUR', 'expected': '€8,765.43'},
        ]
        
        for case in test_cases:
            formatted = CurrencyConverter.format_amount(case['amount'], case['currency'])
            print(f"   💰 {case['amount']} {case['currency']} → {formatted}")
            self.assertEqual(formatted, case['expected'],
                           msg=f"Formatting failed for {case['currency']}: expected {case['expected']}, got {formatted}")
        
        print("   ✅ SUCCESS: Currency formatting working correctly")

def run_tests():
    """Run all currency calculation tests."""
    print("🚀 Starting Currency Calculation Fix Tests")
    print("=" * 70)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCurrencyCalculationFix)
    
    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 70)
    if result.wasSuccessful():
        print("🎉 ALL TESTS PASSED! Currency calculation fixes are working correctly.")
        print("💰 JPY amounts will now be properly converted before share calculations.")
        print("📊 Share counts should now match expected values from imported portfolios.")
    else:
        print("❌ Some tests failed. Please check the currency conversion logic.")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
            print(failure[1])
        for error in result.errors:
            print(f"ERROR: {error[0]}")
            print(error[1])
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)