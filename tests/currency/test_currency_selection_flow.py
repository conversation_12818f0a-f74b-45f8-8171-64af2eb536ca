#!/usr/bin/env python3
"""
Test the complete currency selection flow to ensure user selection is preserved.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService
import j<PERSON>

def test_currency_selection_flow():
    """Test the complete currency selection flow."""
    print("🔄 TESTING COMPLETE CURRENCY SELECTION FLOW")
    print("=" * 60)
    
    # Step 1: Extract portfolio data (this triggers currency selection)
    dkk_portfolio_text = """
    Min Aktieportefølje
    
    Ticker    Markedsværdi    GAK        Antal
    AAPL      2.462,85 DKK    161,61 kr  13 stk
    MSFT      3.250,75 DKK    325,08 kr  10 stk
    NOVO      1.890,50 DKK    189,05 kr  10 stk
    """
    
    service = PortfolioImportService("demo_key", "demo_key")
    result = service.extract_portfolio_from_text(dkk_portfolio_text)
    api_response = service.format_for_api(result)
    
    print("📊 Step 1: Initial Extraction")
    print(f"   Success: {api_response.get('success', False)}")
    print(f"   Detected Currency: {api_response.get('detected_currency', 'None')}")
    print(f"   Requires Selection: {api_response.get('currency_info', {}).get('requires_user_selection', False)}")
    
    # Step 2: Simulate user selecting DKK
    if api_response.get('currency_info', {}).get('requires_user_selection', False):
        print("\n🎯 Step 2: User Selects DKK Currency")
        
        # This simulates what the frontend sends after user selection
        user_selected_data = {
            **api_response,
            'selected_currency': 'DKK',
            'user_selected_currency': True
        }
        
        print(f"   User selected: {user_selected_data.get('selected_currency')}")
        print(f"   Original detected: {user_selected_data.get('detected_currency')}")
        
        # Step 3: Check portfolio entries have correct currency
        print("\n💰 Step 3: Portfolio Entries Currency Check")
        portfolio_entries = user_selected_data.get('portfolio', [])
        
        for i, entry in enumerate(portfolio_entries[:3], 1):  # Check first 3 entries
            ticker = entry.get('ticker', 'Unknown')
            amount = entry.get('amount_invested', 0)
            currency = entry.get('currency', 'Unknown')
            buy_currency = entry.get('buy_price_currency', 'Unknown')
            
            print(f"   Entry {i} ({ticker}):")
            print(f"     Amount: {amount} {currency}")
            print(f"     Buy Price Currency: {buy_currency}")
            
            # Check if currency is correct
            if currency == 'DKK' and buy_currency == 'DKK':
                print(f"     ✅ Currency correctly set to DKK")
            else:
                print(f"     ❌ Currency issue: expected DKK, got {currency}/{buy_currency}")
        
        # Step 4: Simulate backend processing (what confirm_portfolio_import would do)
        print("\n🔧 Step 4: Backend Processing Simulation")
        
        selected_currency = user_selected_data.get('selected_currency', 'USD')
        source_currency = user_selected_data.get('currency', 'DKK')
        
        print(f"   Backend receives:")
        print(f"     - Selected Currency: {selected_currency}")
        print(f"     - Source Currency: {source_currency}")
        print(f"     - Portfolio Entries: {len(portfolio_entries)}")
        
        # Simulate session storage
        simulated_session = {'portfolio_currency': selected_currency}
        print(f"   Session would store: portfolio_currency = {simulated_session['portfolio_currency']}")
        
        # Step 5: Simulate frontend display
        print("\n🖥️  Step 5: Frontend Display Simulation")
        
        # This is what the portfolio page should show
        display_currency = simulated_session.get('portfolio_currency', 'USD')
        
        currency_names = {
            'USD': '🇺🇸 US Dollar ($)',
            'EUR': '🇪🇺 Euro (€)',
            'DKK': '🇩🇰 Danish Krone (kr)',
            'GBP': '🇬🇧 British Pound (£)',
            'JPY': '🇯🇵 Japanese Yen (¥)',
            'CAD': '🇨🇦 Canadian Dollar (C$)',
            'AUD': '🇦🇺 Australian Dollar (A$)',
            'CHF': '🇨🇭 Swiss Franc (CHF)',
            'SEK': '🇸🇪 Swedish Krona (kr)',
            'NOK': '🇳🇴 Norwegian Krone (kr)'
        }
        
        display_name = currency_names.get(display_currency, display_currency)
        
        print(f"   Portfolio page should show:")
        print(f"     Currency dropdown: {display_name}")
        print(f"     Currency display: {display_currency}")
        
        # Step 6: Verify the complete flow
        print("\n🎯 Step 6: Flow Verification")
        
        if selected_currency == 'DKK' and display_currency == 'DKK':
            print("   ✅ COMPLETE FLOW SUCCESS!")
            print("   ✅ User selection preserved through entire process")
            print("   ✅ Portfolio will display in DKK, not USD")
        else:
            print("   ❌ FLOW ISSUE DETECTED!")
            print(f"   ❌ Expected DKK, but final display would be {display_currency}")
        
        return user_selected_data
    
    else:
        print("\n❌ Currency selection not triggered - this is the problem!")
        return api_response

def test_mixed_currency_selection():
    """Test mixed currency selection."""
    print("\n\n🌍 TESTING MIXED CURRENCY SELECTION FLOW")
    print("=" * 60)
    
    mixed_portfolio_text = """
    International Portfolio
    
    AAPL    $2,500.00 USD    $125.00    20 shares
    ASML    €1,800.00 EUR    €180.00    10 shares  
    NOVO    1,200.00 DKK     150.00 kr  8 shares
    """
    
    service = PortfolioImportService("demo_key", "demo_key")
    result = service.extract_portfolio_from_text(mixed_portfolio_text)
    api_response = service.format_for_api(result)
    
    currency_info = api_response.get('currency_info', {})
    requires_selection = currency_info.get('requires_user_selection', False)
    detected_currencies = currency_info.get('detected_currencies', [])
    
    print(f"📊 Mixed Currency Analysis:")
    print(f"   Detected: {detected_currencies}")
    print(f"   Requires Selection: {requires_selection}")
    print(f"   Gemini Question: {currency_info.get('gemini_question', 'None')}")
    
    if requires_selection:
        # User selects EUR
        print(f"\n🎯 User selects EUR from options: {detected_currencies}")
        
        selected_data = {
            **api_response,
            'selected_currency': 'EUR',
            'user_selected_currency': True
        }
        
        print(f"   Final currency should be: EUR")
        print(f"   Session would store: portfolio_currency = EUR")
        print(f"   Portfolio page should show: 🇪🇺 Euro (€)")
        
        return selected_data
    
    return api_response

def main():
    """Run all currency selection flow tests."""
    print("🚨 CURRENCY SELECTION FLOW TEST SUITE")
    print("=" * 70)
    print("Testing the complete flow from detection to display")
    print("=" * 70)
    
    try:
        # Test 1: DKK currency selection flow
        dkk_result = test_currency_selection_flow()
        
        # Test 2: Mixed currency selection flow
        mixed_result = test_mixed_currency_selection()
        
        print("\n" + "=" * 70)
        print("🎯 CURRENCY SELECTION FLOW SUMMARY")
        print("=" * 70)
        
        # Check if flows worked
        dkk_worked = dkk_result.get('selected_currency') == 'DKK'
        mixed_worked = mixed_result.get('selected_currency') == 'EUR'
        
        print(f"DKK Selection Flow: {'✅ WORKING' if dkk_worked else '❌ BROKEN'}")
        print(f"Mixed Currency Flow: {'✅ WORKING' if mixed_worked else '❌ BROKEN'}")
        
        if dkk_worked and mixed_worked:
            print("\n🎉 CURRENCY SELECTION FLOWS ARE WORKING!")
            print("✅ Backend correctly processes user currency selection")
            print("✅ Session storage should preserve selected currency")
            print("✅ Portfolio page should display correct currency")
            print("\n💡 If users still see USD, check:")
            print("   - Frontend JavaScript console for errors")
            print("   - Network tab to verify API calls")
            print("   - Session storage in browser dev tools")
        else:
            print("\n❌ CURRENCY SELECTION FLOWS HAVE ISSUES")
            if not dkk_worked:
                print("   - DKK selection not working properly")
            if not mixed_worked:
                print("   - Mixed currency selection not working properly")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()