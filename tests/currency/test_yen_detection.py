#!/usr/bin/env python3
"""
Test Japanese Yen detection specifically
"""

import sys
sys.path.append('.')
from portfolio_import import AIPortfolioExtractor

def test_yen_detection():
    """Test Japanese Yen detection from your screenshot data"""
    
    print("🇯🇵 Testing Japanese Yen Detection")
    print("=" * 50)
    
    # Simulate the text that should be extracted from your first screenshot
    test_text = """
    Company                Value (¥)      Change %    Today       Price (USD)
    Shopify Inc.          ¥1,803,220     ▲ 13.71 %   ↑ 0.12 %    USD 85.77
    Palantir Tech.        ¥704300        ▼ 1.88%     → 0.00 %    $25.91
    Roblox Corp.          ¥1,020,050     ▲ 9.02%     ↑ 0.31%     39.67 USD
    Pinterest Inc.        ¥892,430       ▲0.96 %     ↑ 0.06%     43.11
    Block Inc.            ¥2,370,100     ▼ 4.20%     ↓ 0.21%     USD: 70.30
    """
    
    extractor = AIPortfolioExtractor("test_key", "test_key")
    
    print("📝 Test Text:")
    print(test_text)
    print()
    
    # Test currency detection
    print("1. CURRENCY DETECTION")
    print("-" * 25)
    
    detected_currency = extractor._detect_primary_currency(test_text)
    print(f"Detected Currency: {detected_currency}")
    
    # Count symbols manually
    yen_count = test_text.count('¥')
    usd_count = test_text.count('USD') + test_text.count('$')
    
    print(f"¥ symbols found: {yen_count}")
    print(f"USD references found: {usd_count}")
    
    # Test language detection
    print("\n2. LANGUAGE DETECTION")
    print("-" * 25)
    
    detected_language = extractor._detect_language(test_text)
    print(f"Detected Language: {detected_language}")
    
    # Test what the AI prompt would contain
    print("\n3. AI PROMPT ANALYSIS")
    print("-" * 25)
    
    # Set up the extractor state
    extractor.detected_language = detected_language
    extractor.detected_currency = detected_currency
    
    # Get the prompt that would be sent to Gemini
    prompt = extractor._build_extraction_prompt(test_text)
    
    # Check if the prompt contains Japanese Yen instructions
    if "¥" in prompt and "JPY" in prompt:
        print("✅ Prompt contains Japanese Yen (¥) instructions")
    else:
        print("❌ Prompt missing Japanese Yen instructions")
    
    if "JAPANESE YEN SPECIAL HANDLING" in prompt:
        print("✅ Prompt contains special Japanese Yen handling section")
    else:
        print("❌ Prompt missing special Japanese Yen handling section")
    
    # Check for specific Yen patterns in prompt
    if "¥1,803,220" in prompt:
        print("✅ Prompt contains specific Yen amount examples")
    else:
        print("❌ Prompt missing specific Yen amount examples")
    
    print("\n4. EXPECTED BEHAVIOR")
    print("-" * 25)
    
    print("Expected extraction results:")
    print("- Shopify Inc: ¥1,803,220 → currency: JPY")
    print("- Palantir Tech: ¥704,300 → currency: JPY") 
    print("- Roblox Corp: ¥1,020,050 → currency: JPY")
    print("- Pinterest Inc: ¥892,430 → currency: JPY")
    print("- Block Inc: ¥2,370,100 → currency: JPY")
    
    print("\n5. CURRENCY SYMBOL PRIORITY")
    print("-" * 30)
    
    # Test the currency symbol priority logic
    currency_symbols = {
        '$': 'USD',
        '€': 'EUR',
        '£': 'GBP',
        '¥': 'JPY',  # Should have highest priority
    }
    
    for symbol, currency in currency_symbols.items():
        count = test_text.count(symbol)
        if count > 0:
            print(f"Symbol '{symbol}' → {currency}: found {count} times")
    
    # The ¥ symbol should win due to high weight multiplier (15x)
    print(f"\n✅ Japanese Yen (¥) should be detected as primary currency")
    print(f"   Actual detection: {detected_currency}")
    
    if detected_currency == 'JPY':
        print("🎉 SUCCESS: Japanese Yen correctly detected!")
    else:
        print("❌ FAILURE: Japanese Yen not detected correctly")
        print("   This suggests an issue with the currency detection logic")
    
    return detected_currency == 'JPY'

if __name__ == "__main__":
    success = test_yen_detection()
    if not success:
        print("\n🔧 DEBUGGING NEEDED:")
        print("- Check currency detection weights")
        print("- Verify ¥ symbol recognition")
        print("- Test Gemini AI response processing")
