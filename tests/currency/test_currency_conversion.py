#!/usr/bin/env python3
"""
Test script to verify currency conversion functionality.
"""

import requests
import json
import time

def test_currency_conversion():
    """Test currency conversion functionality."""
    
    base_url = "http://127.0.0.1:9878"
    
    print("🧪 Testing Currency Conversion Functionality")
    print("=" * 60)
    
    # Test 1: Import CHF portfolio
    print("\n1. Importing CHF Portfolio")
    
    chf_portfolio_data = {
        'success': True,
        'portfolio': [
            {
                'ticker': 'NESN.SW',
                'amount_invested': 5000.0,
                'buy_price': 100.0,
                'shares': 50.0,
                'currency': 'CHF',
                'amount_invested_currency': 'CHF',
                'buy_price_currency': 'CHF',
                'purchase_date': '2024-01-15'
            },
            {
                'ticker': 'NOVN.SW',
                'amount_invested': 3000.0,
                'buy_price': 75.0,
                'shares': 40.0,
                'currency': 'CHF',
                'amount_invested_currency': 'CHF',
                'buy_price_currency': 'CHF',
                'purchase_date': '2024-02-10'
            }
        ],
        'cash_position': 1000.0,
        'detected_currency': 'CHF',
        'currency': 'CHF',
        'selected_currency': 'CHF'
    }
    
    try:
        # Import CHF portfolio
        response = requests.post(
            f"{base_url}/api/import/confirm",
            json=chf_portfolio_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ CHF portfolio imported: {result.get('message')}")
            print(f"   Portfolio currency: {result.get('portfolio_currency')}")
            
            # Test 2: Convert to USD
            print("\n2. Converting Portfolio from CHF to USD")
            
            conversion_response = requests.post(
                f"{base_url}/api/portfolio/currency",
                json={'currency': 'USD'},
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if conversion_response.status_code == 200:
                conversion_result = conversion_response.json()
                print(f"   ✅ Conversion successful: {conversion_result.get('message')}")
                print(f"   New currency: {conversion_result.get('portfolio_currency')}")
                print(f"   Converted entries: {conversion_result.get('converted_entries')}")
                
                # Test 3: Convert to EUR
                print("\n3. Converting Portfolio from USD to EUR")
                
                eur_response = requests.post(
                    f"{base_url}/api/portfolio/currency",
                    json={'currency': 'EUR'},
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                if eur_response.status_code == 200:
                    eur_result = eur_response.json()
                    print(f"   ✅ EUR conversion successful: {eur_result.get('message')}")
                    print(f"   New currency: {eur_result.get('portfolio_currency')}")
                    print(f"   Converted entries: {eur_result.get('converted_entries')}")
                    
                    # Test 4: Check portfolio page
                    print("\n4. Checking Portfolio Page")
                    
                    portfolio_response = requests.get(f"{base_url}/portfolio", timeout=30)
                    
                    if portfolio_response.status_code == 200:
                        portfolio_html = portfolio_response.text
                        
                        # Check for EUR currency indicators
                        eur_indicators = [
                            'EUR' in portfolio_html,
                            'Euro' in portfolio_html,
                            'portfolioCurrencySymbol = \'€\'' in portfolio_html,
                            'portfolioCurrencyFromServer = \'EUR\'' in portfolio_html
                        ]
                        
                        print(f"   EUR in HTML: {'✅' if eur_indicators[0] else '❌'}")
                        print(f"   Euro in HTML: {'✅' if eur_indicators[1] else '❌'}")
                        print(f"   EUR symbol in JS: {'✅' if eur_indicators[2] else '❌'}")
                        print(f"   EUR currency in JS: {'✅' if eur_indicators[3] else '❌'}")
                        
                        if all(eur_indicators):
                            print("   ✅ Portfolio page correctly shows EUR currency")
                        else:
                            print("   ⚠️  Portfolio page missing some EUR indicators")
                            
                    else:
                        print(f"   ❌ Portfolio page request failed: {portfolio_response.status_code}")
                        
                else:
                    print(f"   ❌ EUR conversion failed: {eur_response.status_code}")
                    print(f"   Response: {eur_response.text}")
                    
            else:
                print(f"   ❌ USD conversion failed: {conversion_response.status_code}")
                print(f"   Response: {conversion_response.text}")
                
        else:
            print(f"   ❌ CHF import failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error during test: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Currency Conversion Test Completed!")
    print("\nTo verify manually:")
    print("1. Go to http://127.0.0.1:9878/portfolio")
    print("2. Check Currency Settings section")
    print("3. Try changing the currency dropdown")
    print("4. Verify all values convert properly")
    print("5. Check that there's only ONE currency setting (not two)")

if __name__ == '__main__':
    test_currency_conversion()
