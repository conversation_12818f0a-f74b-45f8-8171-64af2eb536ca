#!/usr/bin/env python3
"""
Test the currency preservation in portfolio import system
"""

import os
import sys

# Set the Google API key for testing
os.environ['GOOGLE_API_KEY'] = 'AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o'

from portfolio_import import AIPortfolioExtractor

def test_euro_currency_preservation():
    """Test that Euro currency is properly detected and preserved"""
    print("Testing Euro Currency Preservation")
    print("=" * 50)
    
    # Test data with Euro symbols
    euro_test_text = """
    Portfolio Overview
    
    GOOGL - Alphabet Inc
    83,56 shares
    Buy Price: €161,2525
    Amount Invested: €13470,8
    Current Value: €15234,67
    
    AMZN - Amazon Inc  
    82,76 shares
    Buy Price: €193,67
    Amount Invested: €16029,3
    Current Value: €17456,89
    
    ASML - ASML Holding
    45,06 shares
    Buy Price: €602,8625
    Amount Invested: €27167,69
    Current Value: €29834,56
    
    UBER - Uber Technologies
    64,85 shares
    Buy Price: €77,1545
    Amount Invested: €5003,1
    Current Value: €5456,78
    
    Total Portfolio Value: €67982,90 EUR
    """
    
    # Initialize the extractor
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    # Test the extraction
    result = extractor.extract_portfolio_data_with_ai(euro_test_text)
    
    print(f"Extraction Result:")
    print(f"Success: {result['success']}")
    print(f"Detected Currency: {result.get('detected_currency', 'Unknown')}")
    print(f"Errors: {result.get('errors', [])}")
    
    if result['success']:
        portfolio = result.get('portfolio', [])
        print(f"\nExtracted {len(portfolio)} portfolio entries:")
        
        all_euro = True
        for i, entry in enumerate(portfolio, 1):
            currency = entry.get('currency', 'Unknown')
            print(f"\n{i}. {entry.get('ticker', 'Unknown')}")
            print(f"   Shares: {entry.get('shares', 0)}")
            print(f"   Buy Price: {entry.get('buy_price', 0):.4f} {currency}")
            print(f"   Amount Invested: {entry.get('amount_invested', 0):.2f} {currency}")
            print(f"   Current Value: {entry.get('current_value', 0):.2f} {currency}")
            print(f"   Currency: {currency}")
            
            if currency != 'EUR':
                all_euro = False
                print(f"   ❌ WRONG CURRENCY! Expected EUR, got {currency}")
            else:
                print(f"   ✅ Correct currency: {currency}")
        
        print(f"\n{'✅ SUCCESS' if all_euro else '❌ FAILED'}: All entries use EUR currency: {all_euro}")
        
        # Test currency detection
        detected_currency = result.get('detected_currency', 'Unknown')
        print(f"Detected Currency: {detected_currency}")
        if detected_currency == 'EUR':
            print("✅ Currency detection working correctly")
        else:
            print(f"❌ Currency detection failed - expected EUR, got {detected_currency}")
    else:
        print("❌ Extraction failed!")

def test_usd_currency_preservation():
    """Test that USD currency is properly detected and preserved"""
    print("\n" + "=" * 50)
    print("Testing USD Currency Preservation")
    print("=" * 50)
    
    # Test data with USD symbols
    usd_test_text = """
    Portfolio Summary
    
    AAPL - Apple Inc
    13 shares
    Buy Price: $161.61
    Amount Invested: $2,100.93
    Current Value: $2,462.85
    
    TSLA - Tesla Inc  
    10 shares
    Buy Price: $185.00
    Amount Invested: $1,850.00
    Current Value: $1,890.50
    
    Total Portfolio Value: $4,353.35 USD
    """
    
    # Initialize the extractor
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    # Test the extraction
    result = extractor.extract_portfolio_data_with_ai(usd_test_text)
    
    print(f"Extraction Result:")
    print(f"Success: {result['success']}")
    print(f"Detected Currency: {result.get('detected_currency', 'Unknown')}")
    
    if result['success']:
        portfolio = result.get('portfolio', [])
        print(f"\nExtracted {len(portfolio)} portfolio entries:")
        
        all_usd = True
        for i, entry in enumerate(portfolio, 1):
            currency = entry.get('currency', 'Unknown')
            print(f"\n{i}. {entry.get('ticker', 'Unknown')}")
            print(f"   Currency: {currency}")
            
            if currency != 'USD':
                all_usd = False
                print(f"   ❌ WRONG CURRENCY! Expected USD, got {currency}")
            else:
                print(f"   ✅ Correct currency: {currency}")
        
        print(f"\n{'✅ SUCCESS' if all_usd else '❌ FAILED'}: All entries use USD currency: {all_usd}")

def test_currency_detection():
    """Test the currency detection function directly"""
    print("\n" + "=" * 50)
    print("Testing Currency Detection Function")
    print("=" * 50)
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    test_cases = [
        ("€13470,8 amount invested", "EUR"),
        ("$2,100.93 invested", "USD"),
        ("£1,500.00 portfolio", "GBP"),
        ("1234,56 kr invested", "DKK"),
        ("Portfolio value: 15000 EUR", "EUR"),
        ("Total: 5000 USD", "USD"),
    ]
    
    print("Testing currency detection:")
    for text, expected in test_cases:
        detected = extractor._detect_primary_currency(text)
        status = "✅" if detected == expected else "❌"
        print(f"{status} '{text}' → {detected} (expected: {expected})")

if __name__ == "__main__":
    test_euro_currency_preservation()
    test_usd_currency_preservation()
    test_currency_detection()
