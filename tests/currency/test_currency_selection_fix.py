#!/usr/bin/env python3
"""
Test script to verify the currency selection fix works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService, AIPortfolioExtractor

def test_currency_selection():
    """Test that currency selection is triggered for DKK portfolios."""
    
    # Mock API keys
    google_vision_api_key = "test_key"
    eodhd_api_key = "test_key"
    
    # Create service
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
    
    # Test text with DKK currency (Danish portfolio)
    test_text = """
    Min Aktieportefølje
    
    AAPL    13 stk    GAK: 161.61 USD    Markedsværdi: 2.462,85 kr
    MSFT    8 stk     GAK: 245.30 USD    Markedsværdi: 1.850,20 kr
    GOOGL   5 stk     GAK: 180.45 USD    Markedsværdi: 1.200,15 kr
    
    Total værdi: 5.513,20 kr
    """
    
    print("🧪 Testing currency selection with Danish portfolio...")
    print(f"Test text: {test_text[:100]}...")
    
    # Extract portfolio data
    result = service.extract_portfolio_from_text(test_text)
    
    print(f"\n📊 Extraction Results:")
    print(f"   Success: {result.success}")
    print(f"   Entries found: {len(result.portfolio_entries)}")
    print(f"   Errors: {result.errors}")
    print(f"   Warnings: {result.warnings}")
    
    if result.portfolio_entries:
        for entry in result.portfolio_entries:
            print(f"   - {entry.ticker}: {entry.amount_invested} {entry.currency}")
    
    # Format for API to check currency selection requirement
    formatted_result = service.format_for_api(result)
    
    print(f"\n🌍 Currency Analysis:")
    currency_info = formatted_result.get('currency_info', {})
    print(f"   Detected currencies: {currency_info.get('detected_currencies', [])}")
    print(f"   Primary currency: {currency_info.get('primary_currency', 'Unknown')}")
    print(f"   Requires user selection: {currency_info.get('requires_user_selection', False)}")
    print(f"   Selection reason: {currency_info.get('currency_selection_reason', 'None')}")
    
    # Check if currency selection is required
    requires_selection = currency_info.get('requires_user_selection', False)
    
    if requires_selection:
        print("✅ SUCCESS: Currency selection modal should be shown!")
        print(f"   Reason: {currency_info.get('currency_selection_reason', 'Unknown')}")
        
        # Test the Gemini question
        gemini_question = currency_info.get('gemini_question')
        if gemini_question:
            print(f"   AI Question: {gemini_question}")
        
        return True
    else:
        print("❌ FAILURE: Currency selection should be required for DKK portfolio!")
        return False

def test_usd_portfolio():
    """Test that USD portfolios don't require currency selection."""
    
    # Mock API keys
    google_vision_api_key = "test_key"
    eodhd_api_key = "test_key"
    
    # Create service
    service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
    
    # Test text with USD currency
    test_text = """
    My Investment Portfolio
    
    AAPL    100 shares    Avg Cost: $150.00    Current Value: $15,000.00
    MSFT    50 shares     Avg Cost: $250.00    Current Value: $12,500.00
    GOOGL   25 shares     Avg Cost: $2,000.00  Current Value: $50,000.00
    
    Total Value: $77,500.00
    """
    
    print("\n🧪 Testing USD portfolio (should not require selection)...")
    
    # Extract portfolio data
    result = service.extract_portfolio_from_text(test_text)
    formatted_result = service.format_for_api(result)
    
    currency_info = formatted_result.get('currency_info', {})
    requires_selection = currency_info.get('requires_user_selection', False)
    
    print(f"   Primary currency: {currency_info.get('primary_currency', 'Unknown')}")
    print(f"   Requires selection: {requires_selection}")
    
    if not requires_selection and currency_info.get('primary_currency') == 'USD':
        print("✅ SUCCESS: USD portfolio correctly doesn't require selection")
        return True
    else:
        print("❌ FAILURE: USD portfolio should not require selection")
        return False

if __name__ == "__main__":
    print("🚀 Testing Currency Selection Fix")
    print("=" * 50)
    
    # Test DKK portfolio (should require selection)
    dkk_test = test_currency_selection()
    
    # Test USD portfolio (should not require selection)  
    usd_test = test_usd_portfolio()
    
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   DKK Portfolio Test: {'✅ PASS' if dkk_test else '❌ FAIL'}")
    print(f"   USD Portfolio Test: {'✅ PASS' if usd_test else '❌ FAIL'}")
    
    if dkk_test and usd_test:
        print("\n🎉 All tests passed! Currency selection fix is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)