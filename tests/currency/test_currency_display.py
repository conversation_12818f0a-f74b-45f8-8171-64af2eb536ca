#!/usr/bin/env python3
"""
Test script to verify currency display in portfolio import
"""

import json
from portfolio_import import process_image_upload, process_spreadsheet_upload

def test_currency_display():
    """Test what currency data is being returned"""
    
    # Create a simple test CSV content
    test_csv_content = """Ticker,Amount Invested,Buy Price,Shares,Purchase Date
GOOGL,2074.53,173.38,11.96,17.07.2025
AMZN,2332.91,194.77,11.98,02.06.2025
ASML,4841.20,750.09,6.45,25.07.2025
UBER,767.92,83.04,9.25,23.06.2025"""
    
    print("🧪 Testing portfolio import with CSV data...")
    print(f"📄 Test CSV content:\n{test_csv_content}")
    
    # Convert to bytes
    csv_bytes = test_csv_content.encode('utf-8')
    
    # Test with different user portfolio currencies
    test_currencies = ['USD', 'DKK', 'EUR']
    
    for user_currency in test_currencies:
        print(f"\n{'='*60}")
        print(f"🔍 Testing with user portfolio currency: {user_currency}")
        print(f"{'='*60}")
        
        try:
            # Process the spreadsheet
            result = process_spreadsheet_upload(
                file_data=csv_bytes,
                filename="test_portfolio.csv",
                google_vision_api_key="dummy_key",  # Not needed for CSV
                eodhd_api_key=None,
                user_portfolio_currency=user_currency
            )
            
            print(f"✅ Processing successful: {result.get('success', False)}")
            print(f"📊 Number of entries: {len(result.get('portfolio', []))}")
            
            # Display currency information
            if 'currency_info' in result:
                currency_info = result['currency_info']
                print(f"🌍 Detected currencies: {currency_info.get('detected_currencies', [])}")
                print(f"💱 Primary currency: {currency_info.get('primary_currency', 'N/A')}")
                print(f"🎯 Final currency: {result.get('currency', 'N/A')}")
            
            # Display first few entries with currency details
            portfolio = result.get('portfolio', [])
            for i, entry in enumerate(portfolio[:2]):  # Show first 2 entries
                print(f"\n📈 Entry {i+1} ({entry.get('ticker', 'N/A')}):")
                print(f"   💰 Amount Invested: {entry.get('amount_invested', 'N/A')} {entry.get('amount_invested_currency', 'N/A')}")
                print(f"   💵 Buy Price: {entry.get('buy_price', 'N/A')} {entry.get('buy_price_currency', 'N/A')}")
                print(f"   📊 Shares: {entry.get('shares', 'N/A')}")
                print(f"   🏷️  Currency: {entry.get('currency', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Error processing with {user_currency}: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_currency_display()
