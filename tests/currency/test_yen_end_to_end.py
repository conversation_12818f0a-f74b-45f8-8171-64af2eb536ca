#!/usr/bin/env python3
"""
Test the complete end-to-end Japanese Yen detection and extraction
"""

import sys
import os
import json

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import AIPortfolioExtractor

def test_yen_end_to_end():
    """Test complete Japanese Yen portfolio extraction"""
    
    # User's actual data from the screenshot
    test_text = """
Company | Value (¥) | Change % | Today | Price (USD)
Shopify Inc. | ¥1,803,220 | ▲ 13.71 % | ↑ 0.12 % | USD 85.77
Palantir Tech. | ¥704300 | ▼ 1.88% | → 0.00 % | $25.91
Roblox Corp. | ¥1 020 050 | ▲ 9.02% | ↑ 0.31% | 39.67 USD
Pinterest Inc. | ¥892,430 | ▲0.96 % | ↑ 0.06% | 43.11
Block Inc. | ¥2.370.100 | ▼ 4.20% | ↑ 0.21% | USD: 70.30
"""

    print("🔍 TESTING COMPLETE JAPANESE YEN EXTRACTION")
    print("=" * 55)
    print(f"Test text:\n{test_text}")
    print("=" * 55)
    
    # Initialize extractor
    extractor = AIPortfolioExtractor("test_key", "test_key")
    
    # Test the complete extraction pipeline
    print("\n1. CURRENCY DETECTION")
    print("-" * 25)
    
    currency_result = extractor._detect_primary_currency(test_text)
    print(f"Currency detection result type: {type(currency_result)}")
    
    if isinstance(currency_result, dict):
        detected_currencies = currency_result.get('detected_currencies', [])
        primary_currency = detected_currencies[0] if detected_currencies else 'USD'
        print(f"Mixed currency detected, primary: {primary_currency}")
        print(f"All detected currencies: {detected_currencies}")
    else:
        primary_currency = currency_result
        print(f"Single currency detected: {primary_currency}")
    
    # Test the AI extraction (skip Gemini for now, test the processing logic)
    print("\n2. AI EXTRACTION SETUP")
    print("-" * 25)
    
    # Set up the extractor with detected currency
    extractor.detected_language = extractor._detect_language(test_text)
    if isinstance(currency_result, dict):
        extractor.detected_currency = currency_result['detected_currencies'][0] if currency_result['detected_currencies'] else 'USD'
        extractor.mixed_currency_info = currency_result
    else:
        extractor.detected_currency = currency_result
        extractor.mixed_currency_info = None
    
    print(f"Extractor detected_currency: {extractor.detected_currency}")
    print(f"Extractor mixed_currency_info: {extractor.mixed_currency_info is not None}")
    
    # Test Gemini prompt generation
    print("\n3. GEMINI PROMPT GENERATION")
    print("-" * 30)
    
    prompt = extractor._create_gemini_extraction_prompt(test_text)
    
    # Check if the prompt includes the correct currency
    if f"DETECTED PRIMARY CURRENCY: {extractor.detected_currency}" in prompt:
        print(f"✅ Prompt correctly includes detected currency: {extractor.detected_currency}")
    else:
        print(f"❌ Prompt missing detected currency: {extractor.detected_currency}")
    
    # Check for Japanese Yen specific instructions
    if "¥" in prompt and "JPY" in prompt:
        print("✅ Prompt includes Japanese Yen specific instructions")
    else:
        print("❌ Prompt missing Japanese Yen specific instructions")
    
    # Test mock Gemini response processing
    print("\n4. MOCK GEMINI RESPONSE PROCESSING")
    print("-" * 40)
    
    # Create a mock Gemini response that should be returned for this data
    mock_gemini_response = [
        {
            "ticker": "SHOP",
            "company_name": "Shopify Inc.",
            "shares": 21.03,
            "current_value": 1803220,
            "current_value_currency": "JPY",
            "current_price": 85.77,
            "current_price_currency": "USD",
            "amount_invested_currency": "JPY",
            "gain_loss_percent": 13.71,
            "currency_context": "Japanese portfolio with ¥ values and USD prices"
        },
        {
            "ticker": "PLTR", 
            "company_name": "Palantir Technologies",
            "shares": 27.17,
            "current_value": 704300,
            "current_value_currency": "JPY",
            "current_price": 25.91,
            "current_price_currency": "USD",
            "amount_invested_currency": "JPY",
            "gain_loss_percent": -1.88,
            "currency_context": "Japanese portfolio with ¥ values and USD prices"
        }
    ]
    
    # Test the response processing
    processed_entries = extractor._process_gemini_response(mock_gemini_response)
    
    print(f"Processed {len(processed_entries)} entries")
    
    for i, entry in enumerate(processed_entries, 1):
        print(f"\nEntry {i}: {entry.get('ticker', 'Unknown')}")
        print(f"  Amount invested: {entry.get('amount_invested', 0)} {entry.get('amount_invested_currency', 'Unknown')}")
        print(f"  Current value: {entry.get('current_value', 0)} {entry.get('current_value_currency', 'Unknown')}")
        print(f"  Shares: {entry.get('shares', 0)}")
        
        # Check if currencies are preserved correctly
        amount_currency = entry.get('amount_invested_currency')
        value_currency = entry.get('current_value_currency')
        
        if amount_currency == 'JPY':
            print(f"  ✅ Amount invested currency correct: {amount_currency}")
        else:
            print(f"  ❌ Amount invested currency wrong: {amount_currency} (should be JPY)")
            
        if value_currency == 'JPY':
            print(f"  ✅ Current value currency correct: {value_currency}")
        else:
            print(f"  ❌ Current value currency wrong: {value_currency} (should be JPY)")
    
    # Final assessment
    print("\n5. FINAL ASSESSMENT")
    print("-" * 20)
    
    if primary_currency == 'JPY':
        print("✅ Currency detection: PASSED")
    else:
        print(f"❌ Currency detection: FAILED (got {primary_currency}, expected JPY)")
    
    if processed_entries:
        all_currencies_correct = all(
            entry.get('amount_invested_currency') == 'JPY' and 
            entry.get('current_value_currency') == 'JPY'
            for entry in processed_entries
        )
        
        if all_currencies_correct:
            print("✅ Currency preservation: PASSED")
        else:
            print("❌ Currency preservation: FAILED")
    else:
        print("❌ No entries processed")
    
    return primary_currency == 'JPY' and processed_entries and all_currencies_correct

if __name__ == "__main__":
    success = test_yen_end_to_end()
    if success:
        print("\n🎉 ALL TESTS PASSED! Japanese Yen detection and extraction working correctly!")
    else:
        print("\n💥 SOME TESTS FAILED! Issues remain with Japanese Yen handling.")
