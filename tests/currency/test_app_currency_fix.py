#!/usr/bin/env python3
"""
Test the actual Flask app currency fix
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_app_currency_fix():
    """Test that the Flask app correctly handles currency display"""
    
    print("🧪 Testing Flask App Currency Fix")
    print("=" * 50)
    
    try:
        # Import Flask app components
        from app import app, safe_float
        
        # Test the safe_float function
        print("Testing safe_float function:")
        test_values = [
            ("123.45", 123.45),
            ("1,234.56", 1234.56),
            ("1.234,56", 1234.56),  # European format
            ("invalid", 0.0),
            (None, 0.0),
            ("", 0.0)
        ]
        
        for test_val, expected in test_values:
            result = safe_float(test_val)
            status = "✅" if result == expected else "❌"
            print(f"  {status} safe_float('{test_val}') = {result} (expected {expected})")
        
        print("\n📊 Testing Currency Preservation Logic:")
        
        # Simulate portfolio entry data after the fix
        test_entry = {
            'ticker': 'SHOP',
            'amount_invested': 1585806.00,
            'buy_price': 75.43,
            'shares': 21023.90,
            'currency': 'DKK',  # This should be preserved
            'amount_invested_currency': 'DKK',
            'buy_price_currency': 'USD',
            'current_value_currency': 'DKK'
        }
        
        # Test that currency field is correctly set
        currency = test_entry.get('currency', 'USD')
        amount_currency = test_entry.get('amount_invested_currency', currency)
        
        print(f"  Entry Currency: {currency}")
        print(f"  Amount Currency: {amount_currency}")
        
        if currency == amount_currency:
            print("  ✅ Currency consistency maintained")
        else:
            print("  ⚠️  Mixed currencies detected")
        
        # Test currency symbol mapping (from template logic)
        currency_symbols = {
            'USD': '$', 'EUR': '€', 'GBP': '£', 'DKK': 'kr', 'SEK': 'kr', 
            'NOK': 'kr', 'JPY': '¥', 'CHF': 'CHF', 'CAD': 'C$', 'AUD': 'A$'
        }
        
        symbol = currency_symbols.get(currency, '$')
        amount = test_entry['amount_invested']
        
        # Template display logic
        if currency in ['DKK', 'SEK', 'NOK']:
            display_text = f"{amount:,.2f} {symbol}"
        else:
            display_text = f"{symbol}{amount:,.2f}"
        
        print(f"  Display Format: {display_text}")
        
        print("\n🎯 Expected Behavior:")
        print("  - Portfolio entries preserve their detected currencies")
        print("  - DKK amounts show as: 1,585,806.00 kr")
        print("  - USD amounts show as: $1,585,806.00")
        print("  - EUR amounts show as: €1,585,806.00")
        
        print("\n✅ App currency fix verification completed!")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import app components: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_app_currency_fix()
    if success:
        print("\n🎉 All tests passed! The currency fix should work correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
