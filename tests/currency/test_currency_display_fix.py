#!/usr/bin/env python3
"""
Test the complete currency display fix for Danish portfolios
"""

import sys
sys.path.append('.')
from portfolio_import import PortfolioImportService

def test_danish_portfolio_currency_selection():
    """Test that Danish portfolios trigger currency selection and display correctly"""
    
    print("=== TESTING DANISH PORTFOLIO CURRENCY SELECTION ===")
    
    # Danish portfolio with USD amounts (common scenario)
    dkk_text = '''
    Versus Systems Inc.
    NasdaqGS:VS
    Antal: 13 stk
    GAK: 161,61 USD
    Markedsværdi: 2.462,85 USD
    '''
    
    service = PortfolioImportService('test_key', 'test_key')
    result = service.extract_portfolio_from_text(dkk_text)
    api_response = service.format_for_api(result)
    
    print(f"✅ Language Detection: {service.ai_extractor.detected_language}")
    print(f"✅ Currency Detection: {api_response.get('detected_currency')}")
    print(f"✅ Requires User Selection: {api_response.get('currency_info', {}).get('requires_user_selection', False)}")
    
    gemini_question = api_response.get('currency_info', {}).get('gemini_question')
    if gemini_question:
        print(f"✅ Gemini Question: {gemini_question}")
    
    # Test the portfolio entry data
    if api_response.get('portfolio'):
        entry = api_response['portfolio'][0]
        print(f"\n📊 Portfolio Entry:")
        print(f"   Ticker: {entry.get('ticker')}")
        print(f"   Shares: {entry.get('shares')}")
        print(f"   Amount Invested: {entry.get('amount_invested')}")
        print(f"   Buy Price: {entry.get('buy_price')}")
        print(f"   Currency: {entry.get('currency')}")
        
        # Check if amounts are reasonable (not corrupted)
        amount_invested = entry.get('amount_invested', 0)
        buy_price = entry.get('buy_price', 0)
        
        if amount_invested > 0 and amount_invested < 1000000:  # Reasonable range
            print("✅ Amount invested is in reasonable range")
        else:
            print(f"❌ Amount invested seems corrupted: {amount_invested}")
            
        if buy_price > 0 and buy_price < 10000:  # Reasonable range for stock price
            print("✅ Buy price is in reasonable range")
        else:
            print(f"❌ Buy price seems corrupted: {buy_price}")
    
    print("\n=== SIMULATION: User Selects DKK ===")
    
    # Simulate what happens when user selects DKK
    # The amounts should be converted from USD to DKK
    usd_to_dkk_rate = 6.9  # Approximate rate
    
    if api_response.get('portfolio'):
        entry = api_response['portfolio'][0]
        usd_amount = entry.get('amount_invested', 0)
        usd_price = entry.get('buy_price', 0)
        
        # Convert to DKK (this is what the backend should do)
        dkk_amount = usd_amount * usd_to_dkk_rate
        dkk_price = usd_price * usd_to_dkk_rate
        
        print(f"📈 Converted to DKK:")
        print(f"   Amount Invested: {dkk_amount:.2f} DKK (was ${usd_amount:.2f} USD)")
        print(f"   Buy Price: {dkk_price:.2f} DKK (was ${usd_price:.2f} USD)")
        
        # This is what should be displayed in the portfolio table
        print(f"\n🎯 Expected Portfolio Display:")
        print(f"   VS: {dkk_amount:.2f} kr invested, {entry.get('shares')} shares @ {dkk_price:.2f} kr")

if __name__ == "__main__":
    test_danish_portfolio_currency_selection()