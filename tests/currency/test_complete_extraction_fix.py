#!/usr/bin/env python3
"""
Complete Extraction Fix Test
This test validates that the complete image extraction and currency calculation fix
works end-to-end, producing the correct results that match the user's expectations.
"""
import sys
import os
import unittest
from unittest.mock import patch, MagicMock

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from portfolio_import import PortfolioImportService
    from app import CurrencyConverter
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)

class TestCompleteExtractionFix(unittest.TestCase):
    
    def setUp(self):
        """Set up test environment."""
        self.service = PortfolioImportService("test_key", "test_key")
        print("🧪 Setting up complete extraction fix tests...")
    
    def test_end_to_end_extraction_and_calculation(self):
        """Test the complete end-to-end extraction and calculation process."""
        print("\n🎯 Testing complete end-to-end fix")
        
        # Simulate the exact text that would be extracted from the user's image
        image_text = """Company                 Value (¥)     Change %    Today      Price (USD)
Shopify Inc.           ¥1,803,220    ▲ 13.71%   ↑ 0.12%   USD 85.77
Palantir Tech.         ¥704,300      ▼ 1.88%    → 0.00%   $25.91
Roblox Corp.           ¥1,020,050    ▲ 9.02%    ↑ 0.31%   39.67 USD
Pinterest Inc.         ¥892,430      ▲0.96%     ↑ 0.06%   43.11
Block Inc.             ¥2,370,100    ▼ 4.20%    ↓ 0.21%   USD: 70.30"""
        
        print(f"📋 Extracting from image text ({len(image_text)} chars)")
        
        # Use the fixed extraction method
        extraction_result = self.service.ai_extractor._simple_text_analysis_extraction(image_text)
        
        # Validate extraction success
        self.assertTrue(extraction_result.get('portfolio'), "Should extract portfolio entries")
        portfolio = extraction_result['portfolio']
        
        print(f"✅ Extracted {len(portfolio)} entries from image")
        
        # Test each stock individually
        expected_results = {
            'SHOP': {
                'original_jpy': 1803220,
                'buy_price_usd': 85.77,
                'expected_shares_range': (140, 145)  # Allow range due to gain calculations
            },
            'PLTR': {
                'original_jpy': 704300,
                'buy_price_usd': 25.91,
                'expected_shares_range': (180, 185)
            },
            'RBLX': {
                'original_jpy': 1020050,
                'buy_price_usd': 39.67,  # From text
                'expected_shares_range': (170, 175)
            }
        }
        
        for entry in portfolio:
            ticker = entry['ticker']
            if ticker in expected_results:
                expected = expected_results[ticker]
                
                print(f"\n📊 Validating {ticker}:")
                print(f"   💰 Current Value: {entry['current_value']} {entry['current_value_currency']}")
                print(f"   💵 Amount Invested: {entry['amount_invested']:.2f} {entry['amount_invested_currency']}")
                print(f"   💲 Buy Price: {entry['buy_price']} {entry['buy_price_currency']}")
                print(f"   📈 Shares: {entry['shares']:.2f}")
                
                # Validate currency
                self.assertEqual(entry['amount_invested_currency'], 'JPY', f"{ticker} should have JPY amount_invested_currency")
                self.assertEqual(entry['current_value_currency'], 'JPY', f"{ticker} should have JPY current_value_currency")
                
                # Validate that current value is close to original JPY amount
                current_value = entry['current_value']
                original_jpy = expected['original_jpy']
                self.assertAlmostEqual(current_value, original_jpy, delta=original_jpy*0.1, 
                                     msg=f"{ticker} current value should be close to original JPY amount")
                
                # Validate share calculation is reasonable
                shares = entry['shares']
                expected_min, expected_max = expected['expected_shares_range']
                self.assertGreater(shares, expected_min, f"{ticker} shares should be > {expected_min}")
                self.assertLess(shares, expected_max, f"{ticker} shares should be < {expected_max}")
                
                print(f"   ✅ {ticker} validation PASSED")
        
        print(f"\n🎉 End-to-end extraction test PASSED!")
        return True
    
    def test_currency_conversion_accuracy(self):
        """Test that currency conversion produces expected share counts."""
        print("\n💱 Testing currency conversion accuracy")
        
        # Test with the specific SHOP example from user's screenshot
        amount_invested_jpy = 1803220
        buy_price_usd = 85.77
        
        # Convert JPY to USD using our converter
        amount_invested_usd = CurrencyConverter.convert_amount(amount_invested_jpy, 'JPY', 'USD')
        calculated_shares = amount_invested_usd / buy_price_usd
        
        print(f"📊 Conversion test for SHOP:")
        print(f"   Original: ¥{amount_invested_jpy:,} JPY")
        print(f"   Converted: ${amount_invested_usd:,.2f} USD")
        print(f"   Buy Price: ${buy_price_usd} USD") 
        print(f"   Calculated Shares: {calculated_shares:.4f}")
        print(f"   Expected Shares: ~140.86")
        
        # Should be very close to 140.86 shares
        self.assertAlmostEqual(calculated_shares, 140.86, places=1,
                              msg=f"SHOP shares should be ~140.86, got {calculated_shares}")
        
        print(f"   ✅ Currency conversion accuracy PASSED")
        return True
    
    def test_comparison_old_vs_new_system(self):
        """Test showing the dramatic improvement from old to new system."""
        print("\n⚖️  Testing old vs new system comparison")
        
        test_data = {
            'RBLX': {'jpy_amount': 1020050, 'usd_price': 39.67},
            'PINS': {'jpy_amount': 892430, 'usd_price': 43.11},
            'SQ': {'jpy_amount': 2370100, 'usd_price': 70.30}
        }
        
        for ticker, data in test_data.items():
            jpy_amount = data['jpy_amount']
            usd_price = data['usd_price']
            
            # OLD (BROKEN) METHOD: Treat JPY as USD
            old_wrong_shares = jpy_amount / usd_price
            
            # NEW (FIXED) METHOD: Convert currencies first
            amount_usd = CurrencyConverter.convert_amount(jpy_amount, 'JPY', 'USD')
            new_correct_shares = amount_usd / usd_price
            
            print(f"\n📊 {ticker} comparison:")
            print(f"   Input: ¥{jpy_amount:,} JPY ÷ ${usd_price} USD")
            print(f"   🚫 OLD (BROKEN): {old_wrong_shares:,.0f} shares")
            print(f"   ✅ NEW (FIXED): {new_correct_shares:.0f} shares")
            print(f"   📉 Reduction: {old_wrong_shares - new_correct_shares:,.0f} shares")
            
            # Validate the massive difference
            self.assertGreater(old_wrong_shares, new_correct_shares * 100, 
                             f"Old method should produce 100x more shares for {ticker}")
            
            # Validate new method produces reasonable results
            self.assertLess(new_correct_shares, 500, 
                           f"New method should produce reasonable share count for {ticker}")
        
        print(f"\n🎉 Old vs new comparison PASSED - dramatic improvements confirmed!")
        return True

def run_complete_tests():
    """Run all complete extraction fix tests."""
    print("🚀 Starting Complete Extraction Fix Tests")
    print("="*80)
    print("This test validates that the currency extraction and calculation fixes")
    print("resolve the critical issues identified in the user's screenshots.")
    print("="*80)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCompleteExtractionFix)
    
    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "="*80)
    if result.wasSuccessful():
        print("🎉 ALL COMPLETE EXTRACTION TESTS PASSED!")
        print("")
        print("✅ IMAGE EXTRACTION: Correctly extracts JPY amounts from images")
        print("✅ CURRENCY DETECTION: Properly identifies JPY vs USD currencies") 
        print("✅ NUMBER PARSING: Accurately parses Japanese number formats")
        print("✅ CURRENCY CONVERSION: Correctly converts JPY→USD for calculations")
        print("✅ SHARE CALCULATION: Produces accurate share counts (140.86 vs 21,023)")
        print("✅ DISPLAY: Shows original JPY amounts with correct currency labels")
        print("")
        print("🔧 CRITICAL ISSUES RESOLVED:")
        print("   ❌ RBLX showing 65M instead of 1M → ✅ Now shows correct amounts")
        print("   ❌ Share counts massively inflated → ✅ Now calculates correctly")
        print("   ❌ Currency symbols inconsistent → ✅ Now displays proper JPY labels")
        print("")
        print("The portfolio import system should now work correctly with the")
        print("user's Japanese Yen portfolio screenshots!")
    else:
        print("❌ Some complete extraction tests failed:")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
            print(failure[1])
        for error in result.errors:
            print(f"ERROR: {error[0]}")
            print(error[1])
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_complete_tests()
    sys.exit(0 if success else 1)