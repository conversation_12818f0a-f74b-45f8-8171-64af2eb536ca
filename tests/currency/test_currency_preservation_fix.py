#!/usr/bin/env python3

"""
Test script to verify the currency preservation fix.

This script tests that amount_invested_currency properly preserves
the detected currency instead of defaulting to USD.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_currency_preservation():
    """Test that currencies are preserved correctly."""
    print("🧪 Testing Currency Preservation Fix")
    print("=" * 60)
    
    try:
        from portfolio_import import AIPortfolioExtractor
        print("✅ Successfully imported AIPortfolioExtractor")
        
        # Create AI extractor with DKK as detected currency
        extractor = AIPortfolioExtractor("test_key", "test_key")
        extractor.detected_currency = 'DKK'
        
        # Simulate Gemini AI response with DKK values
        gemini_response = [
            {
                'ticker': 'GOOGL',
                'shares': 83.26,
                'buy_price': 173.35,
                'buy_price_currency': 'USD',  # Buy price in USD (historical)
                'current_price': 190.45,
                'current_price_currency': 'DKK',  # Current price in DKK
                'current_value': 15848.0,
                'current_value_currency': 'DKK',  # Current value in DKK
                'amount_invested': 14432.0,  # This should be in DKK
                'amount_invested_currency': 'DKK',  # AI should return DKK
                'currency': 'DKK'  # General currency detected
            }
        ]
        
        print(f"\n📊 Test Scenario:")
        print(f"   Detected Currency: {extractor.detected_currency}")
        print(f"   AI Response amount_invested_currency: DKK")
        print(f"   Expected Result: amount_invested_currency = DKK")
        
        # Process the response
        processed_entries = extractor._process_gemini_response(gemini_response)
        
        if len(processed_entries) == 0:
            print(f"❌ FAILED: No entries processed")
            return False
        
        entry = processed_entries[0]
        
        print(f"\n🔧 Processing Results:")
        print(f"   Ticker: {entry.get('ticker', 'N/A')}")
        print(f"   Amount Invested: {entry.get('amount_invested', 'N/A')}")
        print(f"   Amount Invested Currency: {entry.get('amount_invested_currency', 'N/A')}")
        print(f"   Buy Price Currency: {entry.get('buy_price_currency', 'N/A')}")
        print(f"   Current Value Currency: {entry.get('current_value_currency', 'N/A')}")
        
        # Check if amount_invested_currency is preserved correctly
        amount_invested_currency = entry.get('amount_invested_currency')
        
        if amount_invested_currency == 'DKK':
            print(f"\n✅ SUCCESS: amount_invested_currency = DKK (preserved correctly)")
            return True
        elif amount_invested_currency == 'USD':
            print(f"\n❌ FAILED: amount_invested_currency = USD (should be DKK)")
            print(f"   🚨 The fix didn't work - still defaulting to USD")
            return False
        else:
            print(f"\n⚠️  UNEXPECTED: amount_invested_currency = {amount_invested_currency}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_portfolio_entry_class():
    """Test the PortfolioEntry class currency handling."""
    print(f"\n🏗️  Testing PortfolioEntry Class")
    print("-" * 40)
    
    try:
        from portfolio_import import PortfolioEntry
        
        # Test case: DKK detected, but amount_invested_currency not set
        entry = PortfolioEntry(
            ticker='GOOGL',
            shares=83.26,
            buy_price=173.35,
            buy_price_currency='USD',
            current_price=190.45,
            current_price_currency='DKK',
            current_value=15848.0,
            current_value_currency='DKK',
            amount_invested=14432.0,
            amount_invested_currency=None,  # Not set - should default to currency
            currency='DKK'  # Detected currency
        )
        
        print(f"   Input: currency='DKK', amount_invested_currency=None")
        print(f"   Result: amount_invested_currency='{entry.amount_invested_currency}'")
        
        if entry.amount_invested_currency == 'DKK':
            print(f"   ✅ SUCCESS: PortfolioEntry defaults to detected currency")
            return True
        else:
            print(f"   ❌ FAILED: PortfolioEntry defaulted to '{entry.amount_invested_currency}' instead of 'DKK'")
            return False
        
    except Exception as e:
        print(f"   ❌ PortfolioEntry test failed: {e}")
        return False

def test_financial_value_parsing():
    """Test that financial values are not converted to USD."""
    print(f"\n💰 Testing Financial Value Parsing")
    print("-" * 40)
    
    try:
        from portfolio_import import AIPortfolioExtractor
        
        extractor = AIPortfolioExtractor("test_key", "test_key")
        
        # Test DKK value parsing
        dkk_value = "15.848 DKK"
        parsed_value = extractor._parse_financial_value(dkk_value)
        
        print(f"   Input: '{dkk_value}'")
        print(f"   Parsed: {parsed_value}")
        
        # Should return 15848.0 (original value) not converted to USD
        if parsed_value == 15848.0:
            print(f"   ✅ SUCCESS: Value preserved without USD conversion")
            return True
        elif parsed_value and parsed_value < 3000:  # Would be ~2300 if converted to USD
            print(f"   ❌ FAILED: Value was converted to USD ({parsed_value})")
            return False
        else:
            print(f"   ⚠️  UNEXPECTED: Parsed value = {parsed_value}")
            return False
        
    except Exception as e:
        print(f"   ❌ Financial value parsing test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Currency Preservation Fix Tests")
    print("=" * 70)
    
    success = True
    
    # Test 1: Main currency preservation
    if not test_currency_preservation():
        success = False
    
    # Test 2: PortfolioEntry class
    if not test_portfolio_entry_class():
        success = False
    
    # Test 3: Financial value parsing
    if not test_financial_value_parsing():
        success = False
    
    print(f"\n" + "=" * 70)
    if success:
        print("🎉 ALL TESTS PASSED - Currency Preservation Fix Complete!")
        print("")
        print("📋 Summary of Fixes Applied:")
        print("   ✅ PortfolioEntry defaults to detected currency, not buy_price_currency")
        print("   ✅ Processing logic uses detected currency for amount_invested")
        print("   ✅ Financial value parsing preserves original values (no USD conversion)")
        print("   ✅ Gemini AI prompt enforces detected currency usage")
        print("")
        print("🎯 Expected User Experience:")
        print("   📊 User uploads DKK portfolio: '15.848 kr'")
        print("   🔍 AI detects DKK and preserves it")
        print("   💰 amount_invested shows: '15.848,00 DKK' ✅")
        print("   🚫 NO MORE: '15.848,00 USD' ❌")
    else:
        print("❌ SOME TESTS FAILED - Currency preservation needs more work")
    
    print("=" * 70)
