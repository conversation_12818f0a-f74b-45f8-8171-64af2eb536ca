#!/usr/bin/env python3
"""
Test script to verify the currency calculation bug fix.

This script tests the scenario where:
- User shows DKK portfolio
- Amount invested is in DKK
- Shares are calculated correctly in DKK (not converted to USD)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import PortfolioImportService, PortfolioEntry

def test_dkk_currency_preservation():
    """Test that DKK amounts are preserved and shares calculated correctly."""
    print("🧪 Testing DKK Currency Preservation and Calculation")
    print("=" * 60)
    
    # Simulate the exact scenario from the user's DKK portfolio
    # 13 stk, GAK 161.61 USD, Markedsværdi 2.462,85 USD
    # But this should be interpreted as DKK amounts, not USD
    
    # Create a portfolio entry with DKK amounts
    entry = PortfolioEntry(
        ticker="GOOGL",
        shares=13.0,
        buy_price=161.61,  # This should be DKK, not USD
        current_value=2462.85,  # This should be DKK, not USD
        currency="DKK",
        buy_price_currency="DKK",
        current_value_currency="DKK"
    )
    
    print(f"📊 Portfolio Entry Analysis:")
    print(f"   Ticker: {entry.ticker}")
    print(f"   Shares: {entry.shares}")
    print(f"   Buy Price: {entry.buy_price} {entry.buy_price_currency}")
    print(f"   Current Value: {entry.current_value} {entry.current_value_currency}")
    print(f"   Amount Invested: {entry.amount_invested} {entry.currency}")
    
    # Verify the calculation
    expected_amount_invested = entry.shares * entry.buy_price
    print(f"\n🔍 Calculation Verification:")
    print(f"   Expected Amount Invested: {entry.shares} × {entry.buy_price} = {expected_amount_invested} DKK")
    print(f"   Actual Amount Invested: {entry.amount_invested} DKK")
    
    # Check if calculation is correct
    if abs(entry.amount_invested - expected_amount_invested) < 0.01:
        print(f"✅ CALCULATION CORRECT: Shares calculated in DKK currency")
        print(f"   {entry.shares} shares × {entry.buy_price} DKK = {entry.amount_invested} DKK")
        return True
    else:
        print(f"❌ CALCULATION ERROR: Currency conversion bug detected")
        print(f"   Expected: {expected_amount_invested} DKK")
        print(f"   Got: {entry.amount_invested} DKK")
        return False

def test_mixed_currency_scenario():
    """Test mixed currency scenario (buy price in USD, current value in DKK)."""
    print("\n🧪 Testing Mixed Currency Scenario")
    print("=" * 60)
    
    # Create entry with mixed currencies (common in international portfolios)
    entry = PortfolioEntry(
        ticker="AAPL",
        shares=10.0,
        buy_price=150.0,  # USD
        current_value=10350.0,  # DKK (approximately 1500 USD at 6.9 DKK/USD)
        currency="USD",  # Primary currency for amount_invested
        buy_price_currency="USD",
        current_value_currency="DKK"
    )
    
    print(f"📊 Mixed Currency Entry Analysis:")
    print(f"   Ticker: {entry.ticker}")
    print(f"   Shares: {entry.shares}")
    print(f"   Buy Price: {entry.buy_price} {entry.buy_price_currency}")
    print(f"   Current Value: {entry.current_value} {entry.current_value_currency}")
    print(f"   Amount Invested: {entry.amount_invested} {entry.currency}")
    
    # Verify amount_invested is calculated in buy_price currency
    expected_amount_invested = entry.shares * entry.buy_price
    print(f"\n🔍 Mixed Currency Calculation:")
    print(f"   Amount Invested should be in {entry.buy_price_currency}")
    print(f"   Expected: {entry.shares} × {entry.buy_price} = {expected_amount_invested} {entry.buy_price_currency}")
    print(f"   Actual: {entry.amount_invested} {entry.currency}")
    
    if abs(entry.amount_invested - expected_amount_invested) < 0.01 and entry.currency == entry.buy_price_currency:
        print(f"✅ MIXED CURRENCY HANDLING CORRECT")
        return True
    else:
        print(f"❌ MIXED CURRENCY HANDLING ERROR")
        return False

def test_currency_conversion_prevention():
    """Test that currencies are NOT converted during import."""
    print("\n🧪 Testing Currency Conversion Prevention")
    print("=" * 60)
    
    # Test data that should NOT be converted
    test_cases = [
        {
            'name': 'DKK Portfolio',
            'shares': 13.0,
            'buy_price': 161.61,
            'currency': 'DKK',
            'expected_amount': 13.0 * 161.61  # Should stay in DKK
        },
        {
            'name': 'EUR Portfolio',
            'shares': 25.0,
            'buy_price': 45.50,
            'currency': 'EUR',
            'expected_amount': 25.0 * 45.50  # Should stay in EUR
        },
        {
            'name': 'USD Portfolio',
            'shares': 50.0,
            'buy_price': 120.00,
            'currency': 'USD',
            'expected_amount': 50.0 * 120.00  # Should stay in USD
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        entry = PortfolioEntry(
            ticker="TEST",
            shares=test_case['shares'],
            buy_price=test_case['buy_price'],
            currency=test_case['currency'],
            buy_price_currency=test_case['currency']
        )
        
        print(f"\n📊 {test_case['name']}:")
        print(f"   Shares: {entry.shares}")
        print(f"   Buy Price: {entry.buy_price} {entry.currency}")
        print(f"   Amount Invested: {entry.amount_invested} {entry.currency}")
        print(f"   Expected: {test_case['expected_amount']} {test_case['currency']}")
        
        if (abs(entry.amount_invested - test_case['expected_amount']) < 0.01 and 
            entry.currency == test_case['currency']):
            print(f"   ✅ PASSED: No currency conversion")
        else:
            print(f"   ❌ FAILED: Unexpected currency conversion")
            all_passed = False
    
    return all_passed

def main():
    """Run all currency bug fix tests."""
    print("🚀 CURRENCY BUG FIX VERIFICATION")
    print("Testing the fix for DKK portfolio calculation issues")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: DKK currency preservation
    if test_dkk_currency_preservation():
        tests_passed += 1
    
    # Test 2: Mixed currency handling
    if test_mixed_currency_scenario():
        tests_passed += 1
    
    # Test 3: Currency conversion prevention
    if test_currency_conversion_prevention():
        tests_passed += 1
    
    print(f"\n🎯 TEST RESULTS:")
    print(f"   Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print(f"🎉 ALL TESTS PASSED!")
        print(f"✅ Currency calculation bug has been FIXED!")
        print(f"✅ DKK amounts are preserved correctly")
        print(f"✅ Shares are calculated in the correct currency")
        print(f"✅ No unwanted currency conversions")
    else:
        print(f"⚠️  Some tests failed. Currency bug may still exist.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)