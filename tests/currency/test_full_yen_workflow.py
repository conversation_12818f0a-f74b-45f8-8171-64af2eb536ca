#!/usr/bin/env python3
"""
Test the complete workflow for Japanese Yen portfolio import
This simulates exactly what happens when you upload your image
"""

import sys
sys.path.append('.')
from portfolio_import import PortfolioImportService
import json

def test_full_yen_workflow():
    """Test the complete workflow from image upload to portfolio display"""
    
    print("🇯🇵 TESTING COMPLETE JAPANESE YEN WORKFLOW")
    print("=" * 60)
    
    # Step 1: Simulate image upload and extraction
    print("1. IMAGE UPLOAD & EXTRACTION")
    print("-" * 35)
    
    # This is what your image contains (Japanese Yen portfolio)
    image_text = """
    Company                Value (¥)      Change %    Today       Price (USD)
    Shopify Inc.          ¥1,803,220     ▲ 13.71 %   ↑ 0.12 %    USD 85.77
    Palantir Tech.        ¥704300        ▼ 1.88%     → 0.00 %    $25.91
    Roblox Corp.          ¥1,020,050     ▲ 9.02%     ↑ 0.31%     39.67 USD
    Pinterest Inc.        ¥892,430       ▲0.96 %     ↑ 0.06%     43.11
    Block Inc.            ¥2,370,100     ▼ 4.20%     ↓ 0.21%     USD: 70.30
    """
    
    service = PortfolioImportService('test_key', 'test_key')
    
    # Extract portfolio data (this is what happens when you upload the image)
    result = service.extract_portfolio_from_text(image_text)
    api_response = service.format_for_api(result)
    
    print(f"✅ Extraction Success: {api_response.get('success', False)}")
    print(f"💱 Detected Currency: {api_response.get('detected_currency', 'N/A')}")
    
    # Check if mixed currency is detected (this should happen)
    currency_info = api_response.get('currency_info', {})
    requires_selection = currency_info.get('requires_user_selection', False)
    
    print(f"🔄 Requires Currency Selection: {requires_selection}")
    
    if requires_selection:
        print(f"❓ Question: {currency_info.get('gemini_question', 'N/A')}")
        detected_currencies = currency_info.get('detected_currencies', [])
        print(f"📊 Detected Currencies: {detected_currencies}")
        
        # Step 2: Simulate user selecting JPY (what you would do)
        print(f"\n2. USER SELECTS JAPANESE YEN (JPY)")
        print("-" * 40)
        
        # This simulates what happens when you click "Japanese Yen" in the dialog
        selected_currency = 'JPY'
        print(f"👤 User selects: {selected_currency}")
        
        # Prepare data for currency selection (this is what the frontend sends)
        currency_selection_data = {
            'selected_currency': selected_currency,
            'original_data': api_response
        }
        
        # Step 3: Simulate the currency selection processing
        print(f"\n3. PROCESSING CURRENCY SELECTION")
        print("-" * 40)
        
        # This simulates the handle_currency_selection() function
        original_data = currency_selection_data['original_data']
        original_data['selected_currency'] = selected_currency
        original_data['user_selected_currency'] = True
        original_data['display_currency'] = selected_currency
        
        print(f"🎯 Selected Currency: {selected_currency}")
        print(f"📊 Preserving original portfolio data")
        
        # Step 4: Simulate portfolio import confirmation
        print(f"\n4. PORTFOLIO IMPORT CONFIRMATION")
        print("-" * 40)
        
        portfolio_entries = original_data.get('portfolio', [])
        print(f"📊 Processing {len(portfolio_entries)} entries")
        
        # Simulate the confirm_portfolio_import_with_data logic
        processed_entries = []
        
        for entry in portfolio_entries:
            # This is the exact logic from confirm_portfolio_import_with_data
            entry_amount_currency = entry.get('amount_invested_currency', 'JPY')
            entry_buy_currency = entry.get('buy_price_currency', 'JPY')
            entry_current_currency = entry.get('current_value_currency', 'JPY')
            
            entry_main_currency = entry_amount_currency
            
            amount_invested_original = float(entry.get('amount_invested', 0))
            buy_price_original = float(entry.get('buy_price', 0))
            
            # Create the portfolio entry as it would be stored
            processed_entry = {
                'ticker': entry['ticker'],
                'shares': float(entry.get('shares', 0)),
                'amount_invested': amount_invested_original,
                'purchase_price': buy_price_original,
                'buy_price': buy_price_original,
                'currency': entry_main_currency,  # This should be JPY
                'amount_invested_currency': entry_amount_currency,
                'buy_price_currency': entry_buy_currency,
                'current_value_currency': entry_current_currency,
            }
            
            processed_entries.append(processed_entry)
            
            print(f"  {entry['ticker']}: {amount_invested_original:,.0f} {entry_main_currency}")
        
        # Step 5: Verify final results
        print(f"\n5. FINAL VERIFICATION")
        print("-" * 25)
        
        print("Expected results in your portfolio:")
        for entry in processed_entries:
            ticker = entry['ticker']
            amount = entry['amount_invested']
            currency = entry['currency']
            
            # This is how the template would display it
            if currency == 'JPY':
                display = f"¥{amount:,.0f}"
            else:
                display = f"{currency} {amount:,.2f}"
            
            print(f"  {ticker}: {display}")
        
        # Check if all currencies are JPY
        currencies = set(entry['currency'] for entry in processed_entries)
        
        if currencies == {'JPY'}:
            print(f"\n🎉 SUCCESS: All entries correctly show JPY currency!")
            print(f"✅ Your portfolio should display Japanese Yen amounts")
            return True
        else:
            print(f"\n❌ FAILURE: Found currencies: {currencies}")
            print(f"❌ Expected only JPY, but got: {currencies}")
            return False
    
    else:
        print(f"❌ No mixed currency detected - this is unexpected for your image")
        return False

def test_template_display():
    """Test how the template would display Japanese Yen amounts"""
    
    print(f"\n6. TEMPLATE DISPLAY SIMULATION")
    print("-" * 35)
    
    # Simulate portfolio entries as they would be stored
    test_entries = [
        {'ticker': 'SHOP', 'amount_invested': 1803220.0, 'currency': 'JPY'},
        {'ticker': 'PLTR', 'amount_invested': 704300.0, 'currency': 'JPY'},
        {'ticker': 'RBLX', 'amount_invested': 1020050.0, 'currency': 'JPY'},
        {'ticker': 'PINS', 'amount_invested': 892430.0, 'currency': 'JPY'},
        {'ticker': 'SQ', 'amount_invested': 2370100.0, 'currency': 'JPY'},
    ]
    
    # Simulate template currency display logic
    currency_symbols = {
        'USD': '$', 'EUR': '€', 'GBP': '£', 'JPY': '¥', 'DKK': 'kr'
    }
    
    print("How your portfolio should display:")
    for entry in test_entries:
        currency = entry['currency']
        amount = entry['amount_invested']
        symbol = currency_symbols.get(currency, currency)
        
        # Template logic for JPY (symbol before amount)
        if currency == 'JPY':
            display = f"{symbol}{amount:,.0f}"
        else:
            display = f"{symbol}{amount:,.2f}"
        
        print(f"  {entry['ticker']}: {display}")

if __name__ == "__main__":
    success = test_full_yen_workflow()
    test_template_display()
    
    if success:
        print(f"\n✅ WORKFLOW TEST PASSED")
        print(f"Your Japanese Yen portfolio should work correctly!")
        print(f"If you're still seeing USD, there might be an issue with:")
        print(f"  - Image OCR not reading ¥ symbols correctly")
        print(f"  - Browser cache showing old data")
        print(f"  - Session data from previous imports")
    else:
        print(f"\n❌ WORKFLOW TEST FAILED")
        print(f"There's still an issue with the currency handling logic")
