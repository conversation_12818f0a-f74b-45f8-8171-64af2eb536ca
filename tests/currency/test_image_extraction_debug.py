#!/usr/bin/env python3
"""
Image Extraction Debug Test
This test debugs the OCR and AI extraction process to identify why wrong numbers 
are being extracted from images.
"""
import sys
import os
import unittest
from unittest.mock import patch, MagicMock

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from portfolio_import import PortfolioImportService
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)

class TestImageExtractionDebug(unittest.TestCase):
    
    def setUp(self):
        """Set up test environment."""
        self.service = PortfolioImportService("test_key", "test_key")
        print("🧪 Setting up image extraction debug tests...")
    
    def test_manual_text_extraction(self):
        """Test extraction with manually provided text from the user's image."""
        print("\n🔍 Testing manual text extraction from user's screenshot")
        
        # This is the actual text that should be extracted from the user's image
        actual_image_text = """Company                 Value (¥)     Change %    Today      Price (USD)
Shopify Inc.           ¥1,803,220    ▲ 13.71%   ↑ 0.12%   USD 85.77
Palantir Tech.         ¥704,300      ▼ 1.88%    → 0.00%   $25.91
Roblox Corp.           ¥1,020,050    ▲ 9.02%    ↑ 0.31%   39.67 USD
Pinterest Inc.         ¥892,430      ▲0.96%     ↑ 0.06%   43.11
Block Inc.             ¥2,370,100    ▼ 4.20%    ↓ 0.21%   USD: 70.30"""
        
        print(f"📋 Input text:")
        print(actual_image_text)
        print("\n" + "="*60)
        
        # Test AI extraction with this text
        result = self.service.ai_extractor.extract_portfolio_data_with_ai(actual_image_text)
        
        print(f"🤖 AI Extraction Result:")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Portfolio entries: {len(result.get('portfolio', []))}")
        
        if result.get('portfolio'):
            print(f"\n📊 Extracted entries:")
            for i, entry in enumerate(result.get('portfolio', []), 1):
                print(f"   {i}. {entry.get('ticker', 'UNKNOWN')}")
                print(f"      💰 Amount Invested: {entry.get('amount_invested', 'N/A')} {entry.get('amount_invested_currency', 'N/A')}")
                print(f"      💲 Buy Price: {entry.get('buy_price', 'N/A')} {entry.get('buy_price_currency', 'N/A')}")
                print(f"      📈 Shares: {entry.get('shares', 'N/A')}")
                print(f"      📊 Current Value: {entry.get('current_value', 'N/A')} {entry.get('current_value_currency', 'N/A')}")
                print()
        
        # Validate specific expected values
        expected_values = {
            'SHOP': {'amount_invested': 1803220, 'currency': 'JPY'},
            'PLTR': {'amount_invested': 704300, 'currency': 'JPY'},
            'RBLX': {'amount_invested': 1020050, 'currency': 'JPY'},
            'PINS': {'amount_invested': 892430, 'currency': 'JPY'},
            'SQ': {'amount_invested': 2370100, 'currency': 'JPY'}
        }
        
        portfolio = result.get('portfolio', [])
        for entry in portfolio:
            ticker = entry.get('ticker', '')
            if ticker in expected_values:
                expected = expected_values[ticker]
                actual_amount = entry.get('amount_invested', 0)
                actual_currency = entry.get('amount_invested_currency', '')
                
                print(f"✅ VALIDATION for {ticker}:")
                print(f"   Expected: {expected['amount_invested']} {expected['currency']}")
                print(f"   Actual: {actual_amount} {actual_currency}")
                
                # Check if values match within reasonable tolerance
                if isinstance(actual_amount, (int, float)) and abs(actual_amount - expected['amount_invested']) < 1000:
                    print(f"   ✅ Amount PASSED (close enough)")
                else:
                    print(f"   ❌ Amount FAILED - significant difference!")
                
                if actual_currency == expected['currency']:
                    print(f"   ✅ Currency PASSED")
                else:
                    print(f"   ❌ Currency FAILED")
                print()
        
        # Overall success criteria
        if result.get('success') and len(portfolio) >= 3:
            print("🎉 Overall extraction test PASSED!")
            return True
        else:
            print("❌ Overall extraction test FAILED!")
            return False
    
    def test_currency_detection_from_text(self):
        """Test the currency detection logic specifically."""
        print("\n🔍 Testing currency detection from text")
        
        test_text = "Value (¥) ¥1,803,220 USD 85.77"
        
        detected_currency = self.service.ai_extractor._detect_primary_currency(test_text)
        
        print(f"📋 Input text: '{test_text}'")
        print(f"🇯🇵 Detected currency: {detected_currency}")
        
        if detected_currency == 'JPY':
            print("✅ Currency detection PASSED - correctly identified JPY")
            return True
        else:
            print("❌ Currency detection FAILED - should be JPY")
            return False
    
    def test_number_parsing(self):
        """Test the number parsing logic specifically."""
        print("\n🔍 Testing number parsing logic")
        
        test_cases = [
            {'input': '1,803,220', 'expected': 1803220},
            {'input': '704,300', 'expected': 704300},
            {'input': '1,020,050', 'expected': 1020050},
            {'input': '892,430', 'expected': 892430},
            {'input': '2,370,100', 'expected': 2370100},
        ]
        
        all_passed = True
        for case in test_cases:
            parsed = self.service.ai_extractor._parse_financial_value(case['input'])
            
            print(f"   '{case['input']}' → {parsed} (expected: {case['expected']})")
            
            if abs(parsed - case['expected']) < 1:
                print(f"   ✅ PASSED")
            else:
                print(f"   ❌ FAILED")
                all_passed = False
        
        if all_passed:
            print("✅ Number parsing test PASSED!")
        else:
            print("❌ Number parsing test FAILED!")
        
        return all_passed

def run_debug_tests():
    """Run all debug tests."""
    print("🚀 Starting Image Extraction Debug Tests")
    print("="*70)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestImageExtractionDebug)
    
    # Run tests with verbose output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "="*70)
    if result.wasSuccessful():
        print("🎉 ALL DEBUG TESTS PASSED!")
        print("The extraction logic should now work correctly with the enhanced prompts.")
    else:
        print("❌ Some debug tests failed. Need to investigate further:")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
            print(failure[1])
        for error in result.errors:
            print(f"ERROR: {error[0]}")
            print(error[1])
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_debug_tests()
    sys.exit(0 if success else 1)