#!/usr/bin/env python3
"""
Test the web interface endpoints for portfolio import
"""

import requests
import json

def test_web_endpoints():
    """Test the actual web endpoints"""
    base_url = "http://127.0.0.1:9878"
    
    print("Testing Portfolio Import Web Interface")
    print("=" * 50)
    
    # Test 1: Image upload endpoint
    print("\n1. Testing image upload endpoint...")
    
    # Create a dummy image file
    dummy_image = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    
    try:
        files = {'image': ('test.png', dummy_image, 'image/png')}
        response = requests.post(f"{base_url}/api/import/image", files=files, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   Image upload: ✅ SUCCESS")
            print(f"   Entries found: {len(result.get('portfolio', []))}")
            print(f"   Cash position: ${result.get('cash_position', 0)}")
            
            # Show first few entries
            for i, entry in enumerate(result.get('portfolio', [])[:3]):
                print(f"     {entry['ticker']}: ${entry['amount_invested']} @ ${entry['buy_price']}")
        else:
            print(f"   Image upload: ❌ FAILED (Status: {response.status_code})")
            print(f"   Response: {response.text}")
    
    except Exception as e:
        print(f"   Image upload: ❌ ERROR - {e}")
    
    # Test 2: Spreadsheet upload endpoint
    print("\n2. Testing spreadsheet upload endpoint...")
    
    try:
        # Read the test CSV file
        with open('test_simple_columns.csv', 'rb') as f:
            csv_data = f.read()
        
        files = {'spreadsheet': ('test.csv', csv_data, 'text/csv')}
        response = requests.post(f"{base_url}/api/import/spreadsheet", files=files, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   Spreadsheet upload: ✅ SUCCESS")
            print(f"   Entries found: {len(result.get('portfolio', []))}")
            print(f"   Cash position: ${result.get('cash_position', 0)}")
            
            # Show first few entries
            for i, entry in enumerate(result.get('portfolio', [])[:3]):
                print(f"     {entry['ticker']}: ${entry['amount_invested']} @ ${entry['buy_price']}")
        else:
            print(f"   Spreadsheet upload: ❌ FAILED (Status: {response.status_code})")
            print(f"   Response: {response.text}")
    
    except Exception as e:
        print(f"   Spreadsheet upload: ❌ ERROR - {e}")
    
    # Test 3: Import confirmation endpoint
    print("\n3. Testing import confirmation endpoint...")
    
    try:
        # Sample portfolio data to confirm
        test_data = {
            'success': True,
            'portfolio': [
                {
                    'ticker': 'AAPL',
                    'amount_invested': 1500.0,
                    'buy_price': 150.5,
                    'purchase_date': '2023-04-15',
                    'shares': 9.97
                },
                {
                    'ticker': 'MSFT',
                    'amount_invested': 2000.0,
                    'buy_price': 250.75,
                    'purchase_date': '2023-03-20',
                    'shares': 7.98
                }
            ],
            'cash_position': 500.0
        }
        
        response = requests.post(
            f"{base_url}/api/import/confirm", 
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   Import confirmation: ✅ SUCCESS")
            print(f"   Message: {result.get('message', 'No message')}")
        else:
            print(f"   Import confirmation: ❌ FAILED (Status: {response.status_code})")
            print(f"   Response: {response.text}")
    
    except Exception as e:
        print(f"   Import confirmation: ❌ ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("Web interface testing complete!")

if __name__ == "__main__":
    test_web_endpoints()
