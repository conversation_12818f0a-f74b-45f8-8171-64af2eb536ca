#!/usr/bin/env python3
"""
Test the web integration with the improved portfolio import
"""

import os
import sys

# Set the Google API key for testing
os.environ['GOOGLE_API_KEY'] = 'AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o'

from portfolio_import import process_image_upload

def test_web_integration():
    """Test the web integration function that the Flask app calls"""
    print("Testing Web Integration with Improved Portfolio Import")
    print("=" * 60)
    
    # Create a dummy image (the actual OCR will be handled by Google Vision or fallback)
    dummy_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    
    # Use the same function that the web interface calls
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    eodhd_api_key = None  # Optional for this test
    
    print("Calling process_image_upload() function...")
    result = process_image_upload(dummy_image_data, google_vision_api_key, eodhd_api_key)
    
    print(f"\nResult:")
    print(f"Success: {result['success']}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Warnings: {result.get('warnings', [])}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    print(f"Cash position: ${result.get('cash_position', 0)}")
    
    # Show the structure that the web interface expects
    if result['success']:
        portfolio = result.get('portfolio', [])
        if portfolio:
            print(f"\nFirst entry structure:")
            first_entry = portfolio[0]
            for key, value in first_entry.items():
                print(f"  {key}: {value}")
        else:
            print("\nNo portfolio entries found (expected for dummy image)")
    
    print(f"\nResult structure matches web interface expectations: {check_web_format(result)}")

def check_web_format(result):
    """Check if the result matches the expected web interface format"""
    required_fields = ['success', 'portfolio', 'cash_position']
    
    # Check top-level fields
    for field in required_fields:
        if field not in result:
            return f"Missing field: {field}"
    
    # Check portfolio entry structure
    portfolio = result.get('portfolio', [])
    if portfolio:
        entry = portfolio[0]
        expected_entry_fields = ['ticker', 'amount_invested', 'buy_price', 'shares', 'purchase_date']
        for field in expected_entry_fields:
            if field not in entry:
                return f"Missing portfolio entry field: {field}"
    
    return True

if __name__ == "__main__":
    test_web_integration()
