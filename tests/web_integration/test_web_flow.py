#!/usr/bin/env python3
"""
Test the exact web interface flow for portfolio import
"""

import io
import csv
from portfolio_import import process_spreadsheet_upload

def create_test_csv():
    """Create a test CSV file in memory"""
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header and data
    writer.writerow(['A', 'B', 'C', 'D'])
    writer.writerow(['AAPL', '1500.00', '150.50', '2023-04-15'])
    writer.writerow(['MSFT', '2000.00', '250.75', '2023-03-20'])
    writer.writerow(['GOOGL', '1200.00', '120.25', '2023-05-10'])
    
    # Get the CSV content as bytes
    csv_content = output.getvalue()
    return csv_content.encode('utf-8')

def test_web_interface_flow():
    """Test the exact flow that the web interface uses"""
    print("Testing Web Interface Flow")
    print("=" * 40)
    
    # Create test CSV data
    csv_data = create_test_csv()
    print(f"Created test CSV data: {len(csv_data)} bytes")
    
    # Use the same function that the web interface calls
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(csv_data, 'test.csv', google_vision_api_key)
    
    print(f"\nResult:")
    print(f"Success: {result['success']}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Warnings: {result.get('warnings', [])}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    print(f"Cash position: ${result.get('cash_position', 0)}")
    
    # Check if we got real data (not mock data)
    portfolio = result.get('portfolio', [])
    if portfolio:
        print(f"\nPortfolio entries:")
        for entry in portfolio:
            print(f"  {entry['ticker']}: ${entry['amount_invested']} @ ${entry['buy_price']} on {entry['purchase_date']}")
        
        # Check if this looks like real data vs mock data
        tickers = [entry['ticker'] for entry in portfolio]
        amounts = [entry['amount_invested'] for entry in portfolio]
        prices = [entry['buy_price'] for entry in portfolio]
        
        # Mock data has specific patterns like $425.80 and $2,554.80
        is_mock_data = any(price == 425.80 for price in prices) or any(amount == 2554.80 for amount in amounts)
        
        if is_mock_data:
            print("\n❌ PROBLEM: Detected mock/default data instead of real data!")
            return False
        else:
            print("\n✅ SUCCESS: Real data processed correctly!")
            return True
    else:
        print("\n❌ PROBLEM: No portfolio entries found!")
        return False

def test_error_case():
    """Test what happens with invalid data"""
    print("\n\nTesting Error Case")
    print("=" * 40)
    
    # Create invalid CSV data
    invalid_data = b"invalid,csv,data\nno,tickers,here\n123,456,789"
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    result = process_spreadsheet_upload(invalid_data, 'invalid.csv', google_vision_api_key)
    
    print(f"Result for invalid data:")
    print(f"Success: {result['success']}")
    print(f"Errors: {result.get('errors', [])}")
    print(f"Portfolio entries: {len(result.get('portfolio', []))}")
    
    # Should fail gracefully without returning mock data
    if not result['success'] and len(result.get('portfolio', [])) == 0:
        print("✅ Error case handled correctly - no mock data returned")
        return True
    else:
        print("❌ Error case not handled correctly")
        return False

if __name__ == "__main__":
    test1 = test_web_interface_flow()
    test2 = test_error_case()
    
    print("\n" + "=" * 50)
    if test1 and test2:
        print("✅ All web interface tests passed!")
        print("The portfolio import should now work correctly with real data.")
    else:
        print("❌ Some tests failed. Check the implementation.")
