#!/usr/bin/env python3
"""
Simple test for OCR functionality
"""

import sys
import os
import requests
import base64
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ocr_space_simple():
    """Test OCR.space with a simple text image."""
    print("Testing OCR.space API with simple configuration...")
    
    try:
        # Test with a simple URL-based image
        url = "https://api.ocr.space/parse/image"
        
        data = {
            'apikey': 'helloworld',
            'language': 'eng',
            'isOverlayRequired': 'false',
            'OCREngine': '2',
            'url': 'https://i.imgur.com/example.png'  # This won't work, but tests the API
        }
        
        response = requests.post(url, data=data, timeout=15)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API Response structure: {list(result.keys())}")
            
            if 'ParsedResults' in result:
                print("API is working - ParsedResults found")
                return True
            else:
                print(f"API response: {result}")
                return False
        else:
            print(f"HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"OCR.space test failed: {e}")
        return False

def create_simple_test_image():
    """Create a simple test image with text."""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import io
        
        # Create a simple image with text
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add some portfolio-like text
        text_lines = [
            "GOOGL: 10 shares",
            "Price: $161.61",
            "Value: $1,616.10"
        ]
        
        y_position = 20
        for line in text_lines:
            draw.text((20, y_position), line, fill='black')
            y_position += 30
        
        # Convert to bytes
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes.seek(0)
        
        return img_bytes.getvalue()
        
    except ImportError:
        print("PIL not available - cannot create test image")
        return None
    except Exception as e:
        print(f"Error creating test image: {e}")
        return None

def test_ocr_with_image():
    """Test OCR with an actual image."""
    print("\nTesting OCR with generated image...")
    
    image_data = create_simple_test_image()
    if not image_data:
        print("Could not create test image")
        return False
    
    try:
        url = "https://api.ocr.space/parse/image"
        
        files = {'file': ('test.png', image_data, 'image/png')}
        data = {
            'apikey': 'helloworld',
            'language': 'eng',
            'isOverlayRequired': 'false',
            'OCREngine': '2',
            'filetype': 'PNG'
        }
        
        response = requests.post(url, files=files, data=data, timeout=30)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"OCR Result: {result}")
            
            if result.get('ParsedResults'):
                text = result['ParsedResults'][0].get('ParsedText', '')
                print(f"Extracted text: '{text}'")
                return len(text.strip()) > 0
            else:
                print("No text extracted")
                return False
        else:
            print(f"HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"OCR test with image failed: {e}")
        return False

def test_portfolio_import_basic():
    """Test basic portfolio import without OCR."""
    print("\nTesting portfolio import with pre-extracted text...")
    
    try:
        from portfolio_import import PortfolioImportService
        
        service = PortfolioImportService("test_key")
        
        # Test with simple, clear text
        test_text = "GOOGL 10 161.61 1616.10"
        
        result = service.extract_portfolio_from_text(test_text)
        
        print(f"Import successful: {result.success}")
        print(f"Entries found: {len(result.portfolio_entries)}")
        print(f"Errors: {result.errors}")
        
        if result.portfolio_entries:
            entry = result.portfolio_entries[0]
            print(f"First entry: {entry.ticker} - {entry.shares} shares")
        
        return result.success
        
    except Exception as e:
        print(f"Portfolio import test failed: {e}")
        return False

if __name__ == "__main__":
    print("Simple OCR Test")
    print("=" * 40)
    
    # Test 1: Basic API connectivity
    api_test = test_ocr_space_simple()
    print(f"API connectivity: {'PASS' if api_test else 'FAIL'}")
    
    # Test 2: OCR with image
    image_test = test_ocr_with_image()
    print(f"OCR with image: {'PASS' if image_test else 'FAIL'}")
    
    # Test 3: Portfolio import
    import_test = test_portfolio_import_basic()
    print(f"Portfolio import: {'PASS' if import_test else 'FAIL'}")
    
    print("\n" + "=" * 40)
    
    if not any([api_test, image_test, import_test]):
        print("All tests failed. OCR system needs debugging.")
        print("\nTroubleshooting suggestions:")
        print("1. Check internet connectivity")
        print("2. Verify OCR.space API is accessible")
        print("3. Try with a different image format")
        print("4. Consider using local OCR libraries")
    else:
        print("Some tests passed. OCR system has basic functionality.")
