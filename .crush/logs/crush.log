{"time":"2025-08-01T20:30:01.889449+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":113},"msg":"Getting live provider data"}
{"time":"2025-08-01T20:30:02.468516+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-01T20:30:02.470512+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/config.Load","file":"/home/<USER>/work/crush/crush/internal/config/load.go","line":82},"msg":"No providers configured"}
{"time":"2025-08-01T20:30:02.945249+02:00","level":"INFO","msg":"OK   20250424200609_initial.sql (593.63µs)"}
{"time":"2025-08-01T20:30:02.945473+02:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (207.5µs)"}
{"time":"2025-08-01T20:30:02.945631+02:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (147.42µs)"}
{"time":"2025-08-01T20:30:02.945816+02:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (177.79µs)"}
{"time":"2025-08-01T20:30:02.94582+02:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-01T20:30:02.945922+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-01T20:30:02.946023+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/app.New","file":"/home/<USER>/work/crush/crush/internal/app/app.go","line":97},"msg":"No agent configuration found"}
{"time":"2025-08-01T20:32:11.761784+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-01T20:32:11.762481+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-01T20:32:11.777665+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-01T20:32:11.777704+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-01T20:32:13.552038+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processGeneration.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":369},"msg":"failed to generate title","error":"POST \"https://openrouter.ai/api/v1/chat/completions\": 402 Payment Required {\"message\":\"This request requires more credits, or fewer max_tokens. You requested up to 4096 tokens, but can only afford 1423. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account\",\"code\":402,\"metadata\":{\"provider_name\":null}}"}
{"time":"2025-08-01T20:32:18.782707+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_163530dfd12c45ceabbccd80","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:32:19.500937+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_2a7e568e6c29430893a4ab31","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:32:20.029391+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_3e911d2a14d043198e673470","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:32:21.094477+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_f2784c97a66d484696a1a084","name":"grep","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:32:21.525328+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":551},"msg":"Tool execution error","toolCall":"call_3e911d2a14d043198e673470","error":"path does not exist: /Users/<USER>/Downloads/cd-2/.github"}
{"time":"2025-08-01T20:32:36.80227+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_98103b1f614a45c8a43e00da","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:32:37.838125+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_14ab90fe134f4ce5a8f4f88d","name":"grep","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:32:38.646262+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_92143276fcf147f9b5cecbdf","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:32:39.156903+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_86a9c6ea589f41bc9d3c8663","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:33:04.248226+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_c847aff4d30a4039b1757fec","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:33:04.861088+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_96098dba10af4e4a9513e8d0","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:33:05.372394+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_baad0f60b8b44ea58391812f","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:33:06.589742+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_0e9c1c55f83147aa868b77b2","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:33:24.624411+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_45024939def142e684e99bf5","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:33:25.851312+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_0feffef09b934566ba28113d","name":"edit","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:36:54.950609+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":551},"msg":"Tool execution error","toolCall":"call_0feffef09b934566ba28113d","error":"permission denied"}
{"time":"2025-08-01T20:41:19.657423+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processGeneration.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":369},"msg":"failed to generate title","error":"POST \"https://openrouter.ai/api/v1/chat/completions\": 402 Payment Required {\"message\":\"This request requires more credits, or fewer max_tokens. You requested up to 4096 tokens, but can only afford 1423. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account\",\"code\":402,\"metadata\":{\"provider_name\":null}}"}
{"time":"2025-08-01T20:41:26.002351+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_0fef6ab2c9fa4cfc888dfcf6","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:41:35.140185+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_76232797751a487089763145","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:41:43.313587+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":523},"msg":"OpenAI API error","status_code":429,"message":"Provider returned error","type":""}
{"time":"2025-08-01T20:41:43.314552+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":472},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-08-01T20:41:53.557674+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":523},"msg":"OpenAI API error","status_code":429,"message":"Provider returned error","type":""}
{"time":"2025-08-01T20:41:53.557857+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":472},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-08-01T20:42:07.175384+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_822c9c950e30455d8067c364","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:42:16.859345+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_96e6c5f32ed74153bbab0240","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:42:23.449713+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_9777f945b3da447e982a50cd","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-01T20:42:34.056534+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Cancel","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":237},"msg":"Request cancellation initiated","session_id":"dc15a53e-bdd4-4f35-9991-02b156bc52cd"}
