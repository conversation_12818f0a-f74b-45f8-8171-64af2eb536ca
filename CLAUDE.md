# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flask-based financial portfolio analysis and import application. The system supports:
- Portfolio data import from images and spreadsheets using OCR and AI extraction
- Multi-currency portfolio processing with 20+ supported currencies
- Financial analysis including DCF calculations and Buffett-style analysis
- Interactive charting and visualizations with Chart.js
- AI-powered chatbot integration with multiple providers (OpenAI, Anthropic, Gemini)

## Development Commands

### Running the Application
```bash
# Main Flask application
python app.py

# Alternative with Waitress
python -c "from waitress import serve; from app import app; serve(app, host='0.0.0.0', port=5000)"
```

### Testing
```bash
# Run main test suite
python tests/portfolio_test_suite.py

# Run debug suite  
python debug/portfolio_debug_suite.py

# Run specific category tests
python -m pytest tests/currency/ -v
python -m pytest tests/portfolio_import/ -v
python -m pytest tests/ocr/ -v
python -m pytest tests/web_integration/ -v
```

### Dependencies
```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Node.js dependencies (for Chart.js)
cd config && npm install
```

## Architecture

### Core Components

- **app.py** (1.1MB) - Monolithic Flask application containing all routes, services, and business logic
- **portfolio_import.py** (355KB) - PortfolioImportService class handling AI-powered data extraction from images/spreadsheets
- **financial_reports.py** - Blueprint for financial reporting functionality using EODHD API

### Key Services

- **PortfolioImportService** - Main service class for portfolio data import and processing
- **AI Extraction** - Multi-provider AI integration (OpenAI, Anthropic, Gemini) for intelligent data extraction
- **Currency Conversion** - Support for 20+ currencies with automatic detection and conversion
- **OCR Processing** - Multiple OCR providers (EasyOCR, Tesseract, OCR Space) with fallback mechanisms

### Directory Structure

- `tests/` - 101+ organized test files by category (currency, portfolio_import, ocr, web_integration)
- `debug/` - 15 debug scripts organized by functionality (currency, gemini, ocr, general)  
- `demos/` - Working demonstration scripts
- `utils/` - Utility scripts for bug detection, verification, and fixes
- `static/` - Frontend assets (CSS, JS, images)
- `templates/` - Jinja2 HTML templates
- `data/` - Sample data and test files
- `config/` - Node.js package configuration

## Important Notes

### Large Monolithic Structure
The main `app.py` file is 1.1MB and contains all application logic. When making changes:
- Use offset/limit parameters when reading the file due to size
- Consider extracting specific functionality to separate modules
- Test thoroughly as changes can affect multiple features

### Currency Handling
The application has extensive currency support with complex detection logic:
- 20+ supported currencies with conversion rates
- Automatic currency detection from text/images
- Session-based currency preservation
- Mixed currency portfolio support

### Testing Strategy
- Use `tests/portfolio_test_suite.py` for comprehensive testing
- Category-specific tests in organized subdirectories
- Debug suite available at `debug/portfolio_debug_suite.py`
- Many test files appear to be duplicates - review before adding new tests

### AI Integration
Multiple AI providers are integrated:
- Requires API keys in environment variables or session
- Gemini used for advanced portfolio extraction
- OpenAI for general AI features
- Anthropic for specific analysis tasks

### Frontend Dependencies
- Chart.js for financial visualizations
- Custom CSS and JavaScript in static/ directory
- Flask-Session for server-side session management

## Environment Setup

Required environment variables:
- AI provider API keys (OPENAI_API_KEY, GOOGLE_API_KEY, ANTHROPIC_API_KEY)
- EODHD API key for financial data
- Flask session configuration

The application uses Flask-Session with server-side storage and supports development mode with debug=True.