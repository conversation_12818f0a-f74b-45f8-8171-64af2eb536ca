#!/usr/bin/env python3
"""
Debug ticker extraction specifically
"""

from portfolio_import import AIPortfolioExtractor

def debug_ticker_extraction():
    """Debug the ticker extraction"""
    print("🔍 Debugging Ticker Extraction")
    print("=" * 50)
    
    # The problematic text
    text = """
Alphabet Inc.
NasdaqGS:GOOGL
10
161
DKK 12,216.61

ASML Holding N.V.
NasdaqGS:ASML
2
668.5
DKK 9,279.65
"""
    
    print("Text:")
    print(repr(text))
    print()
    
    google_vision_api_key = "AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o"
    extractor = AIPortfolioExtractor(google_vision_api_key)
    
    # Test ticker extraction
    print("Testing _extract_tickers_intelligently:")
    tickers = extractor._extract_tickers_intelligently(text)
    print(f"Found tickers: {tickers}")
    
    # Test currency code detection
    print("\nTesting currency code detection:")
    test_codes = ['DKK', 'GOOGL', 'ASML', 'UBER', 'AMZN', 'USD', 'EUR']
    for code in test_codes:
        is_currency = extractor._is_currency_code(code)
        print(f"  {code}: {'Currency' if is_currency else 'Not currency'}")
    
    # Test likelihood
    print("\nTesting ticker likelihood:")
    for code in test_codes:
        is_likely = extractor._is_likely_ticker(code, text)
        print(f"  {code}: {'Likely ticker' if is_likely else 'Not likely ticker'}")

if __name__ == "__main__":
    debug_ticker_extraction()
