#!/usr/bin/env python3
"""
Debug the portfolio/price pattern matching that's boosting USD
"""

import re

def test_portfolio_price_patterns():
    """Test the portfolio/price pattern matching logic"""
    
    test_text = """
Company | Value (¥) | Change % | Today | Price (USD)
Shopify Inc. | ¥1,803,220 | ▲ 13.71 % | ↑ 0.12 % | USD 85.77
Palantir Tech. | ¥704300 | ▼ 1.88% | → 0.00 % | $25.91
Roblox Corp. | ¥1 020 050 | ▲ 9.02% | ↑ 0.31% | 39.67 USD
Pinterest Inc. | ¥892,430 | ▲0.96 % | ↑ 0.06% | 43.11
Block Inc. | ¥2.370.100 | ▼ 4.20% | ↑ 0.21% | USD: 70.30
"""

    print("🔍 DEBUGGING PORTFOLIO/PRICE PATTERN MATCHING")
    print("=" * 55)
    print(f"Test text:\n{test_text}")
    print("=" * 55)
    
    # Currency symbols from the actual code
    currency_symbols = {
        '$': 'USD',
        '€': 'EUR',
        '£': 'GBP',
        '¥': 'JPY',  # Japanese Yen
        'Y': 'JPY',  # Alternative Yen representation
        'kr': 'DKK',
    }
    
    # Start with base currency counts (simplified)
    currency_counts = {
        'JPY': 30,  # From ¥ symbols
        'USD': 15,  # From $ and USD text
    }
    
    print(f"Starting currency counts: {currency_counts}")
    print()
    
    # Test the portfolio/price pattern logic
    for symbol, curr_code in currency_symbols.items():
        if curr_code not in currency_counts:
            continue
            
        print(f"Testing patterns for {curr_code} (symbol: {symbol})")
        
        escaped_symbol = re.escape(symbol)
        
        # Portfolio value patterns (typically larger amounts)
        portfolio_patterns = [
            f'{escaped_symbol}[\\d,]+(?:,\\d{{3}})*(?:\\.\\d+)?',  # Large amounts with commas
            f'Value\\s*\\({escaped_symbol}\\)',  # Column headers like "Value (¥)"
            f'Total\\s*{escaped_symbol}[\\d,]+',  # Total amounts
        ]
        
        # Stock price patterns (typically smaller amounts)
        price_patterns = [
            f'{curr_code}\\s*:?\\s*[\\d.]+',  # USD: 85.77 or USD 85.77
            f'[\\d.]+\\s*{curr_code}',  # 85.77 USD
            f'Price\\s*\\({curr_code}\\)',  # Column headers like "Price (USD)"
        ]
        
        portfolio_matches = 0
        price_matches = 0
        
        print(f"  Portfolio patterns:")
        for pattern in portfolio_patterns:
            matches = re.findall(pattern, test_text, re.IGNORECASE)
            if matches:
                print(f"    '{pattern}': {len(matches)} matches - {matches}")
            portfolio_matches += len(matches)
        
        print(f"  Price patterns:")
        for pattern in price_patterns:
            matches = re.findall(pattern, test_text, re.IGNORECASE)
            if matches:
                print(f"    '{pattern}': {len(matches)} matches - {matches}")
            price_matches += len(matches)
        
        print(f"  Total portfolio matches: {portfolio_matches}")
        print(f"  Total price matches: {price_matches}")
        
        # Apply the boost logic
        if portfolio_matches > 0 and price_matches > 0:
            boost = portfolio_matches * 6 + price_matches * 4
            print(f"  Mixed context boost: {portfolio_matches} × 6 + {price_matches} × 4 = {boost}")
            currency_counts[curr_code] = currency_counts.get(curr_code, 0) + boost
            print(f"  New {curr_code} count: {currency_counts[curr_code]}")
        elif portfolio_matches > 0 or price_matches > 0:
            print(f"  No mixed context boost (need both portfolio AND price matches)")
        
        print()
    
    print(f"Final currency counts: {currency_counts}")
    
    if currency_counts:
        sorted_currencies = sorted(currency_counts.items(), key=lambda x: x[1], reverse=True)
        print(f"Final ranking:")
        for i, (currency, count) in enumerate(sorted_currencies, 1):
            print(f"  {i}. {currency}: {count} points")
        
        winner = sorted_currencies[0][0]
        if winner == 'JPY':
            print("\n✅ JPY correctly wins!")
        else:
            print(f"\n❌ {winner} wins, but JPY should win!")
            
        # Check if this would trigger mixed currency detection
        if len(sorted_currencies) >= 2:
            top_currency, top_count = sorted_currencies[0]
            second_currency, second_count = sorted_currencies[1]
            
            if second_count >= top_count * 0.25:
                print(f"\n🌍 Mixed currency would be detected: {top_currency}({top_count}) vs {second_currency}({second_count})")
                print(f"Second currency is {second_count/top_count*100:.1f}% of top currency (>25% threshold)")

if __name__ == "__main__":
    test_portfolio_price_patterns()
