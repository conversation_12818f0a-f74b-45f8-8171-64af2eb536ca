#!/usr/bin/env python3
"""
Debug the Japanese Yen currency detection issue
"""

import sys
import os
import re

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_import import AIPortfolioExtractor

def test_yen_detection():
    """Test Japanese Yen detection with user's actual data"""
    
    # User's actual data from the screenshot
    test_text = """
Company | Value (¥) | Change % | Today | Price (USD)
Shopify Inc. | ¥1,803,220 | ▲ 13.71 % | ↑ 0.12 % | USD 85.77
Palantir Tech. | ¥704300 | ▼ 1.88% | → 0.00 % | $25.91
Roblox Corp. | ¥1 020 050 | ▲ 9.02% | ↑ 0.31% | 39.67 USD
Pinterest Inc. | ¥892,430 | ▲0.96 % | ↑ 0.06% | 43.11
Block Inc. | ¥2.370.100 | ▼ 4.20% | ↑ 0.21% | USD: 70.30
"""

    print("🔍 DEBUGGING JAPANESE YEN DETECTION")
    print("=" * 50)
    print(f"Test text:\n{test_text}")
    print("=" * 50)
    
    # Initialize extractor
    extractor = AIPortfolioExtractor("test_key", "test_key")
    
    # Test currency detection
    print("\n1. CURRENCY DETECTION TEST")
    print("-" * 30)
    currency_result = extractor._detect_primary_currency(test_text)
    print(f"Currency detection result: {currency_result}")

    # Handle mixed currency result
    if isinstance(currency_result, dict):
        if 'detected_currencies' in currency_result and currency_result['detected_currencies']:
            detected_currency = currency_result['detected_currencies'][0]
            print(f"Primary currency from mixed result: {detected_currency}")
        else:
            detected_currency = 'USD'
            print(f"Fallback currency: {detected_currency}")
    else:
        detected_currency = currency_result
        print(f"Single currency detected: {detected_currency}")
    
    # Check for yen symbols
    yen_count = test_text.count('¥')
    usd_count = test_text.count('USD') + test_text.count('$')
    print(f"Yen symbols (¥) found: {yen_count}")
    print(f"USD references found: {usd_count}")
    
    # Test language detection
    print("\n2. LANGUAGE DETECTION TEST")
    print("-" * 30)
    detected_language = extractor._detect_language(test_text)
    print(f"Detected language: {detected_language}")
    
    # Test currency symbol mapping
    print("\n3. CURRENCY SYMBOL MAPPING TEST")
    print("-" * 30)
    currency_symbols = {
        '$': 'USD',
        '€': 'EUR',
        '£': 'GBP',
        '¥': 'JPY',  # Japanese Yen
        'Y': 'JPY',  # Alternative Yen representation (Y1,000,000)
        'kr': 'DKK',  # Default to DKK, but check context for SEK/NOK
    }
    
    for symbol, currency in currency_symbols.items():
        count = test_text.count(symbol)
        if count > 0:
            print(f"Symbol '{symbol}' -> {currency}: found {count} times")
    
    # Test the actual currency detection logic
    print("\n4. DETAILED CURRENCY ANALYSIS")
    print("-" * 30)
    
    text_upper = test_text.upper()
    text_lower = test_text.lower()
    
    # Count currency occurrences
    currency_counts = {}
    
    # Enhanced currency symbol detection
    currency_symbols = {
        '$': 'USD',
        '€': 'EUR', 
        '£': 'GBP',
        '¥': 'JPY',  # Japanese Yen
        'Y': 'JPY',  # Alternative Yen representation
        'kr': 'DKK',
        'Kc': 'CZK',
        'Kč': 'CZK',
    }
    
    for symbol, currency in currency_symbols.items():
        symbol_count = test_text.count(symbol)
        if symbol_count > 0:
            print(f"Found {symbol_count} occurrences of '{symbol}' -> {currency}")
            
            # Apply weight multiplier logic
            if symbol == '¥':
                weight_multiplier = 15  # Very high confidence for Japanese Yen
            elif symbol in ['$']:
                weight_multiplier = 5
            else:
                weight_multiplier = 3
                
            weighted_count = symbol_count * weight_multiplier
            currency_counts[currency] = currency_counts.get(currency, 0) + weighted_count
            print(f"  Weighted count for {currency}: {weighted_count}")
    
    # Check for currency codes
    currency_codes = ['USD', 'EUR', 'GBP', 'JPY', 'DKK', 'SEK', 'NOK', 'CHF']
    for code in currency_codes:
        code_count = text_upper.count(code)
        if code_count > 0:
            print(f"Found {code_count} occurrences of currency code '{code}'")
            currency_counts[code] = currency_counts.get(code, 0) + code_count * 8
    
    print(f"\nFinal currency counts: {currency_counts}")
    
    if currency_counts:
        primary_currency = max(currency_counts, key=currency_counts.get)
        print(f"Primary currency should be: {primary_currency}")
    else:
        print("No currencies detected!")
    
    return detected_currency == 'JPY'

if __name__ == "__main__":
    success = test_yen_detection()
    if success:
        print("\n✅ SUCCESS: Japanese Yen correctly detected!")
    else:
        print("\n❌ FAILURE: Japanese Yen not detected correctly!")
