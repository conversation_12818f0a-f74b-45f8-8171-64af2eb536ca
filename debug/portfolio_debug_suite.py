#!/usr/bin/env python3
"""
Portfolio Import Debug Suite

Consolidated debugging tools for portfolio import functionality.
Combines currency detection, OCR processing, and AI extraction debugging.
"""

import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from portfolio_import import PortfolioImportService, AIPortfolioExtractor
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PortfolioDebugSuite:
    """Comprehensive debugging suite for portfolio import functionality."""
    
    def __init__(self, google_vision_api_key: str = "test_key", eodhd_api_key: str = "test_key"):
        self.service = PortfolioImportService(google_vision_api_key, eodhd_api_key)
        self.ai_extractor = AIPortfolioExtractor(google_vision_api_key, eodhd_api_key)
        
    def debug_currency_detection(self, text: str) -> Dict[str, Any]:
        """Debug currency detection logic."""
        print(f"\n🔍 CURRENCY DETECTION DEBUG")
        print("=" * 50)
        print(f"Input text: '{text}'")
        
        # Test primary currency detection
        detected_currency = self.ai_extractor._detect_primary_currency(text)
        print(f"Detected primary currency: {detected_currency}")
        
        # Test currency patterns
        currency_patterns = {
            'USD': r'\$|USD|US\s*Dollar',
            'EUR': r'€|EUR|Euro',
            'GBP': r'£|GBP|Pound',
            'JPY': r'¥|JPY|Yen',
            'DKK': r'DKK|Danish\s*Krone',
            'CHF': r'CHF|Swiss\s*Franc',
            'SEK': r'SEK|Swedish\s*Krona',
            'NOK': r'NOK|Norwegian\s*Krone'
        }
        
        import re
        found_currencies = []
        for currency, pattern in currency_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                found_currencies.append(currency)
        
        print(f"Pattern matches: {found_currencies}")
        
        return {
            'detected_currency': detected_currency,
            'pattern_matches': found_currencies,
            'input_text': text
        }
    
    def debug_ocr_processing(self, ocr_text: str) -> Dict[str, Any]:
        """Debug OCR text processing."""
        print(f"\n🔍 OCR PROCESSING DEBUG")
        print("=" * 50)
        print(f"Raw OCR text:\n{ocr_text}")
        
        # Test text cleaning
        cleaned_text = self.service._clean_ocr_text(ocr_text)
        print(f"\nCleaned text:\n{cleaned_text}")
        
        # Test ticker extraction
        tickers = self.ai_extractor._extract_tickers_from_text(ocr_text)
        print(f"\nExtracted tickers: {tickers}")
        
        return {
            'raw_text': ocr_text,
            'cleaned_text': cleaned_text,
            'extracted_tickers': tickers
        }
    
    def debug_ai_extraction(self, text: str, user_currency: str = None) -> Dict[str, Any]:
        """Debug AI extraction process."""
        print(f"\n🔍 AI EXTRACTION DEBUG")
        print("=" * 50)
        
        if user_currency:
            self.ai_extractor.user_portfolio_currency = user_currency
            print(f"User portfolio currency set to: {user_currency}")
        
        try:
            # Test extraction
            result = self.service.extract_portfolio_from_text(text)
            
            print(f"Extraction successful: {result.success}")
            print(f"Number of entries: {len(result.portfolio_entries)}")
            print(f"Errors: {result.errors}")
            
            if result.portfolio_entries:
                print("\nExtracted entries:")
                for i, entry in enumerate(result.portfolio_entries[:3]):  # Show first 3
                    print(f"  {i+1}. {entry}")
            
            return {
                'success': result.success,
                'entries_count': len(result.portfolio_entries),
                'errors': result.errors,
                'sample_entries': result.portfolio_entries[:3]
            }
            
        except Exception as e:
            print(f"❌ Extraction failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def run_comprehensive_test(self, test_data: Dict[str, str]) -> Dict[str, Any]:
        """Run comprehensive test with sample data."""
        print(f"\n🚀 COMPREHENSIVE DEBUG TEST")
        print("=" * 70)
        
        results = {}
        
        # Test currency detection
        if 'text' in test_data:
            results['currency'] = self.debug_currency_detection(test_data['text'])
        
        # Test OCR processing
        if 'ocr_text' in test_data:
            results['ocr'] = self.debug_ocr_processing(test_data['ocr_text'])
        
        # Test AI extraction
        if 'text' in test_data:
            results['ai_extraction'] = self.debug_ai_extraction(
                test_data['text'], 
                test_data.get('user_currency')
            )
        
        return results

def main():
    """Main debug function with sample test cases."""
    print("🔧 PORTFOLIO IMPORT DEBUG SUITE")
    print("=" * 70)
    
    # Initialize debug suite
    debug_suite = PortfolioDebugSuite()
    
    # Sample test cases
    test_cases = [
        {
            'name': 'DKK Portfolio Test',
            'text': '''
            Ticker Shares Avg. Cost Basis Market Value (DKK) % Chg.
            
            Alphabet Inc.
            NasdaqGS:GOOGL 10 161 DKK 12,216.61 18.1%
            
            ASML Holding N.V.
            NasdaqGS:ASML 2 668.5 DKK 9,279.65 8.1%
            ''',
            'user_currency': 'DKK'
        },
        {
            'name': 'USD Portfolio Test',
            'text': '''
            Symbol | Shares | Price | Value
            AAPL   | 100    | $150  | $15,000
            MSFT   | 50     | $300  | $15,000
            ''',
            'user_currency': 'USD'
        },
        {
            'name': 'Mixed Currency Test',
            'text': '''
            Stock: GOOGL
            Shares: 10
            Price: 2,500 JPY
            Value: 25,000 JPY
            
            Stock: AAPL
            Shares: 5
            Price: $180
            Value: $900
            '''
        }
    ]
    
    # Run tests
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 TEST CASE {i}: {test_case['name']}")
        print("-" * 50)
        
        results = debug_suite.run_comprehensive_test(test_case)
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"debug_results_{test_case['name'].lower().replace(' ', '_')}_{timestamp}.json"
        
        with open(f"debug/{filename}", 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"✅ Results saved to: debug/{filename}")
    
    print(f"\n🎯 DEBUG SUITE COMPLETE")
    print("Check the debug/ folder for detailed results files.")

if __name__ == "__main__":
    main()
