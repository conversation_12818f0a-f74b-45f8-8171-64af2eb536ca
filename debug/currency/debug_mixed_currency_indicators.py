#!/usr/bin/env python3
"""
Debug the mixed currency indicators that are boosting USD
"""

def test_mixed_currency_indicators():
    """Test the mixed currency indicators logic"""
    
    test_text = """
Company | Value (¥) | Change % | Today | Price (USD)
Shopify Inc. | ¥1,803,220 | ▲ 13.71 % | ↑ 0.12 % | USD 85.77
Palantir Tech. | ¥704300 | ▼ 1.88% | → 0.00 % | $25.91
Roblox Corp. | ¥1 020 050 | ▲ 9.02% | ↑ 0.31% | 39.67 USD
Pinterest Inc. | ¥892,430 | ▲0.96 % | ↑ 0.06% | 43.11
Block Inc. | ¥2.370.100 | ▼ 4.20% | ↑ 0.21% | USD: 70.30
"""

    print("🔍 DEBUGGING MIXED CURRENCY INDICATORS")
    print("=" * 50)
    print(f"Test text:\n{test_text}")
    print("=" * 50)
    
    text_lower = test_text.lower()
    
    # Mixed currency indicators from the actual code
    mixed_currency_indicators = [
        ('GAK', 'USD'),  # GAK often appears with USD in Danish interfaces
        ('markedsværdi', 'DKK'),  # Market value often in local currency
        ('afkast', 'DKK'),  # Returns often in local currency
        ('Price (USD)', 'USD'),  # USD price column
        ('Price (EUR)', 'EUR'),  # EUR price column
        ('Price (GBP)', 'GBP'),  # GBP price column
        ('USD:', 'USD'),  # USD price indicators
        ('EUR:', 'EUR'),  # EUR price indicators
        ('GBP:', 'GBP'),  # GBP price indicators
    ]
    
    currency_counts = {}
    
    print("\n1. MIXED CURRENCY INDICATOR ANALYSIS")
    print("-" * 40)
    
    for indicator, likely_currency in mixed_currency_indicators:
        if indicator.lower() in text_lower:
            print(f"Found indicator '{indicator}' -> boosting {likely_currency} by +2")
            if likely_currency not in currency_counts:
                currency_counts[likely_currency] = 0
            currency_counts[likely_currency] += 2  # Moderate boost for context clues
        else:
            print(f"Indicator '{indicator}' not found")
    
    print(f"\nCurrency boosts from indicators: {currency_counts}")
    
    # Now let's check the base symbol counts
    print("\n2. BASE SYMBOL COUNTS")
    print("-" * 25)
    
    # Count ¥ symbols
    yen_count = test_text.count('¥')
    print(f"¥ symbols: {yen_count}")
    
    # Count $ symbols  
    dollar_count = test_text.count('$')
    print(f"$ symbols: {dollar_count}")
    
    # Count USD text
    usd_count = test_text.upper().count('USD')
    print(f"USD text: {usd_count}")
    
    # Calculate base weights (from the actual algorithm)
    base_currency_counts = {}
    
    # JPY from ¥ symbols (weight 5 for symbols)
    if yen_count > 0:
        base_currency_counts['JPY'] = yen_count * 5
        print(f"JPY base score: {yen_count} × 5 = {base_currency_counts['JPY']}")
    
    # USD from $ symbols (weight 5 for symbols)
    if dollar_count > 0:
        base_currency_counts['USD'] = base_currency_counts.get('USD', 0) + dollar_count * 5
        print(f"USD base score from $: {dollar_count} × 5 = {dollar_count * 5}")
    
    # USD from text (weight varies)
    if usd_count > 0:
        base_currency_counts['USD'] = base_currency_counts.get('USD', 0) + usd_count * 5
        print(f"USD base score from text: {usd_count} × 5 = {usd_count * 5}")
    
    print(f"Total USD base score: {base_currency_counts.get('USD', 0)}")
    
    # Combine base counts with indicator boosts
    print("\n3. FINAL COMBINED SCORES")
    print("-" * 30)
    
    final_counts = base_currency_counts.copy()
    for currency, boost in currency_counts.items():
        final_counts[currency] = final_counts.get(currency, 0) + boost
    
    print(f"Final currency counts: {final_counts}")
    
    if final_counts:
        sorted_currencies = sorted(final_counts.items(), key=lambda x: x[1], reverse=True)
        print(f"Winner: {sorted_currencies[0][0]} with {sorted_currencies[0][1]} points")
        
        if sorted_currencies[0][0] == 'JPY':
            print("✅ JPY correctly wins!")
        else:
            print("❌ JPY should win but doesn't!")

if __name__ == "__main__":
    test_mixed_currency_indicators()
