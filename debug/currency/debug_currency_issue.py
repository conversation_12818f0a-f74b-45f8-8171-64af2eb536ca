#!/usr/bin/env python3
"""
Debug script to understand the currency conversion issue.
"""

def test_currency_conversion():
    """Test the currency conversion logic to understand the issue."""
    print("🔍 DEBUGGING CURRENCY CONVERSION ISSUE")
    print("=" * 50)
    
    # These are the rates from app.py
    currency_rates = {
        'USD': 1.0, 'EUR': 1.08, 'GBP': 1.27, 'JPY': 0.0067, 'CAD': 0.74, 'AUD': 0.66,
        'CHF': 1.10, 'CNY': 0.14, 'SEK': 0.092, 'NOK': 0.091, 'DKK': 0.145,
        'PLN': 0.25, 'CZK': 0.044, 'HUF': 0.0027, 'BRL': 0.20, 'MXN': 0.059,
        'INR': 0.012, 'KRW': 0.00076, 'SGD': 0.74, 'HKD': 0.13, 'NZD': 0.61,
        'ZAR': 0.055, 'RUB': 0.011, 'TRY': 0.034, 'THB': 0.028, 'MYR': 0.22,
        'IDR': 0.000066, 'PHP': 0.018, 'ILS': 0.27, 'VND': 0.000041
    }
    
    def convert_to_usd(amount, from_currency):
        """Convert amount from source currency to USD."""
        if from_currency == 'USD':
            return amount
        rate = currency_rates.get(from_currency, 1.0)
        return amount * rate
    
    # Test case: DKK amounts from your screenshot
    test_amounts = [
        {'amount': 1838.36, 'currency': 'DKK'},
        {'amount': 2187.52, 'currency': 'DKK'},
        {'amount': 6900.00, 'currency': 'DKK'},  # Expected DKK amount
    ]
    
    print("\n1. Testing DKK to USD conversion (OLD LOGIC):")
    for test in test_amounts:
        amount = test['amount']
        currency = test['currency']
        
        # This is what the old logic was doing
        usd_amount = convert_to_usd(amount, currency)
        print(f"   {amount} {currency} -> {usd_amount} USD")
        
        # Now convert back to DKK using the wrong logic
        dkk_rate = currency_rates.get('DKK', 1.0)
        back_to_dkk = usd_amount / dkk_rate
        print(f"   {usd_amount} USD -> {back_to_dkk} DKK (back conversion)")
        print(f"   ERROR: {back_to_dkk} is the huge number you're seeing!")
        print()
    
    print("\n2. The CORRECT approach (what we fixed):")
    for test in test_amounts:
        amount = test['amount']
        currency = test['currency']
        print(f"   PRESERVE: {amount} {currency} (no conversion)")
    
    print("\n3. Understanding the huge numbers:")
    print("   The huge numbers like 1838,368000000000 come from:")
    print("   1838.36 DKK * 0.145 = 266.56 USD")
    print("   266.56 USD / 0.145 = 1838.36 DKK (correct)")
    print("   BUT if there was a bug: 266.56 / 0.000145 = 1,838,620 (huge number!)")
    print("   OR: 1838.36 / 0.000001 = 1,838,360,000 (even bigger!)")
    
    print("\n4. The real issue:")
    print("   Your portfolio entries were imported with currency conversion")
    print("   Even though we fixed the import logic, existing entries still have wrong amounts")
    print("   Solution: Clear your portfolio and re-import, or manually fix the amounts")

if __name__ == "__main__":
    test_currency_conversion()