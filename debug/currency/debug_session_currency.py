#!/usr/bin/env python3
"""
Debug what currency data is actually stored in the session.
"""

import sys
import os
sys.path.append('.')

def debug_session_currency():
    """Debug what currency data is in the session."""
    
    print("🧪 Debugging Session Currency Data")
    print("=" * 50)
    
    try:
        from app import app
        
        with app.test_request_context():
            from flask import session
            
            # Check current session data
            portfolio_data = session.get('portfolio_data', [])
            portfolio_currency = session.get('portfolio_currency', 'Not Set')
            
            print(f"📊 Session Analysis:")
            print(f"   Portfolio currency: {portfolio_currency}")
            print(f"   Number of stocks: {len(portfolio_data)}")
            
            if portfolio_data:
                print(f"\n📈 Stock Currency Analysis:")
                for i, stock in enumerate(portfolio_data[:5]):  # Show first 5 stocks
                    ticker = stock.get('ticker', 'Unknown')
                    currency = stock.get('currency', 'Not Set')
                    amount = stock.get('amount_invested', 0)
                    
                    print(f"   Stock {i+1}: {ticker}")
                    print(f"      Currency: {currency}")
                    print(f"      Amount: {amount}")
                    print(f"      All currency fields:")
                    
                    currency_fields = [
                        'currency', 'original_currency', 'buy_price_currency', 
                        'current_value_currency', 'amount_invested_currency'
                    ]
                    
                    for field in currency_fields:
                        value = stock.get(field, 'Not Set')
                        print(f"         {field}: {value}")
                    print()
            else:
                print("   No portfolio data found in session")
                
                # Let's simulate what would happen with JPY data
                print(f"\n🧪 Simulating JPY Portfolio Import:")
                
                # Simulate the import process
                test_data = {
                    'portfolio': [
                        {
                            'ticker': 'SHOP',
                            'amount_invested': 1585806.00,
                            'buy_price': 75.43,
                            'shares': 21023.90,
                            'amount_invested_currency': 'JPY',
                            'buy_price_currency': 'JPY',
                            'current_value_currency': 'JPY'
                        }
                    ],
                    'currency': 'JPY',
                    'selected_currency': 'JPY',
                    'cash_position': 0.0
                }
                
                print(f"   Test data currency: {test_data['currency']}")
                print(f"   Test entry currency: {test_data['portfolio'][0]['amount_invested_currency']}")
                
                # Test the confirm function
                from app import confirm_portfolio_import_with_data
                
                print(f"\n🔄 Testing import process...")
                result = confirm_portfolio_import_with_data(test_data)
                
                # Check what was stored
                updated_portfolio = session.get('portfolio_data', [])
                updated_currency = session.get('portfolio_currency', 'Not Set')
                
                print(f"   After import:")
                print(f"      Portfolio currency: {updated_currency}")
                print(f"      Number of stocks: {len(updated_portfolio)}")
                
                if updated_portfolio:
                    stock = updated_portfolio[0]
                    print(f"      First stock currency: {stock.get('currency', 'Not Set')}")
                    print(f"      First stock amount: {stock.get('amount_invested', 0)}")
                
    except Exception as e:
        print(f"❌ Debug failed with exception: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎯 Session Currency Debug Completed!")

if __name__ == '__main__':
    debug_session_currency()
