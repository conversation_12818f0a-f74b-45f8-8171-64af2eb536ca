#!/usr/bin/env python3
"""
Debug Gemini AI extraction to see why it's failing and falling back to simple text analysis
"""

import sys
sys.path.append('.')
from portfolio_import import PortfolioImportService
import json
import os

def test_gemini_extraction():
    """Test Gemini AI extraction with your Japanese Yen data"""
    
    print("🔍 DEBUGGING GEMINI AI EXTRACTION")
    print("=" * 50)
    
    # Your Japanese Yen portfolio text
    image_text = """
    Company                Value (¥)      Change %    Today       Price (USD)
    Shopify Inc.          ¥1,803,220     ▲ 13.71 %   ↑ 0.12 %    USD 85.77
    Palantir Tech.        ¥704300        ▼ 1.88%     → 0.00 %    $25.91
    Roblox Corp.          ¥1,020,050     ▲ 9.02%     ↑ 0.31%     39.67 USD
    Pinterest Inc.        ¥892,430       ▲0.96 %     ↑ 0.06%     43.11
    Block Inc.            ¥2,370,100     ▼ 4.20%     ↓ 0.21%     USD: 70.30
    """
    
    service = PortfolioImportService('test_key', 'test_key')
    
    print("1. TESTING GEMINI AI DIRECTLY")
    print("-" * 35)
    
    # Test Gemini AI extraction directly
    try:
        gemini_result = service._extract_with_gemini_ai(image_text)
        
        print(f"✅ Gemini AI Success: {gemini_result.get('success', False)}")
        
        if gemini_result.get('success'):
            portfolio = gemini_result.get('portfolio', [])
            print(f"📊 Extracted {len(portfolio)} entries")
            
            for i, entry in enumerate(portfolio, 1):
                print(f"  Entry {i}: {entry.get('ticker', 'N/A')}")
                print(f"    - Shares: {entry.get('shares', 'N/A')}")
                print(f"    - Amount Invested: {entry.get('amount_invested', 'N/A')} {entry.get('amount_invested_currency', 'N/A')}")
                print(f"    - Buy Price: {entry.get('buy_price', 'N/A')} {entry.get('buy_price_currency', 'N/A')}")
                print(f"    - Current Value: {entry.get('current_value', 'N/A')} {entry.get('current_value_currency', 'N/A')}")
                print(f"    - Current Price: {entry.get('current_price', 'N/A')} {entry.get('current_price_currency', 'N/A')}")
                print()
        else:
            print(f"❌ Gemini AI Failed: {gemini_result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Gemini AI Exception: {e}")
    
    print("\n2. TESTING FULL EXTRACTION PIPELINE")
    print("-" * 40)
    
    # Test the full extraction pipeline
    try:
        full_result = service.extract_portfolio_from_text(image_text)
        
        print(f"✅ Full Pipeline Success: {full_result.success}")
        print(f"📊 Extraction Method: {getattr(full_result, 'extraction_method', 'N/A')}")
        print(f"🌍 Detected Currency: {service.detected_currency}")
        
        if hasattr(full_result, 'portfolio_entries'):
            entries = full_result.portfolio_entries
            print(f"📊 Portfolio Entries: {len(entries)}")
            
            for i, entry in enumerate(entries, 1):
                print(f"  Entry {i}: {entry.ticker if hasattr(entry, 'ticker') else 'N/A'}")
                print(f"    - Shares: {entry.shares if hasattr(entry, 'shares') else 'N/A'}")
                print(f"    - Amount Invested: {entry.amount_invested if hasattr(entry, 'amount_invested') else 'N/A'}")
                print(f"    - Buy Price: {entry.buy_price if hasattr(entry, 'buy_price') else 'N/A'}")
                print(f"    - Current Value: {entry.current_value if hasattr(entry, 'current_value') else 'N/A'}")
                print()
        
        # Check for errors and warnings
        if hasattr(full_result, 'errors') and full_result.errors:
            print("❌ Errors:")
            for error in full_result.errors:
                print(f"  - {error}")
        
        if hasattr(full_result, 'warnings') and full_result.warnings:
            print("⚠️ Warnings:")
            for warning in full_result.warnings:
                print(f"  - {warning}")
                
    except Exception as e:
        print(f"❌ Full Pipeline Exception: {e}")
    
    print("\n3. TESTING API FORMATTING")
    print("-" * 30)
    
    # Test API formatting
    try:
        api_result = service.format_for_api(full_result)
        
        print(f"✅ API Format Success: {api_result.get('success', False)}")
        
        portfolio = api_result.get('portfolio', [])
        print(f"📊 API Portfolio Entries: {len(portfolio)}")
        
        for i, entry in enumerate(portfolio, 1):
            print(f"  Entry {i}: {entry.get('ticker', 'N/A')}")
            print(f"    - Shares: {entry.get('shares', 'N/A')}")
            print(f"    - Amount Invested: {entry.get('amount_invested', 'N/A')}")
            print(f"    - Currency: {entry.get('currency', 'N/A')}")
            print()
        
        # Check validation errors
        if api_result.get('errors'):
            print("❌ API Errors:")
            for error in api_result['errors']:
                print(f"  - {error}")
                
    except Exception as e:
        print(f"❌ API Formatting Exception: {e}")

def test_gemini_api_directly():
    """Test Gemini API directly to see if it's working"""
    
    print("\n4. TESTING GEMINI API DIRECTLY")
    print("-" * 35)
    
    try:
        import google.generativeai as genai
        
        # Use the same API key as the service
        api_key = 'AIzaSyB0JByAVdIZ-qjH1cLAwCZt6a8dTqXHz9o'
        genai.configure(api_key=api_key)
        
        model = genai.GenerativeModel('gemini-1.5-flash-latest')
        
        # Simple test prompt
        test_prompt = """Extract portfolio data from this text and return as JSON:
        
Company: Shopify Inc.
Value: ¥1,803,220
Price: USD 85.77

Return JSON with: {"portfolio": [{"ticker": "SHOP", "current_value": 1803220, "current_value_currency": "JPY", "current_price": 85.77, "current_price_currency": "USD"}]}"""
        
        print("🤖 Testing Gemini API with simple prompt...")
        response = model.generate_content(test_prompt)
        
        if response and response.text:
            print(f"✅ Gemini API Response: {response.text[:500]}...")
            
            # Try to parse as JSON
            try:
                response_text = response.text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]
                response_text = response_text.strip()
                
                data = json.loads(response_text)
                print(f"✅ JSON Parse Success: {json.dumps(data, indent=2)}")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON Parse Failed: {e}")
                print(f"Raw response: {response.text}")
        else:
            print("❌ No response from Gemini API")
            
    except ImportError:
        print("❌ Google Generative AI library not installed")
    except Exception as e:
        print(f"❌ Gemini API Test Failed: {e}")

def check_environment():
    """Check environment setup"""
    
    print("\n5. ENVIRONMENT CHECK")
    print("-" * 25)
    
    # Check API keys
    google_key = os.environ.get('GOOGLE_API_KEY')
    gemini_key = os.environ.get('GEMINI_API_KEY')
    
    print(f"GOOGLE_API_KEY: {'Set' if google_key else 'Not set'}")
    print(f"GEMINI_API_KEY: {'Set' if gemini_key else 'Not set'}")
    print(f"SKIP_GEMINI_AI: {os.environ.get('SKIP_GEMINI_AI', 'Not set')}")
    
    # Check library installation
    try:
        import google.generativeai
        print("✅ google-generativeai library: Installed")
    except ImportError:
        print("❌ google-generativeai library: Not installed")
        print("   Run: pip install google-generativeai")

if __name__ == "__main__":
    check_environment()
    test_gemini_extraction()
    test_gemini_api_directly()
    
    print("\n" + "=" * 50)
    print("🔍 DEBUGGING SUMMARY")
    print("=" * 50)
    print("If Gemini AI is failing:")
    print("1. Check if google-generativeai is installed")
    print("2. Check if API key is working")
    print("3. Check if API quota is exceeded")
    print("4. Check if response parsing is failing")
    print("\nIf falling back to simple text analysis:")
    print("1. Simple text analysis doesn't extract financial data properly")
    print("2. This causes validation errors (no shares, amount_invested, etc.)")
    print("3. Need to fix Gemini AI extraction to get proper data")
