#!/usr/bin/env python3
"""
Debug script to test OCR extraction on the Danish portfolio image
"""

import base64
import requests
import json

def test_google_vision_ocr(image_path, api_key):
    """Test Google Vision API OCR on the image"""
    try:
        # Read the image file
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
        
        # Encode image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        # Prepare the request
        url = f"https://vision.googleapis.com/v1/images:annotate?key={api_key}"
        
        payload = {
            "requests": [
                {
                    "image": {
                        "content": image_base64
                    },
                    "features": [
                        {
                            "type": "TEXT_DETECTION",
                            "maxResults": 50
                        }
                    ]
                }
            ]
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        print("Sending request to Google Vision API...")
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        
        if response.status_code == 200:
            result = response.json()
            
            if 'responses' in result and result['responses']:
                response_data = result['responses'][0]
                
                if 'textAnnotations' in response_data:
                    # Get the full text
                    full_text = response_data['textAnnotations'][0]['description']
                    print(f"\n=== FULL EXTRACTED TEXT ===")
                    print(repr(full_text))
                    print(f"\n=== READABLE FORMAT ===")
                    print(full_text)
                    print(f"\n=== END EXTRACTED TEXT ===")
                    
                    return full_text
                else:
                    print("No text found in image")
                    return None
            else:
                print("No response data")
                return None
        else:
            print(f"API Error: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_portfolio_parsing(text):
    """Test the portfolio parsing on extracted text"""
    from portfolio_import import AIPortfolioExtractor
    
    print(f"\n=== TESTING PORTFOLIO PARSING ===")
    
    # Create extractor with test keys
    extractor = AIPortfolioExtractor('test_google_key', 'test_eodhd_key')
    
    # Test the extraction
    result = extractor.extract_portfolio_data_with_ai(text)
    
    print(f"Success: {result['success']}")
    print(f"Portfolio entries: {len(result['portfolio'])}")
    
    if result['portfolio']:
        entry = result['portfolio'][0]
        print(f"Ticker: {entry['ticker']}")
        print(f"Shares: {entry['shares']}")
        print(f"Buy Price: ${entry['buy_price']}")
        print(f"Amount Invested: ${entry['amount_invested']}")
        print(f"Current Value: ${entry['current_value']}")
    
    print(f"Errors: {result['errors']}")
    print(f"Warnings: {result['warnings']}")

if __name__ == "__main__":
    # Test with expected text format
    expected_text = """GOOGL

Mine beholdninger

Antal
13 stk

GAK
161,61 USD

Markedsværdi
2.462,85 USD

Ureal.afkast
+9,9%

Vigtige datoer

Dage til regnskab
1

Næste regnskab
23. jul."""

    print("=== Testing with expected text format ===")
    test_portfolio_parsing(expected_text)

    # Test with OCR-mangled text (simulating OCR errors)
    mangled_text = """G00GL

Mine beholdninger

Antal
13.04 stk

6AK
9.00 U5D

Markedsværdi
117.32 U5D

Ureal.afkast
+9,9%"""

    print("\n=== Testing with OCR-mangled text ===")
    test_portfolio_parsing(mangled_text)

    # Test with completely broken OCR (just numbers)
    broken_text = """G00GL some random text 13 more text 161.61 random 2462.85 more stuff"""

    print("\n=== Testing with broken OCR (just numbers) ===")
    test_portfolio_parsing(broken_text)
    
    # If you have the actual API key and image, uncomment these lines:
    # api_key = "YOUR_GOOGLE_VISION_API_KEY"
    # image_path = "path_to_your_image.png"
    # extracted_text = test_google_vision_ocr(image_path, api_key)
    # if extracted_text:
    #     test_portfolio_parsing(extracted_text)
