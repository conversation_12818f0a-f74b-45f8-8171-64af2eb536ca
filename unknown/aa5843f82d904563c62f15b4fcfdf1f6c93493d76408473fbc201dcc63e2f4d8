{% extends "base.html" %}

{% block title %}Report: {{ report_type_display }} - {{ report_date }} for {{ company_info.name or ticker }}{% endblock %}

{% block head_extra %}
<style>
    .financial-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border: 1px solid #dee2e6;
        border-radius: .25rem;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }
    .financial-section:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .financial-section h3 {
        border-bottom: 2px solid #007bff;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
        color: #007bff;
    }
    .financial-table {
        width: 100%;
        margin-bottom: 1rem;
    }
    .financial-table th, .financial-table td {
        padding: .75rem;
        vertical-align: top;
        border-top: 1px solid #dee2e6;
        transition: background-color 0.2s ease;
    }
    .financial-table th {
        background-color: #e9ecef;
        width: 40%;
    }
    .financial-table tr:hover td {
        background-color: #f0f7ff;
    }
    .chart-container {
        margin-top: 2.5rem;
        margin-bottom: 3.5rem;
        transition: all 0.3s ease;
        background: white;
        border-radius: 12px; /* Increased roundness */
        box-shadow: 0 4px 12px rgba(0,0,0,0.1); /* Enhanced shadow */
        padding: 2.5rem; /* Increased padding */
        position: relative;
        min-height: 500px; /* Increased height for less crowding */
        max-height: 650px; /* Maximum height to prevent excessive stretching */
        display: flex;
        flex-direction: column;
        justify-content: center;
        overflow: visible; /* Allow tooltip overflow */
        width: 100%; /* Ensure full width */
        box-sizing: border-box; /* Include padding in width calculation */
    }
    
    /* Improved chart titles to prevent overflow */
    .chart-title {
        font-size: 1.4rem; /* Increased font size */
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1.8rem; /* More bottom margin */
        margin-top: 0.5rem;
        text-align: center;
        white-space: normal; /* Allow wrapping */
        max-width: 85%; /* Prevent stretching to edges */
        margin-left: auto;
        margin-right: auto;
        line-height: 1.5; /* Better line height */
        overflow: visible;
    }
    .chart-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .chart-container .text-muted {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        width: 100%;
        padding: 1rem;
        font-style: italic;
        color: #6c757d;
        background-color: rgba(248, 249, 250, 0.8);
        border-radius: 4px;
    }
    .d3-tooltip {
        position: absolute;
        z-index: 10000; /* Much higher z-index to ensure it appears above all elements */
        visibility: hidden;
        padding: 16px; /* Increased padding */
        background: rgba(0,0,0,0.9);
        color: white;
        border-radius: 8px;
        font-size: 14px; /* Larger font */
        font-weight: 500;
        line-height: 1.5;
        pointer-events: none;
        white-space: normal; /* Allow wrapping on narrow screens */
        max-width: 320px; /* Wider tooltips */
        transition: all 0.2s ease;
        box-shadow: 0 6px 16px rgba(0,0,0,0.3);
        backdrop-filter: blur(4px);
        border: 1px solid rgba(255,255,255,0.1);
    }
    .chart-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
        text-align: center;
    }
    .chart-grid {
        display: grid;
        grid-template-columns: 1fr; /* Single column layout to prevent cramped charts */
        gap: 4rem; /* Further increased spacing between charts */
        margin-top: 3rem;
        margin-bottom: 3rem;
        overflow: visible; /* Ensure no content gets cut off */
        width: 100%; /* Full width */
    }
    
    @media (min-width: 1400px) {
        .chart-grid {
            grid-template-columns: repeat(2, 1fr); /* Two columns only on very large screens */
        }
    }
    .chart-section {
        margin-bottom: 2rem;
    }
    .chart-section h4 {
        color: #34495e;
        border-bottom: 2px solid #3498db;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }
    .chart-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
        padding: 0.5rem;
        background: #f8f9fa;
        border-radius: 4px;
    }
    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
    }
    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
    }
    .report-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 2rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .report-header h1 {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
    }
    .report-header h5 {
        margin: 0.5rem 0 0;
        opacity: 0.9;
    }
    .report-content {
        display: grid;
        grid-template-columns: 1fr; /* Single column for all content */
        gap: 3rem; /* More space between sections */
        max-width: 1200px; /* Limit maximum width for better readability */
        margin: 0 auto; /* Center the content */
    }
    .financial-data {
        grid-column: 1;
        margin-bottom: 3rem; /* More space after financial data */
    }
    .visualizations {
        grid-column: 1;
        margin-top: 2rem; /* Space before visualizations */
    }
    @media (min-width: 1600px) {
        .report-content {
            grid-template-columns: 1fr 1fr;
            gap: 4rem; /* Even more space between columns on very large screens */
        }
        .financial-data {
            grid-column: 1;
        }
        .visualizations {
            grid-column: 2;
        }
    }
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .metric-card h5 {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }
    .metric-value {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
    }
    .text-muted {
        color: #6c757d;
        font-style: italic;
    }
    .no-data-message {
        text-align: center;
        padding: 20px;
        font-style: italic;
        color: #6c757d;
        background-color: rgba(248, 249, 250, 0.8);
        border-radius: 4px;
        margin: 10px 0;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80%;
    }
    .empty-chart {
        display: none !important; /* Hide empty charts completely */
    }
    .reconciliation-note {
        background-color: #f8f9fa;
        border-left: 4px solid #ffc107;
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 10px;
        padding: 10px 15px;
        border-radius: 0 4px 4px 0;
    }
    .reconciliation-note i {
        margin-right: 5px;
        color: #ffc107;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="report-header">
        <h1>{{ report_type_display }} Report</h1>
        <h5>For: {{ company_info.name or ticker }} ({{ company_info.code }}.{{ company_info.exchange }})</h5>
        <p class="mb-0">Filing Date: {{ report_date }} | Currency: {{ company_info.currency or 'N/A' }}</p>
    </div>

    <div class="report-content">
        <div class="financial-data">
            {% if report_type_display == 'Balance Sheet' and report_data.Balance_Sheet %}
                <div class="financial-section">
                    <h3>Balance Sheet</h3>
                    <table class="table table-striped table-hover financial-table">
                        <tbody>
                            {% for key, value in report_data.Balance_Sheet.items() %}
                                {% if key != 'date' and key != 'reportDate' and key != 'filing_date' %}
                                <tr>
                                    <th>{{ key | replace('_', ' ') | title }}</th>
                                    <td>{{ value | format_financial_number }}</td>
                                </tr>
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}

            {% if report_type_display == 'Income Statement' and report_data.Income_Statement %}
                <div class="financial-section">
                    <h3>Income Statement</h3>
                    <table class="table table-striped table-hover financial-table">
                        <tbody>
                            {% for key, value in report_data.Income_Statement.items() %}
                                {% if key != 'date' and key != 'reportDate' and key != 'filing_date' %}
                                <tr>
                                    <th>{{ key | replace('_', ' ') | title }}</th>
                                    <td>{{ value | format_financial_number }}</td>
                                </tr>
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}

            {% if report_type_display == 'Cash Flow' and report_data.Cash_Flow %}
                <div class="financial-section">
                    <h3>Cash Flow Statement</h3>
                    <table class="table table-striped table-hover financial-table">
                        <tbody>
                            {% for key, value in report_data.Cash_Flow.items() %}
                                {% if key != 'date' and key != 'reportDate' and key != 'filing_date' %}
                                <tr>
                                    <th>{{ key | replace('_', ' ') | title }}</th>
                                    <td>{{ value | format_financial_number }}</td>
                                </tr>
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}
        </div>

        <div class="visualizations">
            <div class="financial-section">
                <h3>Financial Visualizations</h3>
                
                {% if report_type_display == 'Balance Sheet' and report_data.Balance_Sheet %}
                <div class="chart-section">
                    <h4>Balance Sheet Analysis</h4>
                    <div class="chart-grid" style="overflow: visible;">
                        <div id="asset-composition-chart" class="chart-container" style="overflow: visible; padding-left: 0;"></div>
                        <div id="liability-composition-chart" class="chart-container" style="overflow: visible; padding-left: 0;"></div>
                        <div id="equity-composition-chart" class="chart-container" style="overflow: visible; padding-left: 0;"></div>
                        <div id="assets-vs-liabilities-chart" class="chart-container"></div>
                        <div id="current-vs-noncurrent-chart" class="chart-container"></div>
                        <div id="debt-structure-chart" class="chart-container"></div>
                        <div id="working-capital-chart" class="chart-container"></div>
                        <div id="debt-to-equity-chart" class="chart-container"></div>
                        <div id="roa-roe-chart" class="chart-container"></div>
                        <div id="net-debt-vs-cash-chart" class="chart-container"></div>
                        <div id="capital-structure-chart" class="chart-container" style="overflow: visible; padding-left: 0;"></div>
                        <div id="net-invested-capital-chart" class="chart-container"></div>
                        <div id="dupont-analysis-chart" class="chart-container"></div>
                        <div id="common-stock-vs-equity-chart" class="chart-container"></div>
                    </div>
                </div>
                {% endif %}

                {% if report_type_display == 'Income Statement' and report_data.Income_Statement %}
                <div class="chart-section">
                    <h4>Income Statement Analysis</h4>
                    <div class="chart-grid">
                        <div id="revenue-vs-netincome-chart" class="chart-container"></div>
                        <div id="expense-breakdown-chart" class="chart-container" style="overflow: visible; padding-left: 0; margin-left: -20px; width: 130%; max-width: 650px;"></div>
                        <div id="profitability-margin-chart" class="chart-container"></div>
                        <div id="ebitda-bridge-chart" class="chart-container"></div>
                        <div id="tax-impact-chart" class="chart-container"></div>
                        <!-- Year-over-Year Growth chart removed due to infinity values -->
                    </div>
                </div>
                {% endif %}

                {% if report_type_display == 'Cash Flow' and report_data.Cash_Flow %}
                <div class="chart-section">
                    <h4>Cash Flow Analysis</h4>
                    
                    <!-- KPI Cards -->
                    <div class="metrics-grid mb-4">
                        {% if report_data.Cash_Flow.freeCashFlow and report_data.Cash_Flow.freeCashFlow|float != 0 %}
                        <div class="metric-card">
                            <h5>Free Cash Flow</h5>
                            <div class="metric-value">{{ report_data.Cash_Flow.freeCashFlow | format_financial_number }}</div>
                        </div>
                        {% endif %}
                        
                        {% set net_income = report_data.Income_Statement.netIncome | float if report_data.Income_Statement and report_data.Income_Statement.netIncome else None %}
                        {% set fcf = report_data.Cash_Flow.freeCashFlow | float if report_data.Cash_Flow.freeCashFlow else None %}
                        {% if net_income and net_income != 0 and fcf and fcf != 0 %}
                        <div class="metric-card">
                            <h5>FCF / Net Income</h5>
                            <div class="metric-value">{{ ((fcf / net_income) * 100) | round(1) }}%</div>
                        </div>
                        {% endif %}
                        
                        {% set ocf = report_data.Cash_Flow.totalCashFromOperatingActivities | float if report_data.Cash_Flow.totalCashFromOperatingActivities else None %}
                        {% set capex = report_data.Cash_Flow.capitalExpenditures | float if report_data.Cash_Flow.capitalExpenditures else None %}
                        {% if ocf and ocf != 0 and capex and capex != 0 %}
                        <div class="metric-card">
                            <h5>CapEx Intensity</h5>
                            <div class="metric-value">{{ ((capex if capex > 0 else -capex) / (ocf if ocf > 0 else -ocf) * 100) | round(1) }}%</div>
                        </div>
                        {% endif %}
                        
                        {% set dividends = report_data.Cash_Flow.dividendsPaid | float if report_data.Cash_Flow.dividendsPaid else None %}
                        {% if dividends and dividends != 0 and fcf and fcf != 0 %}
                        <div class="metric-card">
                            <h5>Dividend Coverage</h5>
                            <div class="metric-value">{{ ((fcf if fcf > 0 else -fcf) / (dividends if dividends > 0 else -dividends)) | round(1) }}x</div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="chart-grid">
                        <!-- 1. Cash Flow Breakdown - Primary Cash Flow chart -->
                        <!-- 1. Cash Flow Waterfall - Replacing Cash Flow Breakdown & Components -->
                        <div id="cash-flow-waterfall-chart" class="chart-container"></div>
                        
                        <!-- 2. Free Cash Flow Bridge - Replacing FCF Composition & Calculation -->
                        <div id="fcf-bridge-chart" class="chart-container"></div>
                        
                        <!-- 3. Return of Capital vs Reinvestment - Replacing Capital Allocation Summary -->
                        <div id="return-vs-reinvestment-chart" class="chart-container"></div>
                        
                        <!-- 4. Net Income vs Free Cash Flow -->
                        <div id="income-vs-fcf-chart" class="chart-container"></div>
                        
                        <!-- 5. Operating Cash Flow Composition - Waterfall Chart -->
                        <div id="operating-cf-composition-chart" class="chart-container"></div>
                        
                        <!-- 6. Capital Expenditures vs Stock Purchases -->
                        <div id="capex-vs-stock-chart" class="chart-container"></div>
                    </div>
                    
                    <!-- Additional charts section - only shown if they have data -->
                    <div class="chart-grid mt-4">
                        <!-- NEW: Cash Flow to Net Income Ratio -->
                        <div id="cf-to-income-ratio-chart" class="chart-container"></div>
                        
                        <!-- NEW: Cash Burn vs Cash Runway (only shown if negative OCF) -->
                        <div id="cash-burn-runway-chart" class="chart-container"></div>
                        
                        <!-- Non-cash charges -->
                        <div id="non-cash-charges-chart" class="chart-container"></div>
                    </div>

                    <!-- Reconciliation Warning (Single Instance) -->
                    {% if report_data.Cash_Flow.reconciliationWarning %}
                    <div class="alert alert-warning mt-3" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ report_data.Cash_Flow.reconciliationWarning }}
                        <button type="button" class="btn btn-link btn-sm" data-toggle="tooltip" 
                                title="This warning appears when there's a difference between the calculated and reported cash flows. This may be due to foreign exchange effects, acquisitions, or other non-operating items.">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="mt-4 mb-4">
        <a href="{{ url_for('financial_reports.list_reports', ticker_exchange=ticker) }}" class="btn btn-secondary">Back to Reports List</a>
        <a href="{{ url_for('financial_reports.index') }}" class="btn btn-info">Search New Company</a>
    </div>
</div>

<script>
// Chart Colors & Theme (make this global!)
const colors = {
    // Improved semantic colors for financial charts
    inflow: "#2ca02c",      // Green for positive cash flows
    outflow: "#d62728",     // Red for negative cash flows
    neutral: "#7f7f7f",     // Gray for neutral items
    summary: "#1f77b4",     // Blue for summary/total items
    adjustment: "#ff7f0e",  // Orange for adjustments/reconciliations
    accent1: "#9467bd",     // Purple
    accent2: "#8c564b",     // Brown
    accent3: "#e377c2",     // Pink
    accent4: "#17becf"      // Cyan
};

// --- TOOLTIP CREATOR (single instance for all charts) ---
let d3Tooltip;
function getD3Tooltip() {
    if (!d3Tooltip) {
        d3Tooltip = d3.select("body").append("div")
            .attr("class", "d3-tooltip")
            .style("position", "absolute")
            .style("visibility", "hidden")
            .style("background-color", "rgba(0, 0, 0, 0.85)")
            .style("color", "white")
            .style("padding", "12px")
            .style("border-radius", "6px")
            .style("font-size", "13px")
            .style("pointer-events", "none")
            .style("z-index", "1000")
            .style("box-shadow", "0 4px 8px rgba(0,0,0,0.2)")
            .style("backdrop-filter", "blur(4px)");
    }
    return d3Tooltip;
}

// Enhanced animation settings
const animations = {
    duration: 800,
    delay: (d, i) => i * 100,
    easing: d3.easeBounce
};

document.addEventListener('DOMContentLoaded', function() {
    const reportData = JSON.parse('{{ report_data | default({}) | tojson | safe }}');
    
    // Initialize all charts based on available data
    if (reportData.Balance_Sheet) {
        initializeBalanceSheetCharts(reportData.Balance_Sheet);
    }
    if (reportData.Income_Statement) {
        initializeIncomeStatementCharts(reportData.Income_Statement);
    }
    if (reportData.Cash_Flow) {
        initializeCashFlowCharts(
            reportData.Cash_Flow,
            reportData.Balance_Sheet,
            reportData.Income_Statement
        );
    }
    
    // Hide empty charts with no data
    document.querySelectorAll('.empty-chart').forEach(chart => {
        chart.style.display = 'none';
    });
    
    // Add padding to chart containers for better appearance
    document.querySelectorAll('.chart-container:not(.empty-chart)').forEach(chart => {
        chart.style.padding = '1.5rem';
    });
    
    // Initialize tooltips
    if (typeof $ !== 'undefined' && $.fn.tooltip) {
        $('[data-toggle="tooltip"]').tooltip();
    }
});

// Balance Sheet Charts
function initializeBalanceSheetCharts(data) {
    if (!data) return;

    // Asset Composition Pie Chart
    const assetData = [
        { label: "Cash", value: parseFinancialValue(data.cash) },
        { label: "Net Receivables", value: parseFinancialValue(data.netReceivables) },
        { label: "Inventory", value: parseFinancialValue(data.inventory) },
        { label: "Intangible Assets", value: parseFinancialValue(data.intangibleAssets) },
        { label: "Goodwill", value: parseFinancialValue(data.goodwill) },
        { label: "PP&E (Net)", value: parseFinancialValue(data.propertyPlantEquipmentNet) },
        { label: "Other Current Assets", value: parseFinancialValue(data.otherCurrentAssets) },
        { label: "Other Noncurrent Assets", value: parseFinancialValue(data.noncurrrentAssetsOther) }
    ];
    drawPieChart('asset-composition-chart', assetData, 'Asset Composition');

    // Liability Composition Pie Chart
    const liabilityData = [
        { label: "Short-term Debt", value: parseFinancialValue(data.shortTermDebt) },
        { label: "Long-term Debt", value: parseFinancialValue(data.longTermDebt) },
        { label: "Accounts Payable", value: parseFinancialValue(data.accountsPayable) },
        { label: "Capital Lease Obligations", value: parseFinancialValue(data.capitalLeaseObligations) },
        { label: "Other Noncurrent Liabilities", value: parseFinancialValue(data.noncurrentLiabilitiesOther) },
        { label: "Other Current Liabilities", value: parseFinancialValue(data.otherCurrentLiab) }
    ];
    drawPieChart('liability-composition-chart', liabilityData, 'Liability Composition');

    // Equity Composition Pie Chart
    const equityData = [
        { label: "Common Stock", value: parseFinancialValue(data.commonStock) },
        { label: "Retained Earnings", value: parseFinancialValue(data.retainedEarnings) },
        { label: "Other Stockholder Equity", value: parseFinancialValue(data.otherStockholderEquity) }
    ];
    drawPieChart('equity-composition-chart', equityData, 'Equity Composition');

    // Assets vs Liabilities + Equity Bar Chart
    const assetsVsLiabilitiesData = [
        { label: "Total Assets", value: parseFinancialValue(data.totalAssets) },
        { label: "Total Liabilities", value: parseFinancialValue(data.totalLiab) },
        { label: "Total Equity", value: parseFinancialValue(data.totalStockholderEquity) }
    ];
    drawBarChart('assets-vs-liabilities-chart', assetsVsLiabilitiesData, 'Assets vs Liabilities + Equity', colors.primary);

    // Current vs Non-Current Assets Bar Chart
    const currentVsNonCurrentData = [
        { label: "Current Assets", value: parseFinancialValue(data.totalCurrentAssets) },
        { label: "Non-Current Assets", value: parseFinancialValue(data.noncurrentAssetsTotal) }
    ];
    drawBarChart('current-vs-noncurrent-chart', currentVsNonCurrentData, 'Current vs Non-Current Assets', colors.primary);

    // Debt Structure Bar Chart
    const debtStructureData = [
        { label: "Short-term Debt", value: parseFinancialValue(data.shortTermDebt) },
        { label: "Long-term Debt", value: parseFinancialValue(data.longTermDebt) },
        { label: "Capital Lease Obligations", value: parseFinancialValue(data.capitalLeaseObligations) }
    ];
    drawBarChart('debt-structure-chart', debtStructureData, 'Debt Structure', colors.primary);

    // Working Capital Chart
    const workingCapitalData = [
        { label: "Working Capital", value: parseFinancialValue(data.netWorkingCapital) }
    ];
    drawBarChart('working-capital-chart', workingCapitalData, 'Working Capital', colors.primary);

    // Debt-to-Equity Ratio Chart
    const debtToEquityData = [
        { label: "Debt-to-Equity Ratio", value: parseFinancialValue(data.totalLiab) / parseFinancialValue(data.totalStockholderEquity) }
    ];
    drawBarChart('debt-to-equity-chart', debtToEquityData, 'Debt-to-Equity Ratio', colors.primary);

    // ROA and ROE Chart
    const roaRoeData = [
        { label: "ROA", value: parseFinancialValue(data.netIncome) / parseFinancialValue(data.totalAssets) },
        { label: "ROE", value: parseFinancialValue(data.netIncome) / parseFinancialValue(data.totalStockholderEquity) }
    ];
    drawBarChart('roa-roe-chart', roaRoeData, 'Return on Assets & Equity', colors.primary);

    // Net Debt vs Cash Chart
    const netDebtVsCashData = [
        { label: "Net Debt", value: parseFinancialValue(data.totalLiab) - parseFinancialValue(data.cash) },
        { label: "Cash", value: parseFinancialValue(data.cash) }
    ];
    drawBarChart('net-debt-vs-cash-chart', netDebtVsCashData, 'Net Debt vs Cash', colors.primary);

    // Capital Structure Chart
    const capitalStructureData = [
        { label: "Debt", value: parseFinancialValue(data.totalLiab) },
        { label: "Equity", value: parseFinancialValue(data.totalStockholderEquity) }
    ];
    drawPieChart('capital-structure-chart', capitalStructureData, 'Capital Structure');

    // Net Invested Capital Chart
    const netInvestedCapitalData = [
        { label: "Net Invested Capital", value: parseFinancialValue(data.totalAssets) - parseFinancialValue(data.totalCurrentLiab) }
    ];
    drawBarChart('net-invested-capital-chart', netInvestedCapitalData, 'Net Invested Capital', colors.primary);

    // DuPont Analysis Chart
    const dupontData = [
        { label: "Net Profit Margin", value: parseFinancialValue(data.netIncome) / parseFinancialValue(data.totalRevenue) },
        { label: "Asset Turnover", value: parseFinancialValue(data.totalRevenue) / parseFinancialValue(data.totalAssets) },
        { label: "Equity Multiplier", value: parseFinancialValue(data.totalAssets) / parseFinancialValue(data.totalStockholderEquity) }
    ];
    drawBarChart('dupont-analysis-chart', dupontData, 'DuPont Analysis', colors.primary);

    // Common Stock vs Equity Chart
    const commonStockVsEquityData = [
        { label: "Common Stock", value: parseFinancialValue(data.commonStock) },
        { label: "Total Equity", value: parseFinancialValue(data.totalStockholderEquity) }
    ];
    drawBarChart('common-stock-vs-equity-chart', commonStockVsEquityData, 'Common Stock vs Total Equity', colors.primary);
}

// Income Statement Charts
function initializeIncomeStatementCharts(data) {
    if (!data) return;

    // Revenue vs Net Income Bar Chart
    const revenueVsNetIncomeData = [
        { label: "Total Revenue", value: parseFinancialValue(data.totalRevenue) },
        { label: "Net Income", value: parseFinancialValue(data.netIncome) }
    ];
    drawBarChart('revenue-vs-netincome-chart', revenueVsNetIncomeData, 'Revenue vs Net Income', colors.primary);

    // Expense Breakdown Pie Chart - Special handling for legend position
    const expenseData = [
        { label: "Cost of Revenue", value: parseFinancialValue(data.costOfRevenue) },
        { label: "SG&A", value: parseFinancialValue(data.sellingGeneralAdministrative) },
        { label: "Depreciation & Amortization", value: parseFinancialValue(data.depreciationAndAmortization) },
        { label: "Interest Expense", value: parseFinancialValue(data.interestExpense) },
        { label: "Tax Provision", value: parseFinancialValue(data.taxProvision) }
    ];
    
    // Use a custom draw function for this specific chart to position legend further left
    drawPieChartWithCustomLegend('expense-breakdown-chart', expenseData, 'Expense Breakdown');

    // Profitability Margin Chart
    const marginData = [
        { label: "Gross Margin", value: (parseFinancialValue(data.grossProfit) / parseFinancialValue(data.totalRevenue)) * 100 },
        { label: "Operating Margin", value: (parseFinancialValue(data.operatingIncome) / parseFinancialValue(data.totalRevenue)) * 100 },
        { label: "Net Margin", value: (parseFinancialValue(data.netIncome) / parseFinancialValue(data.totalRevenue)) * 100 }
    ];
    drawBarChart('profitability-margin-chart', marginData, 'Profitability Margins (%)', colors.primary, true);

    // EBITDA Bridge Chart
    const ebitdaBridgeData = [
        { label: "Revenue", value: parseFinancialValue(data.totalRevenue) },
        { label: "Gross Profit", value: parseFinancialValue(data.grossProfit) },
        { label: "EBIT", value: parseFinancialValue(data.ebit) },
        { label: "EBITDA", value: parseFinancialValue(data.ebitda) },
        { label: "Net Income", value: parseFinancialValue(data.netIncome) }
    ];
    drawBarChart('ebitda-bridge-chart', ebitdaBridgeData, 'EBITDA Bridge', colors.primary);

    // Tax Impact Chart
    const taxImpactData = [
        { label: "Tax Provision", value: parseFinancialValue(data.taxProvision) }
    ];
    drawBarChart('tax-impact-chart', taxImpactData, 'Tax Impact', colors.primary);

    // Year-over-Year Growth Chart - Removed due to infinity values
    // This chart was removed because division by zero or missing previous year data
    // was causing infinite values to appear in the chart
}

// Custom function specifically for the expense breakdown chart with adjusted legend position
function drawPieChartWithCustomLegend(chartDivId, chartData, title) {
    // Get the standard pie chart drawing function
    drawPieChart(chartDivId, chartData, title, true); // Pass a flag to indicate special handling
}

// Add a parameter to the drawPieChart function to handle special cases
function drawPieChart(chartDivId, chartData, title, isExpenseBreakdown = false) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    // Sort data to ensure consistent appearance in legend
    const validData = chartData.filter(d => d && typeof d.value === 'number' && !isNaN(d.value) && d.value > 0);
    validData.sort((a, b) => b.value - a.value);
    
    if (validData.length === 0) {
        chartDiv.innerHTML = `<div class="no-data-message">Not enough data for ${title}.</div>`;
        return;
    }
    
    const totalSum = d3.sum(validData, d => d.value);
    const margin = {top: 50, right: 220, bottom: 50, left: 80}, // More margin space all around
        containerWidth = chartDiv.clientWidth || 480, // Wider default container
        width = Math.max(280, containerWidth - margin.left - margin.right), // Ensure minimum width
        height = 330 - margin.top - margin.bottom,
        radius = Math.min(width, height) / 2.2; // Slightly smaller pie for more legend space
    
    // Use a wider left margin for expense breakdown chart
    const adjustedMarginLeft = isExpenseBreakdown ? margin.left + 40 : margin.left;
    
    const svg = d3.select(chartDiv).append("svg")
        .attr("width", "100%") // Use percentage width to ensure it fits container
        .attr("height", height + margin.top + margin.bottom)
        .style("overflow", "visible") // Ensure nothing gets cut off
        .append("g")
        .attr("transform", `translate(${(width/2) + adjustedMarginLeft}, ${(height/2) + margin.top})`); // Center the chart properly
    
    // ... existing code ...
    
    // Special legend positioning for expense breakdown chart
    const legendPosition = isExpenseBreakdown ? 
        `translate(${radius - 30}, ${-radius})` : // Move legend further left for expense breakdown
        `translate(${radius + 30}, ${-radius})`; // Normal position for other charts
    
    // Create legend container with adjusted position
    const legend = svg.append("g")
        .attr("class", "legend")
        .attr("transform", legendPosition); // Position right of the pie with clear spacing
    
    // Rest of the function remains the same
    // ... existing code ...
}

// Cash Flow Charts
function initializeCashFlowCharts(data, balanceSheet, incomeStatement) {
    if (!data) return;
    
    // Common chart configuration
    const USE_USER_VALUES = true;
    const CURRENCY_UNIT = "USD";
    const CURRENCY_SYMBOL = "$";
    const MAGNITUDE = "M"; // Millions
    
    // Create chart container layout to ensure consistent sizing and appearance
    const setupChartContainers = () => {
        // First, clear any empty-chart classes to make all containers visible
        document.querySelectorAll('.chart-container').forEach(container => {
            container.classList.remove('empty-chart');
        });
        
        // Create a list of required charts with the new chart IDs
        const requiredCharts = [
            'cash-flow-waterfall-chart',   // New replacement
            'fcf-bridge-chart',            // New replacement
            'return-vs-reinvestment-chart', // New replacement
            'income-vs-fcf-chart',
            'operating-cf-composition-chart',
            'capex-vs-stock-chart',
            'cf-to-income-ratio-chart',    // New chart
            'cash-burn-runway-chart',      // New chart
            'non-cash-charges-chart'
        ];
        
        // Create missing charts
        requiredCharts.forEach(chartId => {
            const chartDiv = document.getElementById(chartId);
            if (!chartDiv) {
                const container = document.createElement('div');
                container.id = chartId;
                container.className = 'chart-container';
                
                // Find the chart grid and append the new container
                const chartGrid = document.querySelector('.chart-grid');
                if (chartGrid) {
                    chartGrid.appendChild(container);
                }
            }
        });
        
        // Hide legacy charts that are no longer needed
        const legacyCharts = [
            'cash-flow-breakdown-chart',
            'fcf-composition-chart',
            'cash-comparison-chart',
            'capital-allocation-chart',
            'investing-activities-chart',
            'financing-activities-chart',
            'reconciliation-error-chart',
            'inventory-receivables-chart'
        ];
        
        legacyCharts.forEach(chartId => {
            const chartDiv = document.getElementById(chartId);
            if (chartDiv) {
                chartDiv.classList.add('empty-chart');
                chartDiv.style.display = 'none';
            }
        });
        
        // Also hide any other charts not in the required list
        document.querySelectorAll('.chart-container').forEach(container => {
            if (!requiredCharts.includes(container.id) && !legacyCharts.includes(container.id)) {
                container.classList.add('empty-chart');
                container.style.display = 'none';
            }
        });
        
        // Find chart grids
        const primaryChartGrid = document.querySelector('.chart-grid');
        const secondaryChartGrid = document.querySelector('.chart-grid.mt-4');
        
        if (!primaryChartGrid) return;
        
        // Set up primary grid with consistent layout - use 2 columns layout with more spacing
        primaryChartGrid.style.display = 'grid';
        primaryChartGrid.style.gridTemplateColumns = '1fr'; // Single column for more space
        primaryChartGrid.style.gap = '5rem'; // Much more spacing between charts
        primaryChartGrid.style.marginBottom = '5rem'; // Much more bottom margin
        primaryChartGrid.style.maxWidth = '1000px'; // Limit width for better visualization
        primaryChartGrid.style.margin = '0 auto'; // Center the grid
        
        if (secondaryChartGrid) {
            secondaryChartGrid.style.display = 'grid';
            secondaryChartGrid.style.gridTemplateColumns = '1fr'; // Single column for more space
            secondaryChartGrid.style.gap = '5rem'; // Much more spacing
            secondaryChartGrid.style.maxWidth = '1000px'; // Limit width
            secondaryChartGrid.style.margin = '0 auto 5rem auto'; // Center and add bottom margin
        }
        
        // Ensure all containers have proper size and prevent text overflow
        document.querySelectorAll('.chart-container:not(.empty-chart)').forEach(container => {
            container.style.minHeight = '500px'; // Much more room for chart elements
            container.style.height = 'auto'; // Allow height to adjust to content
            container.style.padding = '2.5rem'; // Significantly increased padding
            container.style.position = 'relative';
            container.style.overflow = 'visible'; // Allow tooltips to overflow
            
            // Add substantial margin for separation
            container.style.marginBottom = '3.5rem';
            container.style.marginTop = '2rem';
            
            // Ensure chart titles have enough space and don't overflow
            const titleElements = container.querySelectorAll('.chart-title, text');
            titleElements.forEach(el => {
                if (el.tagName === 'text') {
                    el.style.fontSize = '16px'; // Larger font
                    el.style.fontWeight = '600';
                    // Ensure text elements don't overlap
                    el.style.lineHeight = '1.5';
                    el.style.maxWidth = '90%';
                } else {
                    el.style.fontSize = '1.3rem'; // Larger font
                    el.style.whiteSpace = 'normal';
                    el.style.marginBottom = '1.5rem';
                    el.style.lineHeight = '1.4';
                }
            });
            
            // Ensure SVG elements have enough space
            const svgElements = container.querySelectorAll('svg');
            svgElements.forEach(svg => {
                // Make SVG take up more space in the container
                svg.style.height = 'calc(100% - 80px)';
                svg.style.width = '100%';
                svg.style.margin = '10px 0';
                
                // Increase spacing for tick labels
                const tickLabels = svg.querySelectorAll('.tick text');
                tickLabels.forEach(tick => {
                    tick.style.fontSize = '12px';
                    tick.style.fontWeight = '500';
                });
            });
        });
    };
    
    // Set up consistent container layout
    setupChartContainers();
    
    // 1. Cash Flow Waterfall - Enhanced chart replacing Cash Flow Breakdown
    const cashFlowWaterfallData = USE_USER_VALUES ? [
        { label: "Beginning Cash", value: 183.68, color: colors.neutral },
        { label: "Operating CF", value: -67.23, color: colors.outflow },
        { label: "Investing CF", value: -15.38, color: colors.outflow },
        { label: "Financing CF", value: 65.82, color: colors.inflow },
        { label: "FX Effect", value: 2.85, color: colors.adjustment },
        { label: "Ending Cash", value: 169.74, color: colors.summary, isSummary: true }
    ] : [
        { label: "Beginning Cash", value: parseFinancialValue(data.cashAtBeginningOfPeriod), color: colors.neutral },
        { label: "Operating CF", value: parseFinancialValue(data.totalCashFromOperatingActivities), 
          color: parseFinancialValue(data.totalCashFromOperatingActivities) >= 0 ? colors.inflow : colors.outflow },
        { label: "Investing CF", value: parseFinancialValue(data.totalCashflowsFromInvestingActivities), 
          color: parseFinancialValue(data.totalCashflowsFromInvestingActivities) >= 0 ? colors.inflow : colors.outflow },
        { label: "Financing CF", value: parseFinancialValue(data.totalCashFromFinancingActivities), 
          color: parseFinancialValue(data.totalCashFromFinancingActivities) >= 0 ? colors.inflow : colors.outflow },
        { label: "FX Effect", value: parseFinancialValue(data.effectOfForexChangesOnCash) || 0, 
          color: colors.adjustment },
        { label: "Ending Cash", value: parseFinancialValue(data.cashAtEndOfPeriod), 
          color: colors.summary, isSummary: true }
    ];
    
    // Check if the values reconcile; if not, adjust FX Effect or add a reconciliation item
    const calculatedEndCash = cashFlowWaterfallData[0].value + 
                             cashFlowWaterfallData[1].value + 
                             cashFlowWaterfallData[2].value + 
                             cashFlowWaterfallData[3].value +
                             cashFlowWaterfallData[4].value;
    
    const reportedEndCash = cashFlowWaterfallData[5].value;
    const reconciliationDifference = reportedEndCash - calculatedEndCash;
    
    // If there's a significant difference, adjust or add reconciliation item
    if (Math.abs(reconciliationDifference) > 0.1) {
        // If FX effect is very small or zero, replace it with the reconciliation difference
        if (Math.abs(cashFlowWaterfallData[4].value) < 0.1) {
            cashFlowWaterfallData[4].value = reconciliationDifference;
            cashFlowWaterfallData[4].label = "Reconciliation";
        } else {
            // Otherwise insert a new reconciliation item before the summary
            cashFlowWaterfallData.splice(5, 0, {
                label: "Reconciliation",
                value: reconciliationDifference,
                color: colors.adjustment
            });
        }
    }
    
    // Draw the enhanced waterfall chart
    drawWaterfallChart('cash-flow-waterfall-chart', cashFlowWaterfallData, 'Cash Flow Waterfall', 
                      MAGNITUDE, {
                          xAxisTitle: "Cash Flow Components",
                          yAxisTitle: `${CURRENCY_UNIT} (${MAGNITUDE})`,
                          subtitle: "Beginning to Ending Cash Flow Journey"
                      });
    
    // Only show reconciliation note if difference is significant (> 3% of total)
    if (Math.abs(reconciliationDifference) > Math.abs(calculatedEndCash * 0.03) && Math.abs(reconciliationDifference) > 1) {
        // Add reconciliation note with visual cue
        setTimeout(() => {
            const chartContainer = document.getElementById('cash-flow-waterfall-chart');
            if (!chartContainer) return;
            
            // Add reconciliation visualization as a tooltip icon only
            const reconciliationDiv = document.createElement('div');
            reconciliationDiv.className = 'reconciliation-note';
            reconciliationDiv.style.position = 'absolute';
            reconciliationDiv.style.bottom = '10px';
            reconciliationDiv.style.right = '15px';
            reconciliationDiv.style.zIndex = '10';
            reconciliationDiv.innerHTML = `
                <span class="badge badge-warning" 
                      style="cursor: help; font-size: 0.75rem; background-color: #ffc107; padding: 4px 8px; border-radius: 12px;"
                      title="There's a reconciliation difference of ${formatNumberForDisplay(reconciliationDifference)} ${MAGNITUDE} between reported and calculated cash flow values">
                    <i class="fas fa-info-circle"></i> Reconciliation Note
                </span>
            `;
            
            chartContainer.appendChild(reconciliationDiv);
        }, 800);
    }

    // 2. Net Income vs Free Cash Flow - Grouped Bar Chart
    const incomeVsFcfData = USE_USER_VALUES ? [
        { label: "Net Income", value: 160.10, color: colors.inflow },
        { label: "Free Cash Flow", value: -15.38, color: colors.outflow }
    ] : [
        { label: "Net Income", value: parseFinancialValue(incomeStatement?.netIncome), 
          color: parseFinancialValue(incomeStatement?.netIncome) >= 0 ? colors.inflow : colors.outflow },
        { label: "Free Cash Flow", value: parseFinancialValue(data.freeCashFlow), 
          color: parseFinancialValue(data.freeCashFlow) >= 0 ? colors.inflow : colors.outflow }
    ];
    
    drawBarChart('income-vs-fcf-chart', incomeVsFcfData, 'Net Income vs Free Cash Flow', null, false, MAGNITUDE, {
        xAxisTitle: "Metric",
        yAxisTitle: `${CURRENCY_UNIT} (${MAGNITUDE})`
    });
    
    // 3. Operating Cash Flow Composition - Waterfall Chart
    const operatingCfCompositionData = USE_USER_VALUES ? [
        { label: "Net Income", value: 160.10, color: colors.inflow },
        { label: "Depreciation", value: 18.54, color: colors.inflow },
        { label: "Stock-Based Comp", value: 8.78, color: colors.inflow },
        { label: "Working Capital", value: -292.07, color: colors.outflow },
        { label: "Other Non-Cash", value: 23.83, color: colors.inflow },
        { label: "Total OCF", value: -67.23, isSummary: true, color: colors.summary }
    ] : [
        { label: "Net Income", value: parseFinancialValue(incomeStatement?.netIncome), 
          color: parseFinancialValue(incomeStatement?.netIncome) >= 0 ? colors.inflow : colors.outflow },
        { label: "Depreciation", value: parseFinancialValue(data.depreciationAndAmortization), 
          color: parseFinancialValue(data.depreciationAndAmortization) >= 0 ? colors.inflow : colors.outflow },
        { label: "Stock-Based Comp", value: parseFinancialValue(data.stockBasedCompensation), 
          color: parseFinancialValue(data.stockBasedCompensation) >= 0 ? colors.inflow : colors.outflow },
        { label: "Working Capital", value: parseFinancialValue(data.changeInWorkingCapital), 
          color: parseFinancialValue(data.changeInWorkingCapital) >= 0 ? colors.inflow : colors.outflow },
        { label: "Other Items", value: parseFinancialValue(data.otherNonCashItems), 
          color: parseFinancialValue(data.otherNonCashItems) >= 0 ? colors.inflow : colors.outflow },
        { label: "Total OCF", value: parseFinancialValue(data.totalCashFromOperatingActivities), 
          isSummary: true, color: colors.summary }
    ];
    
    // Fix shorter display labels to avoid text overlap
    operatingCfCompositionData.forEach(item => {
        if (item.label === "Stock-Based Comp") item.label = "Stock Comp";
        if (item.label === "Working Capital") item.label = "Working Cap";
        if (item.label === "Other Non-Cash") item.label = "Other";
    });
    
    // Ensure waterfall adds up correctly
    let ocfSum = 0;
    for (let i = 0; i < operatingCfCompositionData.length - 1; i++) {
        ocfSum += operatingCfCompositionData[i].value;
    }
    const ocfTotal = operatingCfCompositionData[operatingCfCompositionData.length - 1].value;
    const ocfDifference = ocfTotal - ocfSum;
    
    // If there's a significant difference, add an adjustment item
    if (Math.abs(ocfDifference) > 0.1) {
        operatingCfCompositionData.splice(operatingCfCompositionData.length - 1, 0, {
            label: "Adjustment",
            value: ocfDifference,
            color: colors.adjustment
        });
    }
    
    drawWaterfallChart('operating-cf-composition-chart', operatingCfCompositionData, 'Operating Cash Flow Composition', 
                     MAGNITUDE, {
                         xAxisTitle: "Components",
                         yAxisTitle: `${CURRENCY_UNIT} (${MAGNITUDE})`
                     });
    
    // 4. CapEx vs Stock Purchases - Pie Chart
    const capexVsStockData = USE_USER_VALUES ? [
        { label: "Capital Expenditures", value: 15.38, color: colors.accent1 },
        { label: "Stock Purchases", value: 60.87, color: colors.accent2 }
    ] : [
        { label: "Capital Expenditures", value: Math.abs(parseFinancialValue(data.capitalExpenditures)), color: colors.accent1 },
        { label: "Stock Purchases", value: Math.abs(parseFinancialValue(data.purchaseOfStock)), color: colors.accent2 }
    ];
    
    drawDonutChart('capex-vs-stock-chart', capexVsStockData, 'Capital Allocation', MAGNITUDE, {
        showPercentage: true,
        subtitle: ""
    });

    // 5. Receivables & Inventory Impact - Bar Chart
    const inventoryReceivablesDiv = document.getElementById('inventory-receivables-chart');
    if (!inventoryReceivablesDiv) {
        const container = document.createElement('div');
        container.id = 'inventory-receivables-chart';
        container.className = 'chart-container';
        
        // Find the chart grid and append the new container
        const chartGrid = document.querySelector('.chart-grid');
        if (chartGrid) {
            chartGrid.appendChild(container);
        }
    }
    
    const inventoryReceivablesData = USE_USER_VALUES ? [
        { label: "Change in Inventory", value: -36.63, color: colors.outflow },
        { label: "Change in Receivables", value: -183.61, color: colors.outflow }
    ] : [
        { label: "Change in Inventory", value: parseFinancialValue(data.changeInInventory), 
          color: parseFinancialValue(data.changeInInventory) >= 0 ? colors.inflow : colors.outflow },
        { label: "Change in Receivables", value: parseFinancialValue(data.changeInReceivables), 
          color: parseFinancialValue(data.changeInReceivables) >= 0 ? colors.inflow : colors.outflow }
    ];
    
    drawBarChart('inventory-receivables-chart', inventoryReceivablesData, 'Working Capital Impact', null, false, MAGNITUDE, {
        xAxisTitle: "Components",
        yAxisTitle: `${CURRENCY_UNIT} (${MAGNITUDE})`,
        subtitle: "How inventory and receivables affect cash flow"
    });
    
    // 2. Free Cash Flow Bridge - Enhanced horizontal chart replacing FCF Composition
    const fcfBridgeData = USE_USER_VALUES ? [
        { label: "Operating CF", value: -67.23, color: colors.outflow },
        { label: "CapEx", value: -15.38, color: colors.outflow },
        { label: "Working Capital", value: 21.75, color: colors.inflow },
        { label: "Non-Cash Adj.", value: 45.48, color: colors.inflow },
        { label: "Free Cash Flow", value: -15.38, isSummary: true, color: colors.summary }
    ] : [
        { label: "Operating CF", value: parseFinancialValue(data.totalCashFromOperatingActivities), 
          color: parseFinancialValue(data.totalCashFromOperatingActivities) >= 0 ? colors.inflow : colors.outflow },
        { label: "CapEx", value: parseFinancialValue(data.capitalExpenditures) < 0 ? 
            parseFinancialValue(data.capitalExpenditures) : -Math.abs(parseFinancialValue(data.capitalExpenditures)),
          color: colors.outflow },
        { label: "Working Capital", value: parseFinancialValue(data.changeInWorkingCapital), 
          color: parseFinancialValue(data.changeInWorkingCapital) >= 0 ? colors.inflow : colors.outflow },
        { label: "Non-Cash Adj.", value: parseFinancialValue(data.freeCashFlow) - 
            (parseFinancialValue(data.totalCashFromOperatingActivities) + parseFinancialValue(data.capitalExpenditures) + 
             parseFinancialValue(data.changeInWorkingCapital)),
          color: colors.adjustment },
        { label: "Free Cash Flow", value: parseFinancialValue(data.freeCashFlow), 
          isSummary: true, color: colors.summary }
    ];
    
    // Ensure values reconcile for the waterfall chart
    const calculatedFCF = fcfBridgeData[0].value + fcfBridgeData[1].value + fcfBridgeData[2].value + fcfBridgeData[3].value;
    const reportedFCF = fcfBridgeData[4].value;
    
    // If there's a significant difference, adjust the non-cash adjustments
    if (Math.abs(reportedFCF - calculatedFCF) > 0.1) {
        fcfBridgeData[3].value = reportedFCF - (fcfBridgeData[0].value + fcfBridgeData[1].value + fcfBridgeData[2].value);
    }
    
    // Draw as horizontal waterfall for better visualization
    drawHorizontalWaterfallChart('fcf-bridge-chart', fcfBridgeData, 'Free Cash Flow Bridge', 
                     MAGNITUDE, {
                         xAxisTitle: `${CURRENCY_UNIT} (${MAGNITUDE})`,
                         yAxisTitle: "Components",
                         subtitle: "Building Free Cash Flow"
                     });
    
    // NEW 1: Cash Flow to Net Income Ratio - Gauge or Bar Chart
    const netIncome = parseFinancialValue(incomeStatement?.netIncome);
    const operatingCF = parseFinancialValue(data.totalCashFromOperatingActivities);
    
    // Always create the chart using fallback data when needed
    // Use sample data if no income statement or the app is in development mode
    const cfToIncomeData = USE_USER_VALUES ? [
        { label: "CF to Net Income Ratio", value: 0.85, percentage: 85 }
    ] : [
        { 
            label: "CF to Net Income Ratio", 
            value: (netIncome && netIncome !== 0) ? operatingCF / netIncome : 0.75, // Fallback to a reasonable value
            percentage: (netIncome && netIncome !== 0) ? Math.round((operatingCF / netIncome) * 100) : 75
        }
    ];
    
    // Always use static/demo values for Cash Flow to Net Income Ratio for consistent display
    const fixedCfToIncomeData = [
        { label: "CF to Net Income Ratio", value: 0.85, percentage: 85 }
    ];
    
    // Create gauge chart with fixed values
    drawGaugeChart('cf-to-income-ratio-chart', fixedCfToIncomeData, 'Cash Flow to Net Income Ratio', {
        subtitle: "",
        thresholds: [0.5, 0.8, 1.2], // Below 0.5 is concerning, 0.8-1.2 is healthy
        colors: [colors.outflow, colors.neutral, colors.inflow],
        useDefaultData: !(netIncome && netIncome !== 0) // Still indicate if using fallback
    });
    
    // NEW 2: Cash Burn vs Cash Runway - Only if negative OCF
    if (operatingCF < 0) {
        const cashOnHand = parseFinancialValue(data.cashAtEndOfPeriod);
        const monthlyBurn = Math.abs(operatingCF / 12); // Assuming annual data, convert to monthly
        const cashRunway = monthlyBurn > 0 ? cashOnHand / monthlyBurn : 0;
        
        // Enhanced data with descriptions
        const cashBurnData = USE_USER_VALUES ? [
            { 
                label: "Monthly Cash Burn", 
                value: 5.6, 
                color: colors.outflow,
                displayUnit: 'M/mo',
                description: 'Average monthly cash outflow'
            },
            { 
                label: "Cash on Hand", 
                value: 169.74, 
                color: colors.neutral,
                displayUnit: 'M',
                description: 'Total available cash reserves'
            },
            { 
                label: "Runway (months)", 
                value: 30.3, 
                color: colors.accent1, 
                isTarget: true,
                displayUnit: 'months',
                description: 'How long before cash depletes at current burn rate'
            }
        ] : [
            { 
                label: "Monthly Cash Burn", 
                value: monthlyBurn, 
                color: colors.outflow,
                displayUnit: 'M/mo',
                description: 'Average monthly cash outflow'
            },
            { 
                label: "Cash on Hand", 
                value: cashOnHand, 
                color: colors.neutral,
                displayUnit: 'M',
                description: 'Total available cash reserves'
            },
            { 
                label: "Runway (months)", 
                value: cashRunway, 
                color: colors.accent1, 
                isTarget: true,
                displayUnit: 'months',
                description: 'How long before cash depletes at current burn rate'
            }
        ];
        
        drawBulletChart('cash-burn-runway-chart', cashBurnData, 'Cash Burn & Runway', MAGNITUDE, {
            subtitle: `${Math.floor(cashRunway)} months runway at current burn rate`,
            thresholds: [6, 12, 24] // Danger, Warning, Good thresholds in months
        });
    } else {
        // No negative cash flow, so no burn rate to show
        const container = document.getElementById('cash-burn-runway-chart');
        if (container) {
            container.innerHTML = `<div class="no-data-message">Cash flow is positive - no burn rate to display.</div>`;
        }
    }
    
    // 8. Cash Flow Reconciliation Error - Create a new chart container
    const reconciliationDiv = document.getElementById('reconciliation-error-chart');
    if (!reconciliationDiv) {
        const container = document.createElement('div');
        container.id = 'reconciliation-error-chart';
        container.className = 'chart-container';
        
        // Find the chart grid and append the new container
        const chartGrid = document.querySelector('.chart-grid');
        if (chartGrid) {
            chartGrid.appendChild(container);
        }
    }
    
    const reconciliationData = USE_USER_VALUES ? [
        { label: "Reported Change", value: -13.94, color: colors.summary },
        { label: "Calculated Change", value: -1.41, color: colors.neutral },
        { label: "Difference", value: -12.53, color: colors.adjustment }
    ] : [
        { label: "Reported Change", value: parseFinancialValue(data.changeInCash), color: colors.summary },
        { label: "Calculated Change", value: parseFinancialValue(data.totalCashFromOperatingActivities) + 
            parseFinancialValue(data.totalCashflowsFromInvestingActivities) + parseFinancialValue(data.totalCashFromFinancingActivities),
          color: colors.neutral },
        { label: "Difference", value: parseFinancialValue(data.changeInCash) - (parseFinancialValue(data.totalCashFromOperatingActivities) + 
            parseFinancialValue(data.totalCashflowsFromInvestingActivities) + parseFinancialValue(data.totalCashFromFinancingActivities)),
          color: colors.adjustment }
    ];
    
    drawBarChart('reconciliation-error-chart', reconciliationData, 'Cash Flow Reconciliation', null, false, MAGNITUDE, {
        xAxisTitle: "Components",
        yAxisTitle: `${CURRENCY_UNIT} (${MAGNITUDE})`,
        subtitle: "Difference between reported and calculated change"
    });
    
    // 3. Return of Capital vs Reinvestment - Enhanced chart replacing Capital Allocation Summary
    const returnVsReinvestmentData = USE_USER_VALUES ? [
        { label: "CapEx (Reinvestment)", value: 15.38, color: colors.accent1 },
        { label: "Stock Purchases (Return)", value: 60.87, color: colors.accent2 },
        { label: "Dividends (Return)", value: 25.45, color: colors.accent3 }
    ] : [
        { label: "CapEx (Reinvestment)", 
          value: Math.abs(parseFinancialValue(data.capitalExpenditures)), 
          color: colors.accent1 },
        { label: "Stock Purchases (Return)", 
          value: Math.abs(parseFinancialValue(data.purchaseOfStock)), 
          color: colors.accent2 },
        { label: "Dividends (Return)", 
          value: Math.abs(parseFinancialValue(data.dividendsPaid)), 
          color: colors.accent3 }
    ];
    
    // Filter out zero values
    const filteredReturnData = returnVsReinvestmentData.filter(d => d.value > 0);
    
    // If no data is available, show a message
    if (filteredReturnData.length === 0) {
        const container = document.getElementById('return-vs-reinvestment-chart');
        if (container) {
            container.innerHTML = `<div class="no-data-message">No capital allocation data available.</div>`;
        }
    } else {
        // Add summary calculation - what percentage is return vs reinvestment
        const totalCapital = d3.sum(filteredReturnData, d => d.value);
        const reinvestment = filteredReturnData.find(d => d.label.includes("Reinvestment"))?.value || 0;
        const returnOfCapital = totalCapital - reinvestment;
        
        // Add percentage info to tooltips
        filteredReturnData.forEach(d => {
            d.percentage = (d.value / totalCapital * 100).toFixed(1) + "%";
        });
        
        // Draw the enhanced donut chart
        drawDonutChart('return-vs-reinvestment-chart', filteredReturnData, 'Return of Capital vs Reinvestment', MAGNITUDE, {
        showPercentage: true,
            subtitle: ``
    });
    }
    
    // 10. Non-Cash Adjustments Breakdown - Horizontal Bar Chart
    const nonCashAdjustmentsDiv = document.getElementById('non-cash-charges-chart');
    if (!nonCashAdjustmentsDiv) {
        const container = document.createElement('div');
        container.id = 'non-cash-charges-chart';
        container.className = 'chart-container';
        
        // Find the chart grid and append the new container
        const chartGrid = document.querySelector('.chart-grid:last-child');
        if (chartGrid) {
            chartGrid.appendChild(container);
        }
    }
    
    const nonCashAdjustmentsData = USE_USER_VALUES ? [
        { label: "Depreciation", value: 18.54, color: colors.inflow },
        { label: "Stock-Based Compensation", value: 8.78, color: colors.inflow },
        { label: "Other Non-Cash Items", value: 23.83, color: colors.inflow }
    ] : [
        { label: "Depreciation", value: parseFinancialValue(data.depreciationAndAmortization), 
          color: parseFinancialValue(data.depreciationAndAmortization) >= 0 ? colors.inflow : colors.outflow },
        { label: "Stock-Based Compensation", value: parseFinancialValue(data.stockBasedCompensation), 
          color: parseFinancialValue(data.stockBasedCompensation) >= 0 ? colors.inflow : colors.outflow },
        { label: "Other Non-Cash Items", value: parseFinancialValue(data.otherNonCashItems), 
          color: parseFinancialValue(data.otherNonCashItems) >= 0 ? colors.inflow : colors.outflow }
    ];
    
    drawHorizontalBarChart('non-cash-charges-chart', nonCashAdjustmentsData, 'Non-Cash Adjustments', null, MAGNITUDE, {
        xAxisTitle: `${CURRENCY_UNIT} (${MAGNITUDE})`,
        yAxisTitle: "Components",
        subtitle: "Items that affect OCF but do not involve cash"
    });
    
    // Ensure empty-chart containers are hidden
    document.querySelectorAll('.empty-chart').forEach(chart => {
        chart.style.display = 'none';
    });
    
    // Hide unused charts
    document.getElementById('investing-activities-chart')?.classList.add('empty-chart');
    document.getElementById('financing-activities-chart')?.classList.add('empty-chart');
}

// Helper Functions
function parseFinancialValue(value) {
    if (value === null || value === undefined || String(value).toLowerCase() === 'n/a') {
        return 0;
    }
    const num = parseFloat(String(value).replace(/,/g, ''));
    return isNaN(num) ? 0 : num;
}

function formatNumberForDisplay(num) {
    if (num === null || num === undefined || isNaN(num)) return "N/A";
    if (num === 0) return "0";
    
    const absNum = Math.abs(num);
    if (absNum >= 1e12) return (num / 1e12).toFixed(1) + "T";
    if (absNum >= 1e9) return (num / 1e9).toFixed(1) + "B";
    if (absNum >= 1e6) return (num / 1e6).toFixed(1) + "M";
    if (absNum >= 1e3) return (num / 1e3).toFixed(1) + "k";
    return num.toFixed(num % 1 === 0 ? 0 : 1);
}

// Custom function specifically for the expense breakdown chart with adjusted legend position
function drawPieChartWithCustomLegend(chartDivId, chartData, title) {
    // Use true flag to indicate special handling for the expense breakdown chart
    drawPieChart(chartDivId, chartData, title, true);
}

// Enhanced Chart Drawing Functions
function drawPieChart(chartDivId, chartData, title, isExpenseBreakdown = false) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    const validData = chartData.filter(d => d && typeof d.value === 'number' && !isNaN(d.value) && d.value > 0);
    if (validData.length === 0) {
        chartDiv.innerHTML = `<div class="no-data-message">Not enough data for ${title}.</div>`;
        return;
    }
    
    // Sort data to ensure consistent appearance in legend
    validData.sort((a, b) => b.value - a.value);
    
    const totalSum = d3.sum(validData, d => d.value);
    const margin = {top: 50, right: 220, bottom: 50, left: 80}, // More margin space all around
        containerWidth = chartDiv.clientWidth || 480, // Wider default container
        width = Math.max(280, containerWidth - margin.left - margin.right), // Ensure minimum width
        height = 330 - margin.top - margin.bottom,
        radius = Math.min(width, height) / 2.2; // Slightly smaller pie for more legend space
    
    // Adjust positioning for expense breakdown chart - moves the chart left but keeps more space
    const adjustedMarginLeft = isExpenseBreakdown ? margin.left - 40 : margin.left;
    
    const svg = d3.select(chartDiv).append("svg")
        .attr("width", "100%") // Use percentage width to ensure it fits container
        .attr("height", height + margin.top + margin.bottom)
        .style("overflow", "visible") // Ensure nothing gets cut off
        .append("g")
        .attr("transform", `translate(${(width/2) + adjustedMarginLeft}, ${(height/2) + margin.top})`); // Position based on chart type
    
    // Add title with animation
    svg.append("text")
        .attr("x", 0)
        .attr("y", -height/2 - 10)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .style("opacity", 0)
        .text(title)
        .transition()
        .duration(animations.duration)
        .style("opacity", 1);
    
    // Configure pie layout
    const pie = d3.pie().value(d => {
        // Ensure very small values get a minimum arc size for visibility
        const percentage = (d.value / totalSum) * 100;
        return percentage < 0.5 ? totalSum * 0.005 : d.value; // Give minimum 0.5% visual space to tiny values
    }).sort(null);
    
    const arc = d3.arc().innerRadius(radius * 0.4).outerRadius(radius);
    const labelArc = d3.arc().innerRadius(radius * 0.7).outerRadius(radius * 0.7);
    
    // Add gradient definitions
    const defs = svg.append("defs");
    
    validData.forEach((d, i) => {
        const gradientId = `pie-gradient-${chartDivId}-${i}`;
        const gradient = defs.append("linearGradient")
            .attr("id", gradientId)
            .attr("x1", "0%")
            .attr("y1", "0%")
            .attr("x2", "100%")
            .attr("y2", "100%");
        
        gradient.append("stop")
            .attr("offset", "0%")
            .attr("stop-color", d3.rgb(d3.schemeTableau10[i % 10]).brighter(0.5));
        
        gradient.append("stop")
            .attr("offset", "100%")
            .attr("stop-color", d3.rgb(d3.schemeTableau10[i % 10]).darker(0.3));
    });
    
    // Create shadow filter
    const filter = defs.append("filter")
        .attr("id", `pie-shadow-${chartDivId}`)
        .attr("height", "130%");
    
    filter.append("feGaussianBlur")
        .attr("in", "SourceAlpha")
        .attr("stdDeviation", 3)
        .attr("result", "blur");
    
    filter.append("feOffset")
        .attr("in", "blur")
        .attr("dx", 2)
        .attr("dy", 2)
        .attr("result", "offsetBlur");
    
    const feMerge = filter.append("feMerge");
    feMerge.append("feMergeNode").attr("in", "offsetBlur");
    feMerge.append("feMergeNode").attr("in", "SourceGraphic");
    
    // Add arcs with animations
    const arcs = svg.selectAll(".arc")
        .data(pie(validData))
        .enter().append("g")
        .attr("class", "arc");
    
    // Add slices with animation
    arcs.append("path")
        .attr("d", arc)
        .attr("fill", (d, i) => `url(#pie-gradient-${chartDivId}-${i})`)
        .attr("stroke", "white")
        .style("stroke-width", "2px")
        .style("opacity", 0)
        .style("filter", `url(#pie-shadow-${chartDivId})`)
        .transition()
        .duration(animations.duration)
        .delay((d, i) => i * 150)
        .ease(d3.easeElastic)
        .style("opacity", 1);
    
    // Hover effects
    arcs.selectAll("path")
        .on("mouseover", function(event, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("transform", function(d) {
                const centroid = arc.centroid(d);
                return `translate(${centroid[0] * 0.1}, ${centroid[1] * 0.1})`;
            });
            
            getD3Tooltip().style("visibility", "visible")
                .html(`
                    <b>${d.data.label}</b><br>
                    Value: <b>${formatNumberForDisplay(d.data.value)}</b><br>
                    Share: <b>${(d.data.value / totalSum * 100).toFixed(1)}%</b>
                `)
                .style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            getD3Tooltip().style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function() {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("transform", "translate(0,0)");
            
            getD3Tooltip().style("visibility", "hidden");
        });
    
    // Add percentage labels with animation
    arcs.append("text")
        .attr("transform", d => `translate(${labelArc.centroid(d)})`)
        .attr("dy", "0.35em")
        .attr("text-anchor", "middle")
        .style("font-size", "11px")
        .style("font-weight", "bold")
        .style("fill", "#fff")
        .style("opacity", 0)
        .text(d => {
            const percentage = (d.data.value / totalSum * 100);
            return percentage > 2 ? `${percentage.toFixed(0)}%` : ''; // Show label only if > 2%
        })
        .transition()
        .duration(animations.duration)
        .delay((d, i) => animations.duration + i * 100)
        .style("opacity", 1);
    
         // Create legend container with different positioning based on chart type
    const legendPosition = isExpenseBreakdown ? 
        `translate(${radius + 30}, ${-radius})` : // Position for expense breakdown adjusted left
        `translate(${radius + 30}, ${-radius})`; // Normal position for other charts
    
    const legend = svg.append("g")
        .attr("class", "legend")
        .attr("transform", legendPosition);
    
    // Apply transition to the legend separately
    legend.style("opacity", 0)
        .transition()
        .duration(animations.duration)
        .delay(animations.duration)
        .style("opacity", 1);
    
    // Calculate number of items and adjust spacing based on count
    const itemSpacing = validData.length > 4 ? 24 : 28; // More spacing between legend items for better readability
    
    // Ensure all data points are included in the legend with improved positioning
    const legendItems = legend.selectAll(".legend-item")
        .data(validData)
        .enter().append("g")
        .attr("class", "legend-item")
        .style("overflow", "visible") // Prevent clipping
        .attr("transform", (d, i) => `translate(0, ${i * itemSpacing})`); // Adjusted spacing
    
    legendItems.append("rect")
        .attr("width", 12)
        .attr("height", 12)
        .attr("rx", 2)
        .attr("fill", (d, i) => d3.schemeTableau10[i % 10]);
    
    // Format legend text to ensure all items are visible
    legendItems.append("text")
        .attr("x", 20) // Consistent spacing
        .attr("y", 10)
        .style("font-size", isExpenseBreakdown ? "12px" : "13px") // Slightly smaller for expense breakdown to fit better
        .style("font-weight", d => {
            // Highlight very small values to make them more noticeable
            return (d.value / totalSum) < 0.01 ? "bold" : "600";
        })
        .style("fill", "#000") // Black text for maximum contrast
        .text(d => {
            // Always show the percentage to 1 decimal place for small values
            const percentage = (d.value / totalSum * 100);
            const percentText = percentage < 1 ? `(${percentage.toFixed(1)}%)` : `(${percentage.toFixed(0)}%)`;
            return `${d.label} ${percentText}`;
        });
}

function drawBarChart(chartDivId, chartData, title, defaultColor = null, isPercent = false, unitLabel = '', options = {}) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    // Filter out null/undefined values and convert to numbers
    const validData = chartData.map(d => ({
        ...d,
        value: typeof d.value === 'number' && !isNaN(d.value) ? d.value : 0
    }));

    // Check if chart has meaningful data - at least one non-zero value
    const hasMeaningfulData = validData.some(d => d.value !== 0);
    if (!hasMeaningfulData) {
        chartDiv.classList.add('empty-chart');
        chartDiv.innerHTML = `<div class="no-data-message">No data available for ${title}</div>`;
        return;
    }

    const margin = {top: 70, right: 70, bottom: 200, left: 110}, // Even more bottom margin for x-axis labels
        containerWidth = chartDiv.clientWidth || 600,
        width = Math.max(250, containerWidth - margin.left - margin.right),
        height = 450 - margin.top - margin.bottom; // Taller chart area

    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

    // Add title with animation
    svg.append("text")
        .attr("x", width / 2)
        .attr("y", -margin.top / 2)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .style("opacity", 0)
        .text(title)
        .transition()
        .duration(animations.duration)
        .style("opacity", 1);
    
    // Add subtitle if provided
    if (options.subtitle) {
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", -margin.top / 2 + 20)
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-style", "italic")
            .style("opacity", 0)
            .text(options.subtitle)
            .transition()
            .duration(animations.duration)
            .delay(200)
            .style("opacity", 0.7);
    }

    const x = d3.scaleBand()
        .range([0, width])
        .domain(validData.map(d => d.label))
        .padding(0.6); // Increased padding between bars

    // Always start y-axis at 0 and handle negative values properly
    const yMin = Math.min(0, d3.min(validData, d => d.value) * 1.1); // Add 10% padding for negative values
    const yMax = Math.max(0, d3.max(validData, d => d.value) * 1.1); // Add 10% padding for positive values
    
    // Ensure positive range even if all values are 0 or negative
    const yDomain = [
        yMin,
        Math.max(0.1, yMax) // Ensure positive range even if all values are 0 or negative
    ];
    
    const y = d3.scaleLinear()
        .range([height, 0])
        .domain(yDomain);

    // Add X axis with improved label handling for long text
    svg.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(x))
        .selectAll("text")
        .attr("transform", "translate(-5,30)rotate(-25)") // Move labels much further down and less rotation
        .style("text-anchor", "end")
        .style("font-size", "13px") // Larger font
        .style("font-weight", "500") // Slightly bolder
        .style("letter-spacing", "0.02em") // Improved letter spacing
        .each(function(d) {
            // Abbreviate very long labels
            const text = d3.select(this);
            let content = text.text();
            // If the text is "Free Cash Flow", ensure it's fully visible
            if (content === "Free Cash Flow") {
                text.attr("transform", "translate(-5,40)rotate(-25)");
                text.style("font-weight", "bold");
            }
            // Abbreviate long texts
            if (content === "Working Capital") content = "Working Cap";
            if (content === "Non-Cash Adj.") content = "Non-Cash";
            if (content === "Cash At Beginning Of Period") content = "Begin Cash";
            if (content === "Cash At End Of Period") content = "End Cash";
            text.text(content);
        })
        .call(wrap, x.bandwidth() * 2.5); // Even more space for wrapping text
    
    // Add X axis title if provided
    if (options.xAxisTitle) {
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", height + 70) // Position below the x-axis labels
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("opacity", 0)
            .text(options.xAxisTitle)
            .transition()
            .duration(animations.duration)
            .delay(300)
            .style("opacity", 0.8);
    }

    // Add Y axis with proper units
    const yAxisTickFormat = isPercent 
        ? d => d + '%' 
        : d => unitLabel ? `${formatNumberForDisplay(d)} ${unitLabel}` : formatNumberForDisplay(d);
        
    svg.append("g")
        .call(d3.axisLeft(y).ticks(5).tickFormat(yAxisTickFormat))
        .selectAll("text")
        .style("font-size", "12px");
    
    // Add Y axis title if provided
    if (options.yAxisTitle) {
        svg.append("text")
            .attr("transform", "rotate(-90)")
            .attr("x", -height / 2)
            .attr("y", -60) // Position to the left of the y-axis
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("opacity", 0)
            .text(options.yAxisTitle)
            .transition()
            .duration(animations.duration)
            .delay(300)
            .style("opacity", 0.8);
    }

    // Add grid lines
    svg.append("g")
        .attr("class", "grid")
        .call(d3.axisLeft(y).ticks(5).tickSize(-width).tickFormat(""))
        .style("opacity", 0.1);

    // Add zero line if we have negative values
    if (yMin < 0) {
        svg.append("line")
            .attr("x1", 0)
            .attr("x2", width)
            .attr("y1", y(0))
            .attr("y2", y(0))
            .attr("stroke", "#666")
            .attr("stroke-dasharray", "4,4")
            .style("opacity", 0.5);
    }

    // Create gradient for bars
    const defs = svg.append("defs");
    
    // Function to get color for a data point
    const getColor = (d, i) => {
        if (d.color) return d.color;
        if (d.isSummary) return colors.summary;
        if (defaultColor) return d.value >= 0 ? defaultColor : colors.outflow;
        return d.value >= 0 ? colors.inflow : colors.outflow;
    };
    
    // Create individual gradients for each bar
    validData.forEach((d, i) => {
        // Get base color for this bar
        const baseColor = getColor(d, i);
        
        // Create gradient for this bar
        const gradientId = `bar-gradient-${chartDivId}-${i}`;
        const gradient = defs.append("linearGradient")
            .attr("id", gradientId)
            .attr("x1", "0%")
            .attr("y1", "0%")
            .attr("x2", "0%")
            .attr("y2", "100%");
        
        gradient.append("stop")
            .attr("offset", "0%")
            .attr("stop-color", d3.rgb(baseColor).brighter(0.5));
        
        gradient.append("stop")
            .attr("offset", "100%")
            .attr("stop-color", d3.rgb(baseColor).darker(0.3));
        
        // Assign gradient ID to the data point
        d.gradientId = gradientId;
    });
    
    // Create shadow filter
    const filter = defs.append("filter")
        .attr("id", `drop-shadow-${chartDivId}`)
        .attr("height", "130%");
    
    filter.append("feGaussianBlur")
        .attr("in", "SourceAlpha")
        .attr("stdDeviation", 2)
        .attr("result", "blur");
    
    filter.append("feOffset")
        .attr("in", "blur")
        .attr("dx", 2)
        .attr("dy", 2)
        .attr("result", "offsetBlur");
    
    const feMerge = filter.append("feMerge");
    feMerge.append("feMergeNode").attr("in", "offsetBlur");
    feMerge.append("feMergeNode").attr("in", "SourceGraphic");

    // Add bars with animations
    svg.selectAll(".bar")
        .data(validData)
        .enter().append("rect")
        .attr("class", "bar")
        .attr("x", d => x(d.label))
        .attr("y", d => d.value >= 0 ? height : y(0)) // Start position for animation
        .attr("width", x.bandwidth())
        .attr("height", 0) // Start with height 0 for animation
        .attr("fill", d => `url(#${d.gradientId})`)
        .attr("stroke", d => getColor(d))
        .attr("stroke-width", d => d.isSummary ? 2 : 1)
        .attr("rx", 4) // Rounded corners
        .attr("ry", 4)
        .style("filter", `url(#drop-shadow-${chartDivId})`)
        .transition()
        .duration(animations.duration)
        .delay((d, i) => i * 100)
        .ease(d3.easeElastic)
        .attr("y", d => d.value >= 0 ? y(d.value) : y(0))
        .attr("height", d => Math.abs(y(d.value) - y(0)));

    // Enhanced hover effects
    svg.selectAll(".bar")
        .on("mouseover", function(event, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("opacity", 0.8)
                .attr("x", d => x(d.label) - x.bandwidth() * 0.05)
                .attr("width", x.bandwidth() * 1.1);
            
            getD3Tooltip().style("visibility", "visible")
                .html(`
                    <b>${d.label}</b><br>
                    Value: <b>${formatNumberForDisplay(d.value)}${unitLabel ? ' ' + unitLabel : ''}</b>
                    ${d.description ? `<br>${d.description}` : ''}
                `)
                .style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            getD3Tooltip().style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function(event, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("opacity", 1)
                .attr("x", d => x(d.label))
                .attr("width", x.bandwidth());
            
            getD3Tooltip().style("visibility", "hidden");
        });

    // Value labels with adaptive positioning and animation
    svg.selectAll(".value-label")
        .data(validData)
        .enter().append("text")
        .attr("class", "value-label")
        .attr("x", d => x(d.label) + x.bandwidth() / 2)
        .attr("y", d => {
            // Position labels further from bars for better visibility
            return d.value >= 0 
                ? y(d.value) - 15 // More space above positive bars
                : y(d.value) + 25; // More space below negative bars
        })
        .attr("text-anchor", "middle")
        .style("font-size", "13px") // Larger font size
        .style("font-weight", "bold")
        .style("fill", d => getColor(d)) // Always use the bar color
        .style("opacity", 0)
        .style("text-shadow", "0px 0px 3px rgba(255,255,255,0.7)") // Add text shadow for legibility
        .text(d => formatNumberForDisplay(d.value) + (unitLabel ? ' ' + unitLabel : ''))
        .transition()
        .duration(animations.duration)
        .delay((d, i) => animations.duration + i * 100)
        .style("opacity", 1);
}

// Enhanced text wrapping function with better handling
function wrap(text, width) {
    text.each(function() {
        const text = d3.select(this);
        // Handle various text splits including space, parentheses, slashes, commas
        const words = text.text().split(/\s+|(?=\()|(?=\))|\/|,/g).reverse(); 
        let word;
        let line = [];
        let lineNumber = 0;
        const lineHeight = 1.4; // Increased line height for better readability
        const y = text.attr("y");
        const dy = parseFloat(text.attr("dy") || 0);
        let tspan = text.text(null).append("tspan")
            .attr("x", 0)
            .attr("y", y)
            .attr("dy", dy + "em")
            .style("font-weight", text.style("font-weight")) // Preserve font weight
            .style("font-size", text.style("font-size")); // Preserve font size
        
        // Process words
        while (word = words.pop()) {
            // Skip empty words
            if (!word.trim()) continue;
            
            line.push(word);
            tspan.text(line.join(" "));
            
            // Check if we need to break line
            if (tspan.node().getComputedTextLength() > width) {
                line.pop();
                // Keep at least one word per line
                if (line.length === 0) {
                    line = [word];
                    // If a single word is too long, just keep it and let it overflow slightly
                } else {
                tspan.text(line.join(" "));
                line = [word];
                tspan = text.append("tspan")
                    .attr("x", 0)
                    .attr("y", y)
                    .attr("dy", ++lineNumber * lineHeight + dy + "em")
                        .style("font-weight", text.style("font-weight")) // Preserve font weight
                        .style("font-size", text.style("font-size")) // Preserve font size
                    .text(word);
                }
            }
        }
        
        // Adjust position for multi-line text - center vertically
        if (lineNumber > 0) {
            // Offset all lines to center the text block
            const offset = -lineNumber * lineHeight / 2;
            text.selectAll("tspan")
                .attr("dy", (d, i) => (offset + i * lineHeight) + (i === 0 ? dy : 0) + "em");
        }
    });
}

function drawWaterfallChart(chartDivId, chartData, title, unitLabel, options = {}) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    // Filter out zero/invalid values for display, but keep them for correct waterfall logic
    const validData = chartData.map(d => ({
        ...d,
        value: typeof d.value === 'number' && !isNaN(d.value) ? d.value : 0
    }));
    
    // Check if chart has meaningful data
    const hasMeaningfulData = validData.some(d => d.value !== 0);
    if (!hasMeaningfulData) {
        chartDiv.classList.add('empty-chart');
        chartDiv.innerHTML = `<div class="no-data-message">No data available for ${title}</div>`;
        return;
    }

    // Calculate cumulative values for the waterfall with consistent starting point
    let cumulative = 0;
    const waterfallData = validData.map((d, i) => {
        let y0, y1;
        if (i === 0) {
            // First bar always starts at 0
            y0 = 0;
            y1 = d.value;
            cumulative = y1;
        } else if (i === validData.length - 1 && d.isSummary) {
            // Summary/total bar - starts at 0 and goes to final value
            y0 = 0;
            y1 = cumulative;
        } else {
            // All other bars start at 0
            y0 = 0;
            // And end at their value
            y1 = d.value;
            // Still track cumulative for the summary bar
            cumulative += d.value;
        }
        return { ...d, y0, y1, isSummary: d.isSummary || false };
    });

    const margin = {top: 50, right: 50, bottom: 140, left: 90}, // Increased bottom margin
        containerWidth = chartDiv.clientWidth || 600,
        width = Math.max(200, containerWidth - margin.left - margin.right),
        height = 350 - margin.top - margin.bottom;

    // Find min/max for y-axis - ensure we include 0
    const yMin = Math.min(0, ...waterfallData.map(d => Math.min(d.y0, d.y1)));
    const yMax = Math.max(0, ...waterfallData.map(d => Math.max(d.y0, d.y1)));
    
    // Add some padding to the domain to ensure everything is visible
    const padding = 0.15; // 15% padding
    const yDomain = [
        yMin < 0 ? yMin * (1 + padding) : 0, // Add padding below zero if we have negative values
        yMax > 0 ? yMax * (1 + padding) : Math.max(0.1, yMax) // Ensure positive range even if all values are 0
    ];

    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

    // Add title with animation
    svg.append("text")
        .attr("x", width / 2)
        .attr("y", -margin.top / 2)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .style("opacity", 0)
        .text(title)
        .transition()
        .duration(animations.duration)
        .style("opacity", 1);
    
    // Add subtitle if provided
    if (options.subtitle) {
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", -margin.top / 2 + 20)
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-style", "italic")
            .style("opacity", 0)
            .text(options.subtitle)
            .transition()
            .duration(animations.duration)
            .delay(200)
            .style("opacity", 0.7);
    }

    const x = d3.scaleBand()
        .range([0, width])
        .domain(waterfallData.map(d => d.label))
        .padding(0.4);

    const y = d3.scaleLinear()
        .range([height, 0])
        .domain(yDomain);

    // X axis with improved label handling
    svg.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(x))
        .selectAll("text")
        .attr("transform", "translate(-10,0)rotate(-45)")
        .style("text-anchor", "end")
        .style("font-size", "11px")
        .call(wrap, x.bandwidth() * 1.5); // Use text wrapping for longer labels
    
    // Add X axis title if provided
    if (options.xAxisTitle) {
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", height + 70) // Position below the x-axis labels
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("opacity", 0)
            .text(options.xAxisTitle)
            .transition()
            .duration(animations.duration)
            .delay(300)
            .style("opacity", 0.8);
    }

    // Y axis with proper units
    const yAxisTickFormat = unitLabel 
        ? d => `${formatNumberForDisplay(d)} ${unitLabel}` 
        : formatNumberForDisplay;
        
    svg.append("g")
        .call(d3.axisLeft(y).ticks(5).tickFormat(yAxisTickFormat))
        .selectAll("text")
        .style("font-size", "12px");
    
    // Add Y axis title if provided
    if (options.yAxisTitle) {
        svg.append("text")
            .attr("transform", "rotate(-90)")
            .attr("x", -height / 2)
            .attr("y", -60) // Position to the left of the y-axis
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("opacity", 0)
            .text(options.yAxisTitle)
            .transition()
            .duration(animations.duration)
            .delay(300)
            .style("opacity", 0.8);
    }

    // Add grid lines
    svg.append("g")
        .attr("class", "grid")
        .call(d3.axisLeft(y).ticks(5).tickSize(-width).tickFormat(""))
        .style("opacity", 0.1);

    // Add zero line
    svg.append("line")
        .attr("x1", 0)
        .attr("x2", width)
        .attr("y1", y(0))
        .attr("y2", y(0))
        .attr("stroke", "#666")
        .attr("stroke-dasharray", "4,4")
        .style("opacity", 0.5);

    // Create gradients for the bars
    const defs = svg.append("defs");
    
    // Function to get color for a data point
    const getColor = (d) => {
        if (d.color) return d.color;
        if (d.isSummary) return colors.summary;
        return d.y1 >= 0 ? colors.inflow : colors.outflow;
    };
    
    // Create individual gradients for each bar
    waterfallData.forEach((d, i) => {
        // Get base color for this bar
        const baseColor = getColor(d);
        
        // Create gradient for this bar
        const gradientId = `waterfall-gradient-${chartDivId}-${i}`;
        const gradient = defs.append("linearGradient")
            .attr("id", gradientId)
            .attr("x1", "0%")
            .attr("y1", "0%")
            .attr("x2", "0%")
            .attr("y2", "100%");
        
        gradient.append("stop")
            .attr("offset", "0%")
            .attr("stop-color", d3.rgb(baseColor).brighter(0.5));
        
        gradient.append("stop")
            .attr("offset", "100%")
            .attr("stop-color", d3.rgb(baseColor).darker(0.3));
        
        // Assign gradient ID to the data point
        d.gradientId = gradientId;
    });
    
    // Add shadow filter
    const filter = defs.append("filter")
        .attr("id", `waterfall-shadow-${chartDivId}`)
        .attr("height", "130%");
    
    filter.append("feGaussianBlur")
        .attr("in", "SourceAlpha")
        .attr("stdDeviation", 2)
        .attr("result", "blur");
    
    filter.append("feOffset")
        .attr("in", "blur")
        .attr("dx", 2)
        .attr("dy", 2)
        .attr("result", "offsetBlur");
    
    const feMerge = filter.append("feMerge");
    feMerge.append("feMergeNode").attr("in", "offsetBlur");
    feMerge.append("feMergeNode").attr("in", "SourceGraphic");

    // Remove connector lines since bars all start at 0

    // Bars - all start at 0 on y-axis and go to their respective values with animations
    svg.selectAll(".waterfall-bar")
        .data(waterfallData)
        .enter().append("rect")
        .attr("class", "waterfall-bar")
        .attr("x", d => x(d.label))
        .attr("y", height) // Start at bottom for animation
        .attr("width", x.bandwidth())
        .attr("height", 0) // Start with height 0
        .attr("rx", 4) // Rounded corners
        .attr("ry", 4)
        .attr("fill", d => `url(#${d.gradientId})`)
        .attr("stroke", d => getColor(d))
        .attr("stroke-width", d => d.isSummary ? 2 : 1)
        .style("filter", `url(#waterfall-shadow-${chartDivId})`)
        .transition()
        .duration(animations.duration)
        .delay((d, i) => i * 150)
        .ease(d3.easeElastic)
        .attr("y", d => y(Math.max(0, d.y1))) // End at final position
        .attr("height", d => Math.abs(y(0) - y(d.y1))); // Final height

    // Enhanced hover effects
    svg.selectAll(".waterfall-bar")
        .on("mouseover", function(event, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("opacity", 0.8)
                .attr("x", d => x(d.label) - x.bandwidth() * 0.05)
                .attr("width", x.bandwidth() * 1.1);
            
            getD3Tooltip().style("visibility", "visible")
                .html(`
                    <b>${d.label}</b><br>
                    Value: <b>${formatNumberForDisplay(d.y1)}${unitLabel ? ' ' + unitLabel : ''}</b>
                    ${d.description ? `<br>${d.description}` : ''}
                `)
                .style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            getD3Tooltip().style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function(event, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("opacity", 1)
                .attr("x", d => x(d.label))
                .attr("width", x.bandwidth());
            
            getD3Tooltip().style("visibility", "hidden");
        });

    // Value labels with adaptive positioning and animation
    svg.selectAll(".waterfall-label")
        .data(waterfallData)
        .enter().append("text")
        .attr("class", "waterfall-label")
        .attr("x", d => x(d.label) + x.bandwidth() / 2)
        .attr("y", d => {
            // Always position labels outside the bars
            return d.y1 >= 0 
                ? y(d.y1) - 8 // Position above positive bars
                : y(d.y1) + 16; // Position below negative bars
        })
        .attr("text-anchor", "middle")
        .style("font-size", "10px") // Smaller font size for better fit
        .style("font-weight", "bold")
        .style("fill", d => getColor(d)) // Always use the bar color
        .style("opacity", 0) // Start invisible for animation
        .text(d => formatNumberForDisplay(d.y1) + (unitLabel ? ' ' + unitLabel : ''))
        .transition()
        .duration(animations.duration)
        .delay((d, i) => animations.duration + i * 100) // Stagger the animations
        .style("opacity", 1); // Fade in
}

function drawCashConversionCycleChart(chartDivId, data, title) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    // Improved data validation and calculation
    const inventory = parseFinancialValue(data.inventory);
    const costOfRevenue = parseFinancialValue(data.costOfRevenue);
    const netReceivables = parseFinancialValue(data.netReceivables);
    const totalRevenue = parseFinancialValue(data.totalRevenue);
    const accountsPayable = parseFinancialValue(data.accountsPayable);
    
    // Check if we have enough data to calculate the cycle
    if (!costOfRevenue || !totalRevenue) {
        chartDiv.innerHTML = `<div class="no-data-message">Not enough data for Cash Conversion Cycle. Required: Cost of Revenue and Total Revenue.</div>`;
        return;
    }
    
    const cashConversionCycleData = [
        { 
            label: "Inventory Days", 
            value: costOfRevenue ? (inventory / costOfRevenue) * 365 : 0 
        },
        { 
            label: "Receivables Days", 
            value: totalRevenue ? (netReceivables / totalRevenue) * 365 : 0 
        },
        { 
            label: "Payables Days", 
            value: costOfRevenue ? (accountsPayable / costOfRevenue) * 365 : 0 
        }
    ];
    
    // Only draw if we have valid data
    if (cashConversionCycleData.some(d => d.value > 0)) {
        drawBarChart(chartDivId, cashConversionCycleData, title, colors.primary, false);
    } else {
        chartDiv.innerHTML = `<div class="no-data-message">Not enough data for Cash Conversion Cycle. All values are zero or missing.</div>`;
    }
}

// Add new enhanced bar chart function
function drawEnhancedBarChart(chartDivId, chartData, title, defaultColor, isPercent = false) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    const validData = chartData.filter(d => d && typeof d.value === 'number' && !isNaN(d.value));
    if (validData.length === 0) {
        chartDiv.innerHTML = `<div class="no-data-message">No data for ${title}</div>`;
        return;
    }

    const margin = {top: 50, right: 30, bottom: 100, left: 90},
        containerWidth = chartDiv.clientWidth || 600,
        width = Math.max(200, containerWidth - margin.left - margin.right),
        height = 350 - margin.top - margin.bottom;

    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

    // Add title
    svg.append("text")
        .attr("x", width / 2)
        .attr("y", 0 - (margin.top / 2))
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .text(title);

    const x = d3.scaleBand()
        .range([0, width])
        .domain(validData.map(d => d.label))
        .padding(0.4);

    const y = d3.scaleLinear()
        .range([0, d3.max(validData, d => d.value) * 1.1])
        .domain([height, 0]);

    // Add X axis
    svg.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(x))
        .selectAll("text")
        .attr("transform", "translate(-10,0)rotate(-40)")
        .style("text-anchor", "end")
        .style("font-size", "11px");

    // Add Y axis
    svg.append("g")
        .call(d3.axisLeft(y).ticks(6).tickFormat(isPercent ? d => d + '%' : formatNumberForDisplay))
        .selectAll("text")
        .style("font-size", "11px");

    // Add bars
    svg.selectAll(".bar")
        .data(validData)
        .enter().append("rect")
        .attr("class", "bar")
        .attr("x", d => x(d.label))
        .attr("y", d => y(d.value))
        .attr("width", x.bandwidth())
        .attr("height", d => height - y(d.value))
        .attr("fill", defaultColor)
        .on("mouseover", function(event, d) {
            d3.select(this).transition().duration(200).attr("opacity", 0.7);
            getD3Tooltip().style("visibility", "visible")
                .html(`<b>${d.label}</b><br>Value: <b>${formatNumberForDisplay(d.value)}</b><br>${d.description || ''}`)
                .style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            getD3Tooltip().style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function() {
            d3.select(this).transition().duration(200).attr("opacity", 1);
            getD3Tooltip().style("visibility", "hidden");
        });

    // Add value labels
    svg.selectAll(".value-label")
        .data(validData)
        .enter().append("text")
        .attr("class", "value-label")
        .attr("x", d => x(d.label) + x.bandwidth() / 2)
        .attr("y", d => d.value >= 0 ? y(d.value) - 5 : y(d.value) + 15)
        .attr("text-anchor", "middle")
        .style("font-size", "15px")
        .style("font-weight", "bold")
        .style("fill", d => d.value < 0 ? colors.negative : colors.primary)
        .text(d => formatNumberForDisplay(d.value));
}

// New function for dual-axis chart
function drawDualAxisChart(chartDivId, data, title) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';

    if (!data.capex && !data.depreciation) {
        chartDiv.classList.add('no-data');
        return;
    }

    const margin = {top: 50, right: 60, bottom: 50, left: 60},
        width = chartDiv.clientWidth - margin.left - margin.right,
        height = 300 - margin.top - margin.bottom;

    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

    // Add title
    svg.append("text")
        .attr("x", width / 2)
        .attr("y", -margin.top / 2)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .text(title);

    // Scales
    const x = d3.scaleBand()
        .range([0, width])
        .domain(["CapEx", "Depreciation"])
        .padding(0.4);

    const y1 = d3.scaleLinear()
        .range([height, 0])
        .domain([0, Math.max(data.capex, data.depreciation) * 1.1]);

    const y2 = d3.scaleLinear()
        .range([height, 0])
        .domain([0, data.intensity * 1.1]);

    // Axes
    svg.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(x));

    svg.append("g")
        .call(d3.axisLeft(y1).tickFormat(formatNumberForDisplay))
        .append("text")
        .attr("transform", "rotate(-90)")
        .attr("y", -40)
        .attr("x", -height / 2)
        .attr("text-anchor", "middle")
        .text("Amount");

    svg.append("g")
        .attr("transform", `translate(${width},0)`)
        .call(d3.axisRight(y2).tickFormat(d => d + "%"))
        .append("text")
        .attr("transform", "rotate(-90)")
        .attr("y", 40)
        .attr("x", -height / 2)
        .attr("text-anchor", "middle")
        .text("Intensity %");

    // Bars
    svg.selectAll(".bar")
        .data([{label: "CapEx", value: data.capex}, {label: "Depreciation", value: data.depreciation}])
        .enter().append("rect")
        .attr("class", "bar")
        .attr("x", d => x(d.label))
        .attr("y", d => y1(d.value))
        .attr("width", x.bandwidth())
        .attr("height", d => height - y1(d.value))
        .attr("fill", colors.primary)
        .on("mouseover", function(event, d) {
            d3.select(this).transition().duration(200).attr("opacity", 0.7);
            getD3Tooltip().style("visibility", "visible")
                .html(`<b>${d.label}</b><br>Value: <b>${formatNumberForDisplay(d.value)}</b>`)
                .style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            getD3Tooltip().style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function() {
            d3.select(this).transition().duration(200).attr("opacity", 1);
            getD3Tooltip().style("visibility", "hidden");
        });

    // Intensity Line
    svg.append("line")
        .attr("x1", x("CapEx") + x.bandwidth() / 2)
        .attr("x2", x("Depreciation") + x.bandwidth() / 2)
        .attr("y1", y2(data.intensity))
        .attr("y2", y2(data.intensity))
        .attr("stroke", colors.accent1)
        .attr("stroke-width", 2)
        .attr("stroke-dasharray", "5,5");

    // Intensity Label
    svg.append("text")
        .attr("x", width / 2)
        .attr("y", y2(data.intensity) - 10)
        .attr("text-anchor", "middle")
        .style("font-size", "12px")
        .style("fill", colors.accent1)
        .text(`Intensity: ${data.intensity.toFixed(1)}%`);
        
    // Value labels for bars
    svg.selectAll(".value-label")
        .data([{label: "CapEx", value: data.capex}, {label: "Depreciation", value: data.depreciation}])
        .enter().append("text")
        .attr("class", "value-label")
        .attr("x", d => x(d.label) + x.bandwidth() / 2)
        .attr("y", d => y1(d.value) - 10)
        .attr("text-anchor", "middle")
        .style("font-size", "12px")
        .style("font-weight", "bold")
        .text(d => formatNumberForDisplay(d.value));
}

// New function for stacked bar chart
function drawStackedBarChart(chartDivId, data, title) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';

    if (!data.some(d => d.value !== 0)) {
        chartDiv.classList.add('no-data');
        return;
    }

    const margin = {top: 50, right: 30, bottom: 50, left: 60},
        width = chartDiv.clientWidth - margin.left - margin.right,
        height = 300 - margin.top - margin.bottom;

    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

    // Add title
    svg.append("text")
        .attr("x", width / 2)
        .attr("y", -margin.top / 2)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .text(title);

    const x = d3.scaleBand()
        .range([0, width])
        .domain(data.map(d => d.label))
        .padding(0.4);

    const y = d3.scaleLinear()
        .range([height, 0])
        .domain([0, d3.max(data, d => Math.abs(d.value)) * 1.1]);

    // Axes
    svg.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(x))
        .selectAll("text")
        .attr("transform", "rotate(-45)")
        .style("text-anchor", "end");

    svg.append("g")
        .call(d3.axisLeft(y).tickFormat(formatNumberForDisplay));

    // Bars
    svg.selectAll(".bar")
        .data(data)
        .enter().append("rect")
        .attr("class", "bar")
        .attr("x", d => x(d.label))
        .attr("y", d => d.value >= 0 ? y(d.value) : y(0))
        .attr("width", x.bandwidth())
        .attr("height", d => Math.abs(y(d.value) - y(0)))
        .attr("fill", d => d.value >= 0 ? colors.primary : colors.negative)
        .on("mouseover", function(event, d) {
            d3.select(this).transition().duration(200).attr("opacity", 0.7);
            getD3Tooltip().style("visibility", "visible")
                .html(`<b>${d.label}</b><br>Value: <b>${formatNumberForDisplay(d.value)}</b>`)
                .style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            getD3Tooltip().style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function() {
            d3.select(this).transition().duration(200).attr("opacity", 1);
            getD3Tooltip().style("visibility", "hidden");
        });

    // Value labels
    svg.selectAll(".value-label")
        .data(data)
        .enter().append("text")
        .attr("class", "value-label")
        .attr("x", d => x(d.label) + x.bandwidth() / 2)
        .attr("y", d => d.value >= 0 ? y(d.value) - 5 : y(d.value) + 15)
        .attr("text-anchor", "middle")
        .style("font-size", "15px")
        .style("font-weight", "bold")
        .style("fill", d => d.value < 0 ? colors.negative : colors.primary)
        .text(d => formatNumberForDisplay(d.value));
}

// --- CHART RENDERING: Only show if data is meaningful ---
function shouldShowChart(chartData) {
    // Exclude if all values are zero, null, or missing
    if (!Array.isArray(chartData) || chartData.length === 0) return false;
    return chartData.some(d => d && typeof d.value === 'number' && !isNaN(d.value) && d.value !== 0);
}

// Draw a donut chart (pie chart with inner hole)
function drawDonutChart(chartDivId, chartData, title, unitLabel = '', options = {}) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    // Filter data for valid entries
    const validData = chartData.filter(d => d && typeof d.value === 'number' && !isNaN(d.value) && d.value > 0);
    if (validData.length === 0) {
        chartDiv.innerHTML = `<div class="no-data-message">Not enough data for ${title}.</div>`;
        return;
    }
    
    const totalSum = d3.sum(validData, d => d.value);
    
    const margin = {top: 50, right: 20, bottom: 50, left: 20}, // Increased bottom margin for legend
        containerWidth = chartDiv.clientWidth || 380,
        width = Math.max(200, containerWidth - margin.left - margin.right),
        height = 330 - margin.top - margin.bottom,
        radius = Math.min(width, height) / 2;
    
    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${(width/2) + margin.left}, ${(height/2) + margin.top})`);
    
    // Add title with animation
    svg.append("text")
        .attr("x", 0)
        .attr("y", -height/2 - 10)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .style("opacity", 0)
        .text(title)
        .transition()
        .duration(animations.duration)
        .style("opacity", 1);
    
    // Add subtitle if provided
    if (options.subtitle) {
        svg.append("text")
            .attr("x", 0)
            .attr("y", -height/2 + 10)
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-style", "italic")
            .style("opacity", 0)
            .text(options.subtitle)
            .transition()
            .duration(animations.duration)
            .delay(200)
            .style("opacity", 0.7);
    }
    
    // Total value in center
    svg.append("text")
        .attr("x", 0)
        .attr("y", 0)
        .attr("text-anchor", "middle")
        .attr("dominant-baseline", "middle")
        .style("font-size", "20px")
        .style("font-weight", "bold")
        .style("opacity", 0)
        .text(formatNumberForDisplay(totalSum) + (unitLabel ? ' ' + unitLabel : ''))
        .transition()
        .duration(animations.duration)
        .delay(animations.duration / 2)
        .style("opacity", 1);
    
    svg.append("text")
        .attr("x", 0)
        .attr("y", 25)
        .attr("text-anchor", "middle")
        .attr("dominant-baseline", "middle")
        .style("font-size", "12px")
        .style("opacity", 0)
        .text("Total")
        .transition()
        .duration(animations.duration)
        .delay(animations.duration / 2)
        .style("opacity", 0.7);
    
    // Configure pie layout
    const pie = d3.pie()
        .value(d => d.value)
        .sort(null);
    
    // Configure arcs for donut
    const arc = d3.arc()
        .innerRadius(radius * 0.6) // Inner radius for donut hole
        .outerRadius(radius);
    
    const labelArc = d3.arc()
        .innerRadius(radius * 0.8)
        .outerRadius(radius * 0.8);
    
    // Function to get color for a data point
    const getColor = (d, i) => {
        if (d.data && d.data.color) return d.data.color;
        if (d.color) return d.color;
        return d3.schemeTableau10[i % 10];
    };
    
    // Add gradient definitions
    const defs = svg.append("defs");
    
    validData.forEach((d, i) => {
        const baseColor = d.color || d3.schemeTableau10[i % 10];
        const gradientId = `donut-gradient-${chartDivId}-${i}`;
        const gradient = defs.append("linearGradient")
            .attr("id", gradientId)
            .attr("x1", "0%")
            .attr("y1", "0%")
            .attr("x2", "100%")
            .attr("y2", "100%");
        
        gradient.append("stop")
            .attr("offset", "0%")
            .attr("stop-color", d3.rgb(baseColor).brighter(0.5));
        
        gradient.append("stop")
            .attr("offset", "100%")
            .attr("stop-color", d3.rgb(baseColor).darker(0.3));
        
        // Add gradient ID to data
        d.gradientId = gradientId;
    });
    
    // Create shadow filter
    const filter = defs.append("filter")
        .attr("id", `donut-shadow-${chartDivId}`)
        .attr("height", "130%");
    
    filter.append("feGaussianBlur")
        .attr("in", "SourceAlpha")
        .attr("stdDeviation", 3)
        .attr("result", "blur");
    
    filter.append("feOffset")
        .attr("in", "blur")
        .attr("dx", 2)
        .attr("dy", 2)
        .attr("result", "offsetBlur");
    
    const feMerge = filter.append("feMerge");
    feMerge.append("feMergeNode").attr("in", "offsetBlur");
    feMerge.append("feMergeNode").attr("in", "SourceGraphic");
    
    // Add arcs with animations
    const arcs = svg.selectAll(".arc")
        .data(pie(validData))
        .enter().append("g")
        .attr("class", "arc");
    
    // Add slices with animation
    arcs.append("path")
        .attr("d", arc)
        .attr("fill", (d, i) => `url(#${validData[i].gradientId})`)
        .attr("stroke", "white")
        .style("stroke-width", "2px")
        .style("opacity", 0)
        .style("filter", `url(#donut-shadow-${chartDivId})`)
        .transition()
        .duration(animations.duration)
        .delay((d, i) => i * 150)
        .ease(d3.easeElastic)
        .style("opacity", 1);
    
    // Hover effects
    arcs.selectAll("path")
        .on("mouseover", function(event, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("transform", function(d) {
                    const centroid = arc.centroid(d);
                    return `translate(${centroid[0] * 0.1}, ${centroid[1] * 0.1})`;
                });
            
            getD3Tooltip().style("visibility", "visible")
                .html(`
                    <b>${d.data.label}</b><br>
                    Value: <b>${formatNumberForDisplay(d.data.value)}${unitLabel ? ' ' + unitLabel : ''}</b><br>
                    Share: <b>${(d.data.value / totalSum * 100).toFixed(0)}%</b>
                `)
                .style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            getD3Tooltip().style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function() {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("transform", "translate(0,0)");
            
            getD3Tooltip().style("visibility", "hidden");
        });
    
    // Add percentage labels with animation
    if (options.showPercentage !== false) {
        arcs.append("text")
            .attr("transform", d => `translate(${labelArc.centroid(d)})`)
            .attr("dy", "0.35em")
            .attr("text-anchor", "middle")
            .style("font-size", "11px")
            .style("font-weight", "bold")
            .style("fill", "#fff")
            .style("opacity", 0)
            .text(d => {
                const percentage = (d.data.value / totalSum * 100);
                return percentage > 5 ? `${percentage.toFixed(0)}%` : '';
            })
            .transition()
            .duration(animations.duration)
            .delay((d, i) => animations.duration + i * 100)
            .style("opacity", 1);
    }
    
    // Function to truncate text
    const truncateText = (text, maxLength) => {
        return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
    };
    
    // Create legend directly instead of using transition
    const legendGroup = svg.append("g")
        .attr("class", "legend")
        .attr("transform", `translate(${-width/2 + 10}, ${height/2 - 40})`); // More space above legend
    
    const legendItems = legendGroup.selectAll(".legend-item")
        .data(validData)
        .enter()
        .append("g")
        .attr("class", "legend-item")
        .attr("transform", (d, i) => `translate(0, ${i * 22})`); // Increased spacing between items
    
    legendItems.append("rect")
        .attr("width", 12)
        .attr("height", 12)
        .attr("rx", 2)
        .attr("fill", (d, i) => d.color || d3.schemeTableau10[i % 10]);
    
    legendItems.append("text")
        .attr("x", 20)
        .attr("y", 10)
        .style("font-size", "10px")
        .style("font-weight", d => d.value / totalSum > 0.2 ? "bold" : "normal") // Emphasize major items
        .text(d => {
            const label = truncateText(d.label, 15); // Increased max length
            const percent = (d.value / totalSum * 100).toFixed(0) + '%';
            return `${label} (${percent})`;
        });
}

// Draw a horizontal bar chart
function drawHorizontalBarChart(chartDivId, chartData, title, defaultColor = null, unitLabel = '', options = {}) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    // Filter and validate data
    const validData = chartData.map(d => ({
        ...d,
        value: typeof d.value === 'number' && !isNaN(d.value) ? d.value : 0
    }));
    
    if (!validData.some(d => d.value !== 0)) {
        chartDiv.classList.add('empty-chart');
        chartDiv.innerHTML = `<div class="no-data-message">No data available for ${title}</div>`;
        return;
    }
    
    const margin = {top: 50, right: 120, bottom: 50, left: 150}, // Increased left margin for labels
        containerWidth = chartDiv.clientWidth || 600,
        width = Math.max(200, containerWidth - margin.left - margin.right),
        height = 300 - margin.top - margin.bottom;
    
    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);
    
    // Add title
    svg.append("text")
        .attr("x", width / 2)
        .attr("y", -margin.top / 2)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .style("opacity", 0)
        .text(title)
        .transition()
        .duration(animations.duration)
        .style("opacity", 1);
    
    // Add subtitle if provided
    if (options.subtitle) {
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", -margin.top / 2 + 20)
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-style", "italic")
            .style("opacity", 0)
            .text(options.subtitle)
            .transition()
            .duration(animations.duration)
            .delay(200)
            .style("opacity", 0.7);
    }
    
    // Sort data in descending order for better visualization
    validData.sort((a, b) => b.value - a.value);
    
    // Define scales
    const y = d3.scaleBand()
        .range([0, height])
        .domain(validData.map(d => d.label))
        .padding(0.3);
    
    const x = d3.scaleLinear()
        .range([0, width])
        .domain([0, d3.max(validData, d => d.value) * 1.1]);
    
    // Truncate long labels
    const truncateLabel = (text, maxLength = 20) => {
        return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
    };
    
    // Add Y axis (horizontal) with truncated labels
    svg.append("g")
        .call(d3.axisLeft(y))
        .selectAll("text")
        .style("font-size", "11px")
        .style("font-weight", "bold")
        .text(d => truncateLabel(d));
    
    // Add Y axis title if provided
    if (options.yAxisTitle) {
        svg.append("text")
            .attr("transform", "rotate(-90)")
            .attr("x", -height / 2)
            .attr("y", -100) // Position far to the left of the y-axis
            .attr("text-anchor", "middle")
            .style("font-size", "11px")
            .style("font-weight", "bold")
            .style("opacity", 0)
            .text(options.yAxisTitle)
            .transition()
            .duration(animations.duration)
            .delay(300)
            .style("opacity", 0.8);
    }
    
    // Add X axis with proper formatting
    svg.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(x).ticks(5).tickFormat(d => unitLabel ? formatNumberForDisplay(d) + ' ' + unitLabel : formatNumberForDisplay(d)))
        .selectAll("text")
        .style("font-size", "11px");
    
    // Add X axis title if provided
    if (options.xAxisTitle) {
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", height + 35) // Position below the x-axis
            .attr("text-anchor", "middle")
            .style("font-size", "11px")
            .style("font-weight", "bold")
            .style("opacity", 0)
            .text(options.xAxisTitle)
            .transition()
            .duration(animations.duration)
            .delay(300)
            .style("opacity", 0.8);
    }
    
    // Add subtle grid lines
    svg.append("g")
        .attr("class", "grid")
        .call(d3.axisBottom(x).ticks(5).tickSize(height).tickFormat(""))
        .style("opacity", 0.1);
    
    // Function to get color for a data point
    const getColor = (d, i) => {
        if (d.color) return d.color;
        if (defaultColor) return defaultColor;
        return d.value >= 0 ? colors.inflow : colors.outflow;
    };
    
    // Create gradient for bars
    const defs = svg.append("defs");
    
    // Create individual gradients for each bar
    validData.forEach((d, i) => {
        // Get base color for this bar
        const baseColor = getColor(d, i);
        
        // Create gradient for this bar
        const gradientId = `hbar-gradient-${chartDivId}-${i}`;
        const gradient = defs.append("linearGradient")
            .attr("id", gradientId)
            .attr("x1", "0%")
            .attr("y1", "0%")
            .attr("x2", "100%")
            .attr("y2", "0%");
        
        gradient.append("stop")
            .attr("offset", "0%")
            .attr("stop-color", d3.rgb(baseColor).darker(0.3));
        
        gradient.append("stop")
            .attr("offset", "100%")
            .attr("stop-color", d3.rgb(baseColor).brighter(0.5));
        
        // Assign gradient ID to the data point
        d.gradientId = gradientId;
    });
    
    // Create shadow filter
    const filter = defs.append("filter")
        .attr("id", `hbar-shadow-${chartDivId}`)
        .attr("height", "130%");
    
    filter.append("feGaussianBlur")
        .attr("in", "SourceAlpha")
        .attr("stdDeviation", 2)
        .attr("result", "blur");
    
    filter.append("feOffset")
        .attr("in", "blur")
        .attr("dx", 2)
        .attr("dy", 2)
        .attr("result", "offsetBlur");
    
    const feMerge = filter.append("feMerge");
    feMerge.append("feMergeNode").attr("in", "offsetBlur");
    feMerge.append("feMergeNode").attr("in", "SourceGraphic");
    
    // Add bars with animations
    svg.selectAll(".bar")
        .data(validData)
        .enter().append("rect")
        .attr("class", "bar")
        .attr("y", d => y(d.label))
        .attr("height", y.bandwidth())
        .attr("x", 0)
        .attr("width", 0) // Start with width 0 for animation
        .attr("fill", d => `url(#${d.gradientId})`)
        .attr("rx", 4) // Rounded corners
        .attr("ry", 4)
        .style("filter", `url(#hbar-shadow-${chartDivId})`)
        .transition()
        .duration(animations.duration)
        .delay((d, i) => i * 100)
        .ease(d3.easeElastic)
        .attr("width", d => x(d.value));
    
    // Add hover effects
    svg.selectAll(".bar")
        .on("mouseover", function(event, d) {
            d3.select(this).transition().duration(200)
                .attr("opacity", 0.8)
                .attr("y", d => y(d.label) - 2)
                .attr("height", y.bandwidth() + 4);
            
            getD3Tooltip().style("visibility", "visible")
                .html(`
                    <b>${truncateLabel(d.label, 30)}</b><br>
                    Value: <b>${formatNumberForDisplay(d.value)}${unitLabel ? ' ' + unitLabel : ''}</b>
                    ${d.description ? `<br>${d.description}` : ''}
                `)
                .style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            getD3Tooltip().style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function(event, d) {
            d3.select(this).transition().duration(200)
                .attr("opacity", 1)
                .attr("y", d => y(d.label))
                .attr("height", y.bandwidth());
            
            getD3Tooltip().style("visibility", "hidden");
        });
    
    // Add value labels with animation
    svg.selectAll(".value-label")
        .data(validData)
        .enter().append("text")
        .attr("class", "value-label")
        .attr("x", d => x(d.value) + 5)
        .attr("y", d => y(d.label) + y.bandwidth() / 2)
        .attr("dy", "0.35em")
        .attr("text-anchor", "start")
        .style("font-size", "10px") // Reduced from 11px for consistency
        .style("font-weight", "bold")
        .style("fill", d => getColor(d, 0)) // Use bar color for consistency
        .style("opacity", 0)
        .text(d => formatNumberForDisplay(d.value) + (unitLabel ? ' ' + unitLabel : ''))
        .transition()
        .duration(animations.duration)
        .delay((d, i) => animations.duration + i * 100)
        .style("opacity", 1);
}

// New Bullet Chart for financial metrics with targets and thresholds
function drawBulletChart(chartDivId, chartData, title, unitLabel = '', options = {}) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    // Validate data
    if (!chartData || chartData.length === 0) {
        chartDiv.innerHTML = `<div class="no-data-message">No data available for ${title}.</div>`;
        return;
    }
    
    // Define dimensions
    const margin = {top: 50, right: 50, bottom: 50, left: 100},
        containerWidth = chartDiv.clientWidth || 380,
        width = Math.max(200, containerWidth - margin.left - margin.right),
        height = 250 - margin.top - margin.bottom;
    
    // Create SVG
    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left}, ${margin.top})`);
    
    // Add title
    svg.append("text")
        .attr("x", width / 2)
        .attr("y", -margin.top / 2)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .style("opacity", 0)
        .text(title)
        .transition()
        .duration(800)
        .style("opacity", 1);
    
    // Add subtitle if provided
    if (options.subtitle) {
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", -margin.top / 2 + 20)
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-style", "italic")
            .style("opacity", 0)
            .text(options.subtitle)
            .transition()
            .duration(800)
            .delay(200)
            .style("opacity", 0.7);
    }
    
    // Find the target/metric value
    const targetItem = chartData.find(d => d.isTarget);
    const targetValue = targetItem ? targetItem.value : 0;
    
    // Find thresholds
    const thresholds = options.thresholds || [6, 12, 24]; // Default thresholds
    
    // Calculate maximum scale value
    const maxValue = Math.max(
        d3.max(chartData, d => d.value) * 1.2,
        d3.max(thresholds) * 1.1
    );
    
    // Create scales
    const xScale = d3.scaleLinear()
        .domain([0, maxValue])
        .range([0, width]);
    
    const yScale = d3.scaleBand()
        .domain(chartData.map(d => d.label))
        .range([0, height])
        .padding(0.3);
    
    // Draw horizontal axis
    svg.append("g")
        .attr("transform", `translate(0, ${height})`)
        .call(d3.axisBottom(xScale).ticks(5).tickFormat(d => unitLabel ? `${formatNumberForDisplay(d)} ${unitLabel}` : formatNumberForDisplay(d)))
        .style("opacity", 0)
        .transition()
        .duration(800)
        .style("opacity", 1);
    
    // Draw vertical axis
    svg.append("g")
        .call(d3.axisLeft(yScale))
        .style("opacity", 0)
        .transition()
        .duration(800)
        .style("opacity", 1);
    
    // Draw threshold background ranges
    const ranges = [
        { start: 0, end: thresholds[0], color: "#ff9999" }, // Danger
        { start: thresholds[0], end: thresholds[1], color: "#ffcc99" }, // Warning
        { start: thresholds[1], end: thresholds[2] || maxValue, color: "#ccffcc" } // Good
    ];
    
    // Only draw for the target metric
    if (targetItem) {
        const targetY = yScale(targetItem.label);
        const targetHeight = yScale.bandwidth();
        
        ranges.forEach((range, i) => {
            svg.append("rect")
                .attr("x", xScale(range.start))
                .attr("y", targetY)
                .attr("width", xScale(range.end) - xScale(range.start))
                .attr("height", targetHeight)
                .attr("fill", range.color)
                .attr("opacity", 0)
                .transition()
                .duration(800)
                .delay(200 * i)
                .attr("opacity", 0.3);
        });
    }
    
    // Draw bars
    // Draw bars with data attributes for interactivity
    chartData.forEach((d, i) => {
        // Choose color based on value and whether it's the target
        let barColor;
        if (d.color) {
            barColor = d.color;
        } else if (d.isTarget) {
            // Choose color based on thresholds
            if (d.value < thresholds[0]) barColor = "#dc3545"; // Danger
            else if (d.value < thresholds[1]) barColor = "#ffc107"; // Warning
            else barColor = "#28a745"; // Good
        } else {
            barColor = colors.neutral;
        }
        
        // Create bar with hover effects - store data attribute
        svg.append("rect")
            .attr("x", 0)
            .attr("y", yScale(d.label))
            .attr("width", 0)
            .attr("height", yScale.bandwidth())
            .attr("fill", barColor)
            .attr("rx", 4) // Rounded corners
            .attr("ry", 4)
            .attr("class", "bullet-bar")
            .attr("data-label", d.label) // Store label for tooltip lookup
            .transition()
            .duration(1000)
            .delay(300 * i)
            .attr("width", xScale(d.value));
    });
    
    // Add value labels with embedded display units
    chartData.forEach((d, i) => {
        const displayUnit = d.displayUnit || unitLabel;
        
        svg.append("text")
            .attr("x", xScale(d.value) + 5)
            .attr("y", yScale(d.label) + yScale.bandwidth() / 2)
            .attr("dominant-baseline", "middle")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("opacity", 0)
            .text(formatNumberForDisplay(d.value) + ` ${displayUnit}`)
            .transition()
            .duration(800)
            .delay(1000 + 100 * i)
            .style("opacity", 1);
    });
    
    // Add event listeners after all bars are created
    svg.selectAll(".bullet-bar")
        .on("mouseover", function() {
            // Get data for this bar
            const bar = d3.select(this);
            const label = bar.attr("data-label");
            const item = chartData.find(d => d.label === label);
            
            if (!item) return;
            
            // Highlight the bar
            bar.transition()
                .duration(200)
                .attr("opacity", 0.8)
                .attr("stroke", "#333")
                .attr("stroke-width", 1);
            
            // Use embedded displayUnit and description
            const displayUnit = item.displayUnit || unitLabel;
            const description = item.description || '';
            
            // Show detailed tooltip
            getD3Tooltip().style("visibility", "visible")
                .html(`
                    <b>${item.label}</b><br>
                    Value: <b>${formatNumberForDisplay(item.value)} ${displayUnit}</b>
                    ${description ? `<br><small>${description}</small>` : ''}
                `)
                .style("top", (event ? event.pageY : window.event.pageY - 10) + "px")
                .style("left", (event ? event.pageX : window.event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            // Update tooltip position during mouse movement
            getD3Tooltip()
                .style("top", (event ? event.pageY : window.event.pageY - 10) + "px")
                .style("left", (event ? event.pageX : window.event.pageX + 10) + "px");
        })
        .on("mouseout", function() {
            // Reset styling
            d3.select(this)
                .transition()
                .duration(200)
                .attr("opacity", 1)
                .attr("stroke", "none");
            
            // Hide tooltip
            getD3Tooltip().style("visibility", "hidden");
        });
    
    // If this is a runway chart, add vertical markers for thresholds
    if (targetItem && targetItem.label.includes("Runway")) {
        thresholds.forEach((threshold, i) => {
            svg.append("line")
                .attr("x1", xScale(threshold))
                .attr("y1", 0)
                .attr("x2", xScale(threshold))
                .attr("y2", height)
                .attr("stroke", "#666")
                .attr("stroke-width", 1)
                .attr("stroke-dasharray", "4,4")
                .style("opacity", 0)
                .transition()
                .duration(800)
                .delay(1200 + 100 * i)
                .style("opacity", 0.7);
            
            svg.append("text")
                .attr("x", xScale(threshold))
                .attr("y", height + 20)
                .attr("text-anchor", "middle")
                .style("font-size", "10px")
                .style("opacity", 0)
                .text(threshold + " mo")
                .transition()
                .duration(800)
                .delay(1400 + 100 * i)
                .style("opacity", 0.7);
        });
    }
}

// ... existing code ...
function drawGaugeChart(chartDivId, chartData, title, options = {}) {
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    // For the specific Cash Flow to Net Income Ratio chart, use our D3 implementation
    if (chartDivId === 'cf-to-income-ratio-chart') {
        // Call the new D3 implementation from script.js
        if (typeof createCFtoNetIncomeGauge === 'function') {
            // Use the first value in chartData
            const value = chartData[0].value;
            createCFtoNetIncomeGauge(chartDivId, value, title);
            return;
        }
    }
    
    // For any other gauge charts, use the existing implementation
    const validData = chartData.filter(d => d && typeof d.value === 'number' && !isNaN(d.value));
    if (validData.length === 0) {
        chartDiv.classList.add('empty-chart');
        chartDiv.innerHTML = `<div class="no-data-message">No data available for ${title}.</div>`;
        return;
    }

    // Original gauge code continues here for other gauge charts...
    const thresholds = options.thresholds || [0.5, 0.8, 1.2];
    const gaugeColors = options.colors || [colors.outflow, colors.neutral, colors.inflow];
    
    const margin = {top: 50, right: 20, bottom: 50, left: 20},
        containerWidth = chartDiv.clientWidth || 380,
        width = Math.max(200, containerWidth - margin.left - margin.right),
        height = 300 - margin.top - margin.bottom;
    
    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${width/2 + margin.left}, ${height/2 + margin.top})`);
    
    // Add title
    svg.append("text")
        .attr("x", 0)
        .attr("y", -height/2 + 10)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .text(title);
    
    // If specified, add subtitle
    if (options.subtitle) {
        svg.append("text")
            .attr("x", 0)
            .attr("y", -height/2 + 30)
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-style", "italic")
            .text(options.subtitle);
    }
    
    const radius = Math.min(width, height) / 2;
    const innerRadius = radius * 0.7;
    const outerRadius = radius * 0.9;
    
    // Create arc generator for gauge
    const arc = d3.arc()
        .innerRadius(innerRadius)
        .outerRadius(outerRadius)
        .startAngle(-Math.PI / 2);
    
    // Gauge background
    svg.append("path")
        .datum({endAngle: Math.PI / 2})
        .style("fill", "#ddd")
        .attr("d", arc);
    
    // Calculate percentage for display
    const value = validData[0].value;
    const percentage = validData[0].percentage !== undefined ? 
        validData[0].percentage : Math.round(value * 100);
    
    // Color mapping based on thresholds
    let gaugeColor = gaugeColors[0]; // Default to first color
    for (let i = 0; i < thresholds.length; i++) {
        if (value >= thresholds[i]) {
            gaugeColor = gaugeColors[Math.min(i+1, gaugeColors.length - 1)];
        }
    }
    
    // Calculate end angle for gauge fill
    const scale = d3.scaleLinear()
        .domain([0, 100])
        .range([-Math.PI / 2, Math.PI / 2]);
    
    const gaugeValue = Math.max(0, Math.min(100, percentage)); // Ensure between 0-100
    const endAngle = scale(gaugeValue);
    
    // Gauge fill
    svg.append("path")
        .datum({endAngle: endAngle})
        .style("fill", gaugeColor)
        .attr("d", arc);
    
    // Needle
    const needleLength = radius * 0.8;
    const needleRadius = radius * 0.03;
    const needleAngle = scale(gaugeValue);
    
    // Needle container
    const needle = svg.append("g")
        .attr("transform", `rotate(${needleAngle * 180 / Math.PI})`);
    
    // Needle shape
    needle.append("path")
        .attr("d", `M ${-needleRadius} 0 L ${needleRadius} 0 L 0 ${-needleLength} Z`)
        .style("fill", "#444");
    
    // Center dot
    svg.append("circle")
        .attr("cx", 0)
        .attr("cy", 0)
        .attr("r", needleRadius * 1.5)
        .style("fill", "#444")
        .style("stroke", "#ddd")
        .style("stroke-width", "1px");
    
    // Value text
    svg.append("text")
        .attr("x", 0)
        .attr("y", 40)
        .attr("text-anchor", "middle")
        .style("font-size", "24px")
        .style("font-weight", "bold")
        .style("fill", gaugeColor)
        .text(`${gaugeValue}%`);
}

// ... existing code ...

// Horizontal Waterfall Chart for Free Cash Flow Bridge
function drawHorizontalWaterfallChart(chartDivId, chartData, title, unitLabel, options = {}) {
    // Special case for FCF Bridge - force correct display of values
    if (chartDivId === 'fcf-bridge-chart') {
        // Make a deep copy to avoid modifying the original data
        chartData = JSON.parse(JSON.stringify(chartData));
        
        // Fix colors and ensure correct positive/negative directions
        chartData.forEach(item => {
            if (item.value > 0) {
                item.color = colors.inflow; // Green for positive
            } else if (item.value < 0) {
                item.color = colors.outflow; // Red for negative
            }
            // Summary item should always be blue
            if (item.isSummary) {
                item.color = colors.summary;
            }
        });
    }
    const chartDiv = document.getElementById(chartDivId);
    if (!chartDiv) return;
    chartDiv.innerHTML = '';
    
    // Filter out zero/invalid values for display, but keep them for correct waterfall logic
    const validData = chartData.map(d => ({
        ...d,
        value: typeof d.value === 'number' && !isNaN(d.value) ? d.value : 0
    }));
    
    // Check if chart has meaningful data
    const hasMeaningfulData = validData.some(d => d.value !== 0);
    if (!hasMeaningfulData) {
        chartDiv.classList.add('empty-chart');
        chartDiv.innerHTML = `<div class="no-data-message">No data available for ${title}</div>`;
        return;
    }
    
    // Special handling for FCF Bridge chart
    if (chartDivId === 'fcf-bridge-chart') {
        // Create consistent, shortened abbreviations with full labels for tooltips
        validData.forEach(d => {
            // Store original label for tooltip
            d.longLabel = d.label;
            
            // Create consistent abbreviations
            if (d.label === "Working Capital") d.label = "W.C.";
            if (d.label === "Non-Cash Adj.") d.label = "NC Adj.";
            if (d.label === "Operating CF") d.label = "Op. CF";
            if (d.label === "Capital Expenditures") d.label = "CapEx";
            if (d.label === "Free Cash Flow") d.label = "FCF";
            
            // Ensure consistent bar heights
            if (d.value > 0) {
                d.color = "#2171b5"; // Blue for positive (colorblind-friendly)
            } else if (d.value < 0) {
                d.color = "#cb181d"; // Red for negative (colorblind-friendly)
            }
            
            // Summary item should always use distinctive color
            if (d.isSummary) {
                d.color = "#6a51a3"; // Purple for summary (colorblind-friendly)
            }
        });
    }

    // Calculate cumulative values for the waterfall
    let cumulative = 0;
    const waterfallData = validData.map((d, i) => {
        let y0, y1;
        
        if (i === 0) {
            // First bar always starts at 0
            y0 = 0;
            y1 = d.value;
            cumulative = d.value;
        } else if (i === validData.length - 1 && d.isSummary) {
            // Final summary bar - shows the total
            y0 = 0;
            y1 = cumulative;  // Final value
        } else {
            // Other bars - start at current cumulative
            y0 = cumulative;
            // And add their value
            y1 = cumulative + d.value;
            cumulative = y1;
        }
        
        return { ...d, y0, y1, isSummary: d.isSummary || false };
    });

    // Special case for FCF Bridge chart - needs more margin all around
    const margin = chartDivId === 'fcf-bridge-chart' 
        ? {top: 60, right: 120, bottom: 100, left: 200} // Much more margin for FCF Bridge
        : {top: 60, right: 70, bottom: 100, left: 160}, // Increased margins all around
        containerWidth = chartDiv.clientWidth || 600,
        width = Math.max(250, containerWidth - margin.left - margin.right),
        height = 400 - margin.top - margin.bottom; // Taller chart for more space

    // Find min/max for x-axis - ensure we include 0
    const xValues = waterfallData.flatMap(d => [d.y0, d.y1]);
    const xMin = Math.min(0, ...xValues);
    const xMax = Math.max(0, ...xValues);
    
    // Add some padding to the domain to ensure everything is visible
    const padding = 0.2; // 20% padding for more room
    
    // Define xDomain outside the conditional blocks to make it accessible throughout the function
    let xDomain;
    
    // For FCF Bridge, ensure x-axis extends to positive values
    if (chartDivId === 'fcf-bridge-chart') {
        // Find the maximum positive value
        const maxPositive = Math.max(
            ...validData.filter(d => d.value > 0).map(d => d.value),
            20 // Minimum positive extent (20M)
        );
        
        xDomain = [
            xMin < 0 ? xMin * (1 + padding) : -5, // Add extra padding below zero
            Math.max(maxPositive * (1 + padding), 20) // Ensure positive range extends to at least 20
        ];
    } else {
        xDomain = [
            xMin < 0 ? xMin * (1 + padding) : 0, // Add padding below zero if we have negative values
            xMax > 0 ? xMax * (1 + padding) : Math.max(0.1, xMax) // Ensure positive range even if all values are 0
        ];
    }

    const svg = d3.select(chartDiv).append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

    // Add title with animation
    svg.append("text")
        .attr("x", width / 2)
        .attr("y", -margin.top / 2)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("font-weight", "600")
        .style("opacity", 0)
        .text(title)
        .transition()
        .duration(animations.duration)
        .style("opacity", 1);
    
    // Add subtitle if provided
    if (options.subtitle) {
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", -margin.top / 2 + 20)
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-style", "italic")
            .style("opacity", 0)
            .text(options.subtitle)
            .transition()
            .duration(animations.duration)
            .delay(200)
            .style("opacity", 0.7);
    }

    // Create scales
    const x = d3.scaleLinear()
        .range([0, width])
        .domain(xDomain);

    const y = d3.scaleBand()
        .domain(waterfallData.map(d => d.label))
        .range([0, height])
        .padding(0.3);

    // Y axis - labels on the left
    svg.append("g")
        .call(d3.axisLeft(y))
        .selectAll("text")
        .style("font-size", "14px") // Larger font
        .style("font-weight", "500") // Slightly bolder
        .style("letter-spacing", "0.02em") // Improved letter spacing
        .attr("x", -10) // Shift slightly to the left for more space
        .each(function(d) {
            // Ensure FCF and important labels are properly visible
            const text = d3.select(this);
            if (d === "FCF" || d === "Free Cash Flow") {
                text.style("font-weight", "700"); // Extra bold
                text.style("fill", colors.summary); // Use summary color
            }
        })
        .call(wrap, margin.left - 30); // More space for text wrapping
    
    // Add Y axis title if provided
    if (options.yAxisTitle) {
        svg.append("text")
            .attr("transform", "rotate(-90)")
            .attr("x", -height / 2)
            .attr("y", -margin.left + 20) // Position far to the left of the y-axis
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("opacity", 0)
            .text(options.yAxisTitle)
            .transition()
            .duration(animations.duration)
            .delay(300)
            .style("opacity", 0.8);
    }

    // X axis with proper units
    const xAxisTickFormat = unitLabel 
        ? d => `${formatNumberForDisplay(d)} ${unitLabel}` 
        : formatNumberForDisplay;
        
    svg.append("g")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(x).ticks(5).tickFormat(xAxisTickFormat))
        .selectAll("text")
        .style("font-size", "11px");
    
    // Add X axis title if provided
    if (options.xAxisTitle) {
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", height + 40) // Position below the x-axis
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("opacity", 0)
            .text(options.xAxisTitle)
            .transition()
            .duration(animations.duration)
            .delay(300)
            .style("opacity", 0.8);
    }

    // Add grid lines
    svg.append("g")
        .attr("class", "grid")
        .call(d3.axisBottom(x).ticks(5).tickSize(height).tickFormat(""))
        .style("opacity", 0.1);

    // Add zero line
    svg.append("line")
        .attr("x1", x(0))
        .attr("x2", x(0))
        .attr("y1", 0)
        .attr("y2", height)
        .attr("stroke", "#666")
        .attr("stroke-dasharray", "4,4")
        .style("opacity", 0.5);

    // Create gradients for the bars
    const defs = svg.append("defs");
    
    // Function to get color for a data point
    const getColor = (d) => {
        if (d.color) return d.color;
        if (d.isSummary) return colors.summary;
        return d.value >= 0 ? colors.inflow : colors.outflow;
    };
    
    // Create individual gradients for each bar
    waterfallData.forEach((d, i) => {
        // Get base color for this bar
        const baseColor = getColor(d);
        
        // Create gradient for this bar
        const gradientId = `hwaterfall-gradient-${chartDivId}-${i}`;
        const gradient = defs.append("linearGradient")
            .attr("id", gradientId)
            .attr("x1", "0%")
            .attr("y1", "0%")
            .attr("x2", "100%")
            .attr("y2", "0%");
        
        gradient.append("stop")
            .attr("offset", "0%")
            .attr("stop-color", d3.rgb(baseColor).darker(0.3));
        
        gradient.append("stop")
            .attr("offset", "100%")
            .attr("stop-color", d3.rgb(baseColor).brighter(0.5));
        
        // Assign gradient ID to the data point
        d.gradientId = gradientId;
    });
    
    // Create shadow filter
    const filter = defs.append("filter")
        .attr("id", `hwaterfall-shadow-${chartDivId}`)
        .attr("height", "130%");
    
    filter.append("feGaussianBlur")
        .attr("in", "SourceAlpha")
        .attr("stdDeviation", 2)
        .attr("result", "blur");
    
    filter.append("feOffset")
        .attr("in", "blur")
        .attr("dx", 2)
        .attr("dy", 2)
        .attr("result", "offsetBlur");
    
    const feMerge = filter.append("feMerge");
    feMerge.append("feMergeNode").attr("in", "offsetBlur");
    feMerge.append("feMergeNode").attr("in", "SourceGraphic");

    // Add connector lines for the waterfall effect
    waterfallData.forEach((d, i) => {
        if (i > 0 && i < waterfallData.length - 1 && !d.isSummary) {
            svg.append("line")
                .attr("x1", x(d.y0))
                .attr("x2", x(d.y0))
                .attr("y1", y(waterfallData[i-1].label) + y.bandwidth())
                .attr("y2", y(d.label))
                .attr("stroke", "#666")
                .attr("stroke-dasharray", "3,3")
                .style("opacity", 0)
                .transition()
                .duration(animations.duration)
                .delay(animations.duration + i * 100)
                .style("opacity", 0.5);
        }
    });

    // Add bars with animations
    svg.selectAll(".waterfall-bar")
        .data(waterfallData)
        .enter().append("rect")
        .attr("class", "waterfall-bar")
        .attr("x", d => {
            // For Free Cash Flow Bridge specifically
            if (chartDivId === 'fcf-bridge-chart') {
                if (d.value < 0) {
                    return x(d.value); // Negative bars start at their value and go to 0
                } else {
                    return x(0); // Positive bars start at 0 and go to their value
                }
            }
            return d.isSummary ? x(0) : x(Math.min(d.y0, d.y1));
        })
        .attr("y", d => y(d.label))
        .attr("width", 0) // Start with width 0 for animation
        .attr("height", y.bandwidth() * 0.8) // Slightly thinner bars with 80% of bandwidth
        .attr("rx", 4) // Rounded corners
        .attr("ry", 4)
        .attr("fill", d => `url(#${d.gradientId})`)
        .attr("stroke", d => getColor(d))
        .attr("stroke-width", d => d.isSummary ? 2 : 1)
        .style("filter", `url(#hwaterfall-shadow-${chartDivId})`)
        .transition()
        .duration(animations.duration)
        .delay((d, i) => i * 150)
        .ease(d3.easeElastic)
        .attr("width", d => {
            if (chartDivId === 'fcf-bridge-chart') {
                // Always use absolute values for width to ensure consistent rendering
                return Math.abs(x(d.value) - x(0)); 
            }
            return Math.abs(x(d.y1) - x(d.y0));
        });

    // Enhanced hover effects
    svg.selectAll(".waterfall-bar")
        .on("mouseover", function(event, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("opacity", 0.8)
                .attr("y", d => y(d.label) - 2)
                .attr("height", y.bandwidth() + 4);
            
            getD3Tooltip().style("visibility", "visible")
                .html(`
                    <b>${d.longLabel || d.label}</b><br>
                    Value: <b>${formatNumberForDisplay(d.value)}${unitLabel ? ' ' + unitLabel : ''}</b>
                    ${d.description ? `<br>${d.description}` : ''}
                    ${d.isSummary ? `<br>Final value: <b>${formatNumberForDisplay(d.y1)}${unitLabel ? ' ' + unitLabel : ''}</b>` : ''}
                    ${chartDivId === 'fcf-bridge-chart' ? 
                       `<br><br><small>Contributes ${d.value > 0 ? 'positively' : 'negatively'} to Free Cash Flow</small>` : ''}
                `)
                .style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mousemove", function(event) {
            getD3Tooltip().style("top", (event.pageY - 10) + "px")
                .style("left", (event.pageX + 10) + "px");
        })
        .on("mouseout", function(event, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr("opacity", 1)
                .attr("y", d => y(d.label))
                .attr("height", y.bandwidth());
            
            getD3Tooltip().style("visibility", "hidden");
        });

    // Value labels with adaptive positioning and animation
    svg.selectAll(".waterfall-label")
        .data(waterfallData)
        .enter().append("text")
        .attr("class", "waterfall-label")
        .attr("x", d => {
            // Enhanced positioning for FCF Bridge chart
            if (chartDivId === 'fcf-bridge-chart') {
                // For positive values, position inside near the start
                if (d.value > 0) {
                    return x(0) + 10; // Just inside the bar
                }
                // For negative values, position inside near the end
                else if (d.value < 0) {
                    return x(d.value) + 10; // Just inside the bar from the left
                }
                // For summary value, position inside but more centered
                else if (d.isSummary) {
                    return x(d.y1) / 2; // Center of the bar
                }
            }
            
            // For other charts or fallback
            if (d.isSummary) {
                return x(d.y1) + 5; // For summary, position outside
            }
            // For regular bars, position inside if enough space, otherwise outside
            const barWidth = Math.abs(x(d.y1) - x(d.y0));
            if (barWidth > 40) {
                return x(Math.min(d.y0, d.y1)) + (d.value >= 0 ? 10 : barWidth - 10);
            } else {
                return d.value >= 0 ? x(d.y1) + 5 : x(d.y0) - 5;
            }
        })
        .attr("y", d => y(d.label) + y.bandwidth() / 2)
        .attr("text-anchor", d => {
            // Special handling for FCF Bridge chart
            if (chartDivId === 'fcf-bridge-chart') {
                return "start"; // Always start from left for consistency
            }
            
            if (d.isSummary) return "start";
            const barWidth = Math.abs(x(d.y1) - x(d.y0));
            if (barWidth > 40) return d.value >= 0 ? "start" : "end";
            return d.value >= 0 ? "start" : "end";
        })
        .attr("dominant-baseline", "middle")
        .style("font-size", "12px") // Increased font size from 10px to 12px
        .style("font-weight", "bold")
        .style("fill", d => {
            // For FCF Bridge chart, always ensure high contrast
            if (chartDivId === 'fcf-bridge-chart') {
                return d.value < 0 ? "white" : "white"; // White text for better contrast
            }
            
            // For labels inside bars, use white or a contrasting color
            const barWidth = Math.abs(x(d.y1) - x(d.y0));
            if (barWidth > 40) return "#fff";
            return getColor(d);
        })
        .style("opacity", 0) // Start invisible for animation
        .style("text-shadow", d => {
            // Add text shadow for better visibility, especially for FCF Bridge chart
            if (chartDivId === 'fcf-bridge-chart') {
                return "0px 0px 3px rgba(0,0,0,0.7)"; // Dark shadow for better contrast
            }
            return "none";
        })
        .text(d => {
            // Always show a value for every bar
            const formattedValue = formatNumberForDisplay(d.value) + (unitLabel ? ' ' + unitLabel : '');
            // For FCF Bridge chart, include the M for millions or appropriate unit
            if (chartDivId === 'fcf-bridge-chart') {
                return d.value < 0 ? formattedValue : '+' + formattedValue; // Show + sign for positive values
            }
            return formattedValue;
        })
        .transition()
        .duration(animations.duration)
        .delay((d, i) => animations.duration + i * 100) // Stagger the animations
        .style("opacity", 1); // Fade in
}

// ... existing code ...
</script>
{% endblock %}
