{% extends "base.html" %} {# Ensure this line is at the very top #}

{% block title %}Home{% endblock %}

{% block header_title %}H Trader Pro{% endblock %}

{% block head_extra %}
    {# Revolutionary Trading Platform Styles #}
    <style>
        /* === MARKET PULSE HERO === */
        .market-pulse-hero {
            position: relative;
            min-height: 85vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            background: #ffffff !important; /* White background for text visibility */
            margin-bottom: 0;
        }

        /* Dark mode background */
        [data-theme="dark"] .market-pulse-hero {
            background: linear-gradient(135deg,
                rgba(0, 0, 0, 0.95) 0%,
                rgba(20, 20, 40, 0.9) 50%,
                rgba(0, 0, 0, 0.95) 100%) !important;
        }

        .market-pulse-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
            animation: pulseGlow 8s ease-in-out infinite;
        }

        @keyframes pulseGlow {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 0.6; transform: scale(1.05); }
        }

        .trading-terminal {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            width: 100%;
            padding: 0 40px;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 60px;
            align-items: center;
        }

        .terminal-left {
            color: var(--text-color);
        }

        /* Light mode text colors for white background */
        :root .terminal-left {
            color: #333333 !important;
        }

        :root .hero-headline {
            color: #333333 !important;
        }

        :root .hero-subline {
            color: #666666 !important;
        }

        :root .metric-label {
            color: #666666 !important;
        }

        .market-status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid rgba(0, 255, 136, 0.4);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 30px;
            animation: statusPulse 2s ease-in-out infinite;
            color: #ffffff !important; /* Force white text for market status */
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        @keyframes statusPulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.4); }
            50% { box-shadow: 0 0 0 8px rgba(0, 255, 136, 0); }
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: blink 1.5s ease-in-out infinite;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .hero-headline {
            font-size: clamp(3rem, 8vw, 4.5rem);
            font-weight: 800;
            line-height: 1.5; /* Increased to prevent text clipping */
            margin-bottom: 25px;
            padding: 20px 0; /* Add more padding to ensure text doesn't get cut off */
            overflow: visible; /* Ensure text is fully visible */
            word-wrap: break-word; /* Handle long words properly */
            /* Additional fixes for text clipping */
            display: block;
            width: 100%;
            max-width: 100%;
            white-space: normal;
            text-overflow: visible;
            background: linear-gradient(135deg, var(--text-color) 0%, var(--highlight-color) 50%, var(--highlight-secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textShimmer 3s ease-in-out infinite;
        }

        @keyframes textShimmer {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.2); }
        }

        .hero-subline {
            font-size: 1.3rem;
            color: var(--text-muted-color);
            margin-bottom: 35px;
            line-height: 1.6;
            font-weight: 400;
        }

        .trading-metrics {
            display: flex;
            gap: 30px;
            margin-bottom: 40px;
        }

        .metric {
            text-align: left;
        }

        .metric-value {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--positive-color);
            display: block;
            font-family: 'Courier New', monospace;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-muted-color);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* === DARK MODE TEXT VISIBILITY FIXES === */
        /* White text on dark background in dark mode */
        [data-theme="dark"] .market-status {
            color: #ffffff !important;
        }

        [data-theme="dark"] .hero-headline {
            color: #ffffff !important;
        }

        [data-theme="dark"] .hero-subline {
            color: #e0e0e0 !important;
        }

        [data-theme="dark"] .metric-value {
            color: var(--positive-color) !important;
        }

        [data-theme="dark"] .metric-label {
            color: #b0b0b0 !important;
        }

        [data-theme="dark"] .terminal-left {
            color: #ffffff !important;
        }

        [data-theme="dark"] .terminal-left * {
            color: inherit !important;
        }

        /* Button styling fixes for dark mode */
        [data-theme="dark"] .button {
            color: #ffffff !important;
        }

        [data-theme="dark"] a.button {
            color: #ffffff !important;
        }

        /* Light mode button styling */
        :root .button {
            color: #000000 !important; /* Black text for See Tools button in light mode */
        }

        :root a.button {
            color: #ffffff !important; /* Keep white text for Launch Terminal button */
        }

        .terminal-right {
            position: relative;
        }

        .live-chart-container {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 16px;
            padding: 25px;
            backdrop-filter: blur(20px);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            color: var(--text-color);
        }

        .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ffffff !important; /* Force white text for S&P 500 Live */
        }

        .chart-change {
            font-size: 1rem;
            font-weight: 700;
            color: #ffffff !important; /* Force white text for +2.34% */
        }

        .mini-chart {
            height: 200px;
            background: linear-gradient(180deg, rgba(0, 255, 136, 0.1) 0%, transparent 100%);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .chart-line {
            position: absolute;
            bottom: 20px;
            left: 10px;
            right: 10px;
            height: 2px;
            background: linear-gradient(90deg, #00ff88 0%, #00d4ff 100%);
            animation: chartGrow 2s ease-out;
        }

        @keyframes chartGrow {
            0% { width: 0; }
            100% { width: calc(100% - 20px); }
        }

        /* === PROFESSIONAL TOOLS SHOWCASE === */
        .tools-showcase {
            padding: 100px 0;
            background: var(--bg-color);
            position: relative;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 40px;
        }

        .tool-card {
            background: var(--card-bg-color);
            border-radius: 20px;
            padding: 0;
            overflow: hidden;
            border: 1px solid var(--border-color);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            cursor: pointer;
        }

        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #00d4ff, #ff6b35, #00ff88);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .tool-card:hover::before {
            transform: scaleX(1);
        }

        .tool-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(0, 212, 255, 0.2);
        }

        .tool-header {
            padding: 30px 30px 20px;
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.05) 0%,
                rgba(255, 107, 53, 0.05) 100%);
        }

        .tool-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--highlight-color), var(--highlight-secondary));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: var(--text-color);
            margin-bottom: 20px;
            box-shadow: 0 8px 20px rgba(var(--highlight-color-rgb), 0.3);
        }

        .tool-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 10px;
        }

        .tool-subtitle {
            font-size: 0.95rem;
            color: var(--text-muted-color);
            line-height: 1.5;
        }

        .tool-body {
            padding: 20px 30px 30px;
        }

        .tool-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tool-features li {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            font-size: 0.9rem;
            color: var(--text-color);
        }

        .tool-features li::before {
            content: '✓';
            color: var(--positive-color);
            font-weight: bold;
            font-size: 1rem;
        }

        .tool-cta {
            margin-top: 25px;
            padding: 12px 24px;
            background: transparent;
            border: 2px solid var(--highlight-color);
            color: var(--highlight-color);
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            cursor: pointer;
        }

        .tool-cta:hover {
            background: var(--highlight-color);
            color: #ffffff;
            transform: translateY(-2px);
        }

        /* === MARKET INTELLIGENCE SECTION === */
        .market-intelligence {
            padding: 100px 0;
            background: linear-gradient(135deg,
                rgba(0, 0, 0, 0.02) 0%,
                rgba(0, 212, 255, 0.02) 50%,
                rgba(255, 107, 53, 0.02) 100%);
            position: relative;
            overflow: hidden;
        }

        .intelligence-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }

        .intelligence-content h2 {
            font-size: clamp(2.5rem, 5vw, 3.5rem);
            font-weight: 800;
            color: var(--text-color);
            margin-bottom: 30px;
            line-height: 1.2;
        }

        .intelligence-content p {
            font-size: 1.2rem;
            color: var(--text-muted-color);
            line-height: 1.7;
            margin-bottom: 40px;
        }

        .intelligence-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
        }

        .intel-stat {
            text-align: left;
        }

        .intel-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--highlight-color);
            display: block;
            margin-bottom: 8px;
        }

        .intel-label {
            font-size: 0.95rem;
            color: var(--text-muted-color);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .intelligence-visual {
            position: relative;
        }

        .data-visualization {
            background: var(--card-bg-color);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid var(--border-color);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .viz-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .viz-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .viz-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: var(--positive-color);
            font-weight: 600;
        }

        .viz-body {
            height: 200px;
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.1) 0%,
                rgba(255, 107, 53, 0.1) 100%);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
        }

        .viz-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.1) 50%, transparent 60%);
            animation: patternMove 3s linear infinite;
        }

        @keyframes patternMove {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 1024px) {
            .trading-terminal {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }

            .intelligence-container {
                grid-template-columns: 1fr;
                gap: 60px;
            }
        }

        @media (max-width: 768px) {
            .market-pulse-hero {
                min-height: 70vh;
                padding: 40px 20px;
            }

            .trading-terminal {
                padding: 0 20px;
            }

            .hero-headline {
                font-size: clamp(2rem, 8vw, 3rem);
            }

            .trading-metrics {
                flex-direction: column;
                gap: 20px;
            }

            .tools-grid {
                grid-template-columns: 1fr;
                padding: 0 20px;
            }

            .intelligence-container {
                padding: 0 20px;
            }
        }

        /* === ADVANCED ANIMATIONS === */
        .fade-in-up {
            opacity: 0;
            transform: translateY(40px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .fade-in-up.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .stagger-delay-1 { transition-delay: 0.1s; }
        .stagger-delay-2 { transition-delay: 0.2s; }
        .stagger-delay-3 { transition-delay: 0.3s; }

        /* === CUSTOM SCROLLBAR === */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-color);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--highlight-color), var(--highlight-secondary));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--highlight-darker), var(--highlight-secondary-darker));
        }

    </style>
{% endblock %}

{% block content %}
    <!-- Market Pulse Hero -->
    <section class="market-pulse-hero">
        <div class="trading-terminal">
            <div class="terminal-left">
                <div class="market-status">
                    <div class="status-dot"></div>
                    <span>MARKETS OPEN</span>
                </div>

                <h1 class="hero-headline">
                    Professional Trading Intelligence
                </h1>

                <p class="hero-subline">
                    Built for serious traders who demand institutional-grade analysis, real-time insights, and precision tools that separate winners from the crowd.
                </p>

                <div class="trading-metrics">
                    <div class="metric">
                        <span class="metric-value" data-counter="2847">0</span>
                        <span class="metric-label">Active Positions</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" data-counter="127" data-suffix="M">0</span>
                        <span class="metric-label">Assets Under Analysis</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" data-counter="94" data-suffix="%">0</span>
                        <span class="metric-label">Accuracy Rate</span>
                    </div>
                </div>

                <div style="display: flex; gap: 20px; margin-top: 30px;">
                    <a href="{{ url_for('portfolio_page') }}" class="button" style="background: linear-gradient(135deg, #00d4ff, #0099cc); padding: 16px 32px; font-size: 1.1rem; font-weight: 600;">
                        <i class="fas fa-chart-line"></i> Launch Terminal
                    </a>
                    <button onclick="document.querySelector('.tools-showcase').scrollIntoView({behavior: 'smooth'})" class="button" style="background: transparent; border: 2px solid var(--highlight-color); color: var(--highlight-color); padding: 14px 32px;">
                        <i class="fas fa-play"></i> See Tools
                    </button>
                </div>
            </div>

            <div class="terminal-right">
                <div class="live-chart-container">
                    <div class="chart-header">
                        <div class="chart-title">S&P 500 • Live</div>
                        <div class="chart-change">+2.34%</div>
                    </div>
                    <div class="mini-chart">
                        <div class="chart-line"></div>
                        <canvas id="liveChart" width="350" height="180"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Professional Tools Showcase -->
    <section class="tools-showcase fade-in-up">
        <div class="tools-grid">
            <div class="tool-card stagger-delay-1">
                <div class="tool-header">
                    <div class="tool-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3 class="tool-title">Portfolio Command Center</h3>
                    <p class="tool-subtitle">Real-time portfolio monitoring with institutional-grade analytics</p>
                </div>
                <div class="tool-body">
                    <ul class="tool-features">
                        <li>Live P&L tracking with microsecond precision</li>
                        <li>Risk-adjusted performance metrics</li>
                        <li>Sector allocation heat maps</li>
                        <li>Correlation analysis & stress testing</li>
                        <li>Custom alert system for position changes</li>
                    </ul>
                    <button class="tool-cta" onclick="window.location.href='{{ url_for('portfolio_page') }}'">
                        Access Portfolio Terminal
                    </button>
                </div>
            </div>

            <div class="tool-card stagger-delay-2">
                <div class="tool-header">
                    <div class="tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h3 class="tool-title">DCF Valuation Engine</h3>
                    <p class="tool-subtitle">Wall Street-caliber discounted cash flow modeling</p>
                </div>
                <div class="tool-body">
                    <ul class="tool-features">
                        <li>Multi-stage growth modeling</li>
                        <li>Monte Carlo simulation capabilities</li>
                        <li>Sensitivity analysis & scenario planning</li>
                        <li>Industry-specific templates</li>
                        <li>Export to institutional formats</li>
                    </ul>
                    <button class="tool-cta" onclick="window.location.href='{{ url_for('dcf_page') }}'">
                        Launch DCF Calculator
                    </button>
                </div>
            </div>

            <div class="tool-card stagger-delay-3">
                <div class="tool-header">
                    <div class="tool-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="tool-title">AI Trading Assistant</h3>
                    <p class="tool-subtitle">Advanced market intelligence powered by machine learning</p>
                </div>
                <div class="tool-body">
                    <ul class="tool-features">
                        <li>Real-time market sentiment analysis</li>
                        <li>Pattern recognition & trend identification</li>
                        <li>Earnings prediction models</li>
                        <li>News impact scoring</li>
                        <li>Custom research queries</li>
                    </ul>
                    <button class="tool-cta" onclick="document.getElementById('chatbot-toggle').click()">
                        Activate AI Assistant
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Market Intelligence Section -->
    <section class="market-intelligence fade-in-up">
        <div class="intelligence-container">
            <div class="intelligence-content">
                <h2>Market Intelligence That Moves Markets</h2>
                <p>
                    While others react to yesterday's news, you'll be positioned ahead of tomorrow's opportunities.
                    Our platform processes millions of data points to deliver the insights that matter when they matter most.
                </p>

                <div class="intelligence-stats">
                    <div class="intel-stat">
                        <span class="intel-number" data-counter="847">0</span>
                        <span class="intel-label">Data Sources</span>
                    </div>
                    <div class="intel-stat">
                        <span class="intel-number" data-counter="12" data-suffix="ms">0</span>
                        <span class="intel-label">Latency</span>
                    </div>
                    <div class="intel-stat">
                        <span class="intel-number" data-counter="99.7" data-suffix="%">0</span>
                        <span class="intel-label">Uptime</span>
                    </div>
                    <div class="intel-stat">
                        <span class="intel-number" data-counter="24">0</span>
                        <span class="intel-label">Hours Coverage</span>
                    </div>
                </div>
            </div>

            <div class="intelligence-visual">
                <div class="data-visualization">
                    <div class="viz-header">
                        <div class="viz-title">Real-Time Market Pulse</div>
                        <div class="viz-indicator">
                            <div class="status-dot"></div>
                            <span>LIVE</span>
                        </div>
                    </div>
                    <div class="viz-body">
                        <div class="viz-pattern"></div>
                        <canvas id="marketPulse" width="320" height="180"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block scripts_extra %}
    {# Advanced Trading Platform JavaScript #}
    <script>
        // === PROFESSIONAL COUNTER ANIMATIONS ===
        function animateCounters() {
            const counters = document.querySelectorAll('[data-counter]');

            counters.forEach(counter => {
                if (counter.dataset.animated === 'true') return;

                const target = parseFloat(counter.getAttribute('data-counter'));
                const suffix = counter.getAttribute('data-suffix') || '';
                const duration = 2000; // 2 seconds
                const startTime = performance.now();

                counter.dataset.animated = 'false';

                const updateCounter = (currentTime) => {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // Easing function for smooth animation
                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                    const currentValue = target * easeOutQuart;

                    // Format the number based on target size
                    let displayValue;
                    if (target >= 1000) {
                        displayValue = Math.floor(currentValue).toLocaleString();
                    } else if (target >= 100) {
                        displayValue = Math.floor(currentValue);
                    } else {
                        displayValue = currentValue.toFixed(1);
                    }

                    counter.textContent = displayValue + suffix;

                    if (progress < 1) {
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.dataset.animated = 'true';
                    }
                };

                requestAnimationFrame(updateCounter);
            });
        }

        // === LIVE CHART SIMULATION ===
        function initLiveChart() {
            const canvas = document.getElementById('liveChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // Chart data
            let dataPoints = [];
            const maxPoints = 50;
            let baseValue = 4200;

            // Generate initial data
            for (let i = 0; i < maxPoints; i++) {
                dataPoints.push(baseValue + Math.random() * 100 - 50);
            }

            function drawChart() {
                // Clear canvas
                ctx.clearRect(0, 0, width, height);

                // Create gradient
                const gradient = ctx.createLinearGradient(0, 0, 0, height);
                gradient.addColorStop(0, 'rgba(0, 255, 136, 0.3)');
                gradient.addColorStop(1, 'rgba(0, 255, 136, 0.05)');

                // Draw area under curve
                ctx.beginPath();
                ctx.moveTo(0, height);

                dataPoints.forEach((point, index) => {
                    const x = (index / (maxPoints - 1)) * width;
                    const y = height - ((point - Math.min(...dataPoints)) / (Math.max(...dataPoints) - Math.min(...dataPoints))) * height;

                    if (index === 0) {
                        ctx.lineTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });

                ctx.lineTo(width, height);
                ctx.closePath();
                ctx.fillStyle = gradient;
                ctx.fill();

                // Draw line
                ctx.beginPath();
                dataPoints.forEach((point, index) => {
                    const x = (index / (maxPoints - 1)) * width;
                    const y = height - ((point - Math.min(...dataPoints)) / (Math.max(...dataPoints) - Math.min(...dataPoints))) * height;

                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });

                ctx.strokeStyle = '#00ff88';
                ctx.lineWidth = 2;
                ctx.stroke();

                // Add new data point
                dataPoints.shift();
                dataPoints.push(baseValue + Math.random() * 100 - 50);
            }

            // Update chart every 2 seconds
            setInterval(drawChart, 2000);
            drawChart(); // Initial draw
        }

        // === MARKET PULSE VISUALIZATION ===
        function initMarketPulse() {
            const canvas = document.getElementById('marketPulse');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let time = 0;

            function drawPulse() {
                ctx.clearRect(0, 0, width, height);

                // Draw multiple sine waves
                const waves = [
                    { amplitude: 30, frequency: 0.02, phase: 0, color: 'rgba(0, 212, 255, 0.8)' },
                    { amplitude: 20, frequency: 0.03, phase: Math.PI / 3, color: 'rgba(255, 107, 53, 0.6)' },
                    { amplitude: 15, frequency: 0.025, phase: Math.PI / 2, color: 'rgba(0, 255, 136, 0.7)' }
                ];

                waves.forEach(wave => {
                    ctx.beginPath();
                    ctx.strokeStyle = wave.color;
                    ctx.lineWidth = 2;

                    for (let x = 0; x < width; x++) {
                        const y = height / 2 + Math.sin(x * wave.frequency + time + wave.phase) * wave.amplitude;

                        if (x === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }

                    ctx.stroke();
                });

                time += 0.05;
                requestAnimationFrame(drawPulse);
            }

            drawPulse();
        }

        // === INTERSECTION OBSERVER FOR ANIMATIONS ===
        function initScrollAnimations() {
            const observerOptions = {
                root: null,
                rootMargin: '0px',
                threshold: 0.1
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');

                        // Trigger counters for specific sections
                        if (entry.target.querySelector('[data-counter]')) {
                            setTimeout(animateCounters, 300);
                        }
                    }
                });
            }, observerOptions);

            // Observe all fade-in-up elements
            document.querySelectorAll('.fade-in-up').forEach(el => {
                observer.observe(el);
            });
        }

        // === TOOL CARD INTERACTIONS ===
        function initToolCards() {
            const toolCards = document.querySelectorAll('.tool-card');

            toolCards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-12px) scale(1.02)';
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // === INITIALIZATION ===
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize all components
            initScrollAnimations();
            initLiveChart();
            initMarketPulse();
            initToolCards();

            // Trigger initial counter animation for hero metrics
            setTimeout(animateCounters, 1000);

            // Add smooth scrolling for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // === PERFORMANCE OPTIMIZATION ===
        // Throttle scroll events for better performance
        let ticking = false;

        function updateOnScroll() {
            // Add any scroll-based animations here
            ticking = false;
        }

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateOnScroll);
                ticking = true;
            }
        });
    </script>
{% endblock %}
