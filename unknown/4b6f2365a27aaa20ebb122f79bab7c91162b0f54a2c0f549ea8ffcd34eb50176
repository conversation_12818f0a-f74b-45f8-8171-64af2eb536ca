from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
import requests
import json
from datetime import datetime, timedelta
import logging

# Create the blueprint
financial_reports_bp = Blueprint('financial_reports', __name__, url_prefix='/financial-reports')

def fetch_eodhd_data(ticker, api_key, filters=None):
    """
    Fetch data from EODHD API with optional filters.
    
    Args:
        ticker (str): Stock ticker symbol
        api_key (str): EODHD API key
        filters (list): Optional list of data filters
        
    Returns:
        dict: API response data
    """
    try:
        base_url = "https://eodhd.com/api/fundamentals"
        params = {
            'api_token': api_key,
            'fmt': 'json'
        }
        
        if filters:
            params['filter'] = ','.join(filters)
            
        url = f"{base_url}/{ticker}"
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        
        return response.json()
        
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Error fetching EODHD data for {ticker}: {e}")
        return None
    except json.JSONDecodeError as e:
        current_app.logger.error(f"Error parsing EODHD response for {ticker}: {e}")
        return None

@financial_reports_bp.route('/')
def index():
    """Financial reports search page."""
    return render_template('financial_reports_search.html')

@financial_reports_bp.route('/', methods=['POST'])
def search_reports():
    """Handle financial reports search."""
    ticker = request.form.get('ticker', '').strip().upper()
    
    if not ticker:
        flash('Please enter a ticker symbol', 'error')
        return redirect(url_for('financial_reports.index'))
    
    # Ensure ticker has exchange suffix
    if '.' not in ticker:
        ticker += '.US'
    
    return redirect(url_for('financial_reports.list_reports', ticker_exchange=ticker))

@financial_reports_bp.route('/reports/<ticker_exchange>')
def list_reports(ticker_exchange):
    """List available financial reports for a ticker."""
    try:
        api_key = current_app.config.get('EODHD_API_KEY')
        if not api_key:
            flash('API key not configured', 'error')
            return redirect(url_for('financial_reports.index'))
        
        # Fetch basic company data to verify ticker exists
        data = fetch_eodhd_data(ticker_exchange, api_key, filters=['General'])
        
        if not data or 'General' not in data:
            flash(f'No data found for ticker {ticker_exchange}', 'error')
            return redirect(url_for('financial_reports.index'))
        
        company_name = data.get('General', {}).get('Name', ticker_exchange)
        
        # Generate list of available reports
        reports = [
            {
                'type': 'Balance Sheet',
                'period': 'Annual',
                'date': 'Latest',
                'description': 'Company assets, liabilities, and equity'
            },
            {
                'type': 'Income Statement',
                'period': 'Annual', 
                'date': 'Latest',
                'description': 'Revenue, expenses, and profit/loss'
            },
            {
                'type': 'Cash Flow Statement',
                'period': 'Annual',
                'date': 'Latest', 
                'description': 'Operating, investing, and financing cash flows'
            },
            {
                'type': 'Key Metrics',
                'period': 'TTM',
                'date': 'Latest',
                'description': 'Financial ratios and performance metrics'
            }
        ]
        
        return render_template('financial_reports_list.html', 
                             ticker=ticker_exchange,
                             company_name=company_name,
                             reports=reports)
        
    except Exception as e:
        current_app.logger.error(f"Error listing reports for {ticker_exchange}: {e}")
        flash('Error retrieving financial reports', 'error')
        return redirect(url_for('financial_reports.index'))

@financial_reports_bp.route('/reports/<ticker_exchange>/<period>/<report_type>/<report_date_str>')
def view_report(ticker_exchange, period, report_type, report_date_str):
    """View detailed financial report."""
    try:
        api_key = current_app.config.get('EODHD_API_KEY')
        if not api_key:
            flash('API key not configured', 'error')
            return redirect(url_for('financial_reports.index'))
        
        # Fetch comprehensive financial data
        filters = ['General', 'Highlights', 'Valuation', 'Financials']
        data = fetch_eodhd_data(ticker_exchange, api_key, filters=filters)
        
        if not data:
            flash(f'No data found for {ticker_exchange}', 'error')
            return redirect(url_for('financial_reports.list_reports', ticker_exchange=ticker_exchange))
        
        return render_template('financial_report_detail.html',
                             ticker=ticker_exchange,
                             report_type=report_type,
                             period=period,
                             data=data)
        
    except Exception as e:
        current_app.logger.error(f"Error viewing report for {ticker_exchange}: {e}")
        flash('Error loading financial report', 'error')
        return redirect(url_for('financial_reports.list_reports', ticker_exchange=ticker_exchange))

@financial_reports_bp.route('/interactive/<ticker_exchange>')
def interactive_statements(ticker_exchange):
    """Interactive financial statements view."""
    try:
        api_key = current_app.config.get('EODHD_API_KEY')
        if not api_key:
            flash('API key not configured', 'error')
            return redirect(url_for('financial_reports.index'))
        
        # Fetch comprehensive financial data
        filters = ['General', 'Highlights', 'Valuation', 'Financials']
        data = fetch_eodhd_data(ticker_exchange, api_key, filters=filters)
        
        if not data:
            flash(f'No data found for {ticker_exchange}', 'error')
            return redirect(url_for('financial_reports.index'))
        
        return render_template('interactive_financial_statements.html',
                             ticker=ticker_exchange,
                             data=data)
        
    except Exception as e:
        current_app.logger.error(f"Error loading interactive statements for {ticker_exchange}: {e}")
        flash('Error loading interactive statements', 'error')
        return redirect(url_for('financial_reports.index'))