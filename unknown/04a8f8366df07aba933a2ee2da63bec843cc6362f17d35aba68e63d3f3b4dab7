{% extends "base.html" %}

{% block title %}Financial Reports for {{ company_info.name or ticker }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-1">Financial Reports for {{ company_info.name or ticker }}</h1>
    {% if company_info.code and company_info.exchange %}
        <h5 class="text-muted mb-4">{{ company_info.code }}.{{ company_info.exchange }} | Currency: {{ company_info.currency or 'N/A' }}</h5>
    {% else %}
        <h5 class="text-muted mb-4">{{ ticker }}</h5>
    {% endif %}

    {% if reports %}
        <p>Select a report to view its details. Data is sourced from EODHD and represents information typically found in 10-K (Annual) and 10-Q (Quarterly) filings.</p>
        <div class="list-group">
            {% for report in reports %}
                <a href="{{ url_for('financial_reports.view_report', ticker_exchange=ticker, period=report.period, report_type=report.type, report_date_str=report.date) }}" class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h5 class="mb-1">{{ report.type }}</h5>
                        <small>{{ report.date_str }}</small>
                    </div>
                    <p class="mb-1">Filed on: {{ report.date_obj.strftime('%B %d, %Y') }}</p>
                </a>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-warning" role="alert">
            No financial reports found for {{ ticker }}, or there was an error fetching the data. Please ensure the ticker is correct (e.g., AAPL.US) and try again.
        </div>
    {% endif %}

    <a href="{{ url_for('financial_reports.index') }}" class="btn btn-secondary mt-4">Search for another company</a>
</div>
{% endblock %}
