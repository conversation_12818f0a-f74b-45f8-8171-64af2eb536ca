#!/usr/bin/env python3
"""
Test script to verify the currency detection and UI fix works correctly.
This simulates the flow that should happen when Gemini AI detects JPY currency.
"""

import sys
import os
import json

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from portfolio_import import PortfolioImportService
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)

def test_japanese_portfolio_currency_detection():
    """Test that Japanese portfolios are detected with JPY currency."""
    print("🧪 Testing Japanese Portfolio Currency Detection")
    print("=" * 60)
    
    # Create service instance
    service = PortfolioImportService("test_key", "test_key")
    
    # Japanese portfolio text that should trigger JPY detection
    japanese_portfolio_text = """
    Portfolio Holdings (JPY):
    
    Ticker: SHOP (Shopify Inc)
    Amount Invested: ¥1,585,806
    Buy Price: ¥75.43
    Shares: 140.86
    
    Ticker: PLTR (Palantir Technologies)  
    Amount Invested: ¥691,303
    Buy Price: ¥25.43
    Shares: 182.12
    
    Ticker: RBLX (Roblox Corporation)
    Amount Invested: ¥935,654
    Buy Price: ¥36.39  
    Shares: 172.28
    """
    
    print("📋 Test Input Text:")
    print(japanese_portfolio_text[:200] + "...")
    print()
    
    # Process the text
    try:
        # Use the correct method name for processing portfolio data
        result = service.process_portfolio_data(japanese_portfolio_text, user_portfolio_currency=None)
        
        print("✅ Processing completed successfully!")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Detected Currency: {result.get('detected_currency', 'None')}")
        print(f"   Portfolio Entries: {len(result.get('portfolio', []))}")
        print()
        
        # Check if JPY was detected
        detected_currency = result.get('detected_currency')
        if detected_currency == 'JPY':
            print("🎯 ✅ JPY currency correctly detected!")
        else:
            print(f"❌ Expected JPY, but detected: {detected_currency}")
        
        # Check portfolio entries
        portfolio = result.get('portfolio', [])
        if portfolio:
            print(f"📊 Portfolio entries ({len(portfolio)}):")
            for i, entry in enumerate(portfolio[:3]):  # Show first 3 entries
                ticker = getattr(entry, 'ticker', 'Unknown')
                amount = getattr(entry, 'amount_invested', 0)
                currency = getattr(entry, 'currency', 'Unknown')
                amount_currency = getattr(entry, 'amount_invested_currency', 'Unknown')
                
                print(f"   {i+1}. {ticker}: {amount:,.2f} {amount_currency}")
                
                # Check if JPY is properly set
                if amount_currency == 'JPY':
                    print(f"      ✅ Amount currency correctly set to JPY")
                else:
                    print(f"      ❌ Amount currency is {amount_currency}, expected JPY")
        
        # Check currency info for UI
        currency_info = result.get('currency_info', {})
        if currency_info:
            print(f"\n🔍 Currency Info for UI:")
            print(f"   Detected Currencies: {currency_info.get('detected_currencies', [])}")
            print(f"   Primary Currency: {currency_info.get('primary_currency', 'None')}")
            print(f"   Requires User Selection: {currency_info.get('requires_user_selection', False)}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error processing text: {e}")
        return None

def test_currency_conversion():
    """Test that currency conversion works properly in the frontend logic."""
    print("\n🧪 Testing Currency Conversion Logic")
    print("=" * 60)
    
    # Define conversion rates (same as in portfolio_import.html)
    CURRENCY_RATES = {
        'USD': 1.0,
        'JPY': 0.0067,
        'EUR': 1.08,
        'DKK': 0.145,
    }
    
    def convert_currency(amount, from_currency, to_currency):
        if from_currency == to_currency:
            return amount
        
        from_rate = CURRENCY_RATES.get(from_currency, 1.0)
        to_rate = CURRENCY_RATES.get(to_currency, 1.0)
        
        # Convert to USD first, then to target currency
        usd_amount = amount * from_rate
        converted_amount = usd_amount / to_rate
        
        return round(converted_amount * 100) / 100
    
    # Test JPY to USD conversion
    jpy_amount = 1585806.00
    usd_converted = convert_currency(jpy_amount, 'JPY', 'USD')
    
    print(f"💱 JPY to USD Conversion Test:")
    print(f"   Original: ¥{jpy_amount:,.2f}")
    print(f"   Converted: ${usd_converted:,.2f}")
    print(f"   Rate used: 1 JPY = {CURRENCY_RATES['JPY']} USD")
    
    # Test USD to JPY conversion
    usd_amount = 10625.00
    jpy_converted = convert_currency(usd_amount, 'USD', 'JPY')
    
    print(f"\n💱 USD to JPY Conversion Test:")
    print(f"   Original: ${usd_amount:,.2f}")
    print(f"   Converted: ¥{jpy_converted:,.2f}")
    print(f"   Rate used: 1 USD = {1/CURRENCY_RATES['JPY']:.2f} JPY")
    
    print(f"\n✅ Currency conversion logic working correctly!")

def main():
    """Run all currency fix tests."""
    print("🚀 CURRENCY FIX VERIFICATION")
    print("=" * 60)
    print("Testing the fixes to ensure detected currency appears in Currency Settings UI")
    print()
    
    # Test 1: Japanese portfolio detection
    jpy_result = test_japanese_portfolio_currency_detection()
    
    # Test 2: Currency conversion logic
    test_currency_conversion()
    
    # Summary
    print("\n📋 TEST SUMMARY")
    print("=" * 60)
    
    if jpy_result:
        detected_currency = jpy_result.get('detected_currency')
        portfolio_count = len(jpy_result.get('portfolio', []))
        
        if detected_currency == 'JPY' and portfolio_count > 0:
            print("✅ CURRENCY DETECTION: Working correctly!")
            print("✅ PORTFOLIO EXTRACTION: Working correctly!")
            print("✅ FRONTEND INTEGRATION: Ready to display JPY in Currency Settings")
            print()
            print("🎯 EXPECTED BEHAVIOR IN UI:")
            print("   1. Currency Settings dropdown should show 'Japanese Yen (¥)' instead of 'US Dollar ($)'")
            print("   2. Individual currency labels should show 'JPY' instead of 'USD'")
            print("   3. When user changes currency, amounts should convert properly")
            print()
            print("✅ ALL FIXES IMPLEMENTED SUCCESSFULLY!")
            
        else:
            print("❌ Some tests failed. Check output above for details.")
    else:
        print("❌ Tests failed to run. Check error messages above.")

if __name__ == "__main__":
    main()