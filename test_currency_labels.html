<!DOCTYPE html>
<html>
<head>
    <title>Test Currency Label Updates</title>
    <style>
        .test-container { margin: 20px; }
        .currency-dropdown { margin: 10px 0; }
        .test-table { border-collapse: collapse; width: 100%; }
        .test-table th, .test-table td { border: 1px solid #ccc; padding: 8px; }
        .amount-currency-label { color: blue; font-weight: bold; }
        .buy-price-currency-label { color: green; font-weight: bold; }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>🧪 Currency Label Update Test</h2>
        
        <div class="currency-dropdown">
            <label>Portfolio Currency:</label>
            <select id="portfolioCurrency" onchange="testCurrencyChange(this.value)">
                <option value="JPY">🇯🇵 Japanese Yen (¥)</option>
                <option value="USD">🇺🇸 US Dollar ($)</option>
                <option value="EUR">🇪🇺 Euro (€)</option>
            </select>
        </div>
        
        <table class="test-table">
            <thead>
                <tr>
                    <th>Ticker</th>
                    <th>Amount Invested</th>
                    <th>Buy Price</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>SHOP</td>
                    <td>
                        1,585,806
                        <br><small class="amount-currency-label">JPY</small>
                    </td>
                    <td>
                        75.43
                        <br><small class="buy-price-currency-label">JPY</small>
                    </td>
                </tr>
                <tr>
                    <td>PLTR</td>
                    <td>
                        691,303
                        <br><small class="amount-currency-label">JPY</small>
                    </td>
                    <td>
                        25.43
                        <br><small class="buy-price-currency-label">JPY</small>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div id="log" style="margin-top: 20px; background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;"></div>
    </div>

    <script>
        let selectedCurrency = 'JPY';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateCurrencyLabelsInTable() {
            log('🔄 Updating currency labels in table...');
            
            // Update all "Amount Invested" currency labels to show selected currency
            const amountLabels = document.querySelectorAll('.amount-currency-label');
            amountLabels.forEach(label => {
                const newCurrencyText = selectedCurrency || 'USD';
                label.textContent = newCurrencyText;
                log(`💰 Updated amount label to: ${newCurrencyText}`);
            });
            
            // Buy price labels stay as original currency (JPY in this case)
            log('🔒 Buy price labels preserved as JPY (original currency)');
        }
        
        function testCurrencyChange(newCurrency) {
            log(`🎯 Currency changed from ${selectedCurrency} to ${newCurrency}`);
            selectedCurrency = newCurrency;
            updateCurrencyLabelsInTable();
        }
        
        // Initialize
        log('✅ Test initialized - try changing the currency dropdown');
        log('Expected behavior:');
        log('- Amount Invested labels should change to selected currency');
        log('- Buy Price labels should stay as JPY (original currency)');
    </script>
</body>
</html>